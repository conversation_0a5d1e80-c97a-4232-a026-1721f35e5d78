import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store";
import NumberCounterItem from "./number-counter-item";
import FilterContentLayout from "./filter-content-layout";
import { useTranslations } from "next-intl";

export default function RoomsAndBeds() {
  const t = useTranslations("seeker")
  const { bathRoom, bedRoom, setBathRoom, setBedroom } = useSeekerFilterStore(state => state)
  return <FilterContentLayout title="Space Overview">
    <NumberCounterItem
      title={t('listing.feature.additionalFeature.bedroom')}
      setValue={setBedroom}
      value={bedRoom || "any"}
    />
    <NumberCounterItem
      title={t('listing.feature.additionalFeature.bathroom')}
      setValue={setBathRoom}
      value={bathRoom || "any"}
    />
  </FilterContentLayout>
}