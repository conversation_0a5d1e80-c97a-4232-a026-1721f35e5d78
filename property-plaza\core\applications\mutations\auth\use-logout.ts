import { logout } from "@/core/infrastructures/auth";
import { ACCESS_TOKEN } from "@/lib/constanta/constant";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Cookies from "js-cookie";
import { MY_DETAIL_QUERY_KEY } from "../../queries/users/use-get-me";

export function useLogout(typeUser: "seekers" | "owner" = "owner") {
  const queryClient = useQueryClient();
  const mutation = useMutation({
    mutationFn: () => logout(),
    onSuccess: () => {
      Cookies.remove(ACCESS_TOKEN);
      Cookies.remove("user");
      queryClient.invalidateQueries({
        queryKey: [MY_DETAIL_QUERY_KEY],
        refetchType: "none",
      });
      window.location.assign("/");
    },
    onError: (error) => {
      Cookies.remove(ACCESS_TOKEN);
      Cookies.remove("user");
      window.location.assign("/");
    },
  });
  return mutation;
}
