import { Metadata } from "next"
import AboutUsContent from "./about-us-content"
import { getLocale, getTranslations } from "next-intl/server"
import { aboutUsUrl } from "@/lib/constanta/route"

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const locale = await getLocale()
  return {
    title: t('metadata.aboutUs.title'),
    description: t('metadata.aboutUs.description'),
    keywords: t('metadata.aboutUs.keyword'),
    openGraph: {
      title: t('metadata.aboutUs.title'),
      description: t('metadata.aboutUs.description'),
      // images: [
      //   {
      //     url: "/about-us-image.webp",
      //     width: 1200,
      //     height: 630,
      //     alt: "Property Plaza Team"
      //   }
      // ],
      type: "website"
    },
    twitter: {
      card: "summary_large_image",
      title: t('metadata.aboutUs.title'),
      description: t('metadata.aboutUs.description'),
      // images: ["/about-us-image.webp"]
    },
    alternates: {
      canonical: 'https://property-plaza.com/' + locale + aboutUsUrl,
      languages: {
        en: process.env.USER_DOMAIN + "en" + aboutUsUrl,
        id: process.env.USER_DOMAIN + "id" + aboutUsUrl
      }
    }
  }
}

export default function AboutUsPage() {
  return <>
    <AboutUsContent />
  </>
} 