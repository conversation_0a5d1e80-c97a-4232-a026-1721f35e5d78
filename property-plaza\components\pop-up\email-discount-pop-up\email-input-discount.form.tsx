import { useEmailFormSchema } from "@/app/[locale]/create-password/form/use-email-form.schema";
import DefaultInput from "@/components/input-form/default-input";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useJoinWaitingList } from "@/core/applications/mutations/waiting-list/use-join-waiting-list";
import { WaitingListDto } from "@/core/infrastructures/waiting-list/dto";
import { gtagEvent } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod"
export default function EmailInputDiscountForm({ setIsSubmitted }: { setIsSubmitted: (val: boolean) => void }) {
  const t = useTranslations("seeker")
  const formSchema = useEmailFormSchema()
  const useWaitingJoinMutation = useJoinWaitingList()
  type formSchemaType = z.infer<typeof formSchema>
  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: ""
    }
  })

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    const data: WaitingListDto = {
      email: values.email,
      name: "discount-popup-lead"
    }
    try {
      await useWaitingJoinMutation.mutateAsync(data)
      gtagEvent({
        action: "lead_magnet_form_submit",
        category: "Lead Magnet",
        label: "Email Capture for Discount Code",
        value: "1"
      })
    } catch (e: any) {
      // Error handling is done in the mutation hook
    } finally {
      setIsSubmitted(true)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <DefaultInput
          type="email"
          form={form}
          name="email"
          variant="float"
          label={t("form.label.email")}
          labelClassName="text-xs text-seekers-text-light font-normal"
          placeholder=""
        />
        <Button
          type="submit"
          className="w-full"
          loading={useWaitingJoinMutation.isPending}
        >
          {t('cta.getDiscount')}
        </Button>
      </form>
    </Form>
  )
}