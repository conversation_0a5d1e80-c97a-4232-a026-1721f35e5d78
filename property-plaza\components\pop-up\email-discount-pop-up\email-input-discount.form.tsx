import { useEmailFormSchema } from "@/app/[locale]/create-password/form/use-email-form.schema";
import DefaultInput from "@/components/input-form/default-input";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useJoinWaitingList } from "@/core/applications/mutations/waiting-list/use-join-waiting-list";
import { WaitingListDto } from "@/core/infrastructures/waiting-list/dto";
import { useToast } from "@/hooks/use-toast";
import { gtagEvent } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod"
import { Gift, Shield, Zap } from "lucide-react"
export default function EmailInputDiscountForm({ setIsSubmitted }: { setIsSubmitted: (val: boolean) => void }) {
  const t = useTranslations("seeker")
  const formSchema = useEmailFormSchema()
  const useWaitingJoinMutation = useJoinWaitingList()
  type formSchemaType = z.infer<typeof formSchema>
  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: ""
    }
  })

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    const data: WaitingListDto = {
      email: values.email,
      name: "discount-popup-lead"
    }
    try {
      await useWaitingJoinMutation.mutateAsync(data)
      gtagEvent({
        action: "discount_popup_conversion",
        category: "Lead Magnet",
        label: "Email Capture for Discount Code - Enhanced Popup",
        value: "25"
      })
    } catch (e: any) {
      // Error handling is done in the mutation hook
    } finally {
      setIsSubmitted(true)
    }
  }

  return (
    <div className="space-y-4">
      {/* Trust Indicators */}
      <div className="flex justify-center space-x-6 text-xs text-gray-600 mb-4">
        <div className="flex items-center space-x-1">
          <Shield className="h-3 w-3 text-green-500" />
          <span>100% Secure</span>
        </div>
        <div className="flex items-center space-x-1">
          <Zap className="h-3 w-3 text-yellow-500" />
          <span>Instant Access</span>
        </div>
        <div className="flex items-center space-x-1">
          <Gift className="h-3 w-3 text-purple-500" />
          <span>No Spam</span>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="relative">
            <DefaultInput
              type="email"
              form={form}
              name="email"
              variant="float"
              label="Enter your email address"
              labelClassName="text-sm text-gray-600 font-medium"
              placeholder="<EMAIL>"
              className="h-12 text-base border-2 border-gray-200 focus:border-teal-500 rounded-lg"
            />
          </div>

          <Button
            type="submit"
            className="w-full h-12 bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white font-bold text-base rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]"
            loading={useWaitingJoinMutation.isPending}
            disabled={useWaitingJoinMutation.isPending}
          >
            {useWaitingJoinMutation.isPending ? (
              "Securing Your Discount..."
            ) : (
              "🎯 Get My 25% Discount + Scam Guide"
            )}
          </Button>

          {/* Additional motivation */}
          <p className="text-center text-xs text-gray-500">
            ✅ Instant download • ✅ No credit card required • ✅ Join 2,500+ protected expats
          </p>
        </form>
      </Form>
    </div>
  )
}