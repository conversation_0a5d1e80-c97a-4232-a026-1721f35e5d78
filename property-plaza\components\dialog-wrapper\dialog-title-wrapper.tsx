"use client"

import { useMediaQuery } from "@/hooks/use-media-query"
import { DrawerTitle } from "../ui/drawer"
import { DialogTitle } from "../ui/dialog"

export default function DialogTitleWrapper({ children, className }: { children: React.ReactNode, className?: string }) {
  const isDesktop = useMediaQuery("(min-width:1024px)")
  if (isDesktop) {
    return <DialogTitle className={className}>
      {children}
    </DialogTitle>
  } else {
    return <DrawerTitle className={className}>
      {children}
    </DrawerTitle>
  }
}