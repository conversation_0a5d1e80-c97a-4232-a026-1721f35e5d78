"use client"
import { But<PERSON> } from "@/components/ui/button";
import { useFavoriteListing } from "@/hooks/use-post-favorite-listing";
import { useUserStore } from "@/stores/user.store";
import { MessagesSquare } from "lucide-react";
import { useTranslations } from "next-intl";
import useImageGallery from "../utils/use-image-gallery";
import StartChatWithOwner from "@/app/[locale]/(user-profile)/message/start-chat-with-owner";
import { toast } from "@/hooks/use-toast";
import { packages } from "@/core/domain/subscription/subscription";

const START_CHAT_OWNER_BUTTON_ID = "start-chat-owner-button"
export default function ContactOwner({ ownerId, propertyId, isActiveListing, middlemanId }: { ownerId: string, propertyId: string, isActiveListing: boolean, middlemanId?: string }) {
  const t = useTranslations("seeker")
  const { seekers } = useUserStore()
  const { authenticated: isAuthenticated } = useFavoriteListing("", false)
  const { handleOpenAuthDialog, handleOpenSubscriptionDialog } = useImageGallery()
  const handleChatOwner = () => {
    if (!isAuthenticated) return handleOpenAuthDialog()
    if (seekers.accounts.membership == packages.free) {
      return handleOpenSubscriptionDialog()
    }
    const chatOwnerBtn = document.getElementById(START_CHAT_OWNER_BUTTON_ID)
    chatOwnerBtn?.click()
  }
  return <>
    <Button
      variant={"default-seekers"}
      className="md:hidden"
      onClick={handleChatOwner}
      disabled={!isActiveListing}
    >
      <MessagesSquare />
      {t('cta.contactOwner')}
    </Button>
    <Button
      onClick={handleChatOwner}
      disabled={!isActiveListing}
      variant={"default-seekers"}
      className="max-md:hidden w-full text-base"
      size={"lg"}>
      <MessagesSquare />
      {middlemanId ? t("cta.contactMiddleman") :
        t('cta.contactOwner')
      }
    </Button>
    <StartChatWithOwner customTrigger={
      <button
        id={START_CHAT_OWNER_BUTTON_ID} className="hidden">
      </button>}
      ownerId={middlemanId || ownerId}
      propertyId={propertyId}
    />
  </>

}