"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_sanity_next-loader_dist__chunks-es_SanityLiveStream_js"],{

/***/ "(app-pages-browser)/./node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeIntoResult: function() { return /* binding */ encodeIntoResult; },\n/* harmony export */   stegaEncodeSourceMap: function() { return /* binding */ stegaEncodeSourceMap; },\n/* harmony export */   stegaEncodeSourceMap$1: function() { return /* binding */ stegaEncodeSourceMap$1; }\n/* harmony export */ });\n/* harmony import */ var _stegaClean_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stegaClean.js */ \"(app-pages-browser)/./node_modules/@sanity/client/dist/_chunks-es/stegaClean.js\");\n\nconst reKeySegment = /_key\\s*==\\s*['\"](.*)['\"]/;\nfunction isKeySegment(segment) {\n  return typeof segment == \"string\" ? reKeySegment.test(segment.trim()) : typeof segment == \"object\" && \"_key\" in segment;\n}\nfunction toString(path) {\n  if (!Array.isArray(path))\n    throw new Error(\"Path is not an array\");\n  return path.reduce((target, segment, i) => {\n    const segmentType = typeof segment;\n    if (segmentType === \"number\")\n      return `${target}[${segment}]`;\n    if (segmentType === \"string\")\n      return `${target}${i === 0 ? \"\" : \".\"}${segment}`;\n    if (isKeySegment(segment) && segment._key)\n      return `${target}[_key==\"${segment._key}\"]`;\n    if (Array.isArray(segment)) {\n      const [from, to] = segment;\n      return `${target}[${from}:${to}]`;\n    }\n    throw new Error(`Unsupported path segment \\`${JSON.stringify(segment)}\\``);\n  }, \"\");\n}\nconst ESCAPE = {\n  \"\\f\": \"\\\\f\",\n  \"\\n\": \"\\\\n\",\n  \"\\r\": \"\\\\r\",\n  \"\t\": \"\\\\t\",\n  \"'\": \"\\\\'\",\n  \"\\\\\": \"\\\\\\\\\"\n}, UNESCAPE = {\n  \"\\\\f\": \"\\f\",\n  \"\\\\n\": `\n`,\n  \"\\\\r\": \"\\r\",\n  \"\\\\t\": \"\t\",\n  \"\\\\'\": \"'\",\n  \"\\\\\\\\\": \"\\\\\"\n};\nfunction jsonPath(path) {\n  return `$${path.map((segment) => typeof segment == \"string\" ? `['${segment.replace(/[\\f\\n\\r\\t'\\\\]/g, (match) => ESCAPE[match])}']` : typeof segment == \"number\" ? `[${segment}]` : segment._key !== \"\" ? `[?(@._key=='${segment._key.replace(/['\\\\]/g, (match) => ESCAPE[match])}')]` : `[${segment._index}]`).join(\"\")}`;\n}\nfunction parseJsonPath(path) {\n  const parsed = [], parseRe = /\\['(.*?)'\\]|\\[(\\d+)\\]|\\[\\?\\(@\\._key=='(.*?)'\\)\\]/g;\n  let match;\n  for (; (match = parseRe.exec(path)) !== null; ) {\n    if (match[1] !== void 0) {\n      const key = match[1].replace(/\\\\(\\\\|f|n|r|t|')/g, (m) => UNESCAPE[m]);\n      parsed.push(key);\n      continue;\n    }\n    if (match[2] !== void 0) {\n      parsed.push(parseInt(match[2], 10));\n      continue;\n    }\n    if (match[3] !== void 0) {\n      const _key = match[3].replace(/\\\\(\\\\')/g, (m) => UNESCAPE[m]);\n      parsed.push({\n        _key,\n        _index: -1\n      });\n      continue;\n    }\n  }\n  return parsed;\n}\nfunction jsonPathToStudioPath(path) {\n  return path.map((segment) => {\n    if (typeof segment == \"string\" || typeof segment == \"number\")\n      return segment;\n    if (segment._key !== \"\")\n      return { _key: segment._key };\n    if (segment._index !== -1)\n      return segment._index;\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`);\n  });\n}\nfunction jsonPathToMappingPath(path) {\n  return path.map((segment) => {\n    if (typeof segment == \"string\" || typeof segment == \"number\")\n      return segment;\n    if (segment._index !== -1)\n      return segment._index;\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`);\n  });\n}\nfunction resolveMapping(resultPath, csm) {\n  if (!csm?.mappings)\n    return;\n  const resultMappingPath = jsonPath(jsonPathToMappingPath(resultPath));\n  if (csm.mappings[resultMappingPath] !== void 0)\n    return {\n      mapping: csm.mappings[resultMappingPath],\n      matchedPath: resultMappingPath,\n      pathSuffix: \"\"\n    };\n  const mappings = Object.entries(csm.mappings).filter(([key]) => resultMappingPath.startsWith(key)).sort(([key1], [key2]) => key2.length - key1.length);\n  if (mappings.length == 0)\n    return;\n  const [matchedPath, mapping] = mappings[0], pathSuffix = resultMappingPath.substring(matchedPath.length);\n  return { mapping, matchedPath, pathSuffix };\n}\nfunction isArray(value) {\n  return value !== null && Array.isArray(value);\n}\nfunction isRecord(value) {\n  return typeof value == \"object\" && value !== null;\n}\nfunction walkMap(value, mappingFn, path = []) {\n  if (isArray(value))\n    return value.map((v, idx) => {\n      if (isRecord(v)) {\n        const _key = v._key;\n        if (typeof _key == \"string\")\n          return walkMap(v, mappingFn, path.concat({ _key, _index: idx }));\n      }\n      return walkMap(v, mappingFn, path.concat(idx));\n    });\n  if (isRecord(value)) {\n    if (value._type === \"block\" || value._type === \"span\") {\n      const result = { ...value };\n      return value._type === \"block\" ? result.children = walkMap(value.children, mappingFn, path.concat(\"children\")) : value._type === \"span\" && (result.text = walkMap(value.text, mappingFn, path.concat(\"text\"))), result;\n    }\n    return Object.fromEntries(\n      Object.entries(value).map(([k, v]) => [k, walkMap(v, mappingFn, path.concat(k))])\n    );\n  }\n  return mappingFn(value, path);\n}\nfunction encodeIntoResult(result, csm, encoder) {\n  return walkMap(result, (value, path) => {\n    if (typeof value != \"string\")\n      return value;\n    const resolveMappingResult = resolveMapping(path, csm);\n    if (!resolveMappingResult)\n      return value;\n    const { mapping, matchedPath } = resolveMappingResult;\n    if (mapping.type !== \"value\" || mapping.source.type !== \"documentValue\")\n      return value;\n    const sourceDocument = csm.documents[mapping.source.document], sourcePath = csm.paths[mapping.source.path], matchPathSegments = parseJsonPath(matchedPath), fullSourceSegments = parseJsonPath(sourcePath).concat(path.slice(matchPathSegments.length));\n    return encoder({\n      sourcePath: fullSourceSegments,\n      sourceDocument,\n      resultPath: path,\n      value\n    });\n  });\n}\nconst DRAFTS_PREFIX = \"drafts.\";\nfunction getPublishedId(id) {\n  return id.startsWith(DRAFTS_PREFIX) ? id.slice(DRAFTS_PREFIX.length) : id;\n}\nfunction createEditUrl(options) {\n  const {\n    baseUrl,\n    workspace: _workspace = \"default\",\n    tool: _tool = \"default\",\n    id: _id,\n    type,\n    path,\n    projectId,\n    dataset\n  } = options;\n  if (!baseUrl)\n    throw new Error(\"baseUrl is required\");\n  if (!path)\n    throw new Error(\"path is required\");\n  if (!_id)\n    throw new Error(\"id is required\");\n  if (baseUrl !== \"/\" && baseUrl.endsWith(\"/\"))\n    throw new Error(\"baseUrl must not end with a slash\");\n  const workspace = _workspace === \"default\" ? void 0 : _workspace, tool = _tool === \"default\" ? void 0 : _tool, id = getPublishedId(_id), stringifiedPath = Array.isArray(path) ? toString(jsonPathToStudioPath(path)) : path, searchParams = new URLSearchParams({\n    baseUrl,\n    id,\n    type,\n    path: stringifiedPath\n  });\n  workspace && searchParams.set(\"workspace\", workspace), tool && searchParams.set(\"tool\", tool), projectId && searchParams.set(\"projectId\", projectId), dataset && searchParams.set(\"dataset\", dataset), _id.startsWith(DRAFTS_PREFIX) && searchParams.set(\"isDraft\", \"\");\n  const segments = [baseUrl === \"/\" ? \"\" : baseUrl];\n  workspace && segments.push(workspace);\n  const routerParams = [\n    \"mode=presentation\",\n    `id=${id}`,\n    `type=${type}`,\n    `path=${encodeURIComponent(stringifiedPath)}`\n  ];\n  return tool && routerParams.push(`tool=${tool}`), segments.push(\"intent\", \"edit\", `${routerParams.join(\";\")}?${searchParams}`), segments.join(\"/\");\n}\nfunction resolveStudioBaseRoute(studioUrl) {\n  let baseUrl = typeof studioUrl == \"string\" ? studioUrl : studioUrl.baseUrl;\n  return baseUrl !== \"/\" && (baseUrl = baseUrl.replace(/\\/$/, \"\")), typeof studioUrl == \"string\" ? { baseUrl } : { ...studioUrl, baseUrl };\n}\nconst filterDefault = ({ sourcePath, resultPath, value }) => {\n  if (isValidDate(value) || isValidURL(value))\n    return !1;\n  const endPath = sourcePath.at(-1);\n  return !(sourcePath.at(-2) === \"slug\" && endPath === \"current\" || typeof endPath == \"string\" && (endPath.startsWith(\"_\") || endPath.endsWith(\"Id\")) || sourcePath.some(\n    (path) => path === \"meta\" || path === \"metadata\" || path === \"openGraph\" || path === \"seo\"\n  ) || hasTypeLike(sourcePath) || hasTypeLike(resultPath) || typeof endPath == \"string\" && denylist.has(endPath));\n}, denylist = /* @__PURE__ */ new Set([\n  \"color\",\n  \"colour\",\n  \"currency\",\n  \"email\",\n  \"format\",\n  \"gid\",\n  \"hex\",\n  \"href\",\n  \"hsl\",\n  \"hsla\",\n  \"icon\",\n  \"id\",\n  \"index\",\n  \"key\",\n  \"language\",\n  \"layout\",\n  \"link\",\n  \"linkAction\",\n  \"locale\",\n  \"lqip\",\n  \"page\",\n  \"path\",\n  \"ref\",\n  \"rgb\",\n  \"rgba\",\n  \"route\",\n  \"secret\",\n  \"slug\",\n  \"status\",\n  \"tag\",\n  \"template\",\n  \"theme\",\n  \"type\",\n  \"textTheme\",\n  \"unit\",\n  \"url\",\n  \"username\",\n  \"variant\",\n  \"website\"\n]);\nfunction isValidDate(dateString) {\n  return /^\\d{4}-\\d{2}-\\d{2}/.test(dateString) ? !!Date.parse(dateString) : !1;\n}\nfunction isValidURL(url) {\n  try {\n    new URL(url, url.startsWith(\"/\") ? \"https://acme.com\" : void 0);\n  } catch {\n    return !1;\n  }\n  return !0;\n}\nfunction hasTypeLike(path) {\n  return path.some((segment) => typeof segment == \"string\" && segment.match(/type/i) !== null);\n}\nconst TRUNCATE_LENGTH = 20;\nfunction stegaEncodeSourceMap(result, resultSourceMap, config) {\n  const { filter, logger, enabled } = config;\n  if (!enabled) {\n    const msg = \"config.enabled must be true, don't call this function otherwise\";\n    throw logger?.error?.(`[@sanity/client]: ${msg}`, { result, resultSourceMap, config }), new TypeError(msg);\n  }\n  if (!resultSourceMap)\n    return logger?.error?.(\"[@sanity/client]: Missing Content Source Map from response body\", {\n      result,\n      resultSourceMap,\n      config\n    }), result;\n  if (!config.studioUrl) {\n    const msg = \"config.studioUrl must be defined\";\n    throw logger?.error?.(`[@sanity/client]: ${msg}`, { result, resultSourceMap, config }), new TypeError(msg);\n  }\n  const report = {\n    encoded: [],\n    skipped: []\n  }, resultWithStega = encodeIntoResult(\n    result,\n    resultSourceMap,\n    ({ sourcePath, sourceDocument, resultPath, value }) => {\n      if ((typeof filter == \"function\" ? filter({ sourcePath, resultPath, filterDefault, sourceDocument, value }) : filterDefault({ sourcePath, resultPath, filterDefault, sourceDocument, value })) === !1)\n        return logger && report.skipped.push({\n          path: prettyPathForLogging(sourcePath),\n          value: `${value.slice(0, TRUNCATE_LENGTH)}${value.length > TRUNCATE_LENGTH ? \"...\" : \"\"}`,\n          length: value.length\n        }), value;\n      logger && report.encoded.push({\n        path: prettyPathForLogging(sourcePath),\n        value: `${value.slice(0, TRUNCATE_LENGTH)}${value.length > TRUNCATE_LENGTH ? \"...\" : \"\"}`,\n        length: value.length\n      });\n      const { baseUrl, workspace, tool } = resolveStudioBaseRoute(\n        typeof config.studioUrl == \"function\" ? config.studioUrl(sourceDocument) : config.studioUrl\n      );\n      if (!baseUrl)\n        return value;\n      const { _id: id, _type: type, _projectId: projectId, _dataset: dataset } = sourceDocument;\n      return (0,_stegaClean_js__WEBPACK_IMPORTED_MODULE_0__.C)(\n        value,\n        {\n          origin: \"sanity.io\",\n          href: createEditUrl({\n            baseUrl,\n            workspace,\n            tool,\n            id,\n            type,\n            path: sourcePath,\n            ...!config.omitCrossDatasetReferenceData && { dataset, projectId }\n          })\n        },\n        // We use custom logic to determine if we should skip encoding\n        !1\n      );\n    }\n  );\n  if (logger) {\n    const isSkipping = report.skipped.length, isEncoding = report.encoded.length;\n    if ((isSkipping || isEncoding) && ((logger?.groupCollapsed || logger.log)?.(\"[@sanity/client]: Encoding source map into result\"), logger.log?.(\n      `[@sanity/client]: Paths encoded: ${report.encoded.length}, skipped: ${report.skipped.length}`\n    )), report.encoded.length > 0 && (logger?.log?.(\"[@sanity/client]: Table of encoded paths\"), (logger?.table || logger.log)?.(report.encoded)), report.skipped.length > 0) {\n      const skipped = /* @__PURE__ */ new Set();\n      for (const { path } of report.skipped)\n        skipped.add(path.replace(reKeySegment, \"0\").replace(/\\[\\d+\\]/g, \"[]\"));\n      logger?.log?.(\"[@sanity/client]: List of skipped paths\", [...skipped.values()]);\n    }\n    (isSkipping || isEncoding) && logger?.groupEnd?.();\n  }\n  return resultWithStega;\n}\nfunction prettyPathForLogging(path) {\n  return toString(jsonPathToStudioPath(path));\n}\nvar stegaEncodeSourceMap$1 = /* @__PURE__ */ Object.freeze({\n  __proto__: null,\n  stegaEncodeSourceMap\n});\n\n//# sourceMappingURL=stegaEncodeSourceMap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@sanity/next-loader/dist/_chunks-es/SanityLiveStream.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@sanity/next-loader/dist/_chunks-es/SanityLiveStream.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("var react__WEBPACK_IMPORTED_MODULE_1___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SanityLiveStream; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var _sanity_client_stega__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @sanity/client/stega */ \"(app-pages-browser)/./node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var use_effect_event__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-effect-event */ \"(app-pages-browser)/./node_modules/use-effect-event/dist/index.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/@sanity/next-loader/dist/_chunks-es/context.js\");\n\n\n\n\n\n\nfunction getDefaultExportFromCjs(x) {\n  return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, \"default\") ? x.default : x;\n}\nvar fastDeepEqual, hasRequiredFastDeepEqual;\nfunction requireFastDeepEqual() {\n  return hasRequiredFastDeepEqual || (hasRequiredFastDeepEqual = 1, fastDeepEqual = function equal(a, b) {\n    if (a === b) return !0;\n    if (a && b && typeof a == \"object\" && typeof b == \"object\") {\n      if (a.constructor !== b.constructor) return !1;\n      var length, i, keys;\n      if (Array.isArray(a)) {\n        if (length = a.length, length != b.length) return !1;\n        for (i = length; i-- !== 0; )\n          if (!equal(a[i], b[i])) return !1;\n        return !0;\n      }\n      if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n      if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n      if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n      if (keys = Object.keys(a), length = keys.length, length !== Object.keys(b).length) return !1;\n      for (i = length; i-- !== 0; )\n        if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return !1;\n      for (i = length; i-- !== 0; ) {\n        var key = keys[i];\n        if (!equal(a[key], b[key])) return !1;\n      }\n      return !0;\n    }\n    return a !== a && b !== b;\n  }), fastDeepEqual;\n}\nvar fastDeepEqualExports = requireFastDeepEqual(), isEqual = /* @__PURE__ */ getDefaultExportFromCjs(fastDeepEqualExports);\nconst use = \"use\" in /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_1___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_1___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_1__, 2))) ? (\n  // @ts-expect-error this is fine\n  react__WEBPACK_IMPORTED_MODULE_1__.use\n) : () => {\n  throw new TypeError(\"SanityLiveStream requires a React version with React.use()\");\n}, LISTEN_HEARTBEAT_INTERVAL = 1e3;\nfunction SanityLiveStream(props) {\n  const { query, dataset, params = {}, perspective, projectId, stega } = props, subscribe = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((listener) => (_context_js__WEBPACK_IMPORTED_MODULE_2__.comlinkListeners.add(listener), () => _context_js__WEBPACK_IMPORTED_MODULE_2__.comlinkListeners.delete(listener)), []), comlink$1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)(\n    subscribe,\n    () => _context_js__WEBPACK_IMPORTED_MODULE_2__.comlink,\n    () => null\n  ), [children, setChildren] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(void 0), handleQueryHeartbeat = (0,use_effect_event__WEBPACK_IMPORTED_MODULE_3__.useEffectEvent)((comlink2) => {\n    comlink2.post(\"loader/query-listen\", {\n      projectId,\n      dataset,\n      perspective,\n      query,\n      params,\n      heartbeat: LISTEN_HEARTBEAT_INTERVAL\n    });\n  }), handleQueryChange = (0,use_effect_event__WEBPACK_IMPORTED_MODULE_3__.useEffectEvent)(\n    (event) => {\n      if (isEqual(\n        {\n          projectId,\n          dataset,\n          query,\n          params\n        },\n        {\n          projectId: event.projectId,\n          dataset: event.dataset,\n          query: event.query,\n          params: event.params\n        }\n      )) {\n        const { result, resultSourceMap, tags } = event, data = stega ? (0,_sanity_client_stega__WEBPACK_IMPORTED_MODULE_4__.stegaEncodeSourceMap)(result, resultSourceMap, { enabled: !0, studioUrl: \"/\" }) : result;\n        console.groupCollapsed(\"rendering with server action\"), props.children({\n          data,\n          sourceMap: resultSourceMap,\n          tags: tags || []\n        }).then(\n          (children2) => {\n            console.log(\"setChildren(children)\"), setChildren(children2);\n          },\n          (reason) => {\n            console.error(\"rendering with server action: render children error\", reason);\n          }\n        ).finally(() => console.groupEnd());\n      }\n    }\n  );\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    if (!comlink$1) return;\n    const unsubscribe = comlink$1.on(\"loader/query-change\", handleQueryChange), interval = setInterval(() => handleQueryHeartbeat(comlink$1), LISTEN_HEARTBEAT_INTERVAL);\n    return () => {\n      clearInterval(interval), unsubscribe();\n    };\n  }, [comlink$1, handleQueryChange, handleQueryHeartbeat]), !comlink$1 || children === void 0 ? use(props.initial) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, { children });\n}\nSanityLiveStream.displayName = \"SanityLiveStream\";\n\n//# sourceMappingURL=SanityLiveStream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@sanity/next-loader/dist/_chunks-es/SanityLiveStream.js\n"));

/***/ })

}]);