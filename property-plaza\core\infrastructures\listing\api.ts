import { apiClient } from "@/core/client";
import { BasePaginationRequest, fetchMethod } from "@/core/utils/types";
import {
  GetBatchPropertyList,
  GetFilteredSeekersListingDto,
  GetLocationSuggestionDto,
  GetSeekersListingDto,
  ListingSeekersDto,
  PostFavoritePropertyDto,
  PutSaveFilterListingDto,
  UpdateListingDto,
} from "./dto";
import ssrApiClient from "@/core/ssr-client";
const baseUrl = process.env.NEXT_PUBLIC_SERVICE_API;

export const getAllListings = (searchParam: BasePaginationRequest) =>
  apiClient.get(
    `listings?page=${searchParam.page}&per_page=${searchParam.per_page}&search=${searchParam.search}`
  );

export const getDetailListing = (id: string) => apiClient.get(`listings/${id}`);

export const reactivateListing = (id: string) =>
  apiClient.put(`listings/reactivation/${id}`);

export const putListing = (id: string, data: UpdateListingDto) =>
  apiClient.put(`listings/${id}`, data);

export const getSeekersListing = (data: GetSeekersListingDto) =>
  apiClient.get(
    `properties?${data.location ? "search=" + data.location : ""}${data.section ? "&section=" + data.section : ""}${data.category ? "&category=" + data.category.toString() : ""}${data.limit ? "&limit=" + data.limit : ""}`
  );

export const postFavoriteProperty = (data: PostFavoritePropertyDto) =>
  apiClient.post(`properties/favorite`, data);

export const getFilteredSeekersListings = (
  data: GetFilteredSeekersListingDto
) =>
  apiClient.get(`properties/filter?page=${data.page}&per_page=${data.per_page}${data.search ? "&search=" + data.search : ""}${data.type ? "&category=" + data.type : ""}${data.min_price ? "&min_price=" + data.min_price : ""}${data.max_price ? "&max_price=" + data.max_price : ""}${data.years_of_building ? "&years_of_building=" + data.years_of_building : ""}${data.bedroom_total ? "&bedroom_total=" + data.bedroom_total : ""}${data.bathroom_total ? "&bathroom_total=" + data.bathroom_total : ""}${data.start_date ? "&start_date=" + data.start_date : ""}${data.end_date ? "&end_date=" + data.end_date : ""}
  `);

export const getLocationSuggestion = (data: GetLocationSuggestionDto) =>
  apiClient(`/properties/filter-location?search=${data.search}`);
export const postFilteredSeekeresListings = (
  data: GetFilteredSeekersListingDto
) => apiClient.post(`properties/filter`, data);

export const getDetailListingSeekers = (id: string) =>
  apiClient.get(`properties/${id}`);

export const getSeekersFilterParameter = () =>
  apiClient.get("filter-parameter");

export const getSeekersFavoriteListing = ({
  page,
  per_page,
  search,
  sort_by,
}: BasePaginationRequest & { sort_by?: string }) =>
  apiClient.get(
    `users/favorite?page=${page}&per_page=${per_page}&search=${search}&sort_by=${sort_by}`
  );

export const saveSearchListing = (data: PutSaveFilterListingDto) =>
  apiClient.put("users/filter-setting", data);

export const getBatchProperties = (data: GetBatchPropertyList) =>
  apiClient.post(`properties/batch-property`, data);

export const getSaveSearchHistory = () => apiClient.get("users/filter-setting");

// SSR call
export const ssrGetSeekersListing = async <T>(
  data: GetSeekersListingDto,
  tag: string
) => {
  const url =
    baseUrl +
    `/properties?${data.section ? "&section=" + data.section : ""}${data.limit ? "&limit=" + data.limit : ""}`;
  return await ssrApiClient<T>(url, fetchMethod.get, {
    next: { revalidate: 60 * 15 },
  });
};

export const ssrGetAllProperties = async <T>() => {
  const url = baseUrl + `/properties/filter`;
  const data = {
    page: "1",
    per_page: "99000",
  };
  return await ssrApiClient<T>(url, fetchMethod.post, {
    next: {
      revalidate: 60 * 60 * 24,
    },
    body: JSON.stringify(data),
  });
};
