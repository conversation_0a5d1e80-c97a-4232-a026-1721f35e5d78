"use client"
import { FormControl, FormField } from '@/components/ui/form'
import { FieldValues } from 'react-hook-form'
import { ComponentProps, ReactNode, useState } from 'react'
import { BaseInputForm, BaseSelectInputValue } from '@/types/base'
import BaseInputLayout from './base-input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"

interface IndividualInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  placeholder: string
  description?: string
  selectList?: BaseSelectInputValue<string>[]
  children?: ReactNode
  disabled?: boolean
}

export default function SelectInput<T extends FieldValues>({ form, label, name, placeholder, description, selectList, children, disabled }: IndividualInputProps<T>) {
  return <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <BaseInputLayout label={label} description={description}>
        <Select onValueChange={field.onChange} name={field.name} value={field.value} disabled={field.disabled || disabled}  >
          <FormControl>
            <SelectTrigger>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
          </FormControl>
          <SelectContent className='z-50' onClick={e => {
            e.stopPropagation()
          }}>
            {
              Array.isArray(selectList) &&
              selectList.map(item => <SelectItem onClick={e => {
                e.stopPropagation()
              }} key={item.id} value={item.value}>
                {item.content}
              </SelectItem>
              )
            }
            {children}
          </SelectContent>
        </Select>
      </BaseInputLayout>

    )}
  />
}