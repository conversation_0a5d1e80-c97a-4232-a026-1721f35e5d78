import { ListingListSeekers } from "@/core/domain/listing/listing-seekers"
import { cn } from "@/lib/utils"
import ListingWrapper from "./listing-wrapper"

interface ListingTitleProps {
  listing: ListingListSeekers,
  className?: string
}
export function ListingTitle({ className, listing }: ListingTitleProps) {
  return <ListingWrapper listing={listing}>
    <h3 className={cn("font-semibold text-seekers-text text-base  text-nowrap truncate", className)}>
      {listing.title}
    </h3>
  </ListingWrapper>
}
