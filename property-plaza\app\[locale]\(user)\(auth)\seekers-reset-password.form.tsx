"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useTranslations } from "next-intl"
import { useToast } from "@/hooks/use-toast"
import { useRequestResetPassword } from "@/core/applications/mutations/auth/use-request-reset-password"
import { RequestForgetPasswordDto } from "@/core/infrastructures/auth/dto"
import { Form } from "@/components/ui/form"
import { useEmailFormSchema } from "../../reset-password/form/use-email-form.schema"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import DefaultInput from "@/components/input-form/default-input"

export default function SeekersResetPasswordForm({ onBack }: { onBack: () => void }) {
  const t = useTranslations("universal")
  const { toast } = useToast()
  const formSchema = useEmailFormSchema()
  type formSchemaType = z.infer<typeof formSchema>
  const useRequestResetPasswordMutation = useRequestResetPassword()

  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: ""
    }
  })

  async function onSubmit(values: formSchemaType) {
    const data: RequestForgetPasswordDto = {
      email: values.email
    }
    try {
      await useRequestResetPasswordMutation.mutateAsync(data)
      toast({
        title: t('success.requestForgotPassword.title'),
        description: t('success.requestForgotPassword.description')
      })
      onBack() // Ga terug naar login na succes
    } catch (error: any) {
      toast({
        title: t("error.requestForgetPassword.title"),
        description: error.response.data.message,
        variant: "destructive"
      })
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4 ">
        <DefaultInput
          form={form}
          label={t("form.label.email")}
          name="email"
          placeholder=""
          type="email"
          variant="float"
          labelClassName="text-xs text-seekers-text-light"
        />
        <Button
          className="w-full"
          variant={"default-seekers"}
          loading={useRequestResetPasswordMutation.isPending}
        >
          {t("cta.requestChangePassword")}
        </Button>

        <div className="mt-4 text-center">
          <Button
            variant="link"
            onClick={onBack}
            className="p-0 h-9 text-seekers-primary hover:underline"
          >
            {t("cta.goBack")}
          </Button>
        </div>
      </form>
    </Form>
  )
} 