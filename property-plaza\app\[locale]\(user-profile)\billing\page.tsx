import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import PlansBreadCrumb from "./bread-crumb";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import { useTranslations } from "next-intl";
import PaymentMethod from "./payment-method";
import BillingHistory from "./billing-history";
import { getPaymentMethodService } from "@/core/infrastructures/transaction/services";
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import { billingUrl } from "@/lib/constanta/route";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  return {
    title: t("metadata.subsriptionPlan.title"),
    description: t("metadata.subsriptionPlan.description"),
    alternates: {
      languages: {
        en: process.env.USER_DOMAIN + "/en" + billingUrl,
        id: process.env.USER_DOMAIN + "/id" + billingUrl
      }
    }
  }
}


export default async function BillingPage() {
  const t = await getTranslations("seeker")
  const paymentMethods = await getPaymentMethodService()
  const conversionRates = await getCurrencyConversion("EUR")
  return <>
    <PlansBreadCrumb />
    <MainContentLayout className="max-sm:px-0 mb-12 my-8 space-y-8">
      <div className="flex justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{t("setting.subscriptionStatus.billing.title")}</h1>
          <h2 className="text-muted-foreground mt-2">{t("setting.subscriptionStatus.billing.description")}</h2>
        </div>
      </div>
      <PaymentMethod paymentMethod={paymentMethods?.data || []} billingInfo={paymentMethods?.billingInfo} />
      <BillingHistory conversionRate={conversionRates.data} />
    </MainContentLayout>
  </>
}