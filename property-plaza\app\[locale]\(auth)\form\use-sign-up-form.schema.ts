import { MAX_NAME_LENGTH, MIN_NAME_LENGTH } from "@/lib/constanta/constant";
import { useTranslations } from "next-intl";
import { z } from "zod";
export const PasswordPattern =
  /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/;

export function useSignUpSeekerFormSchema() {
  const t = useTranslations("seeker");
  const formSchema = z
    .object({
      firstName: z
        .string()
        .min(MIN_NAME_LENGTH, {
          message: t("form.utility.minimumLength", {
            field: t("form.field.firstName"),
            length: MIN_NAME_LENGTH,
          }),
        })
        .max(MAX_NAME_LENGTH, {
          message: t("form.utility.maximumLength", {
            field: t("form.field.firstName"),
            length: MAX_NAME_LENGTH,
          }),
        }),
      lastName: z
        .string()
        .min(MIN_NAME_LENGTH, {
          message: t("form.utility.minimumLength", {
            field: t("form.field.lastName"),
            length: MIN_NAME_LENGTH,
          }),
        })
        .max(MAX_NAME_LENGTH, {
          message: t("form.utility.maximumLength", {
            field: t("form.field.lastName"),
            length: MAX_NAME_LENGTH,
          }),
        }),
      contact: z.string().email({
        message: t("form.utility.enterValidField", {
          field: ` ${t("form.field.email")}`,
        }),
      }),
      password: z.string({
        message: t("form.utility.fieldRequired", {
          field: t("form.field.password"),
        }),
      }),
      confirmPassword: z.string({
        message: t("form.utility.fieldRequired", {
          field: t("form.field.confirmPassword"),
        }),
      }),
    })
    .refine(
      (data) => {
        return data.password == data.confirmPassword;
      },
      {
        message: t("form.utility.fieldNotMatch", {
          field: `${t("form.field.password")}`,
        }),
        path: ["confirmPassword"],
      }
    );
  return formSchema;
}
