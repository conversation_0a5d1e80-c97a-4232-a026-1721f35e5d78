"use client"
import DialogDescriptionWrapper from "@/components/dialog-wrapper/dialog-description-wrapper";
import DialogFooterWrapper from "@/components/dialog-wrapper/dialog-footer.wrapper";
import DialogHeaderWrapper from "@/components/dialog-wrapper/dialog-header-wrapper";
import DialogTitleWrapper from "@/components/dialog-wrapper/dialog-title-wrapper";
import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper";
import { Button } from "@/components/ui/button";
import { TriangleAlert } from "lucide-react";
import moment from "moment";
import { useTranslations } from "next-intl";
import { useState } from "react";

export default function CancelDialog({ trigger, onCancel, nextBillingDate }: { nextBillingDate: string, trigger: React.ReactNode, onCancel: () => void }) {
  const [open, setOpen] = useState(false)
  const t = useTranslations("seeker")
  const missingFeature = [
    t('subscription.cancel.content.optionOne'),
    t('subscription.cancel.content.optionTwo'),
    t('subscription.cancel.content.optionThree'),
    t('subscription.cancel.content.optionFour'),
    t('subscription.cancel.content.optionFive'),
    t('subscription.cancel.content.optionSix'),
    t('subscription.cancel.content.optionSeven')
  ]
  return <DialogWrapper
    setOpen={setOpen}
    open={open}
    openTrigger={trigger}
    dialogClassName="max-w-md"
  >
    <DialogHeaderWrapper>
      <DialogTitleWrapper className="flex gap-2 text-destructive items-center  ">
        <TriangleAlert />
        {t('subscription.cancel.title')}
      </DialogTitleWrapper>
      <DialogDescriptionWrapper className="font-semibold text-seekers-text-light">
        {t('subscription.cancel.description')}
      </DialogDescriptionWrapper>
    </DialogHeaderWrapper>
    <div className="space-y-2">
      <p className="font-semibold text-seekers-text-light">{t('subscription.downgrade.content.title')}</p>
      <ul className="list-disc ml-4 text-seekers-text-light">
        {missingFeature.map((item, idx) => <li key={idx}>{item}</li>
        )}
      </ul>
      <div className="text-seekers-primary space-y-2 bg-yellow-300/10 p-4 border border-yellow-300 rounded-md">
        <h3 className="font-bold uppercase text-lg">{t('misc.importantNotice')}</h3>
        <p className="font-medium text-xs">{t('subscription.downgrade.content.downgradeEffectiveDate', { effectedDate: moment(nextBillingDate).format("DD MMM YYYY"), nextBillingDate: moment(nextBillingDate).format("DD MMM YYYY") })} </p>
      </div>
    </div>
    <DialogFooterWrapper>
      <Button variant={"default-seekers"} onClick={() => setOpen(false)}>
        {t('cta.cancel')}
      </Button>
      <Button variant={"outline"} className="border-destructive text-destructive hover:text-destructive" onClick={onCancel}>
        {t('cta.cancelSubscription')}
      </Button>
    </DialogFooterWrapper>
  </DialogWrapper >
}