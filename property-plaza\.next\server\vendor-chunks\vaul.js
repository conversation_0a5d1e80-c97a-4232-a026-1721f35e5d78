"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vaul";
exports.ids = ["vendor-chunks/vaul"];
exports.modules = {

/***/ "(ssr)/./node_modules/vaul/dist/index.mjs":
/*!******************************************!*\
  !*** ./node_modules/vaul/dist/index.mjs ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Drawer: () => (/* binding */ Drawer)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Drawer auto */ function __insertCSS(code) {\n    if (!code || typeof document == \"undefined\") return;\n    let head = document.head || document.getElementsByTagName(\"head\")[0];\n    let style = document.createElement(\"style\");\n    style.type = \"text/css\";\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\n\nconst DrawerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    drawerRef: {\n        current: null\n    },\n    overlayRef: {\n        current: null\n    },\n    scaleBackground: ()=>{},\n    onPress: ()=>{},\n    onRelease: ()=>{},\n    onDrag: ()=>{},\n    onNestedDrag: ()=>{},\n    onNestedOpenChange: ()=>{},\n    onNestedRelease: ()=>{},\n    openProp: undefined,\n    dismissible: false,\n    handleOnly: false,\n    isOpen: false,\n    isDragging: false,\n    keyboardIsOpen: {\n        current: false\n    },\n    snapPointsOffset: null,\n    snapPoints: null,\n    modal: false,\n    shouldFade: false,\n    activeSnapPoint: null,\n    onOpenChange: ()=>{},\n    setActiveSnapPoint: ()=>{},\n    visible: false,\n    closeDrawer: ()=>{},\n    setVisible: ()=>{},\n    direction: \"bottom\"\n});\nconst useDrawerContext = ()=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DrawerContext);\n    if (!context) {\n        throw new Error(\"useDrawerContext must be used within a Drawer.Root\");\n    }\n    return context;\n};\n__insertCSS(\"[vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1)}[vaul-drawer][vaul-drawer-direction=bottom]{transform:translate3d(0,100%,0)}[vaul-drawer][vaul-drawer-direction=top]{transform:translate3d(0,-100%,0)}[vaul-drawer][vaul-drawer-direction=left]{transform:translate3d(-100%,0,0)}[vaul-drawer][vaul-drawer-direction=right]{transform:translate3d(100%,0,0)}.vaul-dragging .vaul-scrollable [vault-drawer-direction=top]{overflow-y:hidden!important}.vaul-dragging .vaul-scrollable [vault-drawer-direction=bottom]{overflow-y:hidden!important}.vaul-dragging .vaul-scrollable [vault-drawer-direction=left]{overflow-x:hidden!important}.vaul-dragging .vaul-scrollable [vault-drawer-direction=right]{overflow-x:hidden!important}[vaul-drawer][vaul-drawer-visible=true][vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[vaul-drawer][vaul-drawer-visible=true][vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[vaul-drawer][vaul-drawer-visible=true][vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[vaul-drawer][vaul-drawer-visible=true][vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[vaul-overlay]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[vaul-overlay][vaul-drawer-visible=true]{opacity:1}[vaul-drawer]::after{content:'';position:absolute;background:inherit;background-color:inherit}[vaul-drawer][vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[vaul-drawer][vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[vaul-drawer][vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[vaul-drawer][vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[vaul-handle]{display:block;position:relative;opacity:.8;margin-left:auto;margin-right:auto;height:5px;width:56px;border-radius:1rem;touch-action:pan-y;cursor:grab}[vaul-handle]:active,[vaul-handle]:hover{opacity:1}[vaul-handle]:active{cursor:grabbing}[vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}[vaul-overlay][vaul-snap-points=true]:not([vaul-snap-points-overlay=true]):not([data-state=closed]){opacity:0}[vaul-overlay][vaul-snap-points-overlay=true]:not([vaul-drawer-visible=false]){opacity:1}@media (hover:hover) and (pointer:fine){[vaul-drawer]{user-select:none}}@media (pointer:fine){[vaul-handle-hitarea]:{width:100%;height:100%}}\");\n// This code comes from https://github.com/adobe/react-spectrum/blob/main/packages/%40react-aria/overlays/src/usePreventScroll.ts\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction chain(...callbacks) {\n    return (...args)=>{\n        for (let callback of callbacks){\n            if (typeof callback === \"function\") {\n                callback(...args);\n            }\n        }\n    };\n}\nfunction isMac() {\n    return testPlatform(/^Mac/);\n}\nfunction isIPhone() {\n    return testPlatform(/^iPhone/);\n}\nfunction isIPad() {\n    return testPlatform(/^iPad/) || // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    isMac() && navigator.maxTouchPoints > 1;\n}\nfunction isIOS() {\n    return isIPhone() || isIPad();\n}\nfunction testPlatform(re) {\n    return  false ? 0 : undefined;\n}\n// @ts-ignore\nconst visualViewport = typeof document !== \"undefined\" && window.visualViewport;\nfunction isScrollable(node) {\n    let style = window.getComputedStyle(node);\n    return /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n}\nfunction getScrollParent(node) {\n    if (isScrollable(node)) {\n        node = node.parentElement;\n    }\n    while(node && !isScrollable(node)){\n        node = node.parentElement;\n    }\n    return node || document.scrollingElement || document.documentElement;\n}\n// HTML input types that do not cause the software keyboard to appear.\nconst nonTextInputTypes = new Set([\n    \"checkbox\",\n    \"radio\",\n    \"range\",\n    \"color\",\n    \"file\",\n    \"image\",\n    \"button\",\n    \"submit\",\n    \"reset\"\n]);\n// The number of active usePreventScroll calls. Used to determine whether to revert back to the original page style/scroll position\nlet preventScrollCount = 0;\nlet restore;\n/**\n * Prevents scrolling on the document body on mount, and\n * restores it on unmount. Also ensures that content does not\n * shift due to the scrollbars disappearing.\n */ function usePreventScroll(options = {}) {\n    let { isDisabled } = options;\n    useIsomorphicLayoutEffect(()=>{\n        if (isDisabled) {\n            return;\n        }\n        preventScrollCount++;\n        if (preventScrollCount === 1) {\n            if (isIOS()) {\n                restore = preventScrollMobileSafari();\n            } else {\n                restore = preventScrollStandard();\n            }\n        }\n        return ()=>{\n            preventScrollCount--;\n            if (preventScrollCount === 0) {\n                restore();\n            }\n        };\n    }, [\n        isDisabled\n    ]);\n}\n// For most browsers, all we need to do is set `overflow: hidden` on the root element, and\n// add some padding to prevent the page from shifting when the scrollbar is hidden.\nfunction preventScrollStandard() {\n    return chain(setStyle(document.documentElement, \"paddingRight\", `${window.innerWidth - document.documentElement.clientWidth}px`));\n}\n// Mobile Safari is a whole different beast. Even with overflow: hidden,\n// it still scrolls the page in many situations:\n//\n// 1. When the bottom toolbar and address bar are collapsed, page scrolling is always allowed.\n// 2. When the keyboard is visible, the viewport does not resize. Instead, the keyboard covers part of\n//    it, so it becomes scrollable.\n// 3. When tapping on an input, the page always scrolls so that the input is centered in the visual viewport.\n//    This may cause even fixed position elements to scroll off the screen.\n// 4. When using the next/previous buttons in the keyboard to navigate between inputs, the whole page always\n//    scrolls, even if the input is inside a nested scrollable element that could be scrolled instead.\n//\n// In order to work around these cases, and prevent scrolling without jankiness, we do a few things:\n//\n// 1. Prevent default on `touchmove` events that are not in a scrollable element. This prevents touch scrolling\n//    on the window.\n// 2. Prevent default on `touchmove` events inside a scrollable element when the scroll position is at the\n//    top or bottom. This avoids the whole page scrolling instead, but does prevent overscrolling.\n// 3. Prevent default on `touchend` events on input elements and handle focusing the element ourselves.\n// 4. When focusing an input, apply a transform to trick Safari into thinking the input is at the top\n//    of the page, which prevents it from scrolling the page. After the input is focused, scroll the element\n//    into view ourselves, without scrolling the whole page.\n// 5. Offset the body by the scroll position using a negative margin and scroll to the top. This should appear the\n//    same visually, but makes the actual scroll position always zero. This is required to make all of the\n//    above work or Safari will still try to scroll the page when focusing an input.\n// 6. As a last resort, handle window scroll events, and scroll back to the top. This can happen when attempting\n//    to navigate to an input with the next/previous buttons that's outside a modal.\nfunction preventScrollMobileSafari() {\n    let scrollable;\n    let lastY = 0;\n    let onTouchStart = (e)=>{\n        // Store the nearest scrollable parent element from the element that the user touched.\n        scrollable = getScrollParent(e.target);\n        if (scrollable === document.documentElement && scrollable === document.body) {\n            return;\n        }\n        lastY = e.changedTouches[0].pageY;\n    };\n    let onTouchMove = (e)=>{\n        // Prevent scrolling the window.\n        if (!scrollable || scrollable === document.documentElement || scrollable === document.body) {\n            e.preventDefault();\n            return;\n        }\n        // Prevent scrolling up when at the top and scrolling down when at the bottom\n        // of a nested scrollable area, otherwise mobile Safari will start scrolling\n        // the window instead. Unfortunately, this disables bounce scrolling when at\n        // the top but it's the best we can do.\n        let y = e.changedTouches[0].pageY;\n        let scrollTop = scrollable.scrollTop;\n        let bottom = scrollable.scrollHeight - scrollable.clientHeight;\n        if (bottom === 0) {\n            return;\n        }\n        if (scrollTop <= 0 && y > lastY || scrollTop >= bottom && y < lastY) {\n            e.preventDefault();\n        }\n        lastY = y;\n    };\n    let onTouchEnd = (e)=>{\n        let target = e.target;\n        // Apply this change if we're not already focused on the target element\n        if (isInput(target) && target !== document.activeElement) {\n            e.preventDefault();\n            // Apply a transform to trick Safari into thinking the input is at the top of the page\n            // so it doesn't try to scroll it into view. When tapping on an input, this needs to\n            // be done before the \"focus\" event, so we have to focus the element ourselves.\n            target.style.transform = \"translateY(-2000px)\";\n            target.focus();\n            requestAnimationFrame(()=>{\n                target.style.transform = \"\";\n            });\n        }\n    };\n    let onFocus = (e)=>{\n        let target = e.target;\n        if (isInput(target)) {\n            // Transform also needs to be applied in the focus event in cases where focus moves\n            // other than tapping on an input directly, e.g. the next/previous buttons in the\n            // software keyboard. In these cases, it seems applying the transform in the focus event\n            // is good enough, whereas when tapping an input, it must be done before the focus event. 🤷‍♂️\n            target.style.transform = \"translateY(-2000px)\";\n            requestAnimationFrame(()=>{\n                target.style.transform = \"\";\n                // This will have prevented the browser from scrolling the focused element into view,\n                // so we need to do this ourselves in a way that doesn't cause the whole page to scroll.\n                if (visualViewport) {\n                    if (visualViewport.height < window.innerHeight) {\n                        // If the keyboard is already visible, do this after one additional frame\n                        // to wait for the transform to be removed.\n                        requestAnimationFrame(()=>{\n                            scrollIntoView(target);\n                        });\n                    } else {\n                        // Otherwise, wait for the visual viewport to resize before scrolling so we can\n                        // measure the correct position to scroll to.\n                        visualViewport.addEventListener(\"resize\", ()=>scrollIntoView(target), {\n                            once: true\n                        });\n                    }\n                }\n            });\n        }\n    };\n    let onWindowScroll = ()=>{\n        // Last resort. If the window scrolled, scroll it back to the top.\n        // It should always be at the top because the body will have a negative margin (see below).\n        window.scrollTo(0, 0);\n    };\n    // Record the original scroll position so we can restore it.\n    // Then apply a negative margin to the body to offset it by the scroll position. This will\n    // enable us to scroll the window to the top, which is required for the rest of this to work.\n    let scrollX = window.pageXOffset;\n    let scrollY = window.pageYOffset;\n    let restoreStyles = chain(setStyle(document.documentElement, \"paddingRight\", `${window.innerWidth - document.documentElement.clientWidth}px`));\n    // Scroll to the top. The negative margin on the body will make this appear the same.\n    window.scrollTo(0, 0);\n    let removeEvents = chain(addEvent(document, \"touchstart\", onTouchStart, {\n        passive: false,\n        capture: true\n    }), addEvent(document, \"touchmove\", onTouchMove, {\n        passive: false,\n        capture: true\n    }), addEvent(document, \"touchend\", onTouchEnd, {\n        passive: false,\n        capture: true\n    }), addEvent(document, \"focus\", onFocus, true), addEvent(window, \"scroll\", onWindowScroll));\n    return ()=>{\n        // Restore styles and scroll the page back to where it was.\n        restoreStyles();\n        removeEvents();\n        window.scrollTo(scrollX, scrollY);\n    };\n}\n// Sets a CSS property on an element, and returns a function to revert it to the previous value.\nfunction setStyle(element, style, value) {\n    let cur = element.style[style];\n    element.style[style] = value;\n    return ()=>{\n        element.style[style] = cur;\n    };\n}\n// Adds an event listener to an element, and returns a function to remove it.\nfunction addEvent(target, event, handler, options) {\n    // @ts-ignore\n    target.addEventListener(event, handler, options);\n    return ()=>{\n        // @ts-ignore\n        target.removeEventListener(event, handler, options);\n    };\n}\nfunction scrollIntoView(target) {\n    let root = document.scrollingElement || document.documentElement;\n    while(target && target !== root){\n        // Find the parent scrollable element and adjust the scroll position if the target is not already in view.\n        let scrollable = getScrollParent(target);\n        if (scrollable !== document.documentElement && scrollable !== document.body && scrollable !== target) {\n            let scrollableTop = scrollable.getBoundingClientRect().top;\n            let targetTop = target.getBoundingClientRect().top;\n            let targetBottom = target.getBoundingClientRect().bottom;\n            const keyboardHeight = scrollable.getBoundingClientRect().bottom;\n            if (targetBottom > keyboardHeight) {\n                scrollable.scrollTop += targetTop - scrollableTop;\n            }\n        }\n        // @ts-ignore\n        target = scrollable.parentElement;\n    }\n}\nfunction isInput(target) {\n    return target instanceof HTMLInputElement && !nonTextInputTypes.has(target.type) || target instanceof HTMLTextAreaElement || target instanceof HTMLElement && target.isContentEditable;\n}\n// This code comes from https://github.com/radix-ui/primitives/tree/main/packages/react/compose-refs\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */ function setRef(ref, value) {\n    if (typeof ref === \"function\") {\n        ref(value);\n    } else if (ref !== null && ref !== undefined) {\n        ref.current = value;\n    }\n}\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */ function composeRefs(...refs) {\n    return (node)=>refs.forEach((ref)=>setRef(ref, node));\n}\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */ function useComposedRefs(...refs) {\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\nlet previousBodyPosition = null;\nfunction usePositionFixed({ isOpen, modal, nested, hasBeenOpened, preventScrollRestoration, noBodyStyles }) {\n    const [activeUrl, setActiveUrl] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=> false ? 0 : \"\");\n    const scrollPos = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const setPositionFixed = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        // If previousBodyPosition is already set, don't set it again.\n        if (previousBodyPosition === null && isOpen && !noBodyStyles) {\n            previousBodyPosition = {\n                position: document.body.style.position,\n                top: document.body.style.top,\n                left: document.body.style.left,\n                height: document.body.style.height,\n                right: \"unset\"\n            };\n            // Update the dom inside an animation frame\n            const { scrollX, innerHeight } = window;\n            document.body.style.setProperty(\"position\", \"fixed\", \"important\");\n            Object.assign(document.body.style, {\n                top: `${-scrollPos.current}px`,\n                left: `${-scrollX}px`,\n                right: \"0px\",\n                height: \"auto\"\n            });\n            window.setTimeout(()=>window.requestAnimationFrame(()=>{\n                    // Attempt to check if the bottom bar appeared due to the position change\n                    const bottomBarHeight = innerHeight - window.innerHeight;\n                    if (bottomBarHeight && scrollPos.current >= innerHeight) {\n                        // Move the content further up so that the bottom bar doesn't hide it\n                        document.body.style.top = `${-(scrollPos.current + bottomBarHeight)}px`;\n                    }\n                }), 300);\n        }\n    }, [\n        isOpen\n    ]);\n    const restorePositionSetting = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (previousBodyPosition !== null && !noBodyStyles) {\n            // Convert the position from \"px\" to Int\n            const y = -parseInt(document.body.style.top, 10);\n            const x = -parseInt(document.body.style.left, 10);\n            // Restore styles\n            Object.assign(document.body.style, previousBodyPosition);\n            window.requestAnimationFrame(()=>{\n                if (preventScrollRestoration && activeUrl !== window.location.href) {\n                    setActiveUrl(window.location.href);\n                    return;\n                }\n                window.scrollTo(x, y);\n            });\n            previousBodyPosition = null;\n        }\n    }, [\n        activeUrl\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        function onScroll() {\n            scrollPos.current = window.scrollY;\n        }\n        onScroll();\n        window.addEventListener(\"scroll\", onScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", onScroll);\n        };\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (nested || !hasBeenOpened) return;\n        // This is needed to force Safari toolbar to show **before** the drawer starts animating to prevent a gnarly shift from happening\n        if (isOpen) {\n            // avoid for standalone mode (PWA)\n            const isStandalone = window.matchMedia(\"(display-mode: standalone)\").matches;\n            !isStandalone && setPositionFixed();\n            if (!modal) {\n                window.setTimeout(()=>{\n                    restorePositionSetting();\n                }, 500);\n            }\n        } else {\n            restorePositionSetting();\n        }\n    }, [\n        isOpen,\n        hasBeenOpened,\n        activeUrl,\n        modal,\n        nested,\n        setPositionFixed,\n        restorePositionSetting\n    ]);\n    return {\n        restorePositionSetting\n    };\n}\nconst cache = new WeakMap();\nfunction set(el, styles, ignoreCache = false) {\n    if (!el || !(el instanceof HTMLElement)) return;\n    let originalStyles = {};\n    Object.entries(styles).forEach(([key, value])=>{\n        if (key.startsWith(\"--\")) {\n            el.style.setProperty(key, value);\n            return;\n        }\n        originalStyles[key] = el.style[key];\n        el.style[key] = value;\n    });\n    if (ignoreCache) return;\n    cache.set(el, originalStyles);\n}\nfunction reset(el, prop) {\n    if (!el || !(el instanceof HTMLElement)) return;\n    let originalStyles = cache.get(el);\n    if (!originalStyles) {\n        return;\n    }\n    if (prop) {\n        el.style[prop] = originalStyles[prop];\n    } else {\n        Object.entries(originalStyles).forEach(([key, value])=>{\n            el.style[key] = value;\n        });\n    }\n}\nconst isVertical = (direction)=>{\n    switch(direction){\n        case \"top\":\n        case \"bottom\":\n            return true;\n        case \"left\":\n        case \"right\":\n            return false;\n        default:\n            return direction;\n    }\n};\nfunction getTranslate(element, direction) {\n    if (!element) {\n        return null;\n    }\n    const style = window.getComputedStyle(element);\n    const transform = style.transform || style.webkitTransform || style.mozTransform;\n    let mat = transform.match(/^matrix3d\\((.+)\\)$/);\n    if (mat) {\n        // https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix3d\n        return parseFloat(mat[1].split(\", \")[isVertical(direction) ? 13 : 12]);\n    }\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix\n    mat = transform.match(/^matrix\\((.+)\\)$/);\n    return mat ? parseFloat(mat[1].split(\", \")[isVertical(direction) ? 5 : 4]) : null;\n}\nfunction dampenValue(v) {\n    return 8 * (Math.log(v + 1) - 2);\n}\nconst TRANSITIONS = {\n    DURATION: 0.5,\n    EASE: [\n        0.32,\n        0.72,\n        0,\n        1\n    ]\n};\nconst VELOCITY_THRESHOLD = 0.4;\n// This code comes from https://github.com/radix-ui/primitives/blob/main/packages/react/use-controllable-state/src/useControllableState.tsx\nfunction useCallbackRef(callback) {\n    const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        callbackRef.current = callback;\n    });\n    // https://github.com/facebook/react/issues/19240\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>(...args)=>callbackRef.current == null ? void 0 : callbackRef.current.call(callbackRef, ...args), []);\n}\nfunction useUncontrolledState({ defaultProp, onChange }) {\n    const uncontrolledState = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n    const [value] = uncontrolledState;\n    const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n    const handleChange = useCallbackRef(onChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (prevValueRef.current !== value) {\n            handleChange(value);\n            prevValueRef.current = value;\n        }\n    }, [\n        value,\n        prevValueRef,\n        handleChange\n    ]);\n    return uncontrolledState;\n}\nfunction useControllableState({ prop, defaultProp, onChange = ()=>{} }) {\n    const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({\n        defaultProp,\n        onChange\n    });\n    const isControlled = prop !== undefined;\n    const value = isControlled ? prop : uncontrolledProp;\n    const handleChange = useCallbackRef(onChange);\n    const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((nextValue)=>{\n        if (isControlled) {\n            const setter = nextValue;\n            const value = typeof nextValue === \"function\" ? setter(prop) : nextValue;\n            if (value !== prop) handleChange(value);\n        } else {\n            setUncontrolledProp(nextValue);\n        }\n    }, [\n        isControlled,\n        prop,\n        setUncontrolledProp,\n        handleChange\n    ]);\n    return [\n        value,\n        setValue\n    ];\n}\nfunction useSnapPoints({ activeSnapPointProp, setActiveSnapPointProp, snapPoints, drawerRef, overlayRef, fadeFromIndex, onSnapPointChange, direction = \"bottom\" }) {\n    const [activeSnapPoint, setActiveSnapPoint] = useControllableState({\n        prop: activeSnapPointProp,\n        defaultProp: snapPoints == null ? void 0 : snapPoints[0],\n        onChange: setActiveSnapPointProp\n    });\n    const isLastSnapPoint = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>activeSnapPoint === (snapPoints == null ? void 0 : snapPoints[snapPoints.length - 1]) || null, [\n        snapPoints,\n        activeSnapPoint\n    ]);\n    const shouldFade = snapPoints && snapPoints.length > 0 && (fadeFromIndex || fadeFromIndex === 0) && !Number.isNaN(fadeFromIndex) && snapPoints[fadeFromIndex] === activeSnapPoint || !snapPoints;\n    const activeSnapPointIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>snapPoints == null ? void 0 : snapPoints.findIndex((snapPoint)=>snapPoint === activeSnapPoint), [\n        snapPoints,\n        activeSnapPoint\n    ]);\n    const snapPointsOffset = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        var _snapPoints_map;\n        return (_snapPoints_map = snapPoints == null ? void 0 : snapPoints.map((snapPoint)=>{\n            const hasWindow = \"undefined\" !== \"undefined\";\n            const isPx = typeof snapPoint === \"string\";\n            let snapPointAsNumber = 0;\n            if (isPx) {\n                snapPointAsNumber = parseInt(snapPoint, 10);\n            }\n            if (isVertical(direction)) {\n                const height = isPx ? snapPointAsNumber : hasWindow ? snapPoint * window.innerHeight : 0;\n                if (hasWindow) {\n                    return direction === \"bottom\" ? window.innerHeight - height : -window.innerHeight + height;\n                }\n                return height;\n            }\n            const width = isPx ? snapPointAsNumber : hasWindow ? snapPoint * window.innerWidth : 0;\n            if (hasWindow) {\n                return direction === \"right\" ? window.innerWidth - width : -window.innerWidth + width;\n            }\n            return width;\n        })) != null ? _snapPoints_map : [];\n    }, [\n        snapPoints\n    ]);\n    const activeSnapPointOffset = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>activeSnapPointIndex !== null ? snapPointsOffset == null ? void 0 : snapPointsOffset[activeSnapPointIndex] : null, [\n        snapPointsOffset,\n        activeSnapPointIndex\n    ]);\n    const snapToPoint = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((dimension)=>{\n        var _snapPointsOffset_findIndex;\n        const newSnapPointIndex = (_snapPointsOffset_findIndex = snapPointsOffset == null ? void 0 : snapPointsOffset.findIndex((snapPointDim)=>snapPointDim === dimension)) != null ? _snapPointsOffset_findIndex : null;\n        onSnapPointChange(newSnapPointIndex);\n        set(drawerRef.current, {\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n            transform: isVertical(direction) ? `translate3d(0, ${dimension}px, 0)` : `translate3d(${dimension}px, 0, 0)`\n        });\n        if (snapPointsOffset && newSnapPointIndex !== snapPointsOffset.length - 1 && newSnapPointIndex !== fadeFromIndex) {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n                opacity: \"0\"\n            });\n        } else {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n                opacity: \"1\"\n            });\n        }\n        setActiveSnapPoint(newSnapPointIndex !== null ? snapPoints == null ? void 0 : snapPoints[newSnapPointIndex] : null);\n    }, [\n        drawerRef.current,\n        snapPoints,\n        snapPointsOffset,\n        fadeFromIndex,\n        overlayRef,\n        setActiveSnapPoint\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (activeSnapPoint || activeSnapPointProp) {\n            var _snapPoints_findIndex;\n            const newIndex = (_snapPoints_findIndex = snapPoints == null ? void 0 : snapPoints.findIndex((snapPoint)=>snapPoint === activeSnapPointProp || snapPoint === activeSnapPoint)) != null ? _snapPoints_findIndex : -1;\n            if (snapPointsOffset && newIndex !== -1 && typeof snapPointsOffset[newIndex] === \"number\") {\n                snapToPoint(snapPointsOffset[newIndex]);\n            }\n        }\n    }, [\n        activeSnapPoint,\n        activeSnapPointProp,\n        snapPoints,\n        snapPointsOffset,\n        snapToPoint\n    ]);\n    function onRelease({ draggedDistance, closeDrawer, velocity, dismissible }) {\n        if (fadeFromIndex === undefined) return;\n        const currentPosition = direction === \"bottom\" || direction === \"right\" ? (activeSnapPointOffset != null ? activeSnapPointOffset : 0) - draggedDistance : (activeSnapPointOffset != null ? activeSnapPointOffset : 0) + draggedDistance;\n        const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n        const isFirst = activeSnapPointIndex === 0;\n        const hasDraggedUp = draggedDistance > 0;\n        if (isOverlaySnapPoint) {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n            });\n        }\n        if (velocity > 2 && !hasDraggedUp) {\n            if (dismissible) closeDrawer();\n            else snapToPoint(snapPointsOffset[0]); // snap to initial point\n            return;\n        }\n        if (velocity > 2 && hasDraggedUp && snapPointsOffset && snapPoints) {\n            snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n            return;\n        }\n        // Find the closest snap point to the current position\n        const closestSnapPoint = snapPointsOffset == null ? void 0 : snapPointsOffset.reduce((prev, curr)=>{\n            if (typeof prev !== \"number\" || typeof curr !== \"number\") return prev;\n            return Math.abs(curr - currentPosition) < Math.abs(prev - currentPosition) ? curr : prev;\n        });\n        const dim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        if (velocity > VELOCITY_THRESHOLD && Math.abs(draggedDistance) < dim * 0.4) {\n            const dragDirection = hasDraggedUp ? 1 : -1; // 1 = up, -1 = down\n            // Don't do anything if we swipe upwards while being on the last snap point\n            if (dragDirection > 0 && isLastSnapPoint) {\n                snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n                return;\n            }\n            if (isFirst && dragDirection < 0 && dismissible) {\n                closeDrawer();\n            }\n            if (activeSnapPointIndex === null) return;\n            snapToPoint(snapPointsOffset[activeSnapPointIndex + dragDirection]);\n            return;\n        }\n        snapToPoint(closestSnapPoint);\n    }\n    function onDrag({ draggedDistance }) {\n        if (activeSnapPointOffset === null) return;\n        const newValue = direction === \"bottom\" || direction === \"right\" ? activeSnapPointOffset - draggedDistance : activeSnapPointOffset + draggedDistance;\n        // Don't do anything if we exceed the last(biggest) snap point\n        if ((direction === \"bottom\" || direction === \"right\") && newValue < snapPointsOffset[snapPointsOffset.length - 1]) {\n            return;\n        }\n        if ((direction === \"top\" || direction === \"left\") && newValue > snapPointsOffset[snapPointsOffset.length - 1]) {\n            return;\n        }\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `translate3d(0, ${newValue}px, 0)` : `translate3d(${newValue}px, 0, 0)`\n        });\n    }\n    function getPercentageDragged(absDraggedDistance, isDraggingDown) {\n        if (!snapPoints || typeof activeSnapPointIndex !== \"number\" || !snapPointsOffset || fadeFromIndex === undefined) return null;\n        // If this is true we are dragging to a snap point that is supposed to have an overlay\n        const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n        const isOverlaySnapPointOrHigher = activeSnapPointIndex >= fadeFromIndex;\n        if (isOverlaySnapPointOrHigher && isDraggingDown) {\n            return 0;\n        }\n        // Don't animate, but still use this one if we are dragging away from the overlaySnapPoint\n        if (isOverlaySnapPoint && !isDraggingDown) return 1;\n        if (!shouldFade && !isOverlaySnapPoint) return null;\n        // Either fadeFrom index or the one before\n        const targetSnapPointIndex = isOverlaySnapPoint ? activeSnapPointIndex + 1 : activeSnapPointIndex - 1;\n        // Get the distance from overlaySnapPoint to the one before or vice-versa to calculate the opacity percentage accordingly\n        const snapPointDistance = isOverlaySnapPoint ? snapPointsOffset[targetSnapPointIndex] - snapPointsOffset[targetSnapPointIndex - 1] : snapPointsOffset[targetSnapPointIndex + 1] - snapPointsOffset[targetSnapPointIndex];\n        const percentageDragged = absDraggedDistance / Math.abs(snapPointDistance);\n        if (isOverlaySnapPoint) {\n            return 1 - percentageDragged;\n        } else {\n            return percentageDragged;\n        }\n    }\n    return {\n        isLastSnapPoint,\n        activeSnapPoint,\n        shouldFade,\n        getPercentageDragged,\n        setActiveSnapPoint,\n        activeSnapPointIndex,\n        onRelease,\n        onDrag,\n        snapPointsOffset\n    };\n}\nconst CLOSE_THRESHOLD = 0.25;\nconst SCROLL_LOCK_TIMEOUT = 100;\nconst BORDER_RADIUS = 8;\nconst NESTED_DISPLACEMENT = 16;\nconst WINDOW_TOP_OFFSET = 26;\nconst DRAG_CLASS = \"vaul-dragging\";\nfunction Root({ open: openProp, onOpenChange, children, shouldScaleBackground, onDrag: onDragProp, onRelease: onReleaseProp, snapPoints, nested = false, setBackgroundColorOnScale = true, closeThreshold = CLOSE_THRESHOLD, scrollLockTimeout = SCROLL_LOCK_TIMEOUT, dismissible = true, handleOnly = false, fadeFromIndex = snapPoints && snapPoints.length - 1, activeSnapPoint: activeSnapPointProp, setActiveSnapPoint: setActiveSnapPointProp, fixed, modal = true, onClose, noBodyStyles, direction = \"bottom\", preventScrollRestoration = true, disablePreventScroll = false }) {\n    var _drawerRef_current;\n    const [isOpen = false, setIsOpen] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [hasBeenOpened, setHasBeenOpened] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    // Not visible = translateY(100%)\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isDragging, setIsDragging] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [justReleased, setJustReleased] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const overlayRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const openTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const dragEndTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const lastTimeDragPrevented = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isAllowedToDrag = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const nestedOpenChangeTimer = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerStart = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const keyboardIsOpen = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const previousDiffFromInitial = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const drawerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const drawerHeightRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(((_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.getBoundingClientRect().height) || 0);\n    const initialDrawerHeight = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const onSnapPointChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((activeSnapPointIndex)=>{\n        // Change openTime ref when we reach the last snap point to prevent dragging for 500ms incase it's scrollable.\n        if (snapPoints && activeSnapPointIndex === snapPointsOffset.length - 1) openTime.current = new Date();\n    }, []);\n    const { activeSnapPoint, activeSnapPointIndex, setActiveSnapPoint, onRelease: onReleaseSnapPoints, snapPointsOffset, onDrag: onDragSnapPoints, shouldFade, getPercentageDragged: getSnapPointsPercentageDragged } = useSnapPoints({\n        snapPoints,\n        activeSnapPointProp,\n        setActiveSnapPointProp,\n        drawerRef,\n        fadeFromIndex,\n        overlayRef,\n        onSnapPointChange,\n        direction\n    });\n    usePreventScroll({\n        isDisabled: !isOpen || isDragging || !modal || justReleased || !hasBeenOpened || disablePreventScroll\n    });\n    const { restorePositionSetting } = usePositionFixed({\n        isOpen,\n        modal,\n        nested,\n        hasBeenOpened,\n        preventScrollRestoration,\n        noBodyStyles\n    });\n    function getScale() {\n        return (window.innerWidth - WINDOW_TOP_OFFSET) / window.innerWidth;\n    }\n    function onPress(event) {\n        var _drawerRef_current;\n        if (!dismissible && !snapPoints) return;\n        if (drawerRef.current && !drawerRef.current.contains(event.target)) return;\n        drawerHeightRef.current = ((_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.getBoundingClientRect().height) || 0;\n        setIsDragging(true);\n        dragStartTime.current = new Date();\n        // iOS doesn't trigger mouseUp after scrolling so we need to listen to touched in order to disallow dragging\n        if (isIOS()) {\n            window.addEventListener(\"touchend\", ()=>isAllowedToDrag.current = false, {\n                once: true\n            });\n        }\n        // Ensure we maintain correct pointer capture even when going outside of the drawer\n        event.target.setPointerCapture(event.pointerId);\n        pointerStart.current = isVertical(direction) ? event.clientY : event.clientX;\n    }\n    function shouldDrag(el, isDraggingInDirection) {\n        var _window_getSelection;\n        let element = el;\n        const highlightedText = (_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString();\n        const swipeAmount = drawerRef.current ? getTranslate(drawerRef.current, direction) : null;\n        const date = new Date();\n        if (element.hasAttribute(\"data-vaul-no-drag\") || element.closest(\"[data-vaul-no-drag]\")) {\n            return false;\n        }\n        if (direction === \"right\" || direction === \"left\") {\n            return true;\n        }\n        // Allow scrolling when animating\n        if (openTime.current && date.getTime() - openTime.current.getTime() < 500) {\n            return false;\n        }\n        if (swipeAmount !== null) {\n            if (direction === \"bottom\" ? swipeAmount > 0 : swipeAmount < 0) {\n                return true;\n            }\n        }\n        // Don't drag if there's highlighted text\n        if (highlightedText && highlightedText.length > 0) {\n            return false;\n        }\n        // Disallow dragging if drawer was scrolled within `scrollLockTimeout`\n        if (lastTimeDragPrevented.current && date.getTime() - lastTimeDragPrevented.current.getTime() < scrollLockTimeout && swipeAmount === 0) {\n            lastTimeDragPrevented.current = date;\n            return false;\n        }\n        if (isDraggingInDirection) {\n            lastTimeDragPrevented.current = date;\n            // We are dragging down so we should allow scrolling\n            return false;\n        }\n        // Keep climbing up the DOM tree as long as there's a parent\n        while(element){\n            // Check if the element is scrollable\n            if (element.scrollHeight > element.clientHeight) {\n                if (element.scrollTop !== 0) {\n                    lastTimeDragPrevented.current = new Date();\n                    // The element is scrollable and not scrolled to the top, so don't drag\n                    return false;\n                }\n                if (element.getAttribute(\"role\") === \"dialog\") {\n                    return true;\n                }\n            }\n            // Move up to the parent element\n            element = element.parentNode;\n        }\n        // No scrollable parents not scrolled to the top found, so drag\n        return true;\n    }\n    function onDrag(event) {\n        if (!drawerRef.current) {\n            return;\n        }\n        // We need to know how much of the drawer has been dragged in percentages so that we can transform background accordingly\n        if (isDragging) {\n            const directionMultiplier = direction === \"bottom\" || direction === \"right\" ? 1 : -1;\n            const draggedDistance = (pointerStart.current - (isVertical(direction) ? event.clientY : event.clientX)) * directionMultiplier;\n            const isDraggingInDirection = draggedDistance > 0;\n            // Pre condition for disallowing dragging in the close direction.\n            const noCloseSnapPointsPreCondition = snapPoints && !dismissible && !isDraggingInDirection;\n            // Disallow dragging down to close when first snap point is the active one and dismissible prop is set to false.\n            if (noCloseSnapPointsPreCondition && activeSnapPointIndex === 0) return;\n            // We need to capture last time when drag with scroll was triggered and have a timeout between\n            const absDraggedDistance = Math.abs(draggedDistance);\n            const wrapper = document.querySelector(\"[vaul-drawer-wrapper]\");\n            // Calculate the percentage dragged, where 1 is the closed position\n            let percentageDragged = absDraggedDistance / drawerHeightRef.current;\n            const snapPointPercentageDragged = getSnapPointsPercentageDragged(absDraggedDistance, isDraggingInDirection);\n            if (snapPointPercentageDragged !== null) {\n                percentageDragged = snapPointPercentageDragged;\n            }\n            // Disallow close dragging beyond the smallest snap point.\n            if (noCloseSnapPointsPreCondition && percentageDragged >= 1) {\n                return;\n            }\n            if (!isAllowedToDrag.current && !shouldDrag(event.target, isDraggingInDirection)) return;\n            drawerRef.current.classList.add(DRAG_CLASS);\n            // If shouldDrag gave true once after pressing down on the drawer, we set isAllowedToDrag to true and it will remain true until we let go, there's no reason to disable dragging mid way, ever, and that's the solution to it\n            isAllowedToDrag.current = true;\n            set(drawerRef.current, {\n                transition: \"none\"\n            });\n            set(overlayRef.current, {\n                transition: \"none\"\n            });\n            if (snapPoints) {\n                onDragSnapPoints({\n                    draggedDistance\n                });\n            }\n            // Run this only if snapPoints are not defined or if we are at the last snap point (highest one)\n            if (isDraggingInDirection && !snapPoints) {\n                const dampenedDraggedDistance = dampenValue(draggedDistance);\n                const translateValue = Math.min(dampenedDraggedDistance * -1, 0) * directionMultiplier;\n                set(drawerRef.current, {\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n                return;\n            }\n            const opacityValue = 1 - percentageDragged;\n            if (shouldFade || fadeFromIndex && activeSnapPointIndex === fadeFromIndex - 1) {\n                onDragProp == null ? void 0 : onDragProp(event, percentageDragged);\n                set(overlayRef.current, {\n                    opacity: `${opacityValue}`,\n                    transition: \"none\"\n                }, true);\n            }\n            if (wrapper && overlayRef.current && shouldScaleBackground) {\n                // Calculate percentageDragged as a fraction (0 to 1)\n                const scaleValue = Math.min(getScale() + percentageDragged * (1 - getScale()), 1);\n                const borderRadiusValue = 8 - percentageDragged * 8;\n                const translateValue = Math.max(0, 14 - percentageDragged * 14);\n                set(wrapper, {\n                    borderRadius: `${borderRadiusValue}px`,\n                    transform: isVertical(direction) ? `scale(${scaleValue}) translate3d(0, ${translateValue}px, 0)` : `scale(${scaleValue}) translate3d(${translateValue}px, 0, 0)`,\n                    transition: \"none\"\n                }, true);\n            }\n            if (!snapPoints) {\n                const translateValue = absDraggedDistance * directionMultiplier;\n                set(drawerRef.current, {\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n            }\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            scaleBackground(false);\n            restorePositionSetting();\n        };\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        var _window_visualViewport;\n        function onVisualViewportChange() {\n            if (!drawerRef.current) return;\n            const focusedElement = document.activeElement;\n            if (isInput(focusedElement) || keyboardIsOpen.current) {\n                var _window_visualViewport;\n                const visualViewportHeight = ((_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.height) || 0;\n                // This is the height of the keyboard\n                let diffFromInitial = window.innerHeight - visualViewportHeight;\n                const drawerHeight = drawerRef.current.getBoundingClientRect().height || 0;\n                if (!initialDrawerHeight.current) {\n                    initialDrawerHeight.current = drawerHeight;\n                }\n                const offsetFromTop = drawerRef.current.getBoundingClientRect().top;\n                // visualViewport height may change due to some subtle changes to the keyboard. Checking if the height changed by 60 or more will make sure that they keyboard really changed its open state.\n                if (Math.abs(previousDiffFromInitial.current - diffFromInitial) > 60) {\n                    keyboardIsOpen.current = !keyboardIsOpen.current;\n                }\n                if (snapPoints && snapPoints.length > 0 && snapPointsOffset && activeSnapPointIndex) {\n                    const activeSnapPointHeight = snapPointsOffset[activeSnapPointIndex] || 0;\n                    diffFromInitial += activeSnapPointHeight;\n                }\n                previousDiffFromInitial.current = diffFromInitial;\n                // We don't have to change the height if the input is in view, when we are here we are in the opened keyboard state so we can correctly check if the input is in view\n                if (drawerHeight > visualViewportHeight || keyboardIsOpen.current) {\n                    const height = drawerRef.current.getBoundingClientRect().height;\n                    let newDrawerHeight = height;\n                    if (height > visualViewportHeight) {\n                        newDrawerHeight = visualViewportHeight - WINDOW_TOP_OFFSET;\n                    }\n                    // When fixed, don't move the drawer upwards if there's space, but rather only change it's height so it's fully scrollable when the keyboard is open\n                    if (fixed) {\n                        drawerRef.current.style.height = `${height - Math.max(diffFromInitial, 0)}px`;\n                    } else {\n                        drawerRef.current.style.height = `${Math.max(newDrawerHeight, visualViewportHeight - offsetFromTop)}px`;\n                    }\n                } else {\n                    drawerRef.current.style.height = `${initialDrawerHeight.current}px`;\n                }\n                if (snapPoints && snapPoints.length > 0 && !keyboardIsOpen.current) {\n                    drawerRef.current.style.bottom = `0px`;\n                } else {\n                    // Negative bottom value would never make sense\n                    drawerRef.current.style.bottom = `${Math.max(diffFromInitial, 0)}px`;\n                }\n            }\n        }\n        (_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.addEventListener(\"resize\", onVisualViewportChange);\n        return ()=>{\n            var _window_visualViewport;\n            return (_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.removeEventListener(\"resize\", onVisualViewportChange);\n        };\n    }, [\n        activeSnapPointIndex,\n        snapPoints,\n        snapPointsOffset\n    ]);\n    function closeDrawer() {\n        if (!drawerRef.current) return;\n        cancelDrag();\n        onClose == null ? void 0 : onClose();\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `translate3d(0, ${direction === \"bottom\" ? \"100%\" : \"-100%\"}, 0)` : `translate3d(${direction === \"right\" ? \"100%\" : \"-100%\"}, 0, 0)`,\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n        });\n        set(overlayRef.current, {\n            opacity: \"0\",\n            transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n        });\n        scaleBackground(false);\n        setTimeout(()=>{\n            setVisible(false);\n            setIsOpen(false);\n        }, 300);\n        setTimeout(()=>{\n            // reset(document.documentElement, 'scrollBehavior');\n            if (snapPoints) {\n                setActiveSnapPoint(snapPoints[0]);\n            }\n        }, TRANSITIONS.DURATION * 1000); // seconds to ms\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!isOpen && shouldScaleBackground) {\n            // Can't use `onAnimationEnd` as the component will be invisible by then\n            const id = setTimeout(()=>{\n                reset(document.body);\n            }, 200);\n            return ()=>clearTimeout(id);\n        }\n    }, [\n        isOpen,\n        shouldScaleBackground\n    ]);\n    // LayoutEffect to prevent extra render where openProp and isOpen are not synced yet\n    useIsomorphicLayoutEffect(()=>{\n        if (openProp) {\n            setIsOpen(true);\n            setHasBeenOpened(true);\n        } else {\n            closeDrawer();\n        }\n    }, [\n        openProp\n    ]);\n    // This can be done much better\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (mounted) {\n            onOpenChange == null ? void 0 : onOpenChange(isOpen);\n        }\n    }, [\n        isOpen\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    function resetDrawer() {\n        if (!drawerRef.current) return;\n        const wrapper = document.querySelector(\"[vaul-drawer-wrapper]\");\n        const currentSwipeAmount = getTranslate(drawerRef.current, direction);\n        set(drawerRef.current, {\n            transform: \"translate3d(0, 0, 0)\",\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n        });\n        set(overlayRef.current, {\n            transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n            opacity: \"1\"\n        });\n        // Don't reset background if swiped upwards\n        if (shouldScaleBackground && currentSwipeAmount && currentSwipeAmount > 0 && isOpen) {\n            set(wrapper, {\n                borderRadius: `${BORDER_RADIUS}px`,\n                overflow: \"hidden\",\n                ...isVertical(direction) ? {\n                    transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,\n                    transformOrigin: \"top\"\n                } : {\n                    transform: `scale(${getScale()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`,\n                    transformOrigin: \"left\"\n                },\n                transitionProperty: \"transform, border-radius\",\n                transitionDuration: `${TRANSITIONS.DURATION}s`,\n                transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n            }, true);\n        }\n    }\n    function cancelDrag() {\n        if (!isDragging || !drawerRef.current) return;\n        drawerRef.current.classList.remove(DRAG_CLASS);\n        isAllowedToDrag.current = false;\n        setIsDragging(false);\n        dragEndTime.current = new Date();\n    }\n    function onRelease(event) {\n        if (!isDragging || !drawerRef.current) return;\n        drawerRef.current.classList.remove(DRAG_CLASS);\n        isAllowedToDrag.current = false;\n        setIsDragging(false);\n        dragEndTime.current = new Date();\n        const swipeAmount = getTranslate(drawerRef.current, direction);\n        if (!shouldDrag(event.target, false) || !swipeAmount || Number.isNaN(swipeAmount)) return;\n        if (dragStartTime.current === null) return;\n        const timeTaken = dragEndTime.current.getTime() - dragStartTime.current.getTime();\n        const distMoved = pointerStart.current - (isVertical(direction) ? event.clientY : event.clientX);\n        const velocity = Math.abs(distMoved) / timeTaken;\n        if (velocity > 0.05) {\n            // `justReleased` is needed to prevent the drawer from focusing on an input when the drag ends, as it's not the intent most of the time.\n            setJustReleased(true);\n            setTimeout(()=>{\n                setJustReleased(false);\n            }, 200);\n        }\n        if (snapPoints) {\n            const directionMultiplier = direction === \"bottom\" || direction === \"right\" ? 1 : -1;\n            onReleaseSnapPoints({\n                draggedDistance: distMoved * directionMultiplier,\n                closeDrawer,\n                velocity,\n                dismissible\n            });\n            onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n            return;\n        }\n        // Moved upwards, don't do anything\n        if (direction === \"bottom\" || direction === \"right\" ? distMoved > 0 : distMoved < 0) {\n            resetDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n            return;\n        }\n        if (velocity > VELOCITY_THRESHOLD) {\n            closeDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, false);\n            return;\n        }\n        var _drawerRef_current_getBoundingClientRect_height;\n        const visibleDrawerHeight = Math.min((_drawerRef_current_getBoundingClientRect_height = drawerRef.current.getBoundingClientRect().height) != null ? _drawerRef_current_getBoundingClientRect_height : 0, window.innerHeight);\n        if (swipeAmount >= visibleDrawerHeight * closeThreshold) {\n            closeDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, false);\n            return;\n        }\n        onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n        resetDrawer();\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        // Trigger enter animation without using CSS animation\n        if (isOpen) {\n            set(document.documentElement, {\n                scrollBehavior: \"auto\"\n            });\n            openTime.current = new Date();\n            scaleBackground(true);\n        }\n    }, [\n        isOpen\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (drawerRef.current && visible) {\n            var _drawerRef_current;\n            // Find all scrollable elements inside our drawer and assign a class to it so that we can disable overflow when dragging to prevent pointermove not being captured\n            const children = drawerRef == null ? void 0 : (_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.querySelectorAll(\"*\");\n            children == null ? void 0 : children.forEach((child)=>{\n                const htmlChild = child;\n                if (htmlChild.scrollHeight > htmlChild.clientHeight || htmlChild.scrollWidth > htmlChild.clientWidth) {\n                    htmlChild.classList.add(\"vaul-scrollable\");\n                }\n            });\n        }\n    }, [\n        visible\n    ]);\n    function scaleBackground(open) {\n        const wrapper = document.querySelector(\"[vaul-drawer-wrapper]\");\n        if (!wrapper || !shouldScaleBackground) return;\n        if (open) {\n            if (setBackgroundColorOnScale) {\n                if (!noBodyStyles) {\n                    // setting original styles initially\n                    set(document.body, {\n                        background: document.body.style.backgroundColor || document.body.style.background\n                    });\n                    // setting body styles, with cache ignored, so that we can get correct original styles in reset\n                    set(document.body, {\n                        background: \"black\"\n                    }, true);\n                }\n            }\n            set(wrapper, {\n                borderRadius: `${BORDER_RADIUS}px`,\n                overflow: \"hidden\",\n                ...isVertical(direction) ? {\n                    transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,\n                    transformOrigin: \"top\"\n                } : {\n                    transform: `scale(${getScale()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`,\n                    transformOrigin: \"left\"\n                },\n                transitionProperty: \"transform, border-radius\",\n                transitionDuration: `${TRANSITIONS.DURATION}s`,\n                transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n            });\n        } else {\n            // Exit\n            reset(wrapper, \"overflow\");\n            reset(wrapper, \"transform\");\n            reset(wrapper, \"borderRadius\");\n            set(wrapper, {\n                transitionProperty: \"transform, border-radius\",\n                transitionDuration: `${TRANSITIONS.DURATION}s`,\n                transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`\n            });\n        }\n    }\n    function onNestedOpenChange(o) {\n        const scale = o ? (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth : 1;\n        const y = o ? -NESTED_DISPLACEMENT : 0;\n        if (nestedOpenChangeTimer.current) {\n            window.clearTimeout(nestedOpenChangeTimer.current);\n        }\n        set(drawerRef.current, {\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n            transform: `scale(${scale}) translate3d(0, ${y}px, 0)`\n        });\n        if (!o && drawerRef.current) {\n            nestedOpenChangeTimer.current = setTimeout(()=>{\n                const translateValue = getTranslate(drawerRef.current, direction);\n                set(drawerRef.current, {\n                    transition: \"none\",\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n            }, 500);\n        }\n    }\n    function onNestedDrag(event, percentageDragged) {\n        if (percentageDragged < 0) return;\n        const initialDim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        const initialScale = (initialDim - NESTED_DISPLACEMENT) / initialDim;\n        const newScale = initialScale + percentageDragged * (1 - initialScale);\n        const newTranslate = -NESTED_DISPLACEMENT + percentageDragged * NESTED_DISPLACEMENT;\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `scale(${newScale}) translate3d(0, ${newTranslate}px, 0)` : `scale(${newScale}) translate3d(${newTranslate}px, 0, 0)`,\n            transition: \"none\"\n        });\n    }\n    function onNestedRelease(event, o) {\n        const dim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        const scale = o ? (dim - NESTED_DISPLACEMENT) / dim : 1;\n        const translate = o ? -NESTED_DISPLACEMENT : 0;\n        if (o) {\n            set(drawerRef.current, {\n                transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(\",\")})`,\n                transform: isVertical(direction) ? `scale(${scale}) translate3d(0, ${translate}px, 0)` : `scale(${scale}) translate3d(${translate}px, 0, 0)`\n            });\n        }\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Root, {\n        modal: modal,\n        onOpenChange: (o)=>{\n            if (openProp !== undefined) {\n                onOpenChange == null ? void 0 : onOpenChange(o);\n                return;\n            }\n            if (!o) {\n                closeDrawer();\n            } else {\n                setHasBeenOpened(true);\n                setIsOpen(o);\n            }\n        },\n        open: isOpen\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(DrawerContext.Provider, {\n        value: {\n            visible,\n            activeSnapPoint,\n            snapPoints,\n            setActiveSnapPoint,\n            drawerRef,\n            overlayRef,\n            scaleBackground,\n            onOpenChange,\n            onPress,\n            setVisible,\n            onRelease,\n            onDrag,\n            dismissible,\n            handleOnly,\n            isOpen,\n            isDragging,\n            shouldFade,\n            closeDrawer,\n            onNestedDrag,\n            onNestedOpenChange,\n            onNestedRelease,\n            keyboardIsOpen,\n            openProp,\n            modal,\n            snapPointsOffset,\n            direction\n        }\n    }, children));\n}\nconst LONG_HANDLE_PRESS_TIMEOUT = 250;\nconst DOUBLE_TAP_TIMEOUT = 120;\nconst Handle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function({ preventCycle = false, children, ...rest }, ref) {\n    const { visible, closeDrawer, isDragging, snapPoints, activeSnapPoint, setActiveSnapPoint, dismissible, handleOnly, onPress, onDrag } = useDrawerContext();\n    const closeTimeoutIdRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const shouldCancelInteractionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    function handleStartCycle() {\n        // Stop if this is the second click of a double click\n        if (shouldCancelInteractionRef.current) {\n            handleCancelInteraction();\n            return;\n        }\n        window.setTimeout(()=>{\n            handleCycleSnapPoints();\n        }, DOUBLE_TAP_TIMEOUT);\n    }\n    function handleCycleSnapPoints() {\n        // Prevent accidental taps while resizing drawer\n        if (isDragging || preventCycle || shouldCancelInteractionRef.current) {\n            handleCancelInteraction();\n            return;\n        }\n        // Make sure to clear the timeout id if the user releases the handle before the cancel timeout\n        handleCancelInteraction();\n        if ((!snapPoints || snapPoints.length === 0) && dismissible) {\n            closeDrawer();\n            return;\n        }\n        const isLastSnapPoint = activeSnapPoint === snapPoints[snapPoints.length - 1];\n        if (isLastSnapPoint && dismissible) {\n            closeDrawer();\n            return;\n        }\n        const currentSnapIndex = snapPoints.findIndex((point)=>point === activeSnapPoint);\n        if (currentSnapIndex === -1) return; // activeSnapPoint not found in snapPoints\n        const nextSnapPoint = snapPoints[currentSnapIndex + 1];\n        setActiveSnapPoint(nextSnapPoint);\n    }\n    function handleStartInteraction() {\n        closeTimeoutIdRef.current = window.setTimeout(()=>{\n            // Cancel click interaction on a long press\n            shouldCancelInteractionRef.current = true;\n        }, LONG_HANDLE_PRESS_TIMEOUT);\n    }\n    function handleCancelInteraction() {\n        window.clearTimeout(closeTimeoutIdRef.current);\n        shouldCancelInteractionRef.current = false;\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        onClick: handleStartCycle,\n        onDoubleClick: ()=>{\n            shouldCancelInteractionRef.current = true;\n            closeDrawer();\n        },\n        onPointerCancel: handleCancelInteraction,\n        onPointerDown: (e)=>{\n            if (handleOnly) onPress(e);\n            handleStartInteraction();\n        },\n        onPointerMove: (e)=>{\n            if (handleOnly) onDrag(e);\n        },\n        // onPointerUp is already handled by the content component\n        ref: ref,\n        \"vaul-drawer-visible\": visible ? \"true\" : \"false\",\n        \"vaul-handle\": \"\",\n        \"aria-hidden\": \"true\",\n        ...rest\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n        \"vaul-handle-hitarea\": \"\",\n        \"aria-hidden\": \"true\"\n    }, children));\n});\nHandle.displayName = \"Drawer.Handle\";\nconst Overlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function({ children, ...rest }, ref) {\n    const { overlayRef, snapPoints, onRelease, shouldFade, isOpen, visible } = useDrawerContext();\n    const composedRef = useComposedRefs(ref, overlayRef);\n    const hasSnapPoints = snapPoints && snapPoints.length > 0;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Overlay, {\n        onMouseUp: onRelease,\n        ref: composedRef,\n        \"vaul-drawer-visible\": visible ? \"true\" : \"false\",\n        \"vaul-overlay\": \"\",\n        \"vaul-snap-points\": isOpen && hasSnapPoints ? \"true\" : \"false\",\n        \"vaul-snap-points-overlay\": isOpen && shouldFade ? \"true\" : \"false\",\n        ...rest\n    });\n});\nOverlay.displayName = \"Drawer.Overlay\";\nconst Content = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function({ onOpenAutoFocus, onPointerDownOutside, onAnimationEnd, style, ...rest }, ref) {\n    const { drawerRef, onPress, onRelease, onDrag, dismissible, keyboardIsOpen, snapPointsOffset, visible, closeDrawer, modal, openProp, onOpenChange, setVisible, handleOnly, direction } = useDrawerContext();\n    const composedRef = useComposedRefs(ref, drawerRef);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const wasBeyondThePointRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isDeltaInDirection = (delta, direction, threshold = 0)=>{\n        if (wasBeyondThePointRef.current) return true;\n        const deltaY = Math.abs(delta.y);\n        const deltaX = Math.abs(delta.x);\n        const isDeltaX = deltaX > deltaY;\n        const dFactor = [\n            \"bottom\",\n            \"right\"\n        ].includes(direction) ? 1 : -1;\n        if (direction === \"left\" || direction === \"right\") {\n            const isReverseDirection = delta.x * dFactor < 0;\n            if (!isReverseDirection && deltaX >= 0 && deltaX <= threshold) {\n                return isDeltaX;\n            }\n        } else {\n            const isReverseDirection = delta.y * dFactor < 0;\n            if (!isReverseDirection && deltaY >= 0 && deltaY <= threshold) {\n                return !isDeltaX;\n            }\n        }\n        wasBeyondThePointRef.current = true;\n        return true;\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        // Trigger enter animation without using CSS animation\n        setVisible(true);\n    }, []);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Content, {\n        \"vaul-drawer\": \"\",\n        \"vaul-drawer-direction\": direction,\n        \"vaul-drawer-visible\": visible ? \"true\" : \"false\",\n        ...rest,\n        ref: composedRef,\n        style: snapPointsOffset && snapPointsOffset.length > 0 ? {\n            \"--snap-point-height\": `${snapPointsOffset[0]}px`,\n            ...style\n        } : style,\n        onOpenAutoFocus: (e)=>{\n            if (onOpenAutoFocus) {\n                onOpenAutoFocus(e);\n            } else {\n                var _drawerRef_current;\n                e.preventDefault();\n                (_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.focus();\n            }\n        },\n        onPointerDown: (event)=>{\n            if (handleOnly) return;\n            rest.onPointerDown == null ? void 0 : rest.onPointerDown.call(rest, event);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n            onPress(event);\n        },\n        onPointerDownOutside: (e)=>{\n            onPointerDownOutside == null ? void 0 : onPointerDownOutside(e);\n            if (!modal || e.defaultPrevented) {\n                e.preventDefault();\n                return;\n            }\n            if (keyboardIsOpen.current) {\n                keyboardIsOpen.current = false;\n            }\n            e.preventDefault();\n            onOpenChange == null ? void 0 : onOpenChange(false);\n            if (!dismissible || openProp !== undefined) {\n                return;\n            }\n            closeDrawer();\n        },\n        onFocusOutside: (e)=>{\n            if (!modal) {\n                e.preventDefault();\n                return;\n            }\n        },\n        onEscapeKeyDown: (e)=>{\n            if (!modal) {\n                e.preventDefault();\n                return;\n            }\n        },\n        onPointerMove: (event)=>{\n            if (handleOnly) return;\n            rest.onPointerMove == null ? void 0 : rest.onPointerMove.call(rest, event);\n            if (!pointerStartRef.current) return;\n            const yPosition = event.clientY - pointerStartRef.current.y;\n            const xPosition = event.clientX - pointerStartRef.current.x;\n            const swipeStartThreshold = event.pointerType === \"touch\" ? 10 : 2;\n            const delta = {\n                x: xPosition,\n                y: yPosition\n            };\n            const isAllowedToSwipe = isDeltaInDirection(delta, direction, swipeStartThreshold);\n            if (isAllowedToSwipe) onDrag(event);\n            else if (Math.abs(xPosition) > swipeStartThreshold || Math.abs(yPosition) > swipeStartThreshold) {\n                pointerStartRef.current = null;\n            }\n        },\n        onPointerUp: (event)=>{\n            rest.onPointerUp == null ? void 0 : rest.onPointerUp.call(rest, event);\n            pointerStartRef.current = null;\n            wasBeyondThePointRef.current = false;\n            onRelease(event);\n        }\n    });\n});\nContent.displayName = \"Drawer.Content\";\nfunction NestedRoot({ onDrag, onOpenChange, ...rest }) {\n    const { onNestedDrag, onNestedOpenChange, onNestedRelease } = useDrawerContext();\n    if (!onNestedDrag) {\n        throw new Error(\"Drawer.NestedRoot must be placed in another drawer\");\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Root, {\n        nested: true,\n        onClose: ()=>{\n            onNestedOpenChange(false);\n        },\n        onDrag: (e, p)=>{\n            onNestedDrag(e, p);\n            onDrag == null ? void 0 : onDrag(e, p);\n        },\n        onOpenChange: (o)=>{\n            if (o) {\n                onNestedOpenChange(o);\n            }\n            onOpenChange == null ? void 0 : onOpenChange(o);\n        },\n        onRelease: onNestedRelease,\n        ...rest\n    });\n}\nconst Drawer = {\n    Root,\n    NestedRoot,\n    Content,\n    Handle,\n    Overlay,\n    Trigger: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Trigger,\n    Portal: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Portal,\n    Close: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Close,\n    Title: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Title,\n    Description: _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_1__.Description\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vaul/dist/index.mjs\n");

/***/ })

};
;