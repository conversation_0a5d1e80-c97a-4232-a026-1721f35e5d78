"use client"
import Di<PERSON><PERSON>eader<PERSON>rapper from "@/components/dialog-wrapper/dialog-header-wrapper";
import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper";
import { useTranslations } from "next-intl";
import React, { useState } from "react";
import {
  FacebookShareButton,
  FacebookIcon,
  TelegramShareButton,
  TelegramIcon,
  TwitterShareButton,
  TwitterIcon,
  WhatsappShareButton,
  WhatsappIcon,
} from 'next-share'
import { useToast } from "@/hooks/use-toast";
import { Link } from "lucide-react";
import { usePathname } from "@/lib/locale/routing";
import { useSearchParams } from "next/navigation";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";

export default function ShareDialog({ trigger }: { trigger: React.ReactNode }) {
  const [open, setOpen] = useState(false)
  const t = useTranslations("seeker")
  const { toast } = useToast()
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const queryString = searchParams.toString();

  const baseUrl = typeof window !== "undefined"
    ? `${window.location.protocol}//${window.location.host}`
    : "http://localhost:3000";

  const url = `${baseUrl}${pathname}${queryString ? `?${queryString}` : ""}`;
  const handleShareLink = () => {
    navigator.clipboard.writeText(window.location.href)
    toast({
      title: t('success.copyUrl.title'),
      description: t('success.copyUrl.description')
    })
  }
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={trigger}
  >
    <DialogHeaderWrapper>
      <h2 className="text-base font-bold text-seekers-text text-center ">
        {t('misc.shareProperty.title')}
      </h2>

    </DialogHeaderWrapper>
    <ScrollArea className="w-full py-4">
      <div className="flex gap-4">
        <div>
          <FacebookShareButton
            url={url}
            quote={'Hey checkout property that I found'}
          >
            <div className="p-4 rounded-full bg-blue-500/20">
              <FacebookIcon size={32} round />
            </div>

          </FacebookShareButton>
          <p className="text-center text-seekers-text-light text-xs">Facebook</p>
        </div>
        <div>
          <TelegramShareButton
            url={url}
            title={'Hey checkout property that I found'}
          >
            <div className="p-4 rounded-full bg-sky-500/20">
              <TelegramIcon size={32} round />
            </div>
          </TelegramShareButton>
          <p className="text-center text-seekers-text-light text-xs">Telegram</p>

        </div>
        <div>
          <TwitterShareButton
            url={url}
            title={'Hey checkout property that I found'}
          >
            <div className="p-4 rounded-full bg-stone-500/20">
              <TwitterIcon size={32} round />
            </div>
          </TwitterShareButton>
          <p className="text-center text-seekers-text-light text-xs">Twitter</p>
        </div>
        <div>
          <WhatsappShareButton
            url={url}
            title={'Hey checkout property that I found'}
            separator=" "
          >
            <div className="p-4 rounded-full bg-emerald-500/20">
              <WhatsappIcon size={32} round />
            </div>
          </WhatsappShareButton>
          <p className="text-center text-seekers-text-light text-xs">Whatsapp</p>
        </div>
        <div>
          <div className="p-4 rounded-full bg-amber-500/20" onClick={handleShareLink}>
            <Link className="w-8 h-8" />
          </div>
          <p className="text-center text-seekers-text-light text-xs mt-1.5">{t('cta.copyLink')}</p>
        </div>
      </div>
      <ScrollBar orientation="horizontal" />
    </ScrollArea>
  </DialogWrapper>
}