import { postNewChats } from "@/core/infrastructures/messages/api";
import { NewChatDto } from "@/core/infrastructures/messages/dto";
import { getChatRoomDetailService } from "@/core/infrastructures/messages/services";
import { useMutation } from "@tanstack/react-query";

export const usePostNewChat = () => {
  const mutation = useMutation({
    mutationFn: (data:NewChatDto) => postNewChats(data),
    onSuccess: async (response) => {
      const data = response.data.data
      const chatDetail = await getChatRoomDetailService(data.code)
      return chatDetail
    }
  })
  return mutation
}