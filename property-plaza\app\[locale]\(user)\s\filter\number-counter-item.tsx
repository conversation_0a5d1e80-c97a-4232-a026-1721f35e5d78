import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Minus, Plus } from "lucide-react"

interface NumberCounterItemProps {
  value: string,
  setValue: (val: string) => void,
  title: string
}
const MAXIMUM_VALUE = 99
export default function NumberCounterItem({ setValue, value, title }: NumberCounterItemProps) {
  const handleCounter = (val: string, type: "decrement" | "increment") => {
    if (type == "decrement") {
      if (val == "any") return
      if (+val == 0) {
        setValue("any")
        return
      }
      if (+val >= 0) {
        setValue((+val - 1).toString())
        return
      }
    } else {
      if (val == "any") {
        setValue("0")
        return
      } else if (+val >= MAXIMUM_VALUE) {
        return
      } else {
        setValue((+val + 1).toString())

      }
    }
  }
  return <div className="flex items-center justify-between">
    <Label className="font-normal">{title}</Label>
    <div className="flex gap-1 items-center">
      <Button
        type="button"
        size={"icon"}
        variant={"outline"}
        className="h-6 w-6"
        disabled={value == "any" || +value < 0}
        onClick={() => handleCounter(value, "decrement")}>
        <Minus className="w-3 h-3" />
      </Button>
      <p
        className="text-center w-16 text-xs">
        {value}
      </p>
      <Button
        size={"icon"}
        type="button"
        variant={"outline"}
        className="h-6 w-6"
        disabled={+value >= MAXIMUM_VALUE}
        onClick={() => handleCounter(value, "increment")}>
        <Plus className="w-3 h-3" />
      </Button>
    </div>
  </div>
}