import { PASSWORD_MIN_LENGTH } from "@/lib/constanta/constant";
import { useTranslations } from "next-intl";
import { z } from "zod";

export function useLoginOwnerFormSchema() {
  const t = useTranslations("owner")

  const formSchema = z.object({
    contact: z.string({message: t("form.utility.fieldRequired", {field: t("form.field.email") + t("conjuntion.or") + t("form.field.phoneNumber") })}).refine(value => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      const phoneRegex = /^\+?[1-9]\d{1,14}$/
      return emailRegex.test(value.trim()) || phoneRegex.test(value.trim())
    }, { message: t("form.utility.enterValidField", { field: ` ${t("form.field.email")} ${t("conjuntion.or")} ${t("form.field.phoneNumber")} ` }) }),
    password: z.string({
      message: t("form.utility.fieldRequired", { field: t("form.field.password") })
    }).min(PASSWORD_MIN_LENGTH, {message: t("form.utility.minimumLength", {field: t("form.field.password"), length: PASSWORD_MIN_LENGTH})}),
  })
  return formSchema
}

export function useLoginSeekerFormSchema() {
  const t = useTranslations("seeker")

  const formSchema = z.object({
    contact: z.string({message: t("form.utility.fieldRequired", {field: t("form.field.email") })})
    .email({message:t("form.utility.enterValidField", { field: ` ${t("form.field.email")}` })}),
    password: z.string({
      message: t("form.utility.fieldRequired", { field: t("form.field.password") })
    }).min(PASSWORD_MIN_LENGTH, {message: t("form.utility.minimumLength", {field: t("form.field.password"), length: PASSWORD_MIN_LENGTH})}),
  })
  return formSchema
}