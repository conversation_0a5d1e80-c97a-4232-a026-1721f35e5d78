"use client"
import { useRegisterStore } from "@/stores/register.store";
import { useTranslations } from "next-intl";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { REGEXP_ONLY_DIGITS } from "input-otp"
import { useRegister } from "@/core/applications/mutations/auth/use-register";
import { useVerifyOtp } from "@/core/applications/mutations/auth/use-verify-otp";
import { OtpVerificationDto } from "@/core/infrastructures/auth/dto";
import { Form, FormField } from "@/components/ui/form";
import BaseInputLayout from "@/components/input-form/base-input";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";
import { Button } from "@/components/ui/button";
import { useEmailVerification } from "@/core/applications/mutations/auth/use-email-verification";
import { useToast } from "@/hooks/use-toast";
import { useOtpFormSchema } from "../../../(auth)/form/use-otp-form.schema";
import { useEffect } from "react";

const MAX_OTP = 5
export default function SubscriptionOtpForm({ onSuccess, email }: { onSuccess: () => Promise<void>, email: string }) {
  const { toast } = useToast()
  const t = useTranslations("seeker")
  const formSchema = useOtpFormSchema()
  const useOtpVerificationMutation = useVerifyOtp(async () => await onSuccess())
  const useSendOtpViaEmail = useEmailVerification((data) => {
    if ((data as any).response.data.message === "Email verification code is already sent. Please check your email") {
      toast({
        title: t("message.otpRequest.failedToast.title"),
        description: (data as any).response.data.message || "",
        variant: "destructive"
      })
      return
    } else {
      toast({
        title: t('success.sendVerification.title') + " " + email
      })
    }
  })
  type formSchemaType = z.infer<typeof formSchema>
  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      otp: ""
    }
  })

  useEffect(() => {
    const otpLength = form.getValues("otp").length
    const otpButton = document.getElementById("otp-button")
    if (otpLength >= MAX_OTP) {
      otpButton?.click()
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.getValues("otp")])
  useEffect(() => {
    if (email) return useSendOtpViaEmail.mutate({ email: email, category: "REGISTRATION" })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [email])
  async function onSubmit(value: z.infer<typeof formSchema>) {
    const data: OtpVerificationDto = {
      otp: value.otp,
      requested_by: email || "",
      type: "EMAIL"
    }
    try {
      await useOtpVerificationMutation.mutateAsync(data)

    } catch (e: any) {
      toast({
        title: t("error.signUp.title"),
        description: e.response.data.message,
        variant: "destructive"
      })
    }
  }
  async function resendVerification() {
    useSendOtpViaEmail.mutate({ email: email, category: "REGISTRATION" })
  }
  return <Form {...form}>
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
      <FormField
        control={form.control}
        name={"otp"}
        render={({ field }) => (
          <div className="flex justify-center">
            <BaseInputLayout label="">
              <InputOTP maxLength={MAX_OTP} {...field} pattern={REGEXP_ONLY_DIGITS} required containerClassName="flex justify-center max-sm:justify-between" >
                <InputOTPGroup>
                  {Array.from({ length: MAX_OTP }, (_, idx) =>
                    <InputOTPSlot key={idx} index={idx} className="w-16 h-20 text-2xl" />)}
                </InputOTPGroup>
              </InputOTP>
            </BaseInputLayout>
          </div>
        )}
      />
      <div className="space-y-2 flex flex-col items-center">
        <Button id="otp-button" className="w-full" variant={"default-seekers"} loading={useOtpVerificationMutation.isPending}>
          {t("cta.verify")} {t("user.account")}
        </Button>
        <button

          onClick={e => {
            e.preventDefault()
            e.stopPropagation()
            resendVerification()
          }} className="mx-auto text-xs text-seekers-text-light">
          {t('otp.resendVerificationCode')}</button>
      </div>
    </form>
  </Form >

}