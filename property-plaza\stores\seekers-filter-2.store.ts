import { listingViewType, seekersListingFilterType } from "@/core/domain/listing/listing-seekers";
import { toggleStringArrrayItem } from "@/lib/utils";
import { create } from "zustand";

interface RangeFilter {
  min: number, max: number
}
interface Filter {
  typeProperty: string,
  setTypeProperty: (val:string) => void,
  subTypeProperty: string[],
  setSubTypeProperty: (val:string) => void,
  clearSubTypeProperty: () => void,
  priceRange: RangeFilter,
  setPriceRange: (min:number, max:number) => void,
  buildingSize: RangeFilter,
  setBuildingSize: (min:number, max:number) => void,
  landSize: RangeFilter,
  setLandSize: (min:number, max:number) => void,
  gardenSize: RangeFilter,
  setGardenSize: (min:number, max:number) => void ,
  bedRoom:string,
  setBedroom:(val:string) => void,
  bathRoom:string,
  setBathRoom:(val:string) => void,
  rentalIncluding: string[],
  setRentalIncluding: (val:string | string) => void,
  location: string[],
  setLocation: (val:string | string) => void,
  features: string[],
  setFeatures: (val:string | string) => void,
  propertyCondition: string[],
  setPropertyCondition: (val:string | string) => void,
  electricity: string,
  setElectricity: (val:string ) => void,
  typeLiving: string,
  setTypeLiving: (val:string) => void,
  parkingStatus:string,
  setParkingStatus: (val:string) => void,
  poolStatus:string,
  setPoolStatus: (val:string) => void,
  furnishedStatus:string,
  setFurnishedStatus: (val:string) => void,
  view: string[],
  setView: (val:string) => void,
  setViewToAnything: () => void
  minimumContract: string,
  setMinimumContract: (val:string) => void,
  yearsOfBuild: string,
  setYearsOfBuild: (val:string) => void,
  resetFilters: () => void
} 

export const useSeekerFilterStore = create<Filter>(set => ({
  typeProperty:seekersListingFilterType.anything,
  setTypeProperty:(typeProperty) => set(() => ({typeProperty})),

  subTypeProperty:[],
  setSubTypeProperty: (subType) => set(state => ({subTypeProperty: toggleStringArrrayItem(state.subTypeProperty,subType)})),
  clearSubTypeProperty: () => set(() => ({subTypeProperty:[]})),

  priceRange: {min:0, max:50_000_000},
  setPriceRange: (min,max) => set(() => ({priceRange: {min,max}})),

  buildingSize: {min:0, max:100_000},
  setBuildingSize: (min,max) => set(() => ({buildingSize: {min,max}})),

  landSize: {min:0, max:100_000},
  setLandSize: (min,max) => set(() => ({landSize: {min,max}})),

  gardenSize: {min:0, max:100_000},
  setGardenSize: (min,max) => set(() => ({gardenSize: {min,max}})),

  bathRoom: "any",
  setBathRoom: (bathRoom: string) => set(() => ({ bathRoom })),

  bedRoom: "any",
  setBedroom: (bedRoom: string) => set(() => ({ bedRoom })),

  rentalIncluding: [],
  setRentalIncluding: (val) => set((state) => ({
    rentalIncluding: toggleStringArrrayItem(state.rentalIncluding, val),
  })),

  location: [],
  setLocation: (val) => set(state => ({location: toggleStringArrrayItem(state.location,val)})),

  features: [],
  setFeatures: (val) => set(state => ({features: toggleStringArrrayItem(state.features,val)})),

  propertyCondition: [],
  setPropertyCondition:(val) => set(state => ({propertyCondition: toggleStringArrrayItem(state.propertyCondition,val)})),

  electricity: "",
  setElectricity: (electricity ) => set(() => ({electricity})),

  typeLiving: "ANY",
  setTypeLiving: (typeLiving) => set(() => ({typeLiving})),

  parkingStatus: "ANY",
  setParkingStatus: (parkingStatus) => set(() => ({parkingStatus})),

  furnishedStatus: "ANY",
  setFurnishedStatus: (furnishedStatus) => set(() => ({furnishedStatus})),
  
  poolStatus: "ANY",
  setPoolStatus: (poolStatus) => set(() => ({poolStatus})),

  view: [],
  setView: (val: string) => set((state) => {

    // to remove "all" value on selection since we want to specify the view type
    if(state.view.includes(listingViewType.all) && val !== listingViewType.all){
      const newList = toggleStringArrrayItem(state.view, listingViewType.all)
      const storedList = toggleStringArrrayItem(newList,val)
      return {view: storedList}
    }
    return { view: toggleStringArrrayItem(state.view, val) }
  }),
  setViewToAnything: () => set(() => ({view: [listingViewType.all]})),

  minimumContract: "ANY",
  setMinimumContract: (minimumContract) =>set(() => ({ minimumContract })),

  yearsOfBuild: "ANY",
  setYearsOfBuild: (yearsOfBuild) => set(() => ({ yearsOfBuild })),


  resetFilters: () =>
    set(() => ({
      typeProperty: seekersListingFilterType.anything,
      subTypeProperty: [],
      priceRange: { min: 0, max: 50_000_000 },
      buildingSize: { min: 0, max: 100_000 },
      landSize: { min: 0, max: 100_000 },
      gardenSize: { min: 0, max: 100_000 },
      bathRoom: "any",
      bedRoom: "any",
      rentalIncluding: [],
      location: [],
      features: [],
      propertyCondition: [],
      electricity: "",
      typeLiving: "ANY",
      parkingStatus: "ANY",
      furnishedStatus: "ANY",
      poolStatus: "ANY",
      view: [],
      minimumContract: "ANY",
      yearsOfBuild: "ANY",
    }))
}))
