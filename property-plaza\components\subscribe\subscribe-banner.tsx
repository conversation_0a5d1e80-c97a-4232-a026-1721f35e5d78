import { cn } from "@/lib/utils";
import { Alert, AlertDescription } from "../ui/alert";
import { But<PERSON> } from "../ui/button";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import { plansUrl, noLoginPlanUrl } from "@/lib/constanta/route";
import { useUserStore } from "@/stores/user.store";

export default function SubscribeBanner({ isSubscribe, className }: { isSubscribe?: Boolean, className?: string }) {
  const t = useTranslations("seeker")
  const { email } = useUserStore(state => state.seekers)
  const locale = useLocale()
  return <>
    <Alert className={cn("max-w-xs bg-[#B48B5599] font-semibold text-white  shadow-md absolute z-10", className)}>
      <AlertDescription className="text-xs">
        {t('misc.subscibePropgram.detailPage.description')} {" "}
        <Button asChild variant={"link"} size={"sm"} className="p-0 h-fit w-fit text-white underline">
          <Link href={email ? plansUrl : noLoginPlanUrl} hrefLang={locale}>{t('cta.subscribe')}</Link>
        </Button>
      </AlertDescription >
    </Alert >
  </>
}