"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_locale_auth_social-auth_tsx"],{

/***/ "(app-pages-browser)/./app/[locale]/(auth)/social-auth.tsx":
/*!*********************************************!*\
  !*** ./app/[locale]/(auth)/social-auth.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SocialAuthFormatter; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_constanta_constant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/constanta/constant */ \"(app-pages-browser)/./lib/constanta/constant.ts\");\n/* harmony import */ var _hooks_use_search_param_wrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-search-param-wrapper */ \"(app-pages-browser)/./hooks/use-search-param-wrapper.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Function to get auth redirect data\nfunction SocialAuthFormatter(param) {\n    let { accessToken, status, expired } = param;\n    _s();\n    const { removeQueryParam } = (0,_hooks_use_search_param_wrapper__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!accessToken || status !== \"200\") return;\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(_lib_constanta_constant__WEBPACK_IMPORTED_MODULE_3__.ACCESS_TOKEN, accessToken, {\n            expires: +(expired || 1440) / 1440\n        });\n        removeQueryParam([\n            \"request\",\n            \"at\",\n            \"status_code\",\n            \"provider\",\n            \"ma\",\n            \"message\"\n        ], true);\n    }, [\n        accessToken,\n        status,\n        removeQueryParam,\n        expired\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n}\n_s(SocialAuthFormatter, \"XsEkQY/LH+wT0o3UNCJpqEaSvf0=\", false, function() {\n    return [\n        _hooks_use_search_param_wrapper__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = SocialAuthFormatter;\nvar _c;\n$RefreshReg$(_c, \"SocialAuthFormatter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/(auth)/social-auth.tsx\n"));

/***/ })

}]);