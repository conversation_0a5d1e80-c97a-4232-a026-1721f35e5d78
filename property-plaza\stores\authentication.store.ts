import { PROPERTY_SEEKERS } from "@/lib/constanta/constant";
import { UserType } from "@/types/user";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

interface AuthenticationState {
  typeUser: UserType,
  setTypeLogin: (type: UserType) => void
}

export const useAuthenticationStore = create<AuthenticationState>()(
  persist(
    set => ({
      typeUser: PROPERTY_SEEKERS,
      setTypeLogin: typeUser => set(({typeUser}))
    }), {
      name: "authentication",
      storage: createJSONStorage(() => sessionStorage)
    }
  )
)