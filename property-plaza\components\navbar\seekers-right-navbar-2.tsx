"use client"
import * as m from "framer-motion/m"
import { useEffect, useRef, useState } from "react"

import Cookies from "js-cookie"
import { ACCESS_TOKEN } from "@/lib/constanta/constant"
import { useUserStore } from "@/stores/user.store"

// import SeekersProfile from "./seekers-profile"
import { cn } from "@/lib/utils"
import dynamic from "next/dynamic"
import { LazyMotion, domAnimation } from "framer-motion"

const SeekerAuthDialog = dynamic(() => import("@/app/[locale]/(user)/(auth)/seekers-auth-dialog"), { ssr: false })
const SeekersProfile = dynamic(() => import("./seekers-profile"), { ssr: false })
const ProfileDropdown = dynamic(() => import("./seeker-profile-dropdown"), { ssr: false })
const CurrencyForm = dynamic(() => import("../locale/currency-form"), { ssr: false })
const SeekersLocaleForm = dynamic(() => import("../locale/seekers-locale-form"), { ssr: false })

export default function SeekersRightNavbar({ localeId = "EN", currency_ = "EUR" }: { localeId?: string, currency_?: string }) {
  const contentRef = useRef<HTMLDivElement | null>(null)
  const languageRef = useRef<HTMLButtonElement | null>(null)
  const currencyRef = useRef<HTMLButtonElement | null>(null)
  const role = useUserStore(state => state.role)
  const [showOption, setShowOption] = useState(false)
  const [scrollY, setScrollY] = useState(0)
  const [activeForm, setActiveForm] = useState<'currency' | 'language' | null>(null)

  useEffect(() => {
    const clickOutsideElement = (event: MouseEvent) => {
      const target = event.target as Node

      // Cek apakah klik terjadi di dalam form yang aktif
      const isCurrencyClick = currencyRef.current?.contains(target)
      const isLanguageClick = languageRef.current?.contains(target)

      if (isCurrencyClick) {
        setActiveForm('currency')
        setShowOption(true)
        return
      }

      if (isLanguageClick) {
        setActiveForm('language')
        setShowOption(true)
        return
      }

      // Jika klik di luar kedua form
      if (!contentRef.current?.contains(target)) {
        setShowOption(false)
        setActiveForm(null)
      }
    }

    window.addEventListener("mousedown", clickOutsideElement)
    return () => {
      window.removeEventListener("mousedown", clickOutsideElement)
    }
  }, [])
  useEffect(() => {
    const handleScroll = () => {
      if (scrollY === window.scrollY - 4) return
      setScrollY(window.scrollY)
      setShowOption(false)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [scrollY, setShowOption])
  return <LazyMotion features={domAnimation}>
    <div ref={contentRef}
      className="flex gap-2 items-center w-[166px] justify-between overflow-hidden h-[52px]"
    >
      <div className="w-fit">
        <m.div
          className="overflow-hidden shadow-md rounded-full border border-seekers-text-lighter flex"
          initial={{ width: "110px" }}
          animate={{ width: showOption ? "166px" : "112px" }}
          transition={{ duration: 0.1 }}
        >
          <div className="flex items-center justify-center gap-2 py-2 w-full"
          >
            <CurrencyForm
              triggerClassName={cn("rounded-full border-none shadow-none !justify-between flex-grow !min-w-8 py-0 pr-0 !h-5 focus:ring-0", showOption ? "w-full" : "pl-3 max-w-[48px]")}
              defaultCurrency={currency_}
              ref={currencyRef}
              onClick={(e) => {
                e.stopPropagation()
                setActiveForm('currency')
                setShowOption(true)
              }}
              showCaret={showOption && activeForm === 'currency'}
            />
            <div className="w-[2px] h-[24px] bg-seekers-text-lighter"></div>
            <SeekersLocaleForm
              triggerClassName={cn(
                "rounded-full flex-grow !justify-between !min-w-8 border-none shadow-none py-0 pl-0 !h-5 focus:ring-0",
                showOption ? "w-full" : "pl-2 max-w-[32px]"
              )}
              defaultValue={localeId}
              ref={languageRef}
              onClick={(e) => {
                e.stopPropagation()
                setActiveForm('language')
                setShowOption(true)
              }}
              showCaret={showOption && activeForm === 'language'}
            />
          </div>
        </m.div>
      </div>
      <>
        {(Cookies.get(ACCESS_TOKEN) && role == "SEEKER") ? (
          <ProfileDropdown
            trigger={
              <button
                className={`border relative border-seekers-text-lighter shadow-md rounded-full overflow-hidden !h-10 !w-10`}
              >
                <SeekersProfile url={""} />
              </button>
            }
          />
        ) : (
          <div
          >
            <SeekerAuthDialog triggerClassName={cn("!w-10 rounded-full overflow-hidden")} />
          </div>
        )}
      </>
    </div>
  </LazyMotion>
}
