"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_components_pop-up_email-discount-pop-up_tsx",{

/***/ "(app-pages-browser)/./components/pop-up/email-discount-pop-up/email-input-discount.form.tsx":
/*!*******************************************************************************!*\
  !*** ./components/pop-up/email-discount-pop-up/email-input-discount.form.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EmailInputDiscountForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_locale_create_password_form_use_email_form_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/[locale]/create-password/form/use-email-form.schema */ \"(app-pages-browser)/./app/[locale]/create-password/form/use-email-form.schema.ts\");\n/* harmony import */ var _components_input_form_default_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/input-form/default-input */ \"(app-pages-browser)/./components/input-form/default-input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _core_applications_mutations_waiting_list_use_join_waiting_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/core/applications/mutations/waiting-list/use-join-waiting-list */ \"(app-pages-browser)/./core/applications/mutations/waiting-list/use-join-waiting-list.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction EmailInputDiscountForm(param) {\n    let { setIsSubmitted } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"seeker\");\n    const formSchema = (0,_app_locale_create_password_form_use_email_form_schema__WEBPACK_IMPORTED_MODULE_1__.useEmailFormSchema)();\n    const useWaitingJoinMutation = (0,_core_applications_mutations_waiting_list_use_join_waiting_list__WEBPACK_IMPORTED_MODULE_5__.useJoinWaitingList)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_7__.zodResolver)(formSchema),\n        defaultValues: {\n            email: \"\"\n        }\n    });\n    const onSubmit = async (values)=>{\n        const data = {\n            email: values.email,\n            name: \"discount-popup-lead\"\n        };\n        try {\n            await useWaitingJoinMutation.mutateAsync(data);\n            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.gtagEvent)({\n                action: \"lead_magnet_form_submit\",\n                category: \"Lead Magnet\",\n                label: \"Email Capture for Discount Code\",\n                value: \"1\"\n            });\n        } catch (e) {\n        // Error handling is done in the mutation hook\n        } finally{\n            setIsSubmitted(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.Form, {\n        ...form,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: form.handleSubmit(onSubmit),\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_input_form_default_input__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    type: \"email\",\n                    form: form,\n                    name: \"email\",\n                    variant: \"float\",\n                    label: t(\"form.label.email\"),\n                    labelClassName: \"text-sm text-seekers-text-light font-medium\",\n                    placeholder: \"<EMAIL>\",\n                    className: \"h-12 text-base\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up\\\\email-input-discount.form.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    type: \"submit\",\n                    className: \"w-full h-12 text-base font-semibold\",\n                    loading: useWaitingJoinMutation.isPending,\n                    children: t(\"cta.getDiscount\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up\\\\email-input-discount.form.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up\\\\email-input-discount.form.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up\\\\email-input-discount.form.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(EmailInputDiscountForm, \"2o8wMn1xvGb3oiGseIJsTRDmsLE=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        _app_locale_create_password_form_use_email_form_schema__WEBPACK_IMPORTED_MODULE_1__.useEmailFormSchema,\n        _core_applications_mutations_waiting_list_use_join_waiting_list__WEBPACK_IMPORTED_MODULE_5__.useJoinWaitingList,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm\n    ];\n});\n_c = EmailInputDiscountForm;\nvar _c;\n$RefreshReg$(_c, \"EmailInputDiscountForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pop-up/email-discount-pop-up/email-input-discount.form.tsx\n"));

/***/ })

});