"use client"
import { useEffect, useState } from "react"
import { useFaq } from "./use-faq"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { useTranslations } from "next-intl"
import { useFaQStore } from "@/stores/faq.store"
import useSearchParamWrapper from "@/hooks/use-search-param-wrapper"
import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper"
import { Button } from "@/components/ui/button"
import { Menu, Search } from "lucide-react"
import { SidebarContent } from "./sidebar"
import ExtraAnswerContent from "./extra-answer-content"

export default function Detail() {
  const t = useTranslations("seeker")
  const { detail } = useFaQStore(state => state)
  const { searchParams } = useSearchParamWrapper()
  const { filteredData, filteringData } = useFaq()
  const {
    contactingPropertyOwner,
    paymentAndFee,
    propertyVerificationAndSafety,
    propertyVisit,
    usingPlatform,
    data: allFaq
  } = useFaq()
  const [data, setData] = useState<{ question: string; answer: string; extraContent?: string }[]>([])
  const [title, setTitle] = useState("")
  const [open, setOpen] = useState(false)

  useEffect(() => {
    switch (detail) {
      case "using-platform":
        setTitle(t('faq.group.usingThePlatform'))
        return setData(usingPlatform)
      case "contacting-owner":
        setTitle(t('faq.group.contactingPropertyOwner'))
        return setData(contactingPropertyOwner)
      case "property-verification":
        setTitle(t('faq.group.propertyVerificationAndSafety'))
        return setData(propertyVerificationAndSafety)
      case "payment-and-fee":
        setTitle(t('faq.group.paymentAndFee'))
        return setData(paymentAndFee)
      case "property-visit":
        setTitle(t('faq.group.propertyVisitAndContract'))
        return setData(propertyVisit)
      default:
        setTitle("")
        return setData(allFaq)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [detail])
  useEffect(() => {
    filteringData(searchParams.get("search") || "")
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams.get("search")])
  return <div className="w-full space-y-4">
    <div className="lg:hidden flex gap-1 items-center">
      <DialogWrapper
        open={open}
        setOpen={setOpen}
        openTrigger={<Button size={"icon"} className="rounded-full text-primary justify-start" variant={"ghost"}>
          <Menu className="!w-5 !h-5 text-primary " strokeWidth={2} />
        </Button>}
      >
        <SidebarContent />
      </DialogWrapper>

      <h2 className="text-lg font-semibold">{title}</h2>
    </div>
    {!searchParams.get("search") ?
      <Accordion type="single" collapsible >
        {data.map((item, idx) =>
          <AccordionItem key={idx} value={idx.toString()} >
            <AccordionTrigger className="text-start leading-normal">{item.question}</AccordionTrigger>
            <AccordionContent className="max-w-[800px] leading-normal">
              {item.answer} <ExtraAnswerContent extraContentId={item.extraContent} />
            </AccordionContent>
          </AccordionItem>
        )}
      </Accordion>
      :
      filteredData.length > 0 ?
        <Accordion type="single" collapsible>
          {filteredData.map((item, idx) =>
            <AccordionItem key={idx} value={idx.toString()}>
              <AccordionTrigger className="text-start leading-normal">{item.question}</AccordionTrigger>
              <AccordionContent className="max-w-[800px] leading-normal">
                {item.answer}
              </AccordionContent>
            </AccordionItem>
          )
          }
        </Accordion>
        :
        <p className="text-center py-8 w-full">{t('misc.faqNotFound')}</p>

    }
  </div>
}