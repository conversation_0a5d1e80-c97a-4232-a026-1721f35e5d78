import { apiClient } from "@/core/client";

import {
  CreatePasswordDto,
  LoginDto,
  OtpVerificationDto,
  RequestForgetPasswordDto,
  ResetPasswordDto,
  SendEmailVerificationDto,
  SendWhatsappVerificationDto,
  TotpVerificationCode,
  TwoFactorAuthenticationCodeDto,
  VerifyResetPasswordDto,
} from "./dto";
import { AxiosRequestConfig } from "axios";

export const login = (data: LoginDto, captchaToken?: string) =>
  apiClient.post("auth/login", data, {
    headers: {
      "g-token": captchaToken || "",
    },
  });

export const logout = () => apiClient.post("auth/logout");

export const sendEmailverification = (data: SendEmailVerificationDto) =>
  apiClient.post("notifications/email", data);

export const sendWhatsappVerification = (data: SendWhatsappVerificationDto) =>
  apiClient.post("notifications/whatsapp", data);

export const otpVerification = (data: OtpVerificationDto) =>
  apiClient.post("auth/otp-verification", data);

export const requestForgetPassword = (data: RequestForgetPasswordDto) =>
  apiClient.post("auth/forgot-password", data);

export const verifyResetPassword = (data: VerifyResetPasswordDto) =>
  apiClient.get(
    `auth/verify-reset-password?email=${data.email}&token=${data.token}`
  );

export const postResetPassword = (data: ResetPasswordDto) =>
  apiClient.post("auth/reset-password", data);

export const postCreatePassword = (
  data: CreatePasswordDto,
  option?: AxiosRequestConfig<CreatePasswordDto>
) => apiClient.post("auth/create-password", data, option);

export const postTwoFactorAuthenticationCode = (
  data: TwoFactorAuthenticationCodeDto
) => apiClient.post("users/security", data); // to get 2FA code

export const postTotpVerificationCode = (data: TotpVerificationCode) =>
  apiClient.post("auth/totp-verification", data);

export const postGoogleAuth = () => apiClient.get("auth/google");

export const postFacebookAuth = () => apiClient.get("auth/facebook");
