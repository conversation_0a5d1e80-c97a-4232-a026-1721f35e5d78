import { postTopUpCredit } from "@/core/infrastructures/transaction/api";
import { CreateTopUpCreditDto } from "@/core/infrastructures/transaction/dto";
import { useMutation } from "@tanstack/react-query";

export function useTopUpCredit(){
  const mutation = useMutation({
    mutationFn:(data:CreateTopUpCreditDto) => postTopUpCredit(data),
    onSuccess: (response) => {
      // window.location.href = response.data.data.url
      window.open(response.data.data.url)
      return response
    }
  })

  return mutation
}