/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_sanity_eventsource_browser_js"],{

/***/ "(app-pages-browser)/./node_modules/event-source-polyfill/src/eventsource.js":
/*!***************************************************************!*\
  !*** ./node_modules/event-source-polyfill/src/eventsource.js ***!
  \***************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/** @license\r\n * eventsource.js\r\n * Available under MIT License (MIT)\r\n * https://github.com/Yaffle/EventSource/\r\n */\r\n\r\n/*jslint indent: 2, vars: true, plusplus: true */\r\n/*global setTimeout, clearTimeout */\r\n\r\n(function (global) {\r\n  \"use strict\";\r\n\r\n  var setTimeout = global.setTimeout;\r\n  var clearTimeout = global.clearTimeout;\r\n  var XMLHttpRequest = global.XMLHttpRequest;\r\n  var XDomainRequest = global.XDomainRequest;\r\n  var ActiveXObject = global.ActiveXObject;\r\n  var NativeEventSource = global.EventSource;\r\n\r\n  var document = global.document;\r\n  var Promise = global.Promise;\r\n  var fetch = global.fetch;\r\n  var Response = global.Response;\r\n  var TextDecoder = global.TextDecoder;\r\n  var TextEncoder = global.TextEncoder;\r\n  var AbortController = global.AbortController;\r\n\r\n  if (typeof window !== \"undefined\" && typeof document !== \"undefined\" && !(\"readyState\" in document) && document.body == null) { // Firefox 2\r\n    document.readyState = \"loading\";\r\n    window.addEventListener(\"load\", function (event) {\r\n      document.readyState = \"complete\";\r\n    }, false);\r\n  }\r\n\r\n  if (XMLHttpRequest == null && ActiveXObject != null) { // https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Using_XMLHttpRequest_in_IE6\r\n    XMLHttpRequest = function () {\r\n      return new ActiveXObject(\"Microsoft.XMLHTTP\");\r\n    };\r\n  }\r\n\r\n  if (Object.create == undefined) {\r\n    Object.create = function (C) {\r\n      function F(){}\r\n      F.prototype = C;\r\n      return new F();\r\n    };\r\n  }\r\n\r\n  if (!Date.now) {\r\n    Date.now = function now() {\r\n      return new Date().getTime();\r\n    };\r\n  }\r\n\r\n  // see #118 (Promise#finally with polyfilled Promise)\r\n  // see #123 (data URLs crash Edge)\r\n  // see #125 (CSP violations)\r\n  // see pull/#138\r\n  // => No way to polyfill Promise#finally\r\n\r\n  if (AbortController == undefined) {\r\n    var originalFetch2 = fetch;\r\n    fetch = function (url, options) {\r\n      var signal = options.signal;\r\n      return originalFetch2(url, {headers: options.headers, credentials: options.credentials, cache: options.cache}).then(function (response) {\r\n        var reader = response.body.getReader();\r\n        signal._reader = reader;\r\n        if (signal._aborted) {\r\n          signal._reader.cancel();\r\n        }\r\n        return {\r\n          status: response.status,\r\n          statusText: response.statusText,\r\n          headers: response.headers,\r\n          body: {\r\n            getReader: function () {\r\n              return reader;\r\n            }\r\n          }\r\n        };\r\n      });\r\n    };\r\n    AbortController = function () {\r\n      this.signal = {\r\n        _reader: null,\r\n        _aborted: false\r\n      };\r\n      this.abort = function () {\r\n        if (this.signal._reader != null) {\r\n          this.signal._reader.cancel();\r\n        }\r\n        this.signal._aborted = true;\r\n      };\r\n    };\r\n  }\r\n\r\n  function TextDecoderPolyfill() {\r\n    this.bitsNeeded = 0;\r\n    this.codePoint = 0;\r\n  }\r\n\r\n  TextDecoderPolyfill.prototype.decode = function (octets) {\r\n    function valid(codePoint, shift, octetsCount) {\r\n      if (octetsCount === 1) {\r\n        return codePoint >= 0x0080 >> shift && codePoint << shift <= 0x07FF;\r\n      }\r\n      if (octetsCount === 2) {\r\n        return codePoint >= 0x0800 >> shift && codePoint << shift <= 0xD7FF || codePoint >= 0xE000 >> shift && codePoint << shift <= 0xFFFF;\r\n      }\r\n      if (octetsCount === 3) {\r\n        return codePoint >= 0x010000 >> shift && codePoint << shift <= 0x10FFFF;\r\n      }\r\n      throw new Error();\r\n    }\r\n    function octetsCount(bitsNeeded, codePoint) {\r\n      if (bitsNeeded === 6 * 1) {\r\n        return codePoint >> 6 > 15 ? 3 : codePoint > 31 ? 2 : 1;\r\n      }\r\n      if (bitsNeeded === 6 * 2) {\r\n        return codePoint > 15 ? 3 : 2;\r\n      }\r\n      if (bitsNeeded === 6 * 3) {\r\n        return 3;\r\n      }\r\n      throw new Error();\r\n    }\r\n    var REPLACER = 0xFFFD;\r\n    var string = \"\";\r\n    var bitsNeeded = this.bitsNeeded;\r\n    var codePoint = this.codePoint;\r\n    for (var i = 0; i < octets.length; i += 1) {\r\n      var octet = octets[i];\r\n      if (bitsNeeded !== 0) {\r\n        if (octet < 128 || octet > 191 || !valid(codePoint << 6 | octet & 63, bitsNeeded - 6, octetsCount(bitsNeeded, codePoint))) {\r\n          bitsNeeded = 0;\r\n          codePoint = REPLACER;\r\n          string += String.fromCharCode(codePoint);\r\n        }\r\n      }\r\n      if (bitsNeeded === 0) {\r\n        if (octet >= 0 && octet <= 127) {\r\n          bitsNeeded = 0;\r\n          codePoint = octet;\r\n        } else if (octet >= 192 && octet <= 223) {\r\n          bitsNeeded = 6 * 1;\r\n          codePoint = octet & 31;\r\n        } else if (octet >= 224 && octet <= 239) {\r\n          bitsNeeded = 6 * 2;\r\n          codePoint = octet & 15;\r\n        } else if (octet >= 240 && octet <= 247) {\r\n          bitsNeeded = 6 * 3;\r\n          codePoint = octet & 7;\r\n        } else {\r\n          bitsNeeded = 0;\r\n          codePoint = REPLACER;\r\n        }\r\n        if (bitsNeeded !== 0 && !valid(codePoint, bitsNeeded, octetsCount(bitsNeeded, codePoint))) {\r\n          bitsNeeded = 0;\r\n          codePoint = REPLACER;\r\n        }\r\n      } else {\r\n        bitsNeeded -= 6;\r\n        codePoint = codePoint << 6 | octet & 63;\r\n      }\r\n      if (bitsNeeded === 0) {\r\n        if (codePoint <= 0xFFFF) {\r\n          string += String.fromCharCode(codePoint);\r\n        } else {\r\n          string += String.fromCharCode(0xD800 + (codePoint - 0xFFFF - 1 >> 10));\r\n          string += String.fromCharCode(0xDC00 + (codePoint - 0xFFFF - 1 & 0x3FF));\r\n        }\r\n      }\r\n    }\r\n    this.bitsNeeded = bitsNeeded;\r\n    this.codePoint = codePoint;\r\n    return string;\r\n  };\r\n\r\n  // Firefox < 38 throws an error with stream option\r\n  var supportsStreamOption = function () {\r\n    try {\r\n      return new TextDecoder().decode(new TextEncoder().encode(\"test\"), {stream: true}) === \"test\";\r\n    } catch (error) {\r\n      console.debug(\"TextDecoder does not support streaming option. Using polyfill instead: \" + error);\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // IE, Edge\r\n  if (TextDecoder == undefined || TextEncoder == undefined || !supportsStreamOption()) {\r\n    TextDecoder = TextDecoderPolyfill;\r\n  }\r\n\r\n  var k = function () {\r\n  };\r\n\r\n  function XHRWrapper(xhr) {\r\n    this.withCredentials = false;\r\n    this.readyState = 0;\r\n    this.status = 0;\r\n    this.statusText = \"\";\r\n    this.responseText = \"\";\r\n    this.onprogress = k;\r\n    this.onload = k;\r\n    this.onerror = k;\r\n    this.onreadystatechange = k;\r\n    this._contentType = \"\";\r\n    this._xhr = xhr;\r\n    this._sendTimeout = 0;\r\n    this._abort = k;\r\n  }\r\n\r\n  XHRWrapper.prototype.open = function (method, url) {\r\n    this._abort(true);\r\n\r\n    var that = this;\r\n    var xhr = this._xhr;\r\n    var state = 1;\r\n    var timeout = 0;\r\n\r\n    this._abort = function (silent) {\r\n      if (that._sendTimeout !== 0) {\r\n        clearTimeout(that._sendTimeout);\r\n        that._sendTimeout = 0;\r\n      }\r\n      if (state === 1 || state === 2 || state === 3) {\r\n        state = 4;\r\n        xhr.onload = k;\r\n        xhr.onerror = k;\r\n        xhr.onabort = k;\r\n        xhr.onprogress = k;\r\n        xhr.onreadystatechange = k;\r\n        // IE 8 - 9: XDomainRequest#abort() does not fire any event\r\n        // Opera < 10: XMLHttpRequest#abort() does not fire any event\r\n        xhr.abort();\r\n        if (timeout !== 0) {\r\n          clearTimeout(timeout);\r\n          timeout = 0;\r\n        }\r\n        if (!silent) {\r\n          that.readyState = 4;\r\n          that.onabort(null);\r\n          that.onreadystatechange();\r\n        }\r\n      }\r\n      state = 0;\r\n    };\r\n\r\n    var onStart = function () {\r\n      if (state === 1) {\r\n        //state = 2;\r\n        var status = 0;\r\n        var statusText = \"\";\r\n        var contentType = undefined;\r\n        if (!(\"contentType\" in xhr)) {\r\n          try {\r\n            status = xhr.status;\r\n            statusText = xhr.statusText;\r\n            contentType = xhr.getResponseHeader(\"Content-Type\");\r\n          } catch (error) {\r\n            // IE < 10 throws exception for `xhr.status` when xhr.readyState === 2 || xhr.readyState === 3\r\n            // Opera < 11 throws exception for `xhr.status` when xhr.readyState === 2\r\n            // https://bugs.webkit.org/show_bug.cgi?id=29121\r\n            status = 0;\r\n            statusText = \"\";\r\n            contentType = undefined;\r\n            // Firefox < 14, Chrome ?, Safari ?\r\n            // https://bugs.webkit.org/show_bug.cgi?id=29658\r\n            // https://bugs.webkit.org/show_bug.cgi?id=77854\r\n          }\r\n        } else {\r\n          status = 200;\r\n          statusText = \"OK\";\r\n          contentType = xhr.contentType;\r\n        }\r\n        if (status !== 0) {\r\n          state = 2;\r\n          that.readyState = 2;\r\n          that.status = status;\r\n          that.statusText = statusText;\r\n          that._contentType = contentType;\r\n          that.onreadystatechange();\r\n        }\r\n      }\r\n    };\r\n    var onProgress = function () {\r\n      onStart();\r\n      if (state === 2 || state === 3) {\r\n        state = 3;\r\n        var responseText = \"\";\r\n        try {\r\n          responseText = xhr.responseText;\r\n        } catch (error) {\r\n          // IE 8 - 9 with XMLHttpRequest\r\n        }\r\n        that.readyState = 3;\r\n        that.responseText = responseText;\r\n        that.onprogress();\r\n      }\r\n    };\r\n    var onFinish = function (type, event) {\r\n      if (event == null || event.preventDefault == null) {\r\n        event = {\r\n          preventDefault: k\r\n        };\r\n      }\r\n      // Firefox 52 fires \"readystatechange\" (xhr.readyState === 4) without final \"readystatechange\" (xhr.readyState === 3)\r\n      // IE 8 fires \"onload\" without \"onprogress\"\r\n      onProgress();\r\n      if (state === 1 || state === 2 || state === 3) {\r\n        state = 4;\r\n        if (timeout !== 0) {\r\n          clearTimeout(timeout);\r\n          timeout = 0;\r\n        }\r\n        that.readyState = 4;\r\n        if (type === \"load\") {\r\n          that.onload(event);\r\n        } else if (type === \"error\") {\r\n          that.onerror(event);\r\n        } else if (type === \"abort\") {\r\n          that.onabort(event);\r\n        } else {\r\n          throw new TypeError();\r\n        }\r\n        that.onreadystatechange();\r\n      }\r\n    };\r\n    var onReadyStateChange = function (event) {\r\n      if (xhr != undefined) { // Opera 12\r\n        if (xhr.readyState === 4) {\r\n          if (!(\"onload\" in xhr) || !(\"onerror\" in xhr) || !(\"onabort\" in xhr)) {\r\n            onFinish(xhr.responseText === \"\" ? \"error\" : \"load\", event);\r\n          }\r\n        } else if (xhr.readyState === 3) {\r\n          if (!(\"onprogress\" in xhr)) { // testing XMLHttpRequest#responseText too many times is too slow in IE 11\r\n            // and in Firefox 3.6\r\n            onProgress();\r\n          }\r\n        } else if (xhr.readyState === 2) {\r\n          onStart();\r\n        }\r\n      }\r\n    };\r\n    var onTimeout = function () {\r\n      timeout = setTimeout(function () {\r\n        onTimeout();\r\n      }, 500);\r\n      if (xhr.readyState === 3) {\r\n        onProgress();\r\n      }\r\n    };\r\n\r\n    // XDomainRequest#abort removes onprogress, onerror, onload\r\n    if (\"onload\" in xhr) {\r\n      xhr.onload = function (event) {\r\n        onFinish(\"load\", event);\r\n      };\r\n    }\r\n    if (\"onerror\" in xhr) {\r\n      xhr.onerror = function (event) {\r\n        onFinish(\"error\", event);\r\n      };\r\n    }\r\n    // improper fix to match Firefox behaviour, but it is better than just ignore abort\r\n    // see https://bugzilla.mozilla.org/show_bug.cgi?id=768596\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=880200\r\n    // https://code.google.com/p/chromium/issues/detail?id=153570\r\n    // IE 8 fires \"onload\" without \"onprogress\r\n    if (\"onabort\" in xhr) {\r\n      xhr.onabort = function (event) {\r\n        onFinish(\"abort\", event);\r\n      };\r\n    }\r\n\r\n    if (\"onprogress\" in xhr) {\r\n      xhr.onprogress = onProgress;\r\n    }\r\n\r\n    // IE 8 - 9 (XMLHTTPRequest)\r\n    // Opera < 12\r\n    // Firefox < 3.5\r\n    // Firefox 3.5 - 3.6 - ? < 9.0\r\n    // onprogress is not fired sometimes or delayed\r\n    // see also #64 (significant lag in IE 11)\r\n    if (\"onreadystatechange\" in xhr) {\r\n      xhr.onreadystatechange = function (event) {\r\n        onReadyStateChange(event);\r\n      };\r\n    }\r\n\r\n    if (\"contentType\" in xhr || !(\"ontimeout\" in XMLHttpRequest.prototype)) {\r\n      url += (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + \"padding=true\";\r\n    }\r\n    xhr.open(method, url, true);\r\n\r\n    if (\"readyState\" in xhr) {\r\n      // workaround for Opera 12 issue with \"progress\" events\r\n      // #91 (XMLHttpRequest onprogress not fired for streaming response in Edge 14-15-?)\r\n      timeout = setTimeout(function () {\r\n        onTimeout();\r\n      }, 0);\r\n    }\r\n  };\r\n  XHRWrapper.prototype.abort = function () {\r\n    this._abort(false);\r\n  };\r\n  XHRWrapper.prototype.getResponseHeader = function (name) {\r\n    return this._contentType;\r\n  };\r\n  XHRWrapper.prototype.setRequestHeader = function (name, value) {\r\n    var xhr = this._xhr;\r\n    if (\"setRequestHeader\" in xhr) {\r\n      xhr.setRequestHeader(name, value);\r\n    }\r\n  };\r\n  XHRWrapper.prototype.getAllResponseHeaders = function () {\r\n    // XMLHttpRequest#getAllResponseHeaders returns null for CORS requests in Firefox 3.6.28\r\n    return this._xhr.getAllResponseHeaders != undefined ? this._xhr.getAllResponseHeaders() || \"\" : \"\";\r\n  };\r\n  XHRWrapper.prototype.send = function () {\r\n    // loading indicator in Safari < ? (6), Chrome < 14, Firefox\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=736723\r\n    if ((!(\"ontimeout\" in XMLHttpRequest.prototype) || (!(\"sendAsBinary\" in XMLHttpRequest.prototype) && !(\"mozAnon\" in XMLHttpRequest.prototype))) &&\r\n        document != undefined &&\r\n        document.readyState != undefined &&\r\n        document.readyState !== \"complete\") {\r\n      var that = this;\r\n      that._sendTimeout = setTimeout(function () {\r\n        that._sendTimeout = 0;\r\n        that.send();\r\n      }, 4);\r\n      return;\r\n    }\r\n\r\n    var xhr = this._xhr;\r\n    // withCredentials should be set after \"open\" for Safari and Chrome (< 19 ?)\r\n    if (\"withCredentials\" in xhr) {\r\n      xhr.withCredentials = this.withCredentials;\r\n    }\r\n    try {\r\n      // xhr.send(); throws \"Not enough arguments\" in Firefox 3.0\r\n      xhr.send(undefined);\r\n    } catch (error1) {\r\n      // Safari 5.1.7, Opera 12\r\n      throw error1;\r\n    }\r\n  };\r\n\r\n  function toLowerCase(name) {\r\n    return name.replace(/[A-Z]/g, function (c) {\r\n      return String.fromCharCode(c.charCodeAt(0) + 0x20);\r\n    });\r\n  }\r\n\r\n  function HeadersPolyfill(all) {\r\n    // Get headers: implemented according to mozilla's example code: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/getAllResponseHeaders#Example\r\n    var map = Object.create(null);\r\n    var array = all.split(\"\\r\\n\");\r\n    for (var i = 0; i < array.length; i += 1) {\r\n      var line = array[i];\r\n      var parts = line.split(\": \");\r\n      var name = parts.shift();\r\n      var value = parts.join(\": \");\r\n      map[toLowerCase(name)] = value;\r\n    }\r\n    this._map = map;\r\n  }\r\n  HeadersPolyfill.prototype.get = function (name) {\r\n    return this._map[toLowerCase(name)];\r\n  };\r\n\r\n  if (XMLHttpRequest != null && XMLHttpRequest.HEADERS_RECEIVED == null) { // IE < 9, Firefox 3.6\r\n    XMLHttpRequest.HEADERS_RECEIVED = 2;\r\n  }\r\n\r\n  function XHRTransport() {\r\n  }\r\n\r\n  XHRTransport.prototype.open = function (xhr, onStartCallback, onProgressCallback, onFinishCallback, url, withCredentials, headers) {\r\n    xhr.open(\"GET\", url);\r\n    var offset = 0;\r\n    xhr.onprogress = function () {\r\n      var responseText = xhr.responseText;\r\n      var chunk = responseText.slice(offset);\r\n      offset += chunk.length;\r\n      onProgressCallback(chunk);\r\n    };\r\n    xhr.onerror = function (event) {\r\n      event.preventDefault();\r\n      onFinishCallback(new Error(\"NetworkError\"));\r\n    };\r\n    xhr.onload = function () {\r\n      onFinishCallback(null);\r\n    };\r\n    xhr.onabort = function () {\r\n      onFinishCallback(null);\r\n    };\r\n    xhr.onreadystatechange = function () {\r\n      if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {\r\n        var status = xhr.status;\r\n        var statusText = xhr.statusText;\r\n        var contentType = xhr.getResponseHeader(\"Content-Type\");\r\n        var headers = xhr.getAllResponseHeaders();\r\n        onStartCallback(status, statusText, contentType, new HeadersPolyfill(headers));\r\n      }\r\n    };\r\n    xhr.withCredentials = withCredentials;\r\n    for (var name in headers) {\r\n      if (Object.prototype.hasOwnProperty.call(headers, name)) {\r\n        xhr.setRequestHeader(name, headers[name]);\r\n      }\r\n    }\r\n    xhr.send();\r\n    return xhr;\r\n  };\r\n\r\n  function HeadersWrapper(headers) {\r\n    this._headers = headers;\r\n  }\r\n  HeadersWrapper.prototype.get = function (name) {\r\n    return this._headers.get(name);\r\n  };\r\n\r\n  function FetchTransport() {\r\n  }\r\n\r\n  FetchTransport.prototype.open = function (xhr, onStartCallback, onProgressCallback, onFinishCallback, url, withCredentials, headers) {\r\n    var reader = null;\r\n    var controller = new AbortController();\r\n    var signal = controller.signal;\r\n    var textDecoder = new TextDecoder();\r\n    fetch(url, {\r\n      headers: headers,\r\n      credentials: withCredentials ? \"include\" : \"same-origin\",\r\n      signal: signal,\r\n      cache: \"no-store\"\r\n    }).then(function (response) {\r\n      reader = response.body.getReader();\r\n      onStartCallback(response.status, response.statusText, response.headers.get(\"Content-Type\"), new HeadersWrapper(response.headers));\r\n      // see https://github.com/promises-aplus/promises-spec/issues/179\r\n      return new Promise(function (resolve, reject) {\r\n        var readNextChunk = function () {\r\n          reader.read().then(function (result) {\r\n            if (result.done) {\r\n              //Note: bytes in textDecoder are ignored\r\n              resolve(undefined);\r\n            } else {\r\n              var chunk = textDecoder.decode(result.value, {stream: true});\r\n              onProgressCallback(chunk);\r\n              readNextChunk();\r\n            }\r\n          })[\"catch\"](function (error) {\r\n            reject(error);\r\n          });\r\n        };\r\n        readNextChunk();\r\n      });\r\n    })[\"catch\"](function (error) {\r\n      if (error.name === \"AbortError\") {\r\n        return undefined;\r\n      } else {\r\n        return error;\r\n      }\r\n    }).then(function (error) {\r\n      onFinishCallback(error);\r\n    });\r\n    return {\r\n      abort: function () {\r\n        if (reader != null) {\r\n          reader.cancel(); // https://bugzilla.mozilla.org/show_bug.cgi?id=1583815\r\n        }\r\n        controller.abort();\r\n      }\r\n    };\r\n  };\r\n\r\n  function EventTarget() {\r\n    this._listeners = Object.create(null);\r\n  }\r\n\r\n  function throwError(e) {\r\n    setTimeout(function () {\r\n      throw e;\r\n    }, 0);\r\n  }\r\n\r\n  EventTarget.prototype.dispatchEvent = function (event) {\r\n    event.target = this;\r\n    var typeListeners = this._listeners[event.type];\r\n    if (typeListeners != undefined) {\r\n      var length = typeListeners.length;\r\n      for (var i = 0; i < length; i += 1) {\r\n        var listener = typeListeners[i];\r\n        try {\r\n          if (typeof listener.handleEvent === \"function\") {\r\n            listener.handleEvent(event);\r\n          } else {\r\n            listener.call(this, event);\r\n          }\r\n        } catch (e) {\r\n          throwError(e);\r\n        }\r\n      }\r\n    }\r\n  };\r\n  EventTarget.prototype.addEventListener = function (type, listener) {\r\n    type = String(type);\r\n    var listeners = this._listeners;\r\n    var typeListeners = listeners[type];\r\n    if (typeListeners == undefined) {\r\n      typeListeners = [];\r\n      listeners[type] = typeListeners;\r\n    }\r\n    var found = false;\r\n    for (var i = 0; i < typeListeners.length; i += 1) {\r\n      if (typeListeners[i] === listener) {\r\n        found = true;\r\n      }\r\n    }\r\n    if (!found) {\r\n      typeListeners.push(listener);\r\n    }\r\n  };\r\n  EventTarget.prototype.removeEventListener = function (type, listener) {\r\n    type = String(type);\r\n    var listeners = this._listeners;\r\n    var typeListeners = listeners[type];\r\n    if (typeListeners != undefined) {\r\n      var filtered = [];\r\n      for (var i = 0; i < typeListeners.length; i += 1) {\r\n        if (typeListeners[i] !== listener) {\r\n          filtered.push(typeListeners[i]);\r\n        }\r\n      }\r\n      if (filtered.length === 0) {\r\n        delete listeners[type];\r\n      } else {\r\n        listeners[type] = filtered;\r\n      }\r\n    }\r\n  };\r\n\r\n  function Event(type) {\r\n    this.type = type;\r\n    this.target = undefined;\r\n  }\r\n\r\n  function MessageEvent(type, options) {\r\n    Event.call(this, type);\r\n    this.data = options.data;\r\n    this.lastEventId = options.lastEventId;\r\n  }\r\n\r\n  MessageEvent.prototype = Object.create(Event.prototype);\r\n\r\n  function ConnectionEvent(type, options) {\r\n    Event.call(this, type);\r\n    this.status = options.status;\r\n    this.statusText = options.statusText;\r\n    this.headers = options.headers;\r\n  }\r\n\r\n  ConnectionEvent.prototype = Object.create(Event.prototype);\r\n\r\n  function ErrorEvent(type, options) {\r\n    Event.call(this, type);\r\n    this.error = options.error;\r\n  }\r\n\r\n  ErrorEvent.prototype = Object.create(Event.prototype);\r\n\r\n  var WAITING = -1;\r\n  var CONNECTING = 0;\r\n  var OPEN = 1;\r\n  var CLOSED = 2;\r\n\r\n  var AFTER_CR = -1;\r\n  var FIELD_START = 0;\r\n  var FIELD = 1;\r\n  var VALUE_START = 2;\r\n  var VALUE = 3;\r\n\r\n  var contentTypeRegExp = /^text\\/event\\-stream(;.*)?$/i;\r\n\r\n  var MINIMUM_DURATION = 1000;\r\n  var MAXIMUM_DURATION = 18000000;\r\n\r\n  var parseDuration = function (value, def) {\r\n    var n = value == null ? def : parseInt(value, 10);\r\n    if (n !== n) {\r\n      n = def;\r\n    }\r\n    return clampDuration(n);\r\n  };\r\n  var clampDuration = function (n) {\r\n    return Math.min(Math.max(n, MINIMUM_DURATION), MAXIMUM_DURATION);\r\n  };\r\n\r\n  var fire = function (that, f, event) {\r\n    try {\r\n      if (typeof f === \"function\") {\r\n        f.call(that, event);\r\n      }\r\n    } catch (e) {\r\n      throwError(e);\r\n    }\r\n  };\r\n\r\n  function EventSourcePolyfill(url, options) {\r\n    EventTarget.call(this);\r\n    options = options || {};\r\n\r\n    this.onopen = undefined;\r\n    this.onmessage = undefined;\r\n    this.onerror = undefined;\r\n\r\n    this.url = undefined;\r\n    this.readyState = undefined;\r\n    this.withCredentials = undefined;\r\n    this.headers = undefined;\r\n\r\n    this._close = undefined;\r\n\r\n    start(this, url, options);\r\n  }\r\n\r\n  function getBestXHRTransport() {\r\n    return (XMLHttpRequest != undefined && (\"withCredentials\" in XMLHttpRequest.prototype)) || XDomainRequest == undefined\r\n        ? new XMLHttpRequest()\r\n        : new XDomainRequest();\r\n  }\r\n\r\n  var isFetchSupported = fetch != undefined && Response != undefined && \"body\" in Response.prototype;\r\n\r\n  function start(es, url, options) {\r\n    url = String(url);\r\n    var withCredentials = Boolean(options.withCredentials);\r\n    var lastEventIdQueryParameterName = options.lastEventIdQueryParameterName || \"lastEventId\";\r\n\r\n    var initialRetry = clampDuration(1000);\r\n    var heartbeatTimeout = parseDuration(options.heartbeatTimeout, 45000);\r\n\r\n    var lastEventId = \"\";\r\n    var retry = initialRetry;\r\n    var wasActivity = false;\r\n    var textLength = 0;\r\n    var headers = options.headers || {};\r\n    var TransportOption = options.Transport;\r\n    var xhr = isFetchSupported && TransportOption == undefined ? undefined : new XHRWrapper(TransportOption != undefined ? new TransportOption() : getBestXHRTransport());\r\n    var transport = TransportOption != null && typeof TransportOption !== \"string\" ? new TransportOption() : (xhr == undefined ? new FetchTransport() : new XHRTransport());\r\n    var abortController = undefined;\r\n    var timeout = 0;\r\n    var currentState = WAITING;\r\n    var dataBuffer = \"\";\r\n    var lastEventIdBuffer = \"\";\r\n    var eventTypeBuffer = \"\";\r\n\r\n    var textBuffer = \"\";\r\n    var state = FIELD_START;\r\n    var fieldStart = 0;\r\n    var valueStart = 0;\r\n\r\n    var onStart = function (status, statusText, contentType, headers) {\r\n      if (currentState === CONNECTING) {\r\n        if (status === 200 && contentType != undefined && contentTypeRegExp.test(contentType)) {\r\n          currentState = OPEN;\r\n          wasActivity = Date.now();\r\n          retry = initialRetry;\r\n          es.readyState = OPEN;\r\n          var event = new ConnectionEvent(\"open\", {\r\n            status: status,\r\n            statusText: statusText,\r\n            headers: headers\r\n          });\r\n          es.dispatchEvent(event);\r\n          fire(es, es.onopen, event);\r\n        } else {\r\n          var message = \"\";\r\n          if (status !== 200) {\r\n            if (statusText) {\r\n              statusText = statusText.replace(/\\s+/g, \" \");\r\n            }\r\n            message = \"EventSource's response has a status \" + status + \" \" + statusText + \" that is not 200. Aborting the connection.\";\r\n          } else {\r\n            message = \"EventSource's response has a Content-Type specifying an unsupported type: \" + (contentType == undefined ? \"-\" : contentType.replace(/\\s+/g, \" \")) + \". Aborting the connection.\";\r\n          }\r\n          close();\r\n          var event = new ConnectionEvent(\"error\", {\r\n            status: status,\r\n            statusText: statusText,\r\n            headers: headers\r\n          });\r\n          es.dispatchEvent(event);\r\n          fire(es, es.onerror, event);\r\n          console.error(message);\r\n        }\r\n      }\r\n    };\r\n\r\n    var onProgress = function (textChunk) {\r\n      if (currentState === OPEN) {\r\n        var n = -1;\r\n        for (var i = 0; i < textChunk.length; i += 1) {\r\n          var c = textChunk.charCodeAt(i);\r\n          if (c === \"\\n\".charCodeAt(0) || c === \"\\r\".charCodeAt(0)) {\r\n            n = i;\r\n          }\r\n        }\r\n        var chunk = (n !== -1 ? textBuffer : \"\") + textChunk.slice(0, n + 1);\r\n        textBuffer = (n === -1 ? textBuffer : \"\") + textChunk.slice(n + 1);\r\n        if (textChunk !== \"\") {\r\n          wasActivity = Date.now();\r\n          textLength += textChunk.length;\r\n        }\r\n        for (var position = 0; position < chunk.length; position += 1) {\r\n          var c = chunk.charCodeAt(position);\r\n          if (state === AFTER_CR && c === \"\\n\".charCodeAt(0)) {\r\n            state = FIELD_START;\r\n          } else {\r\n            if (state === AFTER_CR) {\r\n              state = FIELD_START;\r\n            }\r\n            if (c === \"\\r\".charCodeAt(0) || c === \"\\n\".charCodeAt(0)) {\r\n              if (state !== FIELD_START) {\r\n                if (state === FIELD) {\r\n                  valueStart = position + 1;\r\n                }\r\n                var field = chunk.slice(fieldStart, valueStart - 1);\r\n                var value = chunk.slice(valueStart + (valueStart < position && chunk.charCodeAt(valueStart) === \" \".charCodeAt(0) ? 1 : 0), position);\r\n                if (field === \"data\") {\r\n                  dataBuffer += \"\\n\";\r\n                  dataBuffer += value;\r\n                } else if (field === \"id\") {\r\n                  lastEventIdBuffer = value;\r\n                } else if (field === \"event\") {\r\n                  eventTypeBuffer = value;\r\n                } else if (field === \"retry\") {\r\n                  initialRetry = parseDuration(value, initialRetry);\r\n                  retry = initialRetry;\r\n                } else if (field === \"heartbeatTimeout\") {\r\n                  heartbeatTimeout = parseDuration(value, heartbeatTimeout);\r\n                  if (timeout !== 0) {\r\n                    clearTimeout(timeout);\r\n                    timeout = setTimeout(function () {\r\n                      onTimeout();\r\n                    }, heartbeatTimeout);\r\n                  }\r\n                }\r\n              }\r\n              if (state === FIELD_START) {\r\n                if (dataBuffer !== \"\") {\r\n                  lastEventId = lastEventIdBuffer;\r\n                  if (eventTypeBuffer === \"\") {\r\n                    eventTypeBuffer = \"message\";\r\n                  }\r\n                  var event = new MessageEvent(eventTypeBuffer, {\r\n                    data: dataBuffer.slice(1),\r\n                    lastEventId: lastEventIdBuffer\r\n                  });\r\n                  es.dispatchEvent(event);\r\n                  if (eventTypeBuffer === \"open\") {\r\n                    fire(es, es.onopen, event);\r\n                  } else if (eventTypeBuffer === \"message\") {\r\n                    fire(es, es.onmessage, event);\r\n                  } else if (eventTypeBuffer === \"error\") {\r\n                    fire(es, es.onerror, event);\r\n                  }\r\n                  if (currentState === CLOSED) {\r\n                    return;\r\n                  }\r\n                }\r\n                dataBuffer = \"\";\r\n                eventTypeBuffer = \"\";\r\n              }\r\n              state = c === \"\\r\".charCodeAt(0) ? AFTER_CR : FIELD_START;\r\n            } else {\r\n              if (state === FIELD_START) {\r\n                fieldStart = position;\r\n                state = FIELD;\r\n              }\r\n              if (state === FIELD) {\r\n                if (c === \":\".charCodeAt(0)) {\r\n                  valueStart = position + 1;\r\n                  state = VALUE_START;\r\n                }\r\n              } else if (state === VALUE_START) {\r\n                state = VALUE;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    var onFinish = function (error) {\r\n      if (currentState === OPEN || currentState === CONNECTING) {\r\n        currentState = WAITING;\r\n        if (timeout !== 0) {\r\n          clearTimeout(timeout);\r\n          timeout = 0;\r\n        }\r\n        timeout = setTimeout(function () {\r\n          onTimeout();\r\n        }, retry);\r\n        retry = clampDuration(Math.min(initialRetry * 16, retry * 2));\r\n\r\n        es.readyState = CONNECTING;\r\n        var event = new ErrorEvent(\"error\", {error: error});\r\n        es.dispatchEvent(event);\r\n        fire(es, es.onerror, event);\r\n        if (error != undefined) {\r\n          console.error(error);\r\n        }\r\n      }\r\n    };\r\n\r\n    var close = function () {\r\n      currentState = CLOSED;\r\n      if (abortController != undefined) {\r\n        abortController.abort();\r\n        abortController = undefined;\r\n      }\r\n      if (timeout !== 0) {\r\n        clearTimeout(timeout);\r\n        timeout = 0;\r\n      }\r\n      es.readyState = CLOSED;\r\n    };\r\n\r\n    var onTimeout = function () {\r\n      timeout = 0;\r\n\r\n      if (currentState !== WAITING) {\r\n        if (!wasActivity && abortController != undefined) {\r\n          onFinish(new Error(\"No activity within \" + heartbeatTimeout + \" milliseconds.\" + \" \" + (currentState === CONNECTING ? \"No response received.\" : textLength + \" chars received.\") + \" \" + \"Reconnecting.\"));\r\n          if (abortController != undefined) {\r\n            abortController.abort();\r\n            abortController = undefined;\r\n          }\r\n        } else {\r\n          var nextHeartbeat = Math.max((wasActivity || Date.now()) + heartbeatTimeout - Date.now(), 1);\r\n          wasActivity = false;\r\n          timeout = setTimeout(function () {\r\n            onTimeout();\r\n          }, nextHeartbeat);\r\n        }\r\n        return;\r\n      }\r\n\r\n      wasActivity = false;\r\n      textLength = 0;\r\n      timeout = setTimeout(function () {\r\n        onTimeout();\r\n      }, heartbeatTimeout);\r\n\r\n      currentState = CONNECTING;\r\n      dataBuffer = \"\";\r\n      eventTypeBuffer = \"\";\r\n      lastEventIdBuffer = lastEventId;\r\n      textBuffer = \"\";\r\n      fieldStart = 0;\r\n      valueStart = 0;\r\n      state = FIELD_START;\r\n\r\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=428916\r\n      // Request header field Last-Event-ID is not allowed by Access-Control-Allow-Headers.\r\n      var requestURL = url;\r\n      if (url.slice(0, 5) !== \"data:\" && url.slice(0, 5) !== \"blob:\") {\r\n        if (lastEventId !== \"\") {\r\n          // Remove the lastEventId parameter if it's already part of the request URL.\r\n          var i = url.indexOf(\"?\");\r\n          requestURL = i === -1 ? url : url.slice(0, i + 1) + url.slice(i + 1).replace(/(?:^|&)([^=&]*)(?:=[^&]*)?/g, function (p, paramName) {\r\n            return paramName === lastEventIdQueryParameterName ? '' : p;\r\n          });\r\n          // Append the current lastEventId to the request URL.\r\n          requestURL += (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + lastEventIdQueryParameterName +\"=\" + encodeURIComponent(lastEventId);\r\n        }\r\n      }\r\n      var withCredentials = es.withCredentials;\r\n      var requestHeaders = {};\r\n      requestHeaders[\"Accept\"] = \"text/event-stream\";\r\n      var headers = es.headers;\r\n      if (headers != undefined) {\r\n        for (var name in headers) {\r\n          if (Object.prototype.hasOwnProperty.call(headers, name)) {\r\n            requestHeaders[name] = headers[name];\r\n          }\r\n        }\r\n      }\r\n      try {\r\n        abortController = transport.open(xhr, onStart, onProgress, onFinish, requestURL, withCredentials, requestHeaders);\r\n      } catch (error) {\r\n        close();\r\n        throw error;\r\n      }\r\n    };\r\n\r\n    es.url = url;\r\n    es.readyState = CONNECTING;\r\n    es.withCredentials = withCredentials;\r\n    es.headers = headers;\r\n    es._close = close;\r\n\r\n    onTimeout();\r\n  }\r\n\r\n  EventSourcePolyfill.prototype = Object.create(EventTarget.prototype);\r\n  EventSourcePolyfill.prototype.CONNECTING = CONNECTING;\r\n  EventSourcePolyfill.prototype.OPEN = OPEN;\r\n  EventSourcePolyfill.prototype.CLOSED = CLOSED;\r\n  EventSourcePolyfill.prototype.close = function () {\r\n    this._close();\r\n  };\r\n\r\n  EventSourcePolyfill.CONNECTING = CONNECTING;\r\n  EventSourcePolyfill.OPEN = OPEN;\r\n  EventSourcePolyfill.CLOSED = CLOSED;\r\n  EventSourcePolyfill.prototype.withCredentials = undefined;\r\n\r\n  var R = NativeEventSource\r\n  if (XMLHttpRequest != undefined && (NativeEventSource == undefined || !(\"withCredentials\" in NativeEventSource.prototype))) {\r\n    // Why replace a native EventSource ?\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=444328\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=831392\r\n    // https://code.google.com/p/chromium/issues/detail?id=260144\r\n    // https://code.google.com/p/chromium/issues/detail?id=225654\r\n    // ...\r\n    R = EventSourcePolyfill;\r\n  }\r\n\r\n  (function (factory) {\r\n    if ( true && typeof module.exports === \"object\") {\r\n      var v = factory(exports);\r\n      if (v !== undefined) module.exports = v;\r\n    }\r\n    else if (true) {\r\n      !(__WEBPACK_AMD_DEFINE_ARRAY__ = [exports], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\r\n    }\r\n    else {}\r\n  })(function (exports) {\r\n    exports.EventSourcePolyfill = EventSourcePolyfill;\r\n    exports.NativeEventSource = NativeEventSource;\r\n    exports.EventSource = R;\r\n  });\r\n}(typeof globalThis === 'undefined' ? (typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : this) : globalThis));\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/event-source-polyfill/src/eventsource.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@sanity/eventsource/browser.js":
/*!*****************************************************!*\
  !*** ./node_modules/@sanity/eventsource/browser.js ***!
  \*****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! event-source-polyfill */ \"(app-pages-browser)/./node_modules/event-source-polyfill/src/eventsource.js\").EventSourcePolyfill\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FuaXR5L2V2ZW50c291cmNlL2Jyb3dzZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQXFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9Ac2FuaXR5L2V2ZW50c291cmNlL2Jyb3dzZXIuanM/ZjcxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJ2V2ZW50LXNvdXJjZS1wb2x5ZmlsbCcpLkV2ZW50U291cmNlUG9seWZpbGxcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@sanity/eventsource/browser.js\n"));

/***/ })

}]);