import type { Viewport } from "next";
// import { Inter } from "next/font/google";
import { NextIntlClientProvider } from 'next-intl';
import "./globals.css";
import { getMessages } from "next-intl/server";
import TanstackQueryProvider from "@/components/providers/tanstack-query-provider";
import { Toaster } from "@/components/ui/toaster"
import NextTopLoader from "nextjs-toploader";
import localFont from "next/font/local";

import { Suspense } from "react";
import ClientLayout from "./client-layout";
// const inter = Inter({ subsets: ["latin"] });

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover"
}
// Load Inter font
const inter = localFont({
  src: [
    { path: "../font/Inter_24pt-Regular.ttf", weight: "400", style: "normal" },
    { path: "../font/Inter_24pt-Medium.ttf", weight: "500", style: "normal" },
    { path: "../font/Inter_24pt-SemiBold.ttf", weight: "600", style: "normal" },
    { path: "../font/Inter_24pt-Bold.ttf", weight: "700", style: "normal" },

  ],
  variable: "--font-inter",
  display: "swap"
});


export default async function RootLayout({
  children, params: { locale }
}: Readonly<{
  children: React.ReactNode, params: { locale: string }
}>) {
  const messages = await getMessages();
  return (
    <html lang={locale}>
      <head>
        <meta name="google-site-verification" content="4eaK7qBBsKK5tqiXQyuYzG6xiv0N80JPVDp4H61aIyw" />
      </head>
      <NextIntlClientProvider messages={messages}>
        <body className={`${inter.className} relative`}>
          <Suspense fallback="null">
            <ClientLayout />
          </Suspense>
          <NextTopLoader
            showSpinner={false}
          />
          <TanstackQueryProvider>
            {children}
            <Toaster />
          </TanstackQueryProvider>
        </body>
      </NextIntlClientProvider>
    </html >
  );
}
