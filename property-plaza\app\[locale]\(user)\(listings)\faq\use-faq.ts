import { BoxSelectIcon, Coins, HelpCircle, MessageCircle, Percent, User2 } from "lucide-react";
import { useTranslations } from "next-intl"
import { useState } from "react";

export function useFaq() {
  const t = useTranslations("seeker")
  const usingPlatform = [
    {
      question: t('faq.searchProperty.title'),
      answer: t('faq.searchProperty.description')
    },
    {
      question: t('faq.contactOwner.title'),
      answer: t('faq.contactOwner.description')
    },
    {
      question: t('faq.savingProperty.title'),
      answer: t('faq.savingProperty.description')
    },
    {
      question: t('faq.receivingNotification.title'),
      answer: t('faq.receivingNotification.description')
    },
    {
      question: t('faq.technicalIssue.title'),
      answer: t('faq.technicalIssue.description')
    },
  ];

  const contactingPropertyOwner = [
    {
      question: t('faq.negotiatingPrice.title'),
      answer: t('faq.negotiatingPrice.description')
    },
    {
      question: t('faq.contactOwnerDirectly.title'),
      answer: t('faq.contactOwnerDirectly.description')
    },
    {
      question: t('faq.limitContactingOwner.title'),
      answer: t('faq.limitContactingOwner.description')
    },
    {
      question: t('faq.reasonOfLimitContactingOwner.title'),
      answer: t('faq.reasonOfLimitContactingOwner.description')
    },
    {
      question: t('faq.ifOwnerDoesntResponse.title'),
      answer: t('faq.ifOwnerDoesntResponse.description')
    }
  ];

  const propertyVerificationAndSafety = [
    {
      question: t('faq.checkOwnerisVerified.title'),
      answer: t('faq.checkOwnerisVerified.description')
    },
    {
      question: t('faq.trustListingPhoto.title'),
      answer: t('faq.trustListingPhoto.description')
    },
    {
      question: t('faq.safeToDeposit.title'),
      answer: t('faq.safeToDeposit.description')
    },
    {
      question: t('faq.issueWhenRenting.title'),
      answer: t('faq.issueWhenRenting.description')
    }
  ];

  const paymentAndFee = [
    {
      question: t('faq.handlingPayment.title'),
      answer: t('faq.handlingPayment.description')
    },
    {
      question: t('faq.additionalFee.title'),
      answer: t('faq.additionalFee.description')
    },
    {
      question: t('faq.payForSubscription.title'),
      answer: t('faq.payForSubscription.description')
    },
        {
      question: t('faq.subscriptionOffer.title'),
      answer: t('faq.subscriptionOffer.description'),
      extraContent: "subscription-offer-url"
    },
    {
      question: t('seeker.faq.cancelSubscription.title'),
      answer: t('faq.cancelSubscription.description'),
      extraContent: "cancelation-subscription-url"
    }
  ];

  const propertyVisit = [
    {
      question: t('faq.schedulePropertyVisit.title'),
      answer: t('faq.schedulePropertyVisit.description')
    },
    {
      question: t('faq.requestContract.title'),
      answer: t('faq.requestContract.description')
    },
    {
      question: t('faq.platformOfferLegalAdvice.title'),
      answer: t('faq.platformOfferLegalAdvice.description')
    },
    {
      question: t('faq.disputeWithOwner.title'),
      answer: t('faq.legalContracts.description')
    }
  ];


  const data = [
    ...usingPlatform, 
    ...contactingPropertyOwner, 
    ...propertyVerificationAndSafety, 
    ...paymentAndFee, 
    ...propertyVisit
  ]
  const [filteredData, setFilteredData] = useState<{ question: string, answer: string }[]>([])
  const filteringData = (value: string) => {
    const filtered = data.filter(item => {
      const relatedQuestion = item.question.includes(value)
      const relatedAnswer = item.answer.includes(value)
      return relatedQuestion || relatedAnswer
    })
    setFilteredData(filtered)
  }

  return { 
    data,  
    filteredData, 
    filteringData,
    usingPlatform,
    contactingPropertyOwner,
    propertyVerificationAndSafety,
    paymentAndFee,
    propertyVisit
  }
}