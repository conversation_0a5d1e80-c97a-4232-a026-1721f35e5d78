import { Satellite } from "lucide-react"
import { create } from "zustand"
import { createJSONStorage, persist } from "zustand/middleware"

interface SettingState {
  hasNotificationSound?:boolean,
  setNotificationSound: (value:boolean) => void,
  isLoading?:boolean,
  setIsLoading: (isLoading:boolean) => void,
  editingStatus: string[],
  setEditingStatus: (status: string,action: "remove" | "add") => void,
  removeEditingStatus: () => void
}

export const useSettingStore = create<SettingState>()(
  persist(
    set =>({
      hasNotificationSound: undefined,
      setNotificationSound: (hasNotificationSound) => set(({hasNotificationSound})),
      isLoading:true,
      setIsLoading: (isLoading) => set(({isLoading})),
      editingStatus: [],
      setEditingStatus: (editingStatus,action) => set(state => {
        const allEditingStatus = state.editingStatus
        if(action == "add"){
          if(allEditingStatus.includes(editingStatus)) return {...state}
          allEditingStatus.push(editingStatus)
          return {...state, editingStatus: allEditingStatus}
        }else if(action == "remove"){
          const finalStatus = allEditingStatus.filter(item => item !== editingStatus)
          return {...state,editingStatus:finalStatus}
        }
        return {...state}
      }),
      removeEditingStatus: () => set(({editingStatus: []}))
    }),{
      name: "settings",
      storage: createJSONStorage(( ) => localStorage),
      onRehydrateStorage: () => (state) =>  {
        if(state){
          state.setIsLoading(false)
        }
      }
    }))