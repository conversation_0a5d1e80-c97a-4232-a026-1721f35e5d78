"use client"
import { forwardRef, useEffect, useState } from "react"
import { BaseSelectInputValue } from "@/types/base"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"
import { useSeekersSettingsStore } from "@/stores/seekers-settings.store"

export const currencies: BaseSelectInputValue<string>[] = [
  {
    id: "1",
    content: "IDR",
    value: "IDR"
  },
  {
    id: "2",
    content: "EUR",
    value: "EUR"
  },
  {
    id: "3",
    content: "GBP",
    value: "GBP"
  },
  {
    id: "4",
    content: "AUD",
    value: "AUD"
  },
  {
    id: "5",
    content: "USD",
    value: "USD"
  },
]

interface CurrencyFormProps {
  triggerClassName?: string
  showCaret?: boolean
  defaultCurrency?: string
  onClick?: (e: React.MouseEvent) => void
}

const CurrencyForm = forwardRef<HTMLButtonElement, CurrencyFormProps>(
  ({ triggerClassName, showCaret = false, defaultCurrency = "IDR", onClick }, ref) => {
    const { currency, setCurrency, isLoading } = useSeekersSettingsStore()
    const [selectedCurrency, setSelectedCurrency] = useState(defaultCurrency)
    const [open, setOpen] = useState(false)

    useEffect(() => {
      if (isLoading) return setSelectedCurrency(defaultCurrency)
      setSelectedCurrency(currency)
    }, [currency, isLoading, defaultCurrency])

    return (
      <div className="max-sm:w-fit w-full">
        <Select
          defaultValue={defaultCurrency}
          value={selectedCurrency}
          onValueChange={setCurrency}
          open={open}
          onOpenChange={(open) => {
            setOpen(open)
            if (open && onClick) {
              const event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
              }) as unknown as React.MouseEvent
              onClick(event)
            }
          }}
        >
          <SelectTrigger
            ref={ref}
            showCaret={showCaret}
            className={`rounded-full border flex items-center justify-center border-seekers-text-lighter shadow-md h-10 px-2 !w-full ${triggerClassName}`}
            onClick={(e) => {
              e.stopPropagation()
              onClick?.(e)
            }}
          >
            <SelectValue className="text-xs text-center" />
          </SelectTrigger>
          <SelectContent>
            {currencies.map(item => (
              <SelectItem key={item.id} value={item.value}>
                {item.content}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    )
  }
)

CurrencyForm.displayName = "CurrencyForm"
export default CurrencyForm