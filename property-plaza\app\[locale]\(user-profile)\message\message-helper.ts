import { messageCategory, messageSeekerCategory } from "@/core/domain/messages/messages";

// helper to convert message category into text with locale for filtering message UI
export function convertFilterToText(text: string, t: any) {
  switch (text) {
    case (messageCategory.accountManager):
      return t('message.category.accountManager')
    case (messageCategory.customerSupport):
      return t('message.category.customerSupport')
    case (messageCategory.seekers):
      return t('message.category.seekers')
    case (messageSeekerCategory.owner):
      return t("message.category.owner")
    default:
      return ""
  }
}