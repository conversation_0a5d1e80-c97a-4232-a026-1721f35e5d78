import { Metadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";
import { BaseMetadataProps } from "@/types/base";
import GuideHeroSection from "./hero/guide-hero-section";
import GuideValueProposition from "./guide-value-proposition";
import GuideEmailCapture from "./guide-email-capture";
import GuideSocialProof from "./guide-social-proof";
import GuideSecondaryCTA from "./guide-secondary-cta";
import { guideUrl } from "@/lib/constanta/route";
import ClientGuidePage from "./client-page";



export async function generateMetadata({ params, searchParams }: BaseMetadataProps<{}>): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const locale = await getLocale()

  return {
    title: t("metadata.guide.title"),
    description: t("metadata.guide.description"),
    alternates: {
      canonical: "https://www.property-plaza.com/" + locale + guideUrl,
      languages: {
        "id": process.env.USER_DOMAIN + "id" + guideUrl,
        "en": process.env.USER_DOMAIN + "en" + guideUrl,
        "x-default": process.env.USER_DOMAIN + "en" + guideUrl,
      },
    },
    openGraph: {
      images: [
        {
          url: "/og.jpg",
          width: 1200,
          height: 630,
          alt: "Property Plaza - Free Bali Housing Guide",
        }
      ],
      type: "website",
      url: process.env.USER_DOMAIN + locale + guideUrl,
      title: t("metadata.guide.title"),
      description: t("metadata.guide.description"),
    },
    twitter: {
      card: "summary_large_image",
      title: t("metadata.guide.title"),
      description: t("metadata.guide.description"),
      images: ["/guide-og.png"],
    },
  }
}

export default async function GuidePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div data-section="hero" className="isolate">
        <GuideHeroSection />
      </div>

      {/* Value Proposition */}
      <div data-section="value-proposition">
        <GuideValueProposition />
      </div>

      {/* Email Capture Form */}
      <div data-section="email-capture">
        <GuideEmailCapture />
      </div>

      {/* Social Proof */}
      <div data-section="social-proof">
        <GuideSocialProof />
      </div>

      {/* Secondary CTA */}
      <div data-section="secondary-cta">
        <GuideSecondaryCTA />
      </div>
      <ClientGuidePage />
    </div>
  );
}
