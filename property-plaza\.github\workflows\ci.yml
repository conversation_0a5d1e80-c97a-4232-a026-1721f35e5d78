name: CI Workflow

on:
  pull_request:
    branches:
      - main
      - development

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm install

      - name: Run linter
        run: npm run lint

  check-types:
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'

      - name: Install dependencies
        run: npm install

      - name: Check types
        run: npm run check-types

  unit-tests:
    runs-on: ubuntu-latest
    needs: check-types
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'

      - name: Install dependencies
        run: npm install

      - name: Run unit tests
        run: npm run test

  e2e-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'

      - name: Install dependencies
        run: npm install

      - name: Install Playwright
        run: npx playwright install

      - name: Set up Percy
        uses: percy/agent@v1

      - name: Run Playwright E2E tests with Percy
        run: npm run e2e-tests
        env:
          PERCY_TOKEN: web_b725798db84a66b9e534f57eeebc5f195f1e52ce143ee423ddd9ab675664a218
