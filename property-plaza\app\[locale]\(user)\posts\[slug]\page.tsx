export const revalidate = 3600;

import { getPostByCategory, getPostBySlug } from "@/core/services/sanity/services";
import BlogContent from "./content";
import MoreArticles from "./more-articles";
import { notFound } from "next/navigation";
import { Metadata } from "next";
import PostBreadCrumb from "../bread-crumb";
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import { getLocale } from "next-intl/server";


export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const locale = await getLocale()
  const blogContent = await getPostBySlug(params.slug)
  if (!blogContent) return {
    title: "blog"
  }
  return {
    title: blogContent.title,
    description: blogContent.metadata,
    openGraph: {
      images: [
        {
          url: blogContent.mainImage.asset.url, // MUST be a full URL like "https://cdn.example.com/img.jpg"
          width: 1200,
          height: 630,
          alt: blogContent.title,
        }
      ],
      type: "article",
      url: process.env.USER_DOMAIN || "" + locale + "/posts/" + blogContent?.slug.current
    },
    alternates: {
      canonical: "https://property-plaza.com/" + locale + "/posts/" + blogContent?.slug.current,
      languages: {
        en: process.env.USER_DOMAIN + "en/posts/" + blogContent?.slug?.current,
        id: process.env.USER_DOMAIN + "id/posts/" + blogContent?.slug?.current
      }
    }
  }
}

export default async function PostDetail({ params }: { params: { slug: string } }) {
  const blogContent = await getPostBySlug(params.slug)
  if (!blogContent) return notFound()
  const defaultCategory = "real-estate"
  const moreContent = await getPostByCategory(blogContent?.category?._id || defaultCategory, blogContent._id.toString())

  const conversionRates = await getCurrencyConversion()

  return <>
    <PostBreadCrumb title={blogContent.title} />
    <BlogContent blogContent={blogContent} conversions={conversionRates.data} />
    <MoreArticles moreContent={moreContent} />
  </>
}