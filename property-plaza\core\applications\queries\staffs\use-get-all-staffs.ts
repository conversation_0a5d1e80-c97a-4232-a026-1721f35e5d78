import { getAllStaffService } from "@/core/infrastructures/staff/service";
import { BasePaginationRequest } from "@/types/base";
import { useQuery } from "@tanstack/react-query";

export const ALL_STAFF_QUERY_KEY = "all-staff"
export function useGetAllStaff(data:BasePaginationRequest){
  const {
    page,
    per_page,
    search
  } = data
  const query = useQuery({
    queryKey: [ALL_STAFF_QUERY_KEY,page,per_page,search],
    queryFn: async () => {
      const data:BasePaginationRequest = {
        page,per_page,search
      } 
      return await getAllStaffService(data)
    },
    retry: 0
  },
)
  return query
}