"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CreditCard } from "lucide-react"
import { useTranslations } from "next-intl"
import { BillingInformation, PaymentMethod } from "@/core/domain/transaction/transaction"
import PaymentItem from "./payment-item"
import EditBillingFormDialog from "./form/edit-billing-form-dialog"
import { Country } from "react-phone-number-input"
import en from 'react-phone-number-input/locale/en'

// Inside your component

export default function PaymentMethodContent({ paymentMethod, billingInfo }: { paymentMethod: PaymentMethod[], billingInfo?: BillingInformation }) {
  const t = useTranslations("seeker")
  return <Card>
    <CardHeader>
      <CardTitle className="text-seekers-primary">{t("setting.subscriptionStatus.billing.paymentMethod.title")}</CardTitle>
      <CardDescription>{t("setting.subscriptionStatus.billing.paymentMethod.description")}</CardDescription>
    </CardHeader>
    <CardContent>
      <div className="space-y-6">
        {/* Payment Method */}
        {
          paymentMethod.length == 0 ? <>
            <div className="flex flex-col items-center text-center w-full">
              <div className="border rounded-xl p-2 w-fit ">
                <CreditCard className="h-6 w-6 text-seekers-primary" />
              </div>
              <p className="text-seekers-text-light">{t('info.noPaymentMethodsAdded')}</p>
            </div>
          </> :
            <>
              <div className="p-4 border border-seekers-primary/20 rounded-lg">
                {paymentMethod.map(item => <PaymentItem key={item.id} item={item} />
                )}
              </div>
            </>
        }
        <Button
          variant="outline"
          className="w-full mt-4 border-seekers-primary text-seekers-primary hover:bg-[#FAF6F0] hover:text-seekers-primary"
        >
          {t("cta.addPaymentMethod")}
        </Button>

        {/* Billing Information */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-seekers-primary">{t("setting.subscriptionStatus.billing.billingInformation.title")}</h3>
            <EditBillingFormDialog billingInfo={billingInfo} />
          </div>
          <div className="space-y-1 text-sm p-4 border border-seekers-primary/20 rounded-lg">
            <>
              <p className="font-medium"> {billingInfo?.name}</p>
              <p>{billingInfo?.addressOne}</p>
              <p>{billingInfo?.addressTwo}</p>
              <p>
                {billingInfo?.postalCode} {billingInfo?.city}
              </p>
              <p>{en[billingInfo?.country as Country]}</p>
            </>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
}