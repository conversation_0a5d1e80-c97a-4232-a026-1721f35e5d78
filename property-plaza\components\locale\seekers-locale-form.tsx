import { BaseSelectInputValue } from "@/types/base"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"
import React, { forwardRef, useState } from "react"
import CountryFlag from "../utility/country-flag"
import useLocaleSwitcher from "@/hooks/use-locale-switcher"

export function Languages() {
  const languages: BaseSelectInputValue<string>[] = [
    {
      id: "2",
      content: <CountryFlag code="US" />,
      value: "EN"
    },
    {
      id: "1",
      content: <CountryFlag code="ID" />,
      value: "ID"
    },
  ]
  return languages
}

interface SeekersLocaleFormProps {
  triggerClassName?: string
  showCaret?: boolean
  defaultValue?: string
  onClick?: (e: React.MouseEvent) => void
}

const SeekersLocaleForm = forwardRef<HTMLButtonElement, SeekersLocaleFormProps>(
  (props, ref) => {
    const {
      triggerClassName,
      showCaret = false,
      defaultValue = "en",
      onClick
    } = props;

    const { changeLanguage, locale } = useLocaleSwitcher();
    const languages: BaseSelectInputValue<string>[] = Languages();
    const [open, setOpen] = useState(false);

    return (
      <div className="max-sm:w-fit w-full" onClick={e => e.stopPropagation()}>
        <Select
          defaultValue={defaultValue}
          onValueChange={changeLanguage}
          value={locale.toUpperCase()}
          open={open}
          onOpenChange={(open) => {
            setOpen(open);
            if (open && onClick) {
              const event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
              }) as unknown as React.MouseEvent;
              onClick(event);
            }
          }}
        >
          <SelectTrigger
            ref={ref}
            showCaret={showCaret}
            className={`rounded-full border border-seekers-text-lighter shadow-md h-10 w-14 px-2 flex items-center justify-center ${triggerClassName}`}
            onClick={(e) => {
              e.stopPropagation();
              onClick?.(e);
            }}
          >
            <SelectValue className="text-xs" />
          </SelectTrigger>
          <SelectContent>
            {languages.map(item => (
              <SelectItem
                className=""
                key={item.id}
                value={item.value}
              >
                {item.content}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  }
);
SeekersLocaleForm.displayName = 'SeekersLocaleForm'

export default SeekersLocaleForm