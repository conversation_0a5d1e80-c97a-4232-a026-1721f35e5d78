"use client"
import { DataTableColumnHeader } from "@/components/table/header";
import { SeekerTransaction } from "@/core/domain/transaction/transaction";
import { ColumnDef } from "@tanstack/react-table";
import moment from "moment";
import { useTranslations } from "next-intl";
import { useSeekersSettingsStore } from "@/stores/seekers-settings.store";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";
import { DataTable } from "@/components/table/data-table";
import { TransactionSeekerColumnHelper } from "./transaction-seeker-column-helper";
import { useSearchParams } from "next/navigation";
import { useGetTransactionSeekerQuery } from "@/core/applications/queries/transaction/use-get-transaction-seeker-list";
import Link from "next/link";
import { packages } from "@/core/domain/subscription/subscription";
import { useCancelSubscribePlan } from "@/core/applications/mutations/subscription/use-cancel-subscription-plan";
import { useToast } from "@/hooks/use-toast";
import CancelDialog from "../subscription/cancel-dialog";
import { formatCurrency } from "@/lib/utils";


export default function BillingHistoryDataTable({ conversionRate }: { conversionRate: { [key: string]: string } }) {
  const t = useTranslations("seeker")

  const { currency } = useSeekersSettingsStore()
  const params = useSearchParams()

  const page = +(params.get("page") || 1)
  const per_page = +(params.get("per_page") || 10)
  const start_date = params.get("start_date") || ""
  const end_date = params.get("end_date") || ""
  const type = params.get("type") as string
  const cancelSubscriptionMutation = useCancelSubscribePlan()

  const transactionQuery = useGetTransactionSeekerQuery({
    page,
    per_page,
    search: "",
    type,
    start_date: start_date,
    end_date: end_date
  })
  const { toast } = useToast()
  const handleCancellation = async () => {
    try {
      const response = await cancelSubscriptionMutation.mutateAsync()
      toast({
        title: t("success.cancelSubscription"),
        description: response.data.message
      })
    } catch (e: any) {
      toast({
        title: t('error.Subscribing'),
        description: e.response.data.message || "",
        variant: "destructive"
      })
    }
  }
  const column: ColumnDef<SeekerTransaction>[] = [
    {
      accessorKey: "download",
      header: () => <></>,
      cell: ({ row }) => row.original.url ? <Button variant="ghost" size="icon" asChild className="hover:bg-[#FAF6F0]">
        <Link href={""} target="_blank" download>
          <Download className="h-4 text-[#C19B67] w-4" />
        </Link>
      </Button> : <></>
    },
    {
      accessorKey: "date",
      header: ({ column }) => <DataTableColumnHeader title={t('transaction.dataTable.transactionDate ')} column={column} />,
      cell: ({ row }) => moment(row.original.date).format("DD MMM YYYY")
    },
    {
      accessorKey: "code",
      header: ({ column }) => <DataTableColumnHeader title={t('transaction.dataTable.invoiceNumber')} column={column} />,
    },
    {
      accessorKey: "productName",
      header: ({ column }) => <DataTableColumnHeader title={t('transaction.dataTable.plan')} column={column} />,
    },
    {
      accessorKey: "grandTotal",
      header: ({ column }) => <DataTableColumnHeader title={t('transaction.dataTable.amount')} column={column} />,
      cell: ({ row }) => formatCurrency((row.original.grandTotal * (+conversionRate[currency] || 1)), currency)
    },
    {
      accessorKey: "nextBilling",
      header: ({ column }) => <DataTableColumnHeader title={t('transaction.dataTable.nextBillingDate')} column={column} />,
      cell: ({ row }) => row.original.nextBilling !== "" ? moment(row.original.nextBilling).format("DD MMM YYYY") : "-"

    },
    {
      accessorKey: "Action",
      header: ({ column }) => <DataTableColumnHeader title={t('transaction.dataTable.action')} column={column} />,
      cell: ({ row }) => row.original.status == "PENDING" ?
        <>
          <Button variant={"default-seekers"} asChild>
            <Link href={row.original.url || ""} target="_blank">
              {t("cta.pay")}
            </Link>
          </Button>
        </>
        :
        <>
          {row.original.productName.includes(packages.finder) && row.original.isActive
            // && moment().isAfter(moment(row.original.nextBillingDate)) 
            ? (
              <CancelDialog
                nextBillingDate={row.original.nextBilling || ""}
                onCancel={handleCancellation}
                trigger={
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-500 hover:bg-red-50 hover:text-red-700 px-0"
                  >
                    {t("cta.cancel")}
                  </Button>
                }
              />
            ) : (
              "-"
            )}
        </>
    },


  ]

  return <>
    <DataTable
      columns={column}
      isLoading={transactionQuery.isLoading}
      onSearch={(e) => { }}
      searchValue={""}
      data={transactionQuery.data?.data?.data as SeekerTransaction[] || []}
      meta={undefined}
      customNoResult={t("transaction.dataTable.noResult")}
      helperColumnFilter={val => TransactionSeekerColumnHelper(val as keyof SeekerTransaction)}
      hasFilter={false}
      disableRowPerPage
      showTableView={false}
    />
  </>
}