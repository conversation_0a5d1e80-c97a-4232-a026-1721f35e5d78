"use client"
import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store"
import { useEffect, useState } from "react"
import FilterContentLayout from "./filter-content-layout"
import { businessBuildingSize, listingCategory, seekersListingFilterType } from "@/core/domain/listing/listing-seekers"
import { useTranslations } from "next-intl"
import { BaseSelectInputValue } from "@/types/base"
import CheckboxFilterItem from "./checkbox-filter-item"
import { CircleCheck, Info } from "lucide-react"
import TooltipWrapper from "@/components/tooltop-wrapper/tooltip-wrapper"
import { arraysShareValue } from "@/lib/utils"

export default function SubtypeFilter() {
  const t = useTranslations("seeker")
  const { typeProperty, subTypeProperty, setSubTypeProperty, clearSubTypeProperty } = useSeekerFilterStore(state => state)
  const [title, setTitle] = useState("")
  const [subtitle, setSubtitle] = useState("")
  const [options, setOptions] = useState<BaseSelectInputValue<string>[]>([])
  const handleSetSubTypeProperty = (val: string) => {
    if (typeProperty == seekersListingFilterType.business) {
      if ((val == businessBuildingSize.small.key && !subTypeProperty.includes(businessBuildingSize.medium.key)) ||
        (val == businessBuildingSize.large.key && !subTypeProperty.includes(businessBuildingSize.medium.key))
      ) {
        clearSubTypeProperty()
        setSubTypeProperty(val)
        return
      } else if (val == businessBuildingSize.medium.key) {
        if (subTypeProperty.length == 3) {
          clearSubTypeProperty()
          setSubTypeProperty(businessBuildingSize.large.key)
        } else {
          setSubTypeProperty(val)
        }
      } else {
        setSubTypeProperty(val)
      }

    } else {
      setSubTypeProperty(val)
    }
  }
  useEffect(() => {
    const businessSubOptions: BaseSelectInputValue<string>[] = [
      {
        id: "1",
        content:
          <TooltipWrapper trigger={
            <div className="w-full flex justify-center items-center">
              <span className="flex gap-1 items-center ">
                <CircleCheck className="w-5 h-5" strokeWidth={1.5} />
                {t('listing.filter.typeProperty.optionThree.subOption.optionOne.title')}
                <Info className="w-3 h-3" />
              </span>
            </div>
          }
            content={<p>
              {t('listing.filter.typeProperty.optionThree.subOption.description', { comparisonType: t('misc.comparisonType.lessThan'), count: "70m" })}
              <span className="align-super">2</span>
            </p>}
            contentClassName="text-seekers-primary shadow-md"
          />
        ,
        value: businessBuildingSize.small.key
      },
      {
        id: "2",
        content: <>
          <TooltipWrapper trigger={
            <div className="w-full flex justify-center items-center">
              <span className="flex gap-1 items-center">
                <CircleCheck className="w-5 h-5" strokeWidth={1.5} />
                {t('listing.filter.typeProperty.optionThree.subOption.optionTwo.title')}
                <Info className="w-3 h-3" />
              </span>
            </div>
          }
            content={<p>
              {t('listing.filter.typeProperty.optionThree.subOption.description', { comparisonType: "", count: "70 - 200m" })}
              <span className="align-super">2</span>
            </p>}
            contentClassName="text-seekers-primary shadow-md"
          />
        </>
        ,
        value: businessBuildingSize.medium.key
      },
      {
        id: "3",
        content: <>

          <TooltipWrapper trigger={
            <div className="w-full flex justify-center items-center">
              <span className="flex gap-1 items-center">
                <CircleCheck className="w-5 h-5" strokeWidth={1.5} />
                {t('listing.filter.typeProperty.optionThree.subOption.optionThree.title')}
                <Info className="w-3 h-3" />

              </span>
            </div>
          }
            content={<p>
              {t('listing.filter.typeProperty.optionThree.subOption.description', { comparisonType: t('misc.comparisonType.moreThan'), count: "200m" })}
              <span className="align-super">2</span>
            </p>}
            contentClassName="text-seekers-primary shadow-md"
          />
        </>
        ,
        value: businessBuildingSize.large.key
      },
    ]
    const placeToLiveOptions: BaseSelectInputValue<string>[] = [
      {
        id: "4",
        content: < span className="flex gap-1 items-center" >
          <CircleCheck className="w-5 h-5" strokeWidth={1.5} />
          {t('listing.filter.typeProperty.optionTwo.subOption.optionOne.title')}
        </ span>
        ,
        value: listingCategory.villas
      },
      {
        id: "5",
        content: < span className="flex gap-1 items-center" >
          <CircleCheck className="w-5 h-5" strokeWidth={1.5} />
          {t('listing.filter.typeProperty.optionTwo.subOption.optionTwo.title')}
        </ span>,
        value: listingCategory.apartment
      },
      {
        id: "6",
        content: < span className="flex gap-1 items-center" >
          <CircleCheck className="w-5 h-5" strokeWidth={1.5} />
          {t('listing.filter.typeProperty.optionTwo.subOption.optionThree.title')}
        </ span >,
        value: listingCategory.rooms
      },
    ]

    switch (typeProperty) {
      case seekersListingFilterType.anything:
        setTitle("")
        setSubtitle("")
        setOptions([])
        clearSubTypeProperty()
        return
      case seekersListingFilterType.business:
        setTitle(t('listing.filter.typeProperty.optionThree.title'))
        setSubtitle(t('listing.filter.typeProperty.optionThree.description'))
        setOptions(businessSubOptions)
        if (!arraysShareValue(subTypeProperty, [businessBuildingSize.small.key, businessBuildingSize.medium.key, businessBuildingSize.large.key])) {
          clearSubTypeProperty()
          setSubTypeProperty(businessBuildingSize.medium.key)
        }
        return
      case seekersListingFilterType.placeToLive:
        setTitle(t('listing.filter.typeProperty.optionTwo.title'))
        setSubtitle(t('listing.filter.typeProperty.optionTwo.description'))
        setOptions(placeToLiveOptions)
        if (!arraysShareValue(subTypeProperty, [listingCategory.villa, listingCategory.apartment, listingCategory.rooms])) {
          clearSubTypeProperty()
          setSubTypeProperty(listingCategory.villa)
          setSubTypeProperty(listingCategory.apartment)
          setSubTypeProperty(listingCategory.rooms)

        }
        return
      case seekersListingFilterType.land:
        setTitle("")
        setSubtitle("")
        setOptions([])
        return
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clearSubTypeProperty, t, typeProperty])
  return <>
    {(typeProperty !== "" && options.length > 1) &&
      <FilterContentLayout title={title} description={subtitle} >
        <div className="flex gap-3 max-sm:flex-wrap">
          {options.map(item => <CheckboxFilterItem
            key={item.id}
            item={item}
            setValue={handleSetSubTypeProperty}
            isActive={subTypeProperty.includes(item.value)}
            className="w-full text-center items-center justify-center"
          />
          )}
        </div>
      </FilterContentLayout>
    }
  </>
}