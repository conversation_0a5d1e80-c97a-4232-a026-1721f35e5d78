"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zustand";
exports.ids = ["vendor-chunks/zustand"];
exports.modules = {

/***/ "(ssr)/./node_modules/zustand/esm/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/zustand/esm/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   createStore: () => (/* reexport safe */ zustand_vanilla__WEBPACK_IMPORTED_MODULE_0__.createStore),\n/* harmony export */   \"default\": () => (/* binding */ react),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-sync-external-store/shim/with-selector.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/with-selector.js\");\n\n\n\n\n\nconst { useDebugValue } = react__WEBPACK_IMPORTED_MODULE_1__;\nconst { useSyncExternalStoreWithSelector } = use_sync_external_store_shim_with_selector_js__WEBPACK_IMPORTED_MODULE_2__;\nlet didWarnAboutEqualityFn = false;\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity, equalityFn) {\n  if (( false ? 0 : void 0) !== \"production\" && equalityFn && !didWarnAboutEqualityFn) {\n    console.warn(\n      \"[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937\"\n    );\n    didWarnAboutEqualityFn = true;\n  }\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getServerState || api.getInitialState,\n    selector,\n    equalityFn\n  );\n  useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  if (( false ? 0 : void 0) !== \"production\" && typeof createState !== \"function\") {\n    console.warn(\n      \"[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.\"\n    );\n  }\n  const api = typeof createState === \"function\" ? (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_0__.createStore)(createState) : createState;\n  const useBoundStore = (selector, equalityFn) => useStore(api, selector, equalityFn);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\nvar react = (createState) => {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { create } from 'zustand'`.\"\n    );\n  }\n  return create(createState);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvenVzdGFuZC9lc20vaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBOEM7QUFDZDtBQUNDO0FBQ3VEOztBQUV4RixRQUFRLGdCQUFnQixFQUFFLGtDQUFZO0FBQ3RDLFFBQVEsbUNBQW1DLEVBQUUsMEVBQTJCO0FBQ3hFO0FBQ0E7QUFDQTtBQUNBLE9BQU8sTUFBZSxHQUFHLENBQW9CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTyxNQUFlLEdBQUcsQ0FBb0I7QUFDN0M7QUFDQSwyR0FBMkcsV0FBVztBQUN0SDtBQUNBO0FBQ0Esa0RBQWtELDREQUFXO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU8sTUFBZSxHQUFHLENBQW9CO0FBQzdDO0FBQ0Esd0VBQXdFLFNBQVM7QUFDakY7QUFDQTtBQUNBO0FBQ0E7O0FBRThDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvenVzdGFuZC9lc20vaW5kZXgubWpzP2ZjMWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlU3RvcmUgfSBmcm9tICd6dXN0YW5kL3ZhbmlsbGEnO1xuZXhwb3J0ICogZnJvbSAnenVzdGFuZC92YW5pbGxhJztcbmltcG9ydCBSZWFjdEV4cG9ydHMgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHVzZVN5bmNFeHRlcm5hbFN0b3JlRXhwb3J0cyBmcm9tICd1c2Utc3luYy1leHRlcm5hbC1zdG9yZS9zaGltL3dpdGgtc2VsZWN0b3IuanMnO1xuXG5jb25zdCB7IHVzZURlYnVnVmFsdWUgfSA9IFJlYWN0RXhwb3J0cztcbmNvbnN0IHsgdXNlU3luY0V4dGVybmFsU3RvcmVXaXRoU2VsZWN0b3IgfSA9IHVzZVN5bmNFeHRlcm5hbFN0b3JlRXhwb3J0cztcbmxldCBkaWRXYXJuQWJvdXRFcXVhbGl0eUZuID0gZmFsc2U7XG5jb25zdCBpZGVudGl0eSA9IChhcmcpID0+IGFyZztcbmZ1bmN0aW9uIHVzZVN0b3JlKGFwaSwgc2VsZWN0b3IgPSBpZGVudGl0eSwgZXF1YWxpdHlGbikge1xuICBpZiAoKGltcG9ydC5tZXRhLmVudiA/IGltcG9ydC5tZXRhLmVudi5NT0RFIDogdm9pZCAwKSAhPT0gXCJwcm9kdWN0aW9uXCIgJiYgZXF1YWxpdHlGbiAmJiAhZGlkV2FybkFib3V0RXF1YWxpdHlGbikge1xuICAgIGNvbnNvbGUud2FybihcbiAgICAgIFwiW0RFUFJFQ0FURURdIFVzZSBgY3JlYXRlV2l0aEVxdWFsaXR5Rm5gIGluc3RlYWQgb2YgYGNyZWF0ZWAgb3IgdXNlIGB1c2VTdG9yZVdpdGhFcXVhbGl0eUZuYCBpbnN0ZWFkIG9mIGB1c2VTdG9yZWAuIFRoZXkgY2FuIGJlIGltcG9ydGVkIGZyb20gJ3p1c3RhbmQvdHJhZGl0aW9uYWwnLiBodHRwczovL2dpdGh1Yi5jb20vcG1uZHJzL3p1c3RhbmQvZGlzY3Vzc2lvbnMvMTkzN1wiXG4gICAgKTtcbiAgICBkaWRXYXJuQWJvdXRFcXVhbGl0eUZuID0gdHJ1ZTtcbiAgfVxuICBjb25zdCBzbGljZSA9IHVzZVN5bmNFeHRlcm5hbFN0b3JlV2l0aFNlbGVjdG9yKFxuICAgIGFwaS5zdWJzY3JpYmUsXG4gICAgYXBpLmdldFN0YXRlLFxuICAgIGFwaS5nZXRTZXJ2ZXJTdGF0ZSB8fCBhcGkuZ2V0SW5pdGlhbFN0YXRlLFxuICAgIHNlbGVjdG9yLFxuICAgIGVxdWFsaXR5Rm5cbiAgKTtcbiAgdXNlRGVidWdWYWx1ZShzbGljZSk7XG4gIHJldHVybiBzbGljZTtcbn1cbmNvbnN0IGNyZWF0ZUltcGwgPSAoY3JlYXRlU3RhdGUpID0+IHtcbiAgaWYgKChpbXBvcnQubWV0YS5lbnYgPyBpbXBvcnQubWV0YS5lbnYuTU9ERSA6IHZvaWQgMCkgIT09IFwicHJvZHVjdGlvblwiICYmIHR5cGVvZiBjcmVhdGVTdGF0ZSAhPT0gXCJmdW5jdGlvblwiKSB7XG4gICAgY29uc29sZS53YXJuKFxuICAgICAgXCJbREVQUkVDQVRFRF0gUGFzc2luZyBhIHZhbmlsbGEgc3RvcmUgd2lsbCBiZSB1bnN1cHBvcnRlZCBpbiBhIGZ1dHVyZSB2ZXJzaW9uLiBJbnN0ZWFkIHVzZSBgaW1wb3J0IHsgdXNlU3RvcmUgfSBmcm9tICd6dXN0YW5kJ2AuXCJcbiAgICApO1xuICB9XG4gIGNvbnN0IGFwaSA9IHR5cGVvZiBjcmVhdGVTdGF0ZSA9PT0gXCJmdW5jdGlvblwiID8gY3JlYXRlU3RvcmUoY3JlYXRlU3RhdGUpIDogY3JlYXRlU3RhdGU7XG4gIGNvbnN0IHVzZUJvdW5kU3RvcmUgPSAoc2VsZWN0b3IsIGVxdWFsaXR5Rm4pID0+IHVzZVN0b3JlKGFwaSwgc2VsZWN0b3IsIGVxdWFsaXR5Rm4pO1xuICBPYmplY3QuYXNzaWduKHVzZUJvdW5kU3RvcmUsIGFwaSk7XG4gIHJldHVybiB1c2VCb3VuZFN0b3JlO1xufTtcbmNvbnN0IGNyZWF0ZSA9IChjcmVhdGVTdGF0ZSkgPT4gY3JlYXRlU3RhdGUgPyBjcmVhdGVJbXBsKGNyZWF0ZVN0YXRlKSA6IGNyZWF0ZUltcGw7XG52YXIgcmVhY3QgPSAoY3JlYXRlU3RhdGUpID0+IHtcbiAgaWYgKChpbXBvcnQubWV0YS5lbnYgPyBpbXBvcnQubWV0YS5lbnYuTU9ERSA6IHZvaWQgMCkgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgY29uc29sZS53YXJuKFxuICAgICAgXCJbREVQUkVDQVRFRF0gRGVmYXVsdCBleHBvcnQgaXMgZGVwcmVjYXRlZC4gSW5zdGVhZCB1c2UgYGltcG9ydCB7IGNyZWF0ZSB9IGZyb20gJ3p1c3RhbmQnYC5cIlxuICAgICk7XG4gIH1cbiAgcmV0dXJuIGNyZWF0ZShjcmVhdGVTdGF0ZSk7XG59O1xuXG5leHBvcnQgeyBjcmVhdGUsIHJlYWN0IGFzIGRlZmF1bHQsIHVzZVN0b3JlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/middleware.mjs":
/*!*************************************************!*\
  !*** ./node_modules/zustand/esm/middleware.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combine: () => (/* binding */ combine),\n/* harmony export */   createJSONStorage: () => (/* binding */ createJSONStorage),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   persist: () => (/* binding */ persist),\n/* harmony export */   redux: () => (/* binding */ redux),\n/* harmony export */   subscribeWithSelector: () => (/* binding */ subscribeWithSelector)\n/* harmony export */ });\nconst reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...a) => api.dispatch(...a), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (_e) {\n  }\n  if (!extensionConnector) {\n    if (( false ? 0 : void 0) !== \"production\" && enabled) {\n      console.warn(\n        \"[zustand devtools middleware] Please install/enable Redux devtools extension\"\n      );\n    }\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === void 0 ? { type: anonymousActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...a) => {\n      if (( false ? 0 : void 0) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...a);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format. \n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) f(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nconst combine = (initialState, create) => (...a) => Object.assign({}, initialState, create(...a));\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (_e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(\n      name,\n      JSON.stringify(newValue, options == null ? void 0 : options.replacer)\n    ),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst oldImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    getStorage: () => localStorage,\n    serialize: JSON.stringify,\n    deserialize: JSON.parse,\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage;\n  try {\n    storage = options.getStorage();\n  } catch (_e) {\n  }\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const thenableSerialize = toThenable(options.serialize);\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    let errorInSync;\n    const thenable = thenableSerialize({ state, version: options.version }).then(\n      (serializedValue) => storage.setItem(options.name, serializedValue)\n    ).catch((e) => {\n      errorInSync = e;\n    });\n    if (errorInSync) {\n      throw errorInSync;\n    }\n    return thenable;\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => cb(get()));\n    const postRehydrationCallback = ((_a = options.onRehydrateStorage) == null ? void 0 : _a.call(options, get())) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((storageValue) => {\n      if (storageValue) {\n        return options.deserialize(storageValue);\n      }\n    }).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return deserializedStorageValue.state;\n        }\n      }\n    }).then((migratedState) => {\n      var _a2;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      return setItem();\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.getStorage) {\n        storage = newOptions.getStorage();\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  hydrate();\n  return stateFromStorage || configResult;\n};\nconst newImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return [\n              true,\n              options.migrate(\n                deserializedStorageValue.state,\n                deserializedStorageValue.version\n              )\n            ];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persistImpl = (config, baseOptions) => {\n  if (\"getStorage\" in baseOptions || \"serialize\" in baseOptions || \"deserialize\" in baseOptions) {\n    if (( false ? 0 : void 0) !== \"production\") {\n      console.warn(\n        \"[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead.\"\n      );\n    }\n    return oldImpl(config, baseOptions);\n  }\n  return newImpl(config, baseOptions);\n};\nconst persist = persistImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvenVzdGFuZC9lc20vbWlkZGxld2FyZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQSw4Q0FBOEM7QUFDOUMsVUFBVSxrREFBa0Q7QUFDNUQ7QUFDQTtBQUNBLHVEQUF1RCxNQUFlLEdBQUcsQ0FBb0I7QUFDN0YsSUFBSTtBQUNKO0FBQ0E7QUFDQSxTQUFTLE1BQWUsR0FBRyxDQUFvQjtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLHVDQUF1QztBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtDQUErQywyQ0FBMkMsdUNBQXVDLHFCQUFxQjtBQUN0SjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixNQUFNLEdBQUcsWUFBWTtBQUN0QyxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsTUFBZSxHQUFHLENBQW9CO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0ZBQW9GLGlDQUFpQyxpQkFBaUI7QUFDdEk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxvQkFBb0Isa0JBQWtCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG9FQUFvRTs7QUFFcEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLGFBQWE7QUFDOUU7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsVUFBVTtBQUNqRDtBQUNBLHlDQUF5QyxpQ0FBaUM7QUFDMUU7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSxhQUFhO0FBQzlFO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxVQUFVO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLE1BQWUsR0FBRyxDQUFvQjtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXVGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvenVzdGFuZC9lc20vbWlkZGxld2FyZS5tanM/YmZhOCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCByZWR1eEltcGwgPSAocmVkdWNlciwgaW5pdGlhbCkgPT4gKHNldCwgX2dldCwgYXBpKSA9PiB7XG4gIGFwaS5kaXNwYXRjaCA9IChhY3Rpb24pID0+IHtcbiAgICBzZXQoKHN0YXRlKSA9PiByZWR1Y2VyKHN0YXRlLCBhY3Rpb24pLCBmYWxzZSwgYWN0aW9uKTtcbiAgICByZXR1cm4gYWN0aW9uO1xuICB9O1xuICBhcGkuZGlzcGF0Y2hGcm9tRGV2dG9vbHMgPSB0cnVlO1xuICByZXR1cm4geyBkaXNwYXRjaDogKC4uLmEpID0+IGFwaS5kaXNwYXRjaCguLi5hKSwgLi4uaW5pdGlhbCB9O1xufTtcbmNvbnN0IHJlZHV4ID0gcmVkdXhJbXBsO1xuXG5jb25zdCB0cmFja2VkQ29ubmVjdGlvbnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpO1xuY29uc3QgZ2V0VHJhY2tlZENvbm5lY3Rpb25TdGF0ZSA9IChuYW1lKSA9PiB7XG4gIGNvbnN0IGFwaSA9IHRyYWNrZWRDb25uZWN0aW9ucy5nZXQobmFtZSk7XG4gIGlmICghYXBpKSByZXR1cm4ge307XG4gIHJldHVybiBPYmplY3QuZnJvbUVudHJpZXMoXG4gICAgT2JqZWN0LmVudHJpZXMoYXBpLnN0b3JlcykubWFwKChba2V5LCBhcGkyXSkgPT4gW2tleSwgYXBpMi5nZXRTdGF0ZSgpXSlcbiAgKTtcbn07XG5jb25zdCBleHRyYWN0Q29ubmVjdGlvbkluZm9ybWF0aW9uID0gKHN0b3JlLCBleHRlbnNpb25Db25uZWN0b3IsIG9wdGlvbnMpID0+IHtcbiAgaWYgKHN0b3JlID09PSB2b2lkIDApIHtcbiAgICByZXR1cm4ge1xuICAgICAgdHlwZTogXCJ1bnRyYWNrZWRcIixcbiAgICAgIGNvbm5lY3Rpb246IGV4dGVuc2lvbkNvbm5lY3Rvci5jb25uZWN0KG9wdGlvbnMpXG4gICAgfTtcbiAgfVxuICBjb25zdCBleGlzdGluZ0Nvbm5lY3Rpb24gPSB0cmFja2VkQ29ubmVjdGlvbnMuZ2V0KG9wdGlvbnMubmFtZSk7XG4gIGlmIChleGlzdGluZ0Nvbm5lY3Rpb24pIHtcbiAgICByZXR1cm4geyB0eXBlOiBcInRyYWNrZWRcIiwgc3RvcmUsIC4uLmV4aXN0aW5nQ29ubmVjdGlvbiB9O1xuICB9XG4gIGNvbnN0IG5ld0Nvbm5lY3Rpb24gPSB7XG4gICAgY29ubmVjdGlvbjogZXh0ZW5zaW9uQ29ubmVjdG9yLmNvbm5lY3Qob3B0aW9ucyksXG4gICAgc3RvcmVzOiB7fVxuICB9O1xuICB0cmFja2VkQ29ubmVjdGlvbnMuc2V0KG9wdGlvbnMubmFtZSwgbmV3Q29ubmVjdGlvbik7XG4gIHJldHVybiB7IHR5cGU6IFwidHJhY2tlZFwiLCBzdG9yZSwgLi4ubmV3Q29ubmVjdGlvbiB9O1xufTtcbmNvbnN0IGRldnRvb2xzSW1wbCA9IChmbiwgZGV2dG9vbHNPcHRpb25zID0ge30pID0+IChzZXQsIGdldCwgYXBpKSA9PiB7XG4gIGNvbnN0IHsgZW5hYmxlZCwgYW5vbnltb3VzQWN0aW9uVHlwZSwgc3RvcmUsIC4uLm9wdGlvbnMgfSA9IGRldnRvb2xzT3B0aW9ucztcbiAgbGV0IGV4dGVuc2lvbkNvbm5lY3RvcjtcbiAgdHJ5IHtcbiAgICBleHRlbnNpb25Db25uZWN0b3IgPSAoZW5hYmxlZCAhPSBudWxsID8gZW5hYmxlZCA6IChpbXBvcnQubWV0YS5lbnYgPyBpbXBvcnQubWV0YS5lbnYuTU9ERSA6IHZvaWQgMCkgIT09IFwicHJvZHVjdGlvblwiKSAmJiB3aW5kb3cuX19SRURVWF9ERVZUT09MU19FWFRFTlNJT05fXztcbiAgfSBjYXRjaCAoX2UpIHtcbiAgfVxuICBpZiAoIWV4dGVuc2lvbkNvbm5lY3Rvcikge1xuICAgIGlmICgoaW1wb3J0Lm1ldGEuZW52ID8gaW1wb3J0Lm1ldGEuZW52Lk1PREUgOiB2b2lkIDApICE9PSBcInByb2R1Y3Rpb25cIiAmJiBlbmFibGVkKSB7XG4gICAgICBjb25zb2xlLndhcm4oXG4gICAgICAgIFwiW3p1c3RhbmQgZGV2dG9vbHMgbWlkZGxld2FyZV0gUGxlYXNlIGluc3RhbGwvZW5hYmxlIFJlZHV4IGRldnRvb2xzIGV4dGVuc2lvblwiXG4gICAgICApO1xuICAgIH1cbiAgICByZXR1cm4gZm4oc2V0LCBnZXQsIGFwaSk7XG4gIH1cbiAgY29uc3QgeyBjb25uZWN0aW9uLCAuLi5jb25uZWN0aW9uSW5mb3JtYXRpb24gfSA9IGV4dHJhY3RDb25uZWN0aW9uSW5mb3JtYXRpb24oc3RvcmUsIGV4dGVuc2lvbkNvbm5lY3Rvciwgb3B0aW9ucyk7XG4gIGxldCBpc1JlY29yZGluZyA9IHRydWU7XG4gIGFwaS5zZXRTdGF0ZSA9IChzdGF0ZSwgcmVwbGFjZSwgbmFtZU9yQWN0aW9uKSA9PiB7XG4gICAgY29uc3QgciA9IHNldChzdGF0ZSwgcmVwbGFjZSk7XG4gICAgaWYgKCFpc1JlY29yZGluZykgcmV0dXJuIHI7XG4gICAgY29uc3QgYWN0aW9uID0gbmFtZU9yQWN0aW9uID09PSB2b2lkIDAgPyB7IHR5cGU6IGFub255bW91c0FjdGlvblR5cGUgfHwgXCJhbm9ueW1vdXNcIiB9IDogdHlwZW9mIG5hbWVPckFjdGlvbiA9PT0gXCJzdHJpbmdcIiA/IHsgdHlwZTogbmFtZU9yQWN0aW9uIH0gOiBuYW1lT3JBY3Rpb247XG4gICAgaWYgKHN0b3JlID09PSB2b2lkIDApIHtcbiAgICAgIGNvbm5lY3Rpb24gPT0gbnVsbCA/IHZvaWQgMCA6IGNvbm5lY3Rpb24uc2VuZChhY3Rpb24sIGdldCgpKTtcbiAgICAgIHJldHVybiByO1xuICAgIH1cbiAgICBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLnNlbmQoXG4gICAgICB7XG4gICAgICAgIC4uLmFjdGlvbixcbiAgICAgICAgdHlwZTogYCR7c3RvcmV9LyR7YWN0aW9uLnR5cGV9YFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgLi4uZ2V0VHJhY2tlZENvbm5lY3Rpb25TdGF0ZShvcHRpb25zLm5hbWUpLFxuICAgICAgICBbc3RvcmVdOiBhcGkuZ2V0U3RhdGUoKVxuICAgICAgfVxuICAgICk7XG4gICAgcmV0dXJuIHI7XG4gIH07XG4gIGNvbnN0IHNldFN0YXRlRnJvbURldnRvb2xzID0gKC4uLmEpID0+IHtcbiAgICBjb25zdCBvcmlnaW5hbElzUmVjb3JkaW5nID0gaXNSZWNvcmRpbmc7XG4gICAgaXNSZWNvcmRpbmcgPSBmYWxzZTtcbiAgICBzZXQoLi4uYSk7XG4gICAgaXNSZWNvcmRpbmcgPSBvcmlnaW5hbElzUmVjb3JkaW5nO1xuICB9O1xuICBjb25zdCBpbml0aWFsU3RhdGUgPSBmbihhcGkuc2V0U3RhdGUsIGdldCwgYXBpKTtcbiAgaWYgKGNvbm5lY3Rpb25JbmZvcm1hdGlvbi50eXBlID09PSBcInVudHJhY2tlZFwiKSB7XG4gICAgY29ubmVjdGlvbiA9PSBudWxsID8gdm9pZCAwIDogY29ubmVjdGlvbi5pbml0KGluaXRpYWxTdGF0ZSk7XG4gIH0gZWxzZSB7XG4gICAgY29ubmVjdGlvbkluZm9ybWF0aW9uLnN0b3Jlc1tjb25uZWN0aW9uSW5mb3JtYXRpb24uc3RvcmVdID0gYXBpO1xuICAgIGNvbm5lY3Rpb24gPT0gbnVsbCA/IHZvaWQgMCA6IGNvbm5lY3Rpb24uaW5pdChcbiAgICAgIE9iamVjdC5mcm9tRW50cmllcyhcbiAgICAgICAgT2JqZWN0LmVudHJpZXMoY29ubmVjdGlvbkluZm9ybWF0aW9uLnN0b3JlcykubWFwKChba2V5LCBzdG9yZTJdKSA9PiBbXG4gICAgICAgICAga2V5LFxuICAgICAgICAgIGtleSA9PT0gY29ubmVjdGlvbkluZm9ybWF0aW9uLnN0b3JlID8gaW5pdGlhbFN0YXRlIDogc3RvcmUyLmdldFN0YXRlKClcbiAgICAgICAgXSlcbiAgICAgIClcbiAgICApO1xuICB9XG4gIGlmIChhcGkuZGlzcGF0Y2hGcm9tRGV2dG9vbHMgJiYgdHlwZW9mIGFwaS5kaXNwYXRjaCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgbGV0IGRpZFdhcm5BYm91dFJlc2VydmVkQWN0aW9uVHlwZSA9IGZhbHNlO1xuICAgIGNvbnN0IG9yaWdpbmFsRGlzcGF0Y2ggPSBhcGkuZGlzcGF0Y2g7XG4gICAgYXBpLmRpc3BhdGNoID0gKC4uLmEpID0+IHtcbiAgICAgIGlmICgoaW1wb3J0Lm1ldGEuZW52ID8gaW1wb3J0Lm1ldGEuZW52Lk1PREUgOiB2b2lkIDApICE9PSBcInByb2R1Y3Rpb25cIiAmJiBhWzBdLnR5cGUgPT09IFwiX19zZXRTdGF0ZVwiICYmICFkaWRXYXJuQWJvdXRSZXNlcnZlZEFjdGlvblR5cGUpIHtcbiAgICAgICAgY29uc29sZS53YXJuKFxuICAgICAgICAgICdbenVzdGFuZCBkZXZ0b29scyBtaWRkbGV3YXJlXSBcIl9fc2V0U3RhdGVcIiBhY3Rpb24gdHlwZSBpcyByZXNlcnZlZCB0byBzZXQgc3RhdGUgZnJvbSB0aGUgZGV2dG9vbHMuIEF2b2lkIHVzaW5nIGl0LidcbiAgICAgICAgKTtcbiAgICAgICAgZGlkV2FybkFib3V0UmVzZXJ2ZWRBY3Rpb25UeXBlID0gdHJ1ZTtcbiAgICAgIH1cbiAgICAgIG9yaWdpbmFsRGlzcGF0Y2goLi4uYSk7XG4gICAgfTtcbiAgfVxuICBjb25uZWN0aW9uLnN1YnNjcmliZSgobWVzc2FnZSkgPT4ge1xuICAgIHZhciBfYTtcbiAgICBzd2l0Y2ggKG1lc3NhZ2UudHlwZSkge1xuICAgICAgY2FzZSBcIkFDVElPTlwiOlxuICAgICAgICBpZiAodHlwZW9mIG1lc3NhZ2UucGF5bG9hZCAhPT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICBcIlt6dXN0YW5kIGRldnRvb2xzIG1pZGRsZXdhcmVdIFVuc3VwcG9ydGVkIGFjdGlvbiBmb3JtYXRcIlxuICAgICAgICAgICk7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBwYXJzZUpzb25UaGVuKFxuICAgICAgICAgIG1lc3NhZ2UucGF5bG9hZCxcbiAgICAgICAgICAoYWN0aW9uKSA9PiB7XG4gICAgICAgICAgICBpZiAoYWN0aW9uLnR5cGUgPT09IFwiX19zZXRTdGF0ZVwiKSB7XG4gICAgICAgICAgICAgIGlmIChzdG9yZSA9PT0gdm9pZCAwKSB7XG4gICAgICAgICAgICAgICAgc2V0U3RhdGVGcm9tRGV2dG9vbHMoYWN0aW9uLnN0YXRlKTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgaWYgKE9iamVjdC5rZXlzKGFjdGlvbi5zdGF0ZSkubGVuZ3RoICE9PSAxKSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICAgICAgIGBcbiAgICAgICAgICAgICAgICAgICAgW3p1c3RhbmQgZGV2dG9vbHMgbWlkZGxld2FyZV0gVW5zdXBwb3J0ZWQgX19zZXRTdGF0ZSBhY3Rpb24gZm9ybWF0LiBcbiAgICAgICAgICAgICAgICAgICAgV2hlbiB1c2luZyAnc3RvcmUnIG9wdGlvbiBpbiBkZXZ0b29scygpLCB0aGUgJ3N0YXRlJyBzaG91bGQgaGF2ZSBvbmx5IG9uZSBrZXksIHdoaWNoIGlzIGEgdmFsdWUgb2YgJ3N0b3JlJyB0aGF0IHdhcyBwYXNzZWQgaW4gZGV2dG9vbHMoKSxcbiAgICAgICAgICAgICAgICAgICAgYW5kIHZhbHVlIG9mIHRoaXMgb25seSBrZXkgc2hvdWxkIGJlIGEgc3RhdGUgb2JqZWN0LiBFeGFtcGxlOiB7IFwidHlwZVwiOiBcIl9fc2V0U3RhdGVcIiwgXCJzdGF0ZVwiOiB7IFwiYWJjMTIzU3RvcmVcIjogeyBcImZvb1wiOiBcImJhclwiIH0gfSB9XG4gICAgICAgICAgICAgICAgICAgIGBcbiAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGNvbnN0IHN0YXRlRnJvbURldnRvb2xzID0gYWN0aW9uLnN0YXRlW3N0b3JlXTtcbiAgICAgICAgICAgICAgaWYgKHN0YXRlRnJvbURldnRvb2xzID09PSB2b2lkIDAgfHwgc3RhdGVGcm9tRGV2dG9vbHMgPT09IG51bGwpIHtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgaWYgKEpTT04uc3RyaW5naWZ5KGFwaS5nZXRTdGF0ZSgpKSAhPT0gSlNPTi5zdHJpbmdpZnkoc3RhdGVGcm9tRGV2dG9vbHMpKSB7XG4gICAgICAgICAgICAgICAgc2V0U3RhdGVGcm9tRGV2dG9vbHMoc3RhdGVGcm9tRGV2dG9vbHMpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghYXBpLmRpc3BhdGNoRnJvbURldnRvb2xzKSByZXR1cm47XG4gICAgICAgICAgICBpZiAodHlwZW9mIGFwaS5kaXNwYXRjaCAhPT0gXCJmdW5jdGlvblwiKSByZXR1cm47XG4gICAgICAgICAgICBhcGkuZGlzcGF0Y2goYWN0aW9uKTtcbiAgICAgICAgICB9XG4gICAgICAgICk7XG4gICAgICBjYXNlIFwiRElTUEFUQ0hcIjpcbiAgICAgICAgc3dpdGNoIChtZXNzYWdlLnBheWxvYWQudHlwZSkge1xuICAgICAgICAgIGNhc2UgXCJSRVNFVFwiOlxuICAgICAgICAgICAgc2V0U3RhdGVGcm9tRGV2dG9vbHMoaW5pdGlhbFN0YXRlKTtcbiAgICAgICAgICAgIGlmIChzdG9yZSA9PT0gdm9pZCAwKSB7XG4gICAgICAgICAgICAgIHJldHVybiBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLmluaXQoYXBpLmdldFN0YXRlKCkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGNvbm5lY3Rpb24gPT0gbnVsbCA/IHZvaWQgMCA6IGNvbm5lY3Rpb24uaW5pdChnZXRUcmFja2VkQ29ubmVjdGlvblN0YXRlKG9wdGlvbnMubmFtZSkpO1xuICAgICAgICAgIGNhc2UgXCJDT01NSVRcIjpcbiAgICAgICAgICAgIGlmIChzdG9yZSA9PT0gdm9pZCAwKSB7XG4gICAgICAgICAgICAgIGNvbm5lY3Rpb24gPT0gbnVsbCA/IHZvaWQgMCA6IGNvbm5lY3Rpb24uaW5pdChhcGkuZ2V0U3RhdGUoKSk7XG4gICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLmluaXQoZ2V0VHJhY2tlZENvbm5lY3Rpb25TdGF0ZShvcHRpb25zLm5hbWUpKTtcbiAgICAgICAgICBjYXNlIFwiUk9MTEJBQ0tcIjpcbiAgICAgICAgICAgIHJldHVybiBwYXJzZUpzb25UaGVuKG1lc3NhZ2Uuc3RhdGUsIChzdGF0ZSkgPT4ge1xuICAgICAgICAgICAgICBpZiAoc3RvcmUgPT09IHZvaWQgMCkge1xuICAgICAgICAgICAgICAgIHNldFN0YXRlRnJvbURldnRvb2xzKHN0YXRlKTtcbiAgICAgICAgICAgICAgICBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLmluaXQoYXBpLmdldFN0YXRlKCkpO1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICBzZXRTdGF0ZUZyb21EZXZ0b29scyhzdGF0ZVtzdG9yZV0pO1xuICAgICAgICAgICAgICBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLmluaXQoZ2V0VHJhY2tlZENvbm5lY3Rpb25TdGF0ZShvcHRpb25zLm5hbWUpKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIGNhc2UgXCJKVU1QX1RPX1NUQVRFXCI6XG4gICAgICAgICAgY2FzZSBcIkpVTVBfVE9fQUNUSU9OXCI6XG4gICAgICAgICAgICByZXR1cm4gcGFyc2VKc29uVGhlbihtZXNzYWdlLnN0YXRlLCAoc3RhdGUpID0+IHtcbiAgICAgICAgICAgICAgaWYgKHN0b3JlID09PSB2b2lkIDApIHtcbiAgICAgICAgICAgICAgICBzZXRTdGF0ZUZyb21EZXZ0b29scyhzdGF0ZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIGlmIChKU09OLnN0cmluZ2lmeShhcGkuZ2V0U3RhdGUoKSkgIT09IEpTT04uc3RyaW5naWZ5KHN0YXRlW3N0b3JlXSkpIHtcbiAgICAgICAgICAgICAgICBzZXRTdGF0ZUZyb21EZXZ0b29scyhzdGF0ZVtzdG9yZV0pO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICBjYXNlIFwiSU1QT1JUX1NUQVRFXCI6IHtcbiAgICAgICAgICAgIGNvbnN0IHsgbmV4dExpZnRlZFN0YXRlIH0gPSBtZXNzYWdlLnBheWxvYWQ7XG4gICAgICAgICAgICBjb25zdCBsYXN0Q29tcHV0ZWRTdGF0ZSA9IChfYSA9IG5leHRMaWZ0ZWRTdGF0ZS5jb21wdXRlZFN0YXRlcy5zbGljZSgtMSlbMF0pID09IG51bGwgPyB2b2lkIDAgOiBfYS5zdGF0ZTtcbiAgICAgICAgICAgIGlmICghbGFzdENvbXB1dGVkU3RhdGUpIHJldHVybjtcbiAgICAgICAgICAgIGlmIChzdG9yZSA9PT0gdm9pZCAwKSB7XG4gICAgICAgICAgICAgIHNldFN0YXRlRnJvbURldnRvb2xzKGxhc3RDb21wdXRlZFN0YXRlKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIHNldFN0YXRlRnJvbURldnRvb2xzKGxhc3RDb21wdXRlZFN0YXRlW3N0b3JlXSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25uZWN0aW9uID09IG51bGwgPyB2b2lkIDAgOiBjb25uZWN0aW9uLnNlbmQoXG4gICAgICAgICAgICAgIG51bGwsXG4gICAgICAgICAgICAgIC8vIEZJWE1FIG5vLWFueVxuICAgICAgICAgICAgICBuZXh0TGlmdGVkU3RhdGVcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuICAgICAgICAgIGNhc2UgXCJQQVVTRV9SRUNPUkRJTkdcIjpcbiAgICAgICAgICAgIHJldHVybiBpc1JlY29yZGluZyA9ICFpc1JlY29yZGluZztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm47XG4gICAgfVxuICB9KTtcbiAgcmV0dXJuIGluaXRpYWxTdGF0ZTtcbn07XG5jb25zdCBkZXZ0b29scyA9IGRldnRvb2xzSW1wbDtcbmNvbnN0IHBhcnNlSnNvblRoZW4gPSAoc3RyaW5naWZpZWQsIGYpID0+IHtcbiAgbGV0IHBhcnNlZDtcbiAgdHJ5IHtcbiAgICBwYXJzZWQgPSBKU09OLnBhcnNlKHN0cmluZ2lmaWVkKTtcbiAgfSBjYXRjaCAoZSkge1xuICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICBcIlt6dXN0YW5kIGRldnRvb2xzIG1pZGRsZXdhcmVdIENvdWxkIG5vdCBwYXJzZSB0aGUgcmVjZWl2ZWQganNvblwiLFxuICAgICAgZVxuICAgICk7XG4gIH1cbiAgaWYgKHBhcnNlZCAhPT0gdm9pZCAwKSBmKHBhcnNlZCk7XG59O1xuXG5jb25zdCBzdWJzY3JpYmVXaXRoU2VsZWN0b3JJbXBsID0gKGZuKSA9PiAoc2V0LCBnZXQsIGFwaSkgPT4ge1xuICBjb25zdCBvcmlnU3Vic2NyaWJlID0gYXBpLnN1YnNjcmliZTtcbiAgYXBpLnN1YnNjcmliZSA9IChzZWxlY3Rvciwgb3B0TGlzdGVuZXIsIG9wdGlvbnMpID0+IHtcbiAgICBsZXQgbGlzdGVuZXIgPSBzZWxlY3RvcjtcbiAgICBpZiAob3B0TGlzdGVuZXIpIHtcbiAgICAgIGNvbnN0IGVxdWFsaXR5Rm4gPSAob3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5lcXVhbGl0eUZuKSB8fCBPYmplY3QuaXM7XG4gICAgICBsZXQgY3VycmVudFNsaWNlID0gc2VsZWN0b3IoYXBpLmdldFN0YXRlKCkpO1xuICAgICAgbGlzdGVuZXIgPSAoc3RhdGUpID0+IHtcbiAgICAgICAgY29uc3QgbmV4dFNsaWNlID0gc2VsZWN0b3Ioc3RhdGUpO1xuICAgICAgICBpZiAoIWVxdWFsaXR5Rm4oY3VycmVudFNsaWNlLCBuZXh0U2xpY2UpKSB7XG4gICAgICAgICAgY29uc3QgcHJldmlvdXNTbGljZSA9IGN1cnJlbnRTbGljZTtcbiAgICAgICAgICBvcHRMaXN0ZW5lcihjdXJyZW50U2xpY2UgPSBuZXh0U2xpY2UsIHByZXZpb3VzU2xpY2UpO1xuICAgICAgICB9XG4gICAgICB9O1xuICAgICAgaWYgKG9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMuZmlyZUltbWVkaWF0ZWx5KSB7XG4gICAgICAgIG9wdExpc3RlbmVyKGN1cnJlbnRTbGljZSwgY3VycmVudFNsaWNlKTtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG9yaWdTdWJzY3JpYmUobGlzdGVuZXIpO1xuICB9O1xuICBjb25zdCBpbml0aWFsU3RhdGUgPSBmbihzZXQsIGdldCwgYXBpKTtcbiAgcmV0dXJuIGluaXRpYWxTdGF0ZTtcbn07XG5jb25zdCBzdWJzY3JpYmVXaXRoU2VsZWN0b3IgPSBzdWJzY3JpYmVXaXRoU2VsZWN0b3JJbXBsO1xuXG5jb25zdCBjb21iaW5lID0gKGluaXRpYWxTdGF0ZSwgY3JlYXRlKSA9PiAoLi4uYSkgPT4gT2JqZWN0LmFzc2lnbih7fSwgaW5pdGlhbFN0YXRlLCBjcmVhdGUoLi4uYSkpO1xuXG5mdW5jdGlvbiBjcmVhdGVKU09OU3RvcmFnZShnZXRTdG9yYWdlLCBvcHRpb25zKSB7XG4gIGxldCBzdG9yYWdlO1xuICB0cnkge1xuICAgIHN0b3JhZ2UgPSBnZXRTdG9yYWdlKCk7XG4gIH0gY2F0Y2ggKF9lKSB7XG4gICAgcmV0dXJuO1xuICB9XG4gIGNvbnN0IHBlcnNpc3RTdG9yYWdlID0ge1xuICAgIGdldEl0ZW06IChuYW1lKSA9PiB7XG4gICAgICB2YXIgX2E7XG4gICAgICBjb25zdCBwYXJzZSA9IChzdHIyKSA9PiB7XG4gICAgICAgIGlmIChzdHIyID09PSBudWxsKSB7XG4gICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIEpTT04ucGFyc2Uoc3RyMiwgb3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5yZXZpdmVyKTtcbiAgICAgIH07XG4gICAgICBjb25zdCBzdHIgPSAoX2EgPSBzdG9yYWdlLmdldEl0ZW0obmFtZSkpICE9IG51bGwgPyBfYSA6IG51bGw7XG4gICAgICBpZiAoc3RyIGluc3RhbmNlb2YgUHJvbWlzZSkge1xuICAgICAgICByZXR1cm4gc3RyLnRoZW4ocGFyc2UpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHBhcnNlKHN0cik7XG4gICAgfSxcbiAgICBzZXRJdGVtOiAobmFtZSwgbmV3VmFsdWUpID0+IHN0b3JhZ2Uuc2V0SXRlbShcbiAgICAgIG5hbWUsXG4gICAgICBKU09OLnN0cmluZ2lmeShuZXdWYWx1ZSwgb3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5yZXBsYWNlcilcbiAgICApLFxuICAgIHJlbW92ZUl0ZW06IChuYW1lKSA9PiBzdG9yYWdlLnJlbW92ZUl0ZW0obmFtZSlcbiAgfTtcbiAgcmV0dXJuIHBlcnNpc3RTdG9yYWdlO1xufVxuY29uc3QgdG9UaGVuYWJsZSA9IChmbikgPT4gKGlucHV0KSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gZm4oaW5wdXQpO1xuICAgIGlmIChyZXN1bHQgaW5zdGFuY2VvZiBQcm9taXNlKSB7XG4gICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgdGhlbihvbkZ1bGZpbGxlZCkge1xuICAgICAgICByZXR1cm4gdG9UaGVuYWJsZShvbkZ1bGZpbGxlZCkocmVzdWx0KTtcbiAgICAgIH0sXG4gICAgICBjYXRjaChfb25SZWplY3RlZCkge1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgIH1cbiAgICB9O1xuICB9IGNhdGNoIChlKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIHRoZW4oX29uRnVsZmlsbGVkKSB7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgICAgfSxcbiAgICAgIGNhdGNoKG9uUmVqZWN0ZWQpIHtcbiAgICAgICAgcmV0dXJuIHRvVGhlbmFibGUob25SZWplY3RlZCkoZSk7XG4gICAgICB9XG4gICAgfTtcbiAgfVxufTtcbmNvbnN0IG9sZEltcGwgPSAoY29uZmlnLCBiYXNlT3B0aW9ucykgPT4gKHNldCwgZ2V0LCBhcGkpID0+IHtcbiAgbGV0IG9wdGlvbnMgPSB7XG4gICAgZ2V0U3RvcmFnZTogKCkgPT4gbG9jYWxTdG9yYWdlLFxuICAgIHNlcmlhbGl6ZTogSlNPTi5zdHJpbmdpZnksXG4gICAgZGVzZXJpYWxpemU6IEpTT04ucGFyc2UsXG4gICAgcGFydGlhbGl6ZTogKHN0YXRlKSA9PiBzdGF0ZSxcbiAgICB2ZXJzaW9uOiAwLFxuICAgIG1lcmdlOiAocGVyc2lzdGVkU3RhdGUsIGN1cnJlbnRTdGF0ZSkgPT4gKHtcbiAgICAgIC4uLmN1cnJlbnRTdGF0ZSxcbiAgICAgIC4uLnBlcnNpc3RlZFN0YXRlXG4gICAgfSksXG4gICAgLi4uYmFzZU9wdGlvbnNcbiAgfTtcbiAgbGV0IGhhc0h5ZHJhdGVkID0gZmFsc2U7XG4gIGNvbnN0IGh5ZHJhdGlvbkxpc3RlbmVycyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KCk7XG4gIGNvbnN0IGZpbmlzaEh5ZHJhdGlvbkxpc3RlbmVycyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KCk7XG4gIGxldCBzdG9yYWdlO1xuICB0cnkge1xuICAgIHN0b3JhZ2UgPSBvcHRpb25zLmdldFN0b3JhZ2UoKTtcbiAgfSBjYXRjaCAoX2UpIHtcbiAgfVxuICBpZiAoIXN0b3JhZ2UpIHtcbiAgICByZXR1cm4gY29uZmlnKFxuICAgICAgKC4uLmFyZ3MpID0+IHtcbiAgICAgICAgY29uc29sZS53YXJuKFxuICAgICAgICAgIGBbenVzdGFuZCBwZXJzaXN0IG1pZGRsZXdhcmVdIFVuYWJsZSB0byB1cGRhdGUgaXRlbSAnJHtvcHRpb25zLm5hbWV9JywgdGhlIGdpdmVuIHN0b3JhZ2UgaXMgY3VycmVudGx5IHVuYXZhaWxhYmxlLmBcbiAgICAgICAgKTtcbiAgICAgICAgc2V0KC4uLmFyZ3MpO1xuICAgICAgfSxcbiAgICAgIGdldCxcbiAgICAgIGFwaVxuICAgICk7XG4gIH1cbiAgY29uc3QgdGhlbmFibGVTZXJpYWxpemUgPSB0b1RoZW5hYmxlKG9wdGlvbnMuc2VyaWFsaXplKTtcbiAgY29uc3Qgc2V0SXRlbSA9ICgpID0+IHtcbiAgICBjb25zdCBzdGF0ZSA9IG9wdGlvbnMucGFydGlhbGl6ZSh7IC4uLmdldCgpIH0pO1xuICAgIGxldCBlcnJvckluU3luYztcbiAgICBjb25zdCB0aGVuYWJsZSA9IHRoZW5hYmxlU2VyaWFsaXplKHsgc3RhdGUsIHZlcnNpb246IG9wdGlvbnMudmVyc2lvbiB9KS50aGVuKFxuICAgICAgKHNlcmlhbGl6ZWRWYWx1ZSkgPT4gc3RvcmFnZS5zZXRJdGVtKG9wdGlvbnMubmFtZSwgc2VyaWFsaXplZFZhbHVlKVxuICAgICkuY2F0Y2goKGUpID0+IHtcbiAgICAgIGVycm9ySW5TeW5jID0gZTtcbiAgICB9KTtcbiAgICBpZiAoZXJyb3JJblN5bmMpIHtcbiAgICAgIHRocm93IGVycm9ySW5TeW5jO1xuICAgIH1cbiAgICByZXR1cm4gdGhlbmFibGU7XG4gIH07XG4gIGNvbnN0IHNhdmVkU2V0U3RhdGUgPSBhcGkuc2V0U3RhdGU7XG4gIGFwaS5zZXRTdGF0ZSA9IChzdGF0ZSwgcmVwbGFjZSkgPT4ge1xuICAgIHNhdmVkU2V0U3RhdGUoc3RhdGUsIHJlcGxhY2UpO1xuICAgIHZvaWQgc2V0SXRlbSgpO1xuICB9O1xuICBjb25zdCBjb25maWdSZXN1bHQgPSBjb25maWcoXG4gICAgKC4uLmFyZ3MpID0+IHtcbiAgICAgIHNldCguLi5hcmdzKTtcbiAgICAgIHZvaWQgc2V0SXRlbSgpO1xuICAgIH0sXG4gICAgZ2V0LFxuICAgIGFwaVxuICApO1xuICBsZXQgc3RhdGVGcm9tU3RvcmFnZTtcbiAgY29uc3QgaHlkcmF0ZSA9ICgpID0+IHtcbiAgICB2YXIgX2E7XG4gICAgaWYgKCFzdG9yYWdlKSByZXR1cm47XG4gICAgaGFzSHlkcmF0ZWQgPSBmYWxzZTtcbiAgICBoeWRyYXRpb25MaXN0ZW5lcnMuZm9yRWFjaCgoY2IpID0+IGNiKGdldCgpKSk7XG4gICAgY29uc3QgcG9zdFJlaHlkcmF0aW9uQ2FsbGJhY2sgPSAoKF9hID0gb3B0aW9ucy5vblJlaHlkcmF0ZVN0b3JhZ2UpID09IG51bGwgPyB2b2lkIDAgOiBfYS5jYWxsKG9wdGlvbnMsIGdldCgpKSkgfHwgdm9pZCAwO1xuICAgIHJldHVybiB0b1RoZW5hYmxlKHN0b3JhZ2UuZ2V0SXRlbS5iaW5kKHN0b3JhZ2UpKShvcHRpb25zLm5hbWUpLnRoZW4oKHN0b3JhZ2VWYWx1ZSkgPT4ge1xuICAgICAgaWYgKHN0b3JhZ2VWYWx1ZSkge1xuICAgICAgICByZXR1cm4gb3B0aW9ucy5kZXNlcmlhbGl6ZShzdG9yYWdlVmFsdWUpO1xuICAgICAgfVxuICAgIH0pLnRoZW4oKGRlc2VyaWFsaXplZFN0b3JhZ2VWYWx1ZSkgPT4ge1xuICAgICAgaWYgKGRlc2VyaWFsaXplZFN0b3JhZ2VWYWx1ZSkge1xuICAgICAgICBpZiAodHlwZW9mIGRlc2VyaWFsaXplZFN0b3JhZ2VWYWx1ZS52ZXJzaW9uID09PSBcIm51bWJlclwiICYmIGRlc2VyaWFsaXplZFN0b3JhZ2VWYWx1ZS52ZXJzaW9uICE9PSBvcHRpb25zLnZlcnNpb24pIHtcbiAgICAgICAgICBpZiAob3B0aW9ucy5taWdyYXRlKSB7XG4gICAgICAgICAgICByZXR1cm4gb3B0aW9ucy5taWdyYXRlKFxuICAgICAgICAgICAgICBkZXNlcmlhbGl6ZWRTdG9yYWdlVmFsdWUuc3RhdGUsXG4gICAgICAgICAgICAgIGRlc2VyaWFsaXplZFN0b3JhZ2VWYWx1ZS52ZXJzaW9uXG4gICAgICAgICAgICApO1xuICAgICAgICAgIH1cbiAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgYFN0YXRlIGxvYWRlZCBmcm9tIHN0b3JhZ2UgY291bGRuJ3QgYmUgbWlncmF0ZWQgc2luY2Ugbm8gbWlncmF0ZSBmdW5jdGlvbiB3YXMgcHJvdmlkZWRgXG4gICAgICAgICAgKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXR1cm4gZGVzZXJpYWxpemVkU3RvcmFnZVZhbHVlLnN0YXRlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSkudGhlbigobWlncmF0ZWRTdGF0ZSkgPT4ge1xuICAgICAgdmFyIF9hMjtcbiAgICAgIHN0YXRlRnJvbVN0b3JhZ2UgPSBvcHRpb25zLm1lcmdlKFxuICAgICAgICBtaWdyYXRlZFN0YXRlLFxuICAgICAgICAoX2EyID0gZ2V0KCkpICE9IG51bGwgPyBfYTIgOiBjb25maWdSZXN1bHRcbiAgICAgICk7XG4gICAgICBzZXQoc3RhdGVGcm9tU3RvcmFnZSwgdHJ1ZSk7XG4gICAgICByZXR1cm4gc2V0SXRlbSgpO1xuICAgIH0pLnRoZW4oKCkgPT4ge1xuICAgICAgcG9zdFJlaHlkcmF0aW9uQ2FsbGJhY2sgPT0gbnVsbCA/IHZvaWQgMCA6IHBvc3RSZWh5ZHJhdGlvbkNhbGxiYWNrKHN0YXRlRnJvbVN0b3JhZ2UsIHZvaWQgMCk7XG4gICAgICBoYXNIeWRyYXRlZCA9IHRydWU7XG4gICAgICBmaW5pc2hIeWRyYXRpb25MaXN0ZW5lcnMuZm9yRWFjaCgoY2IpID0+IGNiKHN0YXRlRnJvbVN0b3JhZ2UpKTtcbiAgICB9KS5jYXRjaCgoZSkgPT4ge1xuICAgICAgcG9zdFJlaHlkcmF0aW9uQ2FsbGJhY2sgPT0gbnVsbCA/IHZvaWQgMCA6IHBvc3RSZWh5ZHJhdGlvbkNhbGxiYWNrKHZvaWQgMCwgZSk7XG4gICAgfSk7XG4gIH07XG4gIGFwaS5wZXJzaXN0ID0ge1xuICAgIHNldE9wdGlvbnM6IChuZXdPcHRpb25zKSA9PiB7XG4gICAgICBvcHRpb25zID0ge1xuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAuLi5uZXdPcHRpb25zXG4gICAgICB9O1xuICAgICAgaWYgKG5ld09wdGlvbnMuZ2V0U3RvcmFnZSkge1xuICAgICAgICBzdG9yYWdlID0gbmV3T3B0aW9ucy5nZXRTdG9yYWdlKCk7XG4gICAgICB9XG4gICAgfSxcbiAgICBjbGVhclN0b3JhZ2U6ICgpID0+IHtcbiAgICAgIHN0b3JhZ2UgPT0gbnVsbCA/IHZvaWQgMCA6IHN0b3JhZ2UucmVtb3ZlSXRlbShvcHRpb25zLm5hbWUpO1xuICAgIH0sXG4gICAgZ2V0T3B0aW9uczogKCkgPT4gb3B0aW9ucyxcbiAgICByZWh5ZHJhdGU6ICgpID0+IGh5ZHJhdGUoKSxcbiAgICBoYXNIeWRyYXRlZDogKCkgPT4gaGFzSHlkcmF0ZWQsXG4gICAgb25IeWRyYXRlOiAoY2IpID0+IHtcbiAgICAgIGh5ZHJhdGlvbkxpc3RlbmVycy5hZGQoY2IpO1xuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgaHlkcmF0aW9uTGlzdGVuZXJzLmRlbGV0ZShjYik7XG4gICAgICB9O1xuICAgIH0sXG4gICAgb25GaW5pc2hIeWRyYXRpb246IChjYikgPT4ge1xuICAgICAgZmluaXNoSHlkcmF0aW9uTGlzdGVuZXJzLmFkZChjYik7XG4gICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICBmaW5pc2hIeWRyYXRpb25MaXN0ZW5lcnMuZGVsZXRlKGNiKTtcbiAgICAgIH07XG4gICAgfVxuICB9O1xuICBoeWRyYXRlKCk7XG4gIHJldHVybiBzdGF0ZUZyb21TdG9yYWdlIHx8IGNvbmZpZ1Jlc3VsdDtcbn07XG5jb25zdCBuZXdJbXBsID0gKGNvbmZpZywgYmFzZU9wdGlvbnMpID0+IChzZXQsIGdldCwgYXBpKSA9PiB7XG4gIGxldCBvcHRpb25zID0ge1xuICAgIHN0b3JhZ2U6IGNyZWF0ZUpTT05TdG9yYWdlKCgpID0+IGxvY2FsU3RvcmFnZSksXG4gICAgcGFydGlhbGl6ZTogKHN0YXRlKSA9PiBzdGF0ZSxcbiAgICB2ZXJzaW9uOiAwLFxuICAgIG1lcmdlOiAocGVyc2lzdGVkU3RhdGUsIGN1cnJlbnRTdGF0ZSkgPT4gKHtcbiAgICAgIC4uLmN1cnJlbnRTdGF0ZSxcbiAgICAgIC4uLnBlcnNpc3RlZFN0YXRlXG4gICAgfSksXG4gICAgLi4uYmFzZU9wdGlvbnNcbiAgfTtcbiAgbGV0IGhhc0h5ZHJhdGVkID0gZmFsc2U7XG4gIGNvbnN0IGh5ZHJhdGlvbkxpc3RlbmVycyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KCk7XG4gIGNvbnN0IGZpbmlzaEh5ZHJhdGlvbkxpc3RlbmVycyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KCk7XG4gIGxldCBzdG9yYWdlID0gb3B0aW9ucy5zdG9yYWdlO1xuICBpZiAoIXN0b3JhZ2UpIHtcbiAgICByZXR1cm4gY29uZmlnKFxuICAgICAgKC4uLmFyZ3MpID0+IHtcbiAgICAgICAgY29uc29sZS53YXJuKFxuICAgICAgICAgIGBbenVzdGFuZCBwZXJzaXN0IG1pZGRsZXdhcmVdIFVuYWJsZSB0byB1cGRhdGUgaXRlbSAnJHtvcHRpb25zLm5hbWV9JywgdGhlIGdpdmVuIHN0b3JhZ2UgaXMgY3VycmVudGx5IHVuYXZhaWxhYmxlLmBcbiAgICAgICAgKTtcbiAgICAgICAgc2V0KC4uLmFyZ3MpO1xuICAgICAgfSxcbiAgICAgIGdldCxcbiAgICAgIGFwaVxuICAgICk7XG4gIH1cbiAgY29uc3Qgc2V0SXRlbSA9ICgpID0+IHtcbiAgICBjb25zdCBzdGF0ZSA9IG9wdGlvbnMucGFydGlhbGl6ZSh7IC4uLmdldCgpIH0pO1xuICAgIHJldHVybiBzdG9yYWdlLnNldEl0ZW0ob3B0aW9ucy5uYW1lLCB7XG4gICAgICBzdGF0ZSxcbiAgICAgIHZlcnNpb246IG9wdGlvbnMudmVyc2lvblxuICAgIH0pO1xuICB9O1xuICBjb25zdCBzYXZlZFNldFN0YXRlID0gYXBpLnNldFN0YXRlO1xuICBhcGkuc2V0U3RhdGUgPSAoc3RhdGUsIHJlcGxhY2UpID0+IHtcbiAgICBzYXZlZFNldFN0YXRlKHN0YXRlLCByZXBsYWNlKTtcbiAgICB2b2lkIHNldEl0ZW0oKTtcbiAgfTtcbiAgY29uc3QgY29uZmlnUmVzdWx0ID0gY29uZmlnKFxuICAgICguLi5hcmdzKSA9PiB7XG4gICAgICBzZXQoLi4uYXJncyk7XG4gICAgICB2b2lkIHNldEl0ZW0oKTtcbiAgICB9LFxuICAgIGdldCxcbiAgICBhcGlcbiAgKTtcbiAgYXBpLmdldEluaXRpYWxTdGF0ZSA9ICgpID0+IGNvbmZpZ1Jlc3VsdDtcbiAgbGV0IHN0YXRlRnJvbVN0b3JhZ2U7XG4gIGNvbnN0IGh5ZHJhdGUgPSAoKSA9PiB7XG4gICAgdmFyIF9hLCBfYjtcbiAgICBpZiAoIXN0b3JhZ2UpIHJldHVybjtcbiAgICBoYXNIeWRyYXRlZCA9IGZhbHNlO1xuICAgIGh5ZHJhdGlvbkxpc3RlbmVycy5mb3JFYWNoKChjYikgPT4ge1xuICAgICAgdmFyIF9hMjtcbiAgICAgIHJldHVybiBjYigoX2EyID0gZ2V0KCkpICE9IG51bGwgPyBfYTIgOiBjb25maWdSZXN1bHQpO1xuICAgIH0pO1xuICAgIGNvbnN0IHBvc3RSZWh5ZHJhdGlvbkNhbGxiYWNrID0gKChfYiA9IG9wdGlvbnMub25SZWh5ZHJhdGVTdG9yYWdlKSA9PSBudWxsID8gdm9pZCAwIDogX2IuY2FsbChvcHRpb25zLCAoX2EgPSBnZXQoKSkgIT0gbnVsbCA/IF9hIDogY29uZmlnUmVzdWx0KSkgfHwgdm9pZCAwO1xuICAgIHJldHVybiB0b1RoZW5hYmxlKHN0b3JhZ2UuZ2V0SXRlbS5iaW5kKHN0b3JhZ2UpKShvcHRpb25zLm5hbWUpLnRoZW4oKGRlc2VyaWFsaXplZFN0b3JhZ2VWYWx1ZSkgPT4ge1xuICAgICAgaWYgKGRlc2VyaWFsaXplZFN0b3JhZ2VWYWx1ZSkge1xuICAgICAgICBpZiAodHlwZW9mIGRlc2VyaWFsaXplZFN0b3JhZ2VWYWx1ZS52ZXJzaW9uID09PSBcIm51bWJlclwiICYmIGRlc2VyaWFsaXplZFN0b3JhZ2VWYWx1ZS52ZXJzaW9uICE9PSBvcHRpb25zLnZlcnNpb24pIHtcbiAgICAgICAgICBpZiAob3B0aW9ucy5taWdyYXRlKSB7XG4gICAgICAgICAgICByZXR1cm4gW1xuICAgICAgICAgICAgICB0cnVlLFxuICAgICAgICAgICAgICBvcHRpb25zLm1pZ3JhdGUoXG4gICAgICAgICAgICAgICAgZGVzZXJpYWxpemVkU3RvcmFnZVZhbHVlLnN0YXRlLFxuICAgICAgICAgICAgICAgIGRlc2VyaWFsaXplZFN0b3JhZ2VWYWx1ZS52ZXJzaW9uXG4gICAgICAgICAgICAgIClcbiAgICAgICAgICAgIF07XG4gICAgICAgICAgfVxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICBgU3RhdGUgbG9hZGVkIGZyb20gc3RvcmFnZSBjb3VsZG4ndCBiZSBtaWdyYXRlZCBzaW5jZSBubyBtaWdyYXRlIGZ1bmN0aW9uIHdhcyBwcm92aWRlZGBcbiAgICAgICAgICApO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiBbZmFsc2UsIGRlc2VyaWFsaXplZFN0b3JhZ2VWYWx1ZS5zdGF0ZV07XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIHJldHVybiBbZmFsc2UsIHZvaWQgMF07XG4gICAgfSkudGhlbigobWlncmF0aW9uUmVzdWx0KSA9PiB7XG4gICAgICB2YXIgX2EyO1xuICAgICAgY29uc3QgW21pZ3JhdGVkLCBtaWdyYXRlZFN0YXRlXSA9IG1pZ3JhdGlvblJlc3VsdDtcbiAgICAgIHN0YXRlRnJvbVN0b3JhZ2UgPSBvcHRpb25zLm1lcmdlKFxuICAgICAgICBtaWdyYXRlZFN0YXRlLFxuICAgICAgICAoX2EyID0gZ2V0KCkpICE9IG51bGwgPyBfYTIgOiBjb25maWdSZXN1bHRcbiAgICAgICk7XG4gICAgICBzZXQoc3RhdGVGcm9tU3RvcmFnZSwgdHJ1ZSk7XG4gICAgICBpZiAobWlncmF0ZWQpIHtcbiAgICAgICAgcmV0dXJuIHNldEl0ZW0oKTtcbiAgICAgIH1cbiAgICB9KS50aGVuKCgpID0+IHtcbiAgICAgIHBvc3RSZWh5ZHJhdGlvbkNhbGxiYWNrID09IG51bGwgPyB2b2lkIDAgOiBwb3N0UmVoeWRyYXRpb25DYWxsYmFjayhzdGF0ZUZyb21TdG9yYWdlLCB2b2lkIDApO1xuICAgICAgc3RhdGVGcm9tU3RvcmFnZSA9IGdldCgpO1xuICAgICAgaGFzSHlkcmF0ZWQgPSB0cnVlO1xuICAgICAgZmluaXNoSHlkcmF0aW9uTGlzdGVuZXJzLmZvckVhY2goKGNiKSA9PiBjYihzdGF0ZUZyb21TdG9yYWdlKSk7XG4gICAgfSkuY2F0Y2goKGUpID0+IHtcbiAgICAgIHBvc3RSZWh5ZHJhdGlvbkNhbGxiYWNrID09IG51bGwgPyB2b2lkIDAgOiBwb3N0UmVoeWRyYXRpb25DYWxsYmFjayh2b2lkIDAsIGUpO1xuICAgIH0pO1xuICB9O1xuICBhcGkucGVyc2lzdCA9IHtcbiAgICBzZXRPcHRpb25zOiAobmV3T3B0aW9ucykgPT4ge1xuICAgICAgb3B0aW9ucyA9IHtcbiAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgLi4ubmV3T3B0aW9uc1xuICAgICAgfTtcbiAgICAgIGlmIChuZXdPcHRpb25zLnN0b3JhZ2UpIHtcbiAgICAgICAgc3RvcmFnZSA9IG5ld09wdGlvbnMuc3RvcmFnZTtcbiAgICAgIH1cbiAgICB9LFxuICAgIGNsZWFyU3RvcmFnZTogKCkgPT4ge1xuICAgICAgc3RvcmFnZSA9PSBudWxsID8gdm9pZCAwIDogc3RvcmFnZS5yZW1vdmVJdGVtKG9wdGlvbnMubmFtZSk7XG4gICAgfSxcbiAgICBnZXRPcHRpb25zOiAoKSA9PiBvcHRpb25zLFxuICAgIHJlaHlkcmF0ZTogKCkgPT4gaHlkcmF0ZSgpLFxuICAgIGhhc0h5ZHJhdGVkOiAoKSA9PiBoYXNIeWRyYXRlZCxcbiAgICBvbkh5ZHJhdGU6IChjYikgPT4ge1xuICAgICAgaHlkcmF0aW9uTGlzdGVuZXJzLmFkZChjYik7XG4gICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICBoeWRyYXRpb25MaXN0ZW5lcnMuZGVsZXRlKGNiKTtcbiAgICAgIH07XG4gICAgfSxcbiAgICBvbkZpbmlzaEh5ZHJhdGlvbjogKGNiKSA9PiB7XG4gICAgICBmaW5pc2hIeWRyYXRpb25MaXN0ZW5lcnMuYWRkKGNiKTtcbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGZpbmlzaEh5ZHJhdGlvbkxpc3RlbmVycy5kZWxldGUoY2IpO1xuICAgICAgfTtcbiAgICB9XG4gIH07XG4gIGlmICghb3B0aW9ucy5za2lwSHlkcmF0aW9uKSB7XG4gICAgaHlkcmF0ZSgpO1xuICB9XG4gIHJldHVybiBzdGF0ZUZyb21TdG9yYWdlIHx8IGNvbmZpZ1Jlc3VsdDtcbn07XG5jb25zdCBwZXJzaXN0SW1wbCA9IChjb25maWcsIGJhc2VPcHRpb25zKSA9PiB7XG4gIGlmIChcImdldFN0b3JhZ2VcIiBpbiBiYXNlT3B0aW9ucyB8fCBcInNlcmlhbGl6ZVwiIGluIGJhc2VPcHRpb25zIHx8IFwiZGVzZXJpYWxpemVcIiBpbiBiYXNlT3B0aW9ucykge1xuICAgIGlmICgoaW1wb3J0Lm1ldGEuZW52ID8gaW1wb3J0Lm1ldGEuZW52Lk1PREUgOiB2b2lkIDApICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgICAgY29uc29sZS53YXJuKFxuICAgICAgICBcIltERVBSRUNBVEVEXSBgZ2V0U3RvcmFnZWAsIGBzZXJpYWxpemVgIGFuZCBgZGVzZXJpYWxpemVgIG9wdGlvbnMgYXJlIGRlcHJlY2F0ZWQuIFVzZSBgc3RvcmFnZWAgb3B0aW9uIGluc3RlYWQuXCJcbiAgICAgICk7XG4gICAgfVxuICAgIHJldHVybiBvbGRJbXBsKGNvbmZpZywgYmFzZU9wdGlvbnMpO1xuICB9XG4gIHJldHVybiBuZXdJbXBsKGNvbmZpZywgYmFzZU9wdGlvbnMpO1xufTtcbmNvbnN0IHBlcnNpc3QgPSBwZXJzaXN0SW1wbDtcblxuZXhwb3J0IHsgY29tYmluZSwgY3JlYXRlSlNPTlN0b3JhZ2UsIGRldnRvb2xzLCBwZXJzaXN0LCByZWR1eCwgc3Vic2NyaWJlV2l0aFNlbGVjdG9yIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/middleware.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/vanilla.mjs":
/*!**********************************************!*\
  !*** ./node_modules/zustand/esm/vanilla.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   \"default\": () => (/* binding */ vanilla)\n/* harmony export */ });\nconst createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const destroy = () => {\n    if (( false ? 0 : void 0) !== \"production\") {\n      console.warn(\n        \"[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected.\"\n      );\n    }\n    listeners.clear();\n  };\n  const api = { setState, getState, getInitialState, subscribe, destroy };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\nvar vanilla = (createState) => {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use import { createStore } from 'zustand/vanilla'.\"\n    );\n  }\n  return createStore(createState);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/vanilla.mjs\n");

/***/ })

};
;