
import { cn } from "@/lib/utils";
import * as Flag from "country-flag-icons/react/1x1";

export type CountryCode = keyof typeof Flag
type CountryFlagProps = {
  code: string;
  className?: string
};

export default function CountryFlag({ code, className }: CountryFlagProps) {
  const FlagComponent = Flag[code as CountryCode];
  return <FlagComponent className={cn("border border-colortext-foreground rounded-full w-4 h-4 aspect-square my-auto", className)} />;
}