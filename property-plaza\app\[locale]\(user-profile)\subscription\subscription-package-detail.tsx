import { PackageFeatureDetailType, PackagesType, Subscription, SubscriptionPricingDetail } from "@/core/domain/subscription/subscription"
import useSubscription, { PlanSelector } from "./use-subscription"
import { useTranslations } from "next-intl"
import { useSeekersSettingsStore } from "@/stores/seekers-settings.store"
import { formatCurrency } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import CollapsibleFeatures from "./collapsible-features"
import { Check, X } from "lucide-react"
import { useUserStore } from "@/stores/user.store"
import { useEffect, useState } from "react"
import DowngradeDialog from "./downgrade-dialog"
import SeekerAuthDialog from "../../(user)/(auth)/seekers-auth-dialog"
import SubscriptionSignUpDialog from "./subscription-sign-up-dialog"

export default function PackageContent({
  plan, isQuaterlyBilling, conversionRate,
  isCurrentPlan, canUpgrade, canDowngrade, features, onUpgrde, onDowngrade, isLoading = false,
  nextBillingDate
}
  : {
    plan: Subscription, isQuaterlyBilling?: boolean,
    conversionRate: { [key: string]: string }, isCurrentPlan?: boolean,
    canUpgrade?: boolean, canDowngrade?: boolean, features: PackageFeatureDetailType,
    onUpgrde: (productId: string, priceId: string) => void,
    onDowngrade: (productId: string, priceId: string) => void,
    isLoading?: boolean,
    nextBillingDate: string
  }) {
  const t = useTranslations("seeker")
  const { currency } = useSeekersSettingsStore()
  const { packageFeatureLabel, handleDowngradeLevelLabel, handleUpgradeLevelLabel } = useSubscription(null)
  const membership = useUserStore(state => state.seekers.accounts.membership)
  const email = useUserStore(state => state.seekers.email)
  const [monthlyPricing, setMonthlyPricing] = useState<SubscriptionPricingDetail | undefined>()
  const [quarterlyPricing, setQuarterlyPricing] = useState<SubscriptionPricingDetail | undefined>()
  const [monthlyPrice, setMonthlyPrice] = useState(0)
  const [quarterlyPrice, setQuarterlyPrice] = useState(0)
  const [isClicked, setIsClicked] = useState(false)
  useEffect(() => {
    if (!isLoading) return setIsClicked(false)

  }, [isLoading])
  useEffect(() => {
    const monthlyPricingData = plan.priceVariant.find(item => item.cycleCount == 1)
    const quarterlyPricingData = plan.priceVariant.find(item => item.cycleCount == 3)
    setMonthlyPrice(monthlyPricingData?.price || 0)
    setMonthlyPricing(monthlyPricingData)
    setQuarterlyPricing(quarterlyPricingData)
    setQuarterlyPrice(quarterlyPricingData?.price || 0)
  }, [currency, plan])
  return <div className="px-4 py-6">
    <div className="min-h-[160px]">

      <p className="text-xl font-semibold capitalize">{plan.name}</p>
      <span className="text-3xl font-bold">
        {monthlyPrice === 0 ? t("misc.free")
          :
          formatCurrency(
            (isQuaterlyBilling ?
              (quarterlyPrice * +(conversionRate[currency] || 1) / 3)
              :
              monthlyPrice * +(conversionRate[currency] || 1)),
            currency
          )

        }
      </span>
      <div className="text-right flex justify-between flex-grow items-start">
        <span className="text-muted-foreground">{t('misc.perMonth')}</span>
        {isQuaterlyBilling && monthlyPrice > 0 && (
          <span className="-mt-1 rounded-full bg-[#DAFBE5] px-2 py-1 text-xs font-medium text-[#0F8534]">
            {t('cta.saveUpTo')} {" "}
            {formatCurrency(((monthlyPrice * 3) - quarterlyPrice) * +(conversionRate[currency] || 1), currency)}
          </span>
        )}
      </div>
      <div className="mt-4 space-y-2">
        {isCurrentPlan ? (
          <>
            {
              email ?
                <Button variant="outline" className="w-full bg-[#FAF6F0] text-[#C19B67]">
                  {t("misc.yourCurrentPlan")}
                </Button>
                :
                <SeekerAuthDialog
                  customTrigger={
                    <Button
                      variant="outline"
                      className="w-full bg-[#FAF6F0] text-[#C19B67]">
                      {t("cta.createAccount")}
                    </Button>
                  }
                />
            }
          </>
        ) : canUpgrade ? (
          <>
            {email ?
              <Button
                loading={isClicked}
                disabled={isLoading}
                variant={"default-seekers"}
                className="w-full text-white"
                onClick={() => {
                  setIsClicked(true)
                  isQuaterlyBilling ?
                    onUpgrde(plan.productId, quarterlyPricing?.priceId || "")
                    :
                    onUpgrde(plan.productId, monthlyPricing?.priceId || "")
                }
                }
              >
                {t("misc.upgradeTo", { plan: handleUpgradeLevelLabel(plan.name as PackagesType) })}
              </Button>
              :
              <SubscriptionSignUpDialog
                priceId={(isQuaterlyBilling ? quarterlyPricing?.priceId : monthlyPricing?.priceId) || ""}
                productId={plan.productId}
                packageName={plan.name} customTrigger={
                  <Button
                    loading={isClicked}
                    disabled={isLoading}
                    variant={"default-seekers"}
                    className="w-full text-white"
                  >
                    {t("misc.upgradeTo", { plan: handleUpgradeLevelLabel(plan.name as PackagesType) })}
                  </Button>
                } />
            }

          </>
        ) : canDowngrade ? (
          <DowngradeDialog
            nextBillingDate={nextBillingDate}
            onDowngrade={() => onDowngrade(plan.productId, monthlyPricing?.priceId || "")}
            downgradePackageName={handleDowngradeLevelLabel(membership)}
            currentPackage={membership}
            trigger={
              <Button
                variant="outline"
                className="w-full border-[#C19B67] text-[#C19B67]"
              >
                {t("misc.downgradeTo", { plan: handleDowngradeLevelLabel(membership) })}
              </Button>
            }
          />
        ) : <button className="h-8"></button>}
      </div>
      <div className="md:hidden pb-6">
        <CollapsibleFeatures features={features} />
      </div>
    </div>
    <div className="max-sm:hidden ">
      {packageFeatureLabel.map((item, index) => (
        <div
          key={item.id}
          className={`h-12 flex items-center justify-center text-center mx-4 ${index !== 0 ? "border-t border-dashed border-gray-200" : ""
            }`}
        >
          {typeof features[item.id] === "boolean" ? <>
            {
              features[item.id] ?
                <>
                  <Check className="h-5 w-5 text-[#C19B67] stroke-[3]" />
                </>

                :
                <>
                  <X className="h-5 w-5 text-red-500 stroke-[3]" />
                </>

            }
          </> : features[item.id]}
        </div>
      ))}
    </div>
  </div>
}

