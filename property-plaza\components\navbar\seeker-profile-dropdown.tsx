import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "../ui/dropdown-menu"
import { favoriteUrl, profileUrl, seekersMessageUrl } from "@/lib/constanta/route"
import Link from "next/link"
import { useLocale, useTranslations } from "next-intl"
import dynamic from "next/dynamic"
const LogoutDialog = dynamic(() => import("./seeker-logout"), { ssr: false })
export default function ProfileDropdown({ trigger }: { trigger: React.ReactNode }) {
  const t = useTranslations("seeker")
  const handleOpenLogoutDialog = () => {
    const button = document.getElementById("open-logout-dialog")
    button?.click()
  }
  const locale = useLocale()
  return <>
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        {trigger}
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="!w-[256px]">
        <DropdownMenuItem asChild>
          <Link href={profileUrl} hrefLang={locale} prefetch={false}>
            {t('accountAndProfile.profile')}
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={favoriteUrl} hrefLang={locale} prefetch={false}>
            {t('accountAndProfile.favorite')}
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem className="w-full" asChild>
          <Link href={seekersMessageUrl} hrefLang={locale} prefetch={false}>
            <div className="flex justify-between items-center w-full ">
              {t('accountAndProfile.message')}
              {/* TODO:
                Make this works as when we working on Messaging page
              */}
              {/* <div className="text-xs aspect-square w-6 rounded-full bg-seekers-primary p-1 text-white items-center justify-center text-center">
                2
              </div> */}
            </div>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={e => {
          e.preventDefault()
          handleOpenLogoutDialog()
        }}>{t('accountAndProfile.logout.title')}</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
    <LogoutDialog
      trigger={<button id="open-logout-dialog"></button>
      }
    />
  </>
}