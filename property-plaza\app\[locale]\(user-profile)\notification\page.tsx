import { Metadata } from "next"
import { getTranslations } from "next-intl/server"
import NotificationBreadCrumb from "./bread-crumb"
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout"
import { useTranslations } from "next-intl"
import NotificationForm from "./form/notification.form"
import { notificationSettingUrl } from "@/lib/constanta/route"

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  return {
    title: t("metadata.notificationSetting.title"),
    description: t("metadata.notificationSetting.description"),
    alternates: {
      languages: {
        en: process.env.USER_DOMAIN + "/en" + notificationSettingUrl,
        id: process.env.USER_DOMAIN + "/id" + notificationSettingUrl
      }
    }
  }
}



export default function NotificationPage() {
  const t = useTranslations("seeker")

  return <>
    <NotificationBreadCrumb />
    <MainContentLayout className="space-y-8 my-8 max-sm:px-0">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">{t("setting.profile.notification.title")}</h1>
        <h2 className="text-muted-foreground mt-2">{t("settings.profile.notification.description")}</h2>
      </div>
      <NotificationForm />
    </MainContentLayout>

  </>
}