export interface LoginDto {
  username: string;
  password: string;
  login_with: "DEFAULT" | "PHONE_NUMBER";
}

export interface StaffLoginDto {
  email: string;
  password: string;
}

export interface SendEmailVerificationDto {
  email: string;
  category: string;
}

export interface SendWhatsappVerificationDto {
  phone_number: string;
}

export interface OtpVerificationDto {
  requested_by: string;
  otp: string;
  type: "EMAIL" | "PHONE_NUMBER";
}

export interface RequestForgetPasswordDto {
  email: string;
}

export interface VerifyResetPasswordDto {
  email: string;
  token: string;
}

export interface ResetPasswordDto {
  email: string;
  token: string;
  password: string;
  confirm_password: string;
}

export interface CreatePasswordDto {
  email: string;
  locale: string;
  token: string;
  password: string;
  confirm_password: string;
}

export interface TwoFactorAuthenticationCodeDto {
  request_setting: "ACTIVE_2FA" | "INACTIVE_2FA" | "GET_2FA" | "REQUEST_2FA";
  password?: string;
  otp?: number;
}

export interface TotpVerificationCode {
  requested_by: string;
  otp: string;
}

export interface TwoFactorAuthenticationCodeResponseDto {
  qr_image?: string;
}
