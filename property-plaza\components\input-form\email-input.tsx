"use client"
import React, { ComponentProps } from "react"
import { useState } from "react"
import { FieldValues } from "react-hook-form"
import { FormField } from "../ui/form"
import { Input } from "../ui/input"
import ActionInsideForm from "./action-inside-form"
import { BaseInputForm } from "@/types/base"
import BaseInputLayout from "./base-input"
import { cn } from "@/lib/utils"


interface EmailInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  placeholder: string,
  description?: string,
  children?: React.ReactNode,
  inputProps?: ComponentProps<"input">,
  isEditable?: boolean,
}

export default function EmailInput<T extends FieldValues>({ form, label, name, placeholder, description, children, isEditable, inputProps }: EmailInputProps<T>) {
  const [disabled, setDisabled] = useState<boolean | undefined>(true)
  const handleDisabledState = (disabledStatus?: boolean, isEdited?: boolean) => {
    if (isEdited) {

    } else {
      form.reset()
    }
    setDisabled(disabledStatus)
  }
  return <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <BaseInputLayout label={label} description={description}>
        <div className='flex items-center w-full border rounded-sm focus-within:border-neutral-light overflow-hidden'>
          <Input type="email" placeholder={placeholder} {...field} {...inputProps} disabled={disabled} className={cn('border-none focus:outline-none shadow-none focus-visible:ring-0', inputProps?.className)} />
          {children}
          {isEditable ??
            <>
              <ActionInsideForm setDisabledStatus={handleDisabledState} isFormDisabled={disabled} />
            </>
          }
        </div>
      </BaseInputLayout>

    )}
  />
}