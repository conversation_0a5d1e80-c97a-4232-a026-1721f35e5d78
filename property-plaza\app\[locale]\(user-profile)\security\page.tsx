import { Metadata } from "next"
import { getTranslations } from "next-intl/server"
import SecurityBreadCrumb from "./bread-crumb"
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout"
import { useTranslations } from "next-intl"
import ConnectedDevice from "./connected-device"
import LoginHistory from "./login-history"
import ChangePassword from "./change-password"
import TwoFA from "./two-fa"
import { securitySettingUrl } from "@/lib/constanta/route"

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  return {
    title: t("metadata.security.title"),
    description: t("metadata.security.description"),
    alternates: {
      languages: {
        en: process.env.USER_DOMAIN + "/en" + securitySettingUrl,
        id: process.env.USER_DOMAIN + "/id" + securitySettingUrl
      }
    }
  }
}

export default function SecurityPage() {
  const t = useTranslations("seeker")
  return <>
    <SecurityBreadCrumb />
    <MainContentLayout className="space-y-8 mt-8 mb-14 max-sm:px-0">
      <div className="flex justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{t("setting.accountAndProfile.security.title")}</h1>
          <h2 className="text-muted-foreground mt-2">{t("setting.accountAndProfile.security.description")}</h2>
        </div>
      </div>
      <ChangePassword />
      <TwoFA />
      <LoginHistory />
      <ConnectedDevice />
    </MainContentLayout>
  </>
}