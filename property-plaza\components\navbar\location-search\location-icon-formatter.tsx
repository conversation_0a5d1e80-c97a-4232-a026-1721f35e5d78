import Image from "next/image";
import CangguImage from "@/public/canggu.png"
import UbudImage from "@/public/ubud.png"
import NusaDuaImage from "@/public/nusa-dua.png"
import UluwatuImage from "@/public/uluwatu.png"
import SeminyakImage from "@/public/seminyak.png"

export default function LocationIconFormatter({ locationName }: { locationName: string }) {
  switch (locationName) {
    case "canggu":
      return <Image src={CangguImage} alt="canggu" width={36} className="aspect-square " />
    case "ubud":
      return <Image src={UbudImage} alt="ubud" width={32} className="aspect-square " />
    case "seminyak":
      return <Image src={SeminyakImage} alt="Seminyak" width={32} className="aspect-square" />
    case "uluwatu":
      return <Image src={UluwatuImage} alt="uluwatu" width={32} className="aspect-square " />
    case "Nusa Dua":
      return <Image src={NusaDuaImage} alt="nusa dua" width={32} className="aspect-square " />
    default:
      return <></>
  }
}