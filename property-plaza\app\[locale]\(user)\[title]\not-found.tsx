import { But<PERSON> } from '@/components/ui/button'
import { useLocale, useTranslations } from 'next-intl'
import Link from 'next/link'

export default function NotFound() {
  const t = useTranslations("seeker")
  const locale = useLocale()
  return (
    <div className='min-h-[80vh] flex justify-center items-center'>
      <div className='space-y-4 text-center flex flex-col items-center'>
        <h1 className='text-2xl text-seekers-text font-bold'>{t('misc.error.seekers.propertyNotFound.title')}</h1>
        <p className='tex'>{t('misc.error.propertyNotFound.description')}</p>
        <Button asChild variant={"default-seekers"}>
          <Link href="/" hrefLang={locale}>{t('cta.findOtherProperty')} </Link>
        </Button>
      </div>
    </div>
  )
}