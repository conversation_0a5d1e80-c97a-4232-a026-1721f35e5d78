import { postCreatePassword } from "@/core/infrastructures/auth";
import { CreatePasswordDto } from "@/core/infrastructures/auth/dto";
import { useMutation } from "@tanstack/react-query";

export function useCreatePassord() {
  const mutation = useMutation({
    mutationFn: (data: CreatePasswordDto) =>
      postCreatePassword(data, {
        headers: {
          Authorization: data.token,
        },
      }),
  });
  return mutation;
}
