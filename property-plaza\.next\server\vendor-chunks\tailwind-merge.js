"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tailwind-merge";
exports.ids = ["vendor-chunks/tailwind-merge"];
exports.modules = {

/***/ "(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/tailwind-merge/dist/bundle-mjs.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTailwindMerge: () => (/* binding */ createTailwindMerge),\n/* harmony export */   extendTailwindMerge: () => (/* binding */ extendTailwindMerge),\n/* harmony export */   fromTheme: () => (/* binding */ fromTheme),\n/* harmony export */   getDefaultConfig: () => (/* binding */ getDefaultConfig),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   twJoin: () => (/* binding */ twJoin),\n/* harmony export */   twMerge: () => (/* binding */ twMerge),\n/* harmony export */   validators: () => (/* binding */ validators)\n/* harmony export */ });\nconst CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    prefix\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  const prefixedClassGroupEntries = getPrefixedClassGroupEntries(Object.entries(config.classGroups), prefix);\n  prefixedClassGroupEntries.forEach(([classGroupId, classGroup]) => {\n    processClassesRecursively(classGroup, classMap, classGroupId, theme);\n  });\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\nconst getPrefixedClassGroupEntries = (classGroupEntries, prefix) => {\n  if (!prefix) {\n    return classGroupEntries;\n  }\n  return classGroupEntries.map(([classGroupId, classGroup]) => {\n    const prefixedClassGroup = classGroup.map(classDefinition => {\n      if (typeof classDefinition === 'string') {\n        return prefix + classDefinition;\n      }\n      if (typeof classDefinition === 'object') {\n        return Object.fromEntries(Object.entries(classDefinition).map(([key, value]) => [prefix + key, value]));\n      }\n      return classDefinition;\n    });\n    return [classGroupId, prefixedClassGroup];\n  });\n};\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst createParseClassName = config => {\n  const {\n    separator,\n    experimentalParseClassName\n  } = config;\n  const isSeparatorSingleCharacter = separator.length === 1;\n  const firstSeparatorCharacter = separator[0];\n  const separatorLength = separator.length;\n  // parseClassName inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n  const parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0) {\n        if (currentCharacter === firstSeparatorCharacter && (isSeparatorSingleCharacter || className.slice(index, index + separatorLength) === separator)) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + separatorLength;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const hasImportantModifier = baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER);\n    const baseClassName = hasImportantModifier ? baseClassNameWithImportantModifier.substring(1) : baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (experimentalParseClassName) {\n    return className => experimentalParseClassName({\n      className,\n      parseClassName\n    });\n  }\n  return parseClassName;\n};\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst sortModifiers = modifiers => {\n  if (modifiers.length <= 1) {\n    return modifiers;\n  }\n  const sortedModifiers = [];\n  let unsortedModifiers = [];\n  modifiers.forEach(modifier => {\n    const isArbitraryVariant = modifier[0] === '[';\n    if (isArbitraryVariant) {\n      sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n      unsortedModifiers = [];\n    } else {\n      unsortedModifiers.push(modifier);\n    }\n  });\n  sortedModifiers.push(...unsortedModifiers.sort());\n  return sortedModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  ...createClassGroupUtils(config)\n});\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  let result = '';\n  for (let i = classList.length - 1; i >= 0;) {\n    while (classList[i] === ' ') {\n      --i;\n    }\n    const nextI = classList.lastIndexOf(' ', i);\n    const originalClassName = classList.slice(nextI === -1 ? 0 : nextI + 1, i + 1);\n    i = nextI;\n    const {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    let hasPostfixModifier = Boolean(maybePostfixModifierPosition);\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst stringLengths = /*#__PURE__*/new Set(['px', 'full', 'screen']);\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isLength = value => isNumber(value) || stringLengths.has(value) || fractionRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, 'length', isLengthOnly);\nconst isNumber = value => Boolean(value) && !Number.isNaN(Number(value));\nconst isArbitraryNumber = value => getIsArbitraryValue(value, 'number', isNumber);\nconst isInteger = value => Boolean(value) && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst sizeLabels = /*#__PURE__*/new Set(['length', 'size', 'percentage']);\nconst isArbitrarySize = value => getIsArbitraryValue(value, sizeLabels, isNever);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, 'position', isNever);\nconst imageLabels = /*#__PURE__*/new Set(['image', 'url']);\nconst isArbitraryImage = value => getIsArbitraryValue(value, imageLabels, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, '', isShadow);\nconst isAny = () => true;\nconst getIsArbitraryValue = (value, label, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return typeof label === 'string' ? result[1] === label : label.has(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isInteger,\n  isLength,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  const colors = fromTheme('colors');\n  const spacing = fromTheme('spacing');\n  const blur = fromTheme('blur');\n  const brightness = fromTheme('brightness');\n  const borderColor = fromTheme('borderColor');\n  const borderRadius = fromTheme('borderRadius');\n  const borderSpacing = fromTheme('borderSpacing');\n  const borderWidth = fromTheme('borderWidth');\n  const contrast = fromTheme('contrast');\n  const grayscale = fromTheme('grayscale');\n  const hueRotate = fromTheme('hueRotate');\n  const invert = fromTheme('invert');\n  const gap = fromTheme('gap');\n  const gradientColorStops = fromTheme('gradientColorStops');\n  const gradientColorStopPositions = fromTheme('gradientColorStopPositions');\n  const inset = fromTheme('inset');\n  const margin = fromTheme('margin');\n  const opacity = fromTheme('opacity');\n  const padding = fromTheme('padding');\n  const saturate = fromTheme('saturate');\n  const scale = fromTheme('scale');\n  const sepia = fromTheme('sepia');\n  const skew = fromTheme('skew');\n  const space = fromTheme('space');\n  const translate = fromTheme('translate');\n  const getOverscroll = () => ['auto', 'contain', 'none'];\n  const getOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const getSpacingWithAutoAndArbitrary = () => ['auto', isArbitraryValue, spacing];\n  const getSpacingWithArbitrary = () => [isArbitraryValue, spacing];\n  const getLengthWithEmptyAndArbitrary = () => ['', isLength, isArbitraryLength];\n  const getNumberWithAutoAndArbitrary = () => ['auto', isNumber, isArbitraryValue];\n  const getPositions = () => ['bottom', 'center', 'left', 'left-bottom', 'left-top', 'right', 'right-bottom', 'right-top', 'top'];\n  const getLineStyles = () => ['solid', 'dashed', 'dotted', 'double', 'none'];\n  const getBlendModes = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const getAlign = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch'];\n  const getZeroAndEmpty = () => ['', '0', isArbitraryValue];\n  const getBreaks = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const getNumberAndArbitrary = () => [isNumber, isArbitraryValue];\n  return {\n    cacheSize: 500,\n    separator: ':',\n    theme: {\n      colors: [isAny],\n      spacing: [isLength, isArbitraryLength],\n      blur: ['none', '', isTshirtSize, isArbitraryValue],\n      brightness: getNumberAndArbitrary(),\n      borderColor: [colors],\n      borderRadius: ['none', '', 'full', isTshirtSize, isArbitraryValue],\n      borderSpacing: getSpacingWithArbitrary(),\n      borderWidth: getLengthWithEmptyAndArbitrary(),\n      contrast: getNumberAndArbitrary(),\n      grayscale: getZeroAndEmpty(),\n      hueRotate: getNumberAndArbitrary(),\n      invert: getZeroAndEmpty(),\n      gap: getSpacingWithArbitrary(),\n      gradientColorStops: [colors],\n      gradientColorStopPositions: [isPercent, isArbitraryLength],\n      inset: getSpacingWithAutoAndArbitrary(),\n      margin: getSpacingWithAutoAndArbitrary(),\n      opacity: getNumberAndArbitrary(),\n      padding: getSpacingWithArbitrary(),\n      saturate: getNumberAndArbitrary(),\n      scale: getNumberAndArbitrary(),\n      sepia: getZeroAndEmpty(),\n      skew: getNumberAndArbitrary(),\n      space: getSpacingWithArbitrary(),\n      translate: getSpacingWithArbitrary()\n    },\n    classGroups: {\n      // Layout\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', 'video', isArbitraryValue]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isTshirtSize]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': getBreaks()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': getBreaks()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: [...getPositions(), isArbitraryValue]\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: getOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': getOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': getOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: getOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': getOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': getOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: [inset]\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': [inset]\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': [inset]\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: [inset]\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: [inset]\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: [inset]\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: [inset]\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: [inset]\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: [inset]\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: ['auto', isInteger, isArbitraryValue]\n      }],\n      // Flexbox and Grid\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: getSpacingWithAutoAndArbitrary()\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['wrap', 'wrap-reverse', 'nowrap']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: ['1', 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: getZeroAndEmpty()\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: getZeroAndEmpty()\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: ['first', 'last', 'none', isInteger, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': [isAny]\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: ['auto', {\n          span: ['full', isInteger, isArbitraryValue]\n        }, isArbitraryValue]\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': [isAny]\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: ['auto', {\n          span: [isInteger, isArbitraryValue]\n        }, isArbitraryValue]\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': ['auto', 'min', 'max', 'fr', isArbitraryValue]\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': ['auto', 'min', 'max', 'fr', isArbitraryValue]\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: [gap]\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': [gap]\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': [gap]\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: ['normal', ...getAlign()]\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': ['start', 'end', 'center', 'stretch']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', 'start', 'end', 'center', 'stretch']\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...getAlign(), 'baseline']\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: ['start', 'end', 'center', 'baseline', 'stretch']\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', 'start', 'end', 'center', 'stretch', 'baseline']\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': [...getAlign(), 'baseline']\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': ['start', 'end', 'center', 'baseline', 'stretch']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', 'start', 'end', 'center', 'stretch']\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: [padding]\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: [padding]\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: [padding]\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: [padding]\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: [padding]\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: [padding]\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: [padding]\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: [padding]\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: [padding]\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: [margin]\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: [margin]\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: [margin]\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: [margin]\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: [margin]\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: [margin]\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: [margin]\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: [margin]\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: [margin]\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-x': [{\n        'space-x': [space]\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-y': [{\n        'space-y': [space]\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // Sizing\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: ['auto', 'min', 'max', 'fit', 'svw', 'lvw', 'dvw', isArbitraryValue, spacing]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [isArbitraryValue, spacing, 'min', 'max', 'fit']\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [isArbitraryValue, spacing, 'none', 'full', 'min', 'max', 'fit', 'prose', {\n          screen: [isTshirtSize]\n        }, isTshirtSize]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/size\n       */\n      size: [{\n        size: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit']\n      }],\n      // Typography\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', isTshirtSize, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black', isArbitraryNumber]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isAny]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractons'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest', isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': ['none', isNumber, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose', isLength, isArbitraryValue]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryValue]\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['none', 'disc', 'decimal', isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: [colors]\n      }],\n      /**\n       * Placeholder Opacity\n       * @see https://tailwindcss.com/docs/placeholder-opacity\n       */\n      'placeholder-opacity': [{\n        'placeholder-opacity': [opacity]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: [colors]\n      }],\n      /**\n       * Text Opacity\n       * @see https://tailwindcss.com/docs/text-opacity\n       */\n      'text-opacity': [{\n        'text-opacity': [opacity]\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...getLineStyles(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: ['auto', 'from-font', isLength, isArbitraryLength]\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': ['auto', isLength, isArbitraryValue]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: [colors]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: getSpacingWithArbitrary()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryValue]\n      }],\n      // Backgrounds\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Opacity\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/background-opacity\n       */\n      'bg-opacity': [{\n        'bg-opacity': [opacity]\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: [...getPositions(), isArbitraryPosition]\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: ['no-repeat', {\n          repeat: ['', 'x', 'y', 'round', 'space']\n        }]\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: ['auto', 'cover', 'contain', isArbitrarySize]\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          'gradient-to': ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n        }, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: [colors]\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: [gradientColorStops]\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: [gradientColorStops]\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: [gradientColorStops]\n      }],\n      // Borders\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: [borderRadius]\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': [borderRadius]\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': [borderRadius]\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': [borderRadius]\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': [borderRadius]\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': [borderRadius]\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': [borderRadius]\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': [borderRadius]\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': [borderRadius]\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': [borderRadius]\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': [borderRadius]\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': [borderRadius]\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: [borderWidth]\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': [borderWidth]\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': [borderWidth]\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': [borderWidth]\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': [borderWidth]\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': [borderWidth]\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': [borderWidth]\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': [borderWidth]\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': [borderWidth]\n      }],\n      /**\n       * Border Opacity\n       * @see https://tailwindcss.com/docs/border-opacity\n       */\n      'border-opacity': [{\n        'border-opacity': [opacity]\n      }],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...getLineStyles(), 'hidden']\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-x': [{\n        'divide-x': [borderWidth]\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-y': [{\n        'divide-y': [borderWidth]\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Divide Opacity\n       * @see https://tailwindcss.com/docs/divide-opacity\n       */\n      'divide-opacity': [{\n        'divide-opacity': [opacity]\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/divide-style\n       */\n      'divide-style': [{\n        divide: getLineStyles()\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: [borderColor]\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': [borderColor]\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': [borderColor]\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': [borderColor]\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': [borderColor]\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': [borderColor]\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': [borderColor]\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: [borderColor]\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: ['', ...getLineStyles()]\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isLength, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: [isLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: [colors]\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/ring-width\n       */\n      'ring-w': [{\n        ring: getLengthWithEmptyAndArbitrary()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://tailwindcss.com/docs/ring-width\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/ring-color\n       */\n      'ring-color': [{\n        ring: [colors]\n      }],\n      /**\n       * Ring Opacity\n       * @see https://tailwindcss.com/docs/ring-opacity\n       */\n      'ring-opacity': [{\n        'ring-opacity': [opacity]\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://tailwindcss.com/docs/ring-offset-width\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isLength, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://tailwindcss.com/docs/ring-offset-color\n       */\n      'ring-offset-color': [{\n        'ring-offset': [colors]\n      }],\n      // Effects\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: ['', 'inner', 'none', isTshirtSize, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow-color\n       */\n      'shadow-color': [{\n        shadow: [isAny]\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [opacity]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...getBlendModes(), 'plus-lighter', 'plus-darker']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': getBlendModes()\n      }],\n      // Filters\n      /**\n       * Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: ['', 'none']\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: [blur]\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [brightness]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [contrast]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': ['', 'none', isTshirtSize, isArbitraryValue]\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: [grayscale]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [hueRotate]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: [invert]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [saturate]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: [sepia]\n      }],\n      /**\n       * Backdrop Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': ['', 'none']\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': [blur]\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [brightness]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [contrast]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': [grayscale]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [hueRotate]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': [invert]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [opacity]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [saturate]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': [sepia]\n      }],\n      // Tables\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': [borderSpacing]\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': [borderSpacing]\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': [borderSpacing]\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // Transitions and Animation\n      /**\n       * Tranisition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['none', 'all', '', 'colors', 'opacity', 'shadow', 'transform', isArbitraryValue]\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: getNumberAndArbitrary()\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'in', 'out', 'in-out', isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: getNumberAndArbitrary()\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', 'spin', 'ping', 'pulse', 'bounce', isArbitraryValue]\n      }],\n      // Transforms\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: ['', 'gpu', 'none']\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: [scale]\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': [scale]\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': [scale]\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: [isInteger, isArbitraryValue]\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': [translate]\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': [translate]\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': [skew]\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': [skew]\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: ['center', 'top', 'top-right', 'right', 'bottom-right', 'bottom', 'bottom-left', 'left', 'top-left', isArbitraryValue]\n      }],\n      // Interactivity\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: ['auto', colors]\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryValue]\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: [colors]\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['none', 'auto']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', 'y', 'x', '']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryValue]\n      }],\n      // SVG\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: [colors, 'none']\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: [colors, 'none']\n      }],\n      // Accessibility\n      /**\n       * Screen Readers\n       * @see https://tailwindcss.com/docs/screen-readers\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    }\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  separator,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'separator', separator);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  for (const configKey in override) {\n    overrideConfigProperties(baseConfig[configKey], override[configKey]);\n  }\n  for (const key in extend) {\n    mergeConfigProperties(baseConfig[key], extend[key]);\n  }\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      const mergeValue = mergeObject[key];\n      if (mergeValue !== undefined) {\n        baseObject[key] = (baseObject[key] || []).concat(mergeValue);\n      }\n    }\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\n\n//# sourceMappingURL=bundle-mjs.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/tailwind-merge/dist/bundle-mjs.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTailwindMerge: () => (/* binding */ createTailwindMerge),\n/* harmony export */   extendTailwindMerge: () => (/* binding */ extendTailwindMerge),\n/* harmony export */   fromTheme: () => (/* binding */ fromTheme),\n/* harmony export */   getDefaultConfig: () => (/* binding */ getDefaultConfig),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   twJoin: () => (/* binding */ twJoin),\n/* harmony export */   twMerge: () => (/* binding */ twMerge),\n/* harmony export */   validators: () => (/* binding */ validators)\n/* harmony export */ });\nconst CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    prefix\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  const prefixedClassGroupEntries = getPrefixedClassGroupEntries(Object.entries(config.classGroups), prefix);\n  prefixedClassGroupEntries.forEach(([classGroupId, classGroup]) => {\n    processClassesRecursively(classGroup, classMap, classGroupId, theme);\n  });\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\nconst getPrefixedClassGroupEntries = (classGroupEntries, prefix) => {\n  if (!prefix) {\n    return classGroupEntries;\n  }\n  return classGroupEntries.map(([classGroupId, classGroup]) => {\n    const prefixedClassGroup = classGroup.map(classDefinition => {\n      if (typeof classDefinition === 'string') {\n        return prefix + classDefinition;\n      }\n      if (typeof classDefinition === 'object') {\n        return Object.fromEntries(Object.entries(classDefinition).map(([key, value]) => [prefix + key, value]));\n      }\n      return classDefinition;\n    });\n    return [classGroupId, prefixedClassGroup];\n  });\n};\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst createParseClassName = config => {\n  const {\n    separator,\n    experimentalParseClassName\n  } = config;\n  const isSeparatorSingleCharacter = separator.length === 1;\n  const firstSeparatorCharacter = separator[0];\n  const separatorLength = separator.length;\n  // parseClassName inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n  const parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0) {\n        if (currentCharacter === firstSeparatorCharacter && (isSeparatorSingleCharacter || className.slice(index, index + separatorLength) === separator)) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + separatorLength;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const hasImportantModifier = baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER);\n    const baseClassName = hasImportantModifier ? baseClassNameWithImportantModifier.substring(1) : baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (experimentalParseClassName) {\n    return className => experimentalParseClassName({\n      className,\n      parseClassName\n    });\n  }\n  return parseClassName;\n};\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst sortModifiers = modifiers => {\n  if (modifiers.length <= 1) {\n    return modifiers;\n  }\n  const sortedModifiers = [];\n  let unsortedModifiers = [];\n  modifiers.forEach(modifier => {\n    const isArbitraryVariant = modifier[0] === '[';\n    if (isArbitraryVariant) {\n      sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n      unsortedModifiers = [];\n    } else {\n      unsortedModifiers.push(modifier);\n    }\n  });\n  sortedModifiers.push(...unsortedModifiers.sort());\n  return sortedModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  ...createClassGroupUtils(config)\n});\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  let result = '';\n  for (let i = classList.length - 1; i >= 0;) {\n    while (classList[i] === ' ') {\n      --i;\n    }\n    const nextI = classList.lastIndexOf(' ', i);\n    const originalClassName = classList.slice(nextI === -1 ? 0 : nextI + 1, i + 1);\n    i = nextI;\n    const {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    let hasPostfixModifier = Boolean(maybePostfixModifierPosition);\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst stringLengths = /*#__PURE__*/new Set(['px', 'full', 'screen']);\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isLength = value => isNumber(value) || stringLengths.has(value) || fractionRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, 'length', isLengthOnly);\nconst isNumber = value => Boolean(value) && !Number.isNaN(Number(value));\nconst isArbitraryNumber = value => getIsArbitraryValue(value, 'number', isNumber);\nconst isInteger = value => Boolean(value) && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst sizeLabels = /*#__PURE__*/new Set(['length', 'size', 'percentage']);\nconst isArbitrarySize = value => getIsArbitraryValue(value, sizeLabels, isNever);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, 'position', isNever);\nconst imageLabels = /*#__PURE__*/new Set(['image', 'url']);\nconst isArbitraryImage = value => getIsArbitraryValue(value, imageLabels, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, '', isShadow);\nconst isAny = () => true;\nconst getIsArbitraryValue = (value, label, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return typeof label === 'string' ? result[1] === label : label.has(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isInteger,\n  isLength,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  const colors = fromTheme('colors');\n  const spacing = fromTheme('spacing');\n  const blur = fromTheme('blur');\n  const brightness = fromTheme('brightness');\n  const borderColor = fromTheme('borderColor');\n  const borderRadius = fromTheme('borderRadius');\n  const borderSpacing = fromTheme('borderSpacing');\n  const borderWidth = fromTheme('borderWidth');\n  const contrast = fromTheme('contrast');\n  const grayscale = fromTheme('grayscale');\n  const hueRotate = fromTheme('hueRotate');\n  const invert = fromTheme('invert');\n  const gap = fromTheme('gap');\n  const gradientColorStops = fromTheme('gradientColorStops');\n  const gradientColorStopPositions = fromTheme('gradientColorStopPositions');\n  const inset = fromTheme('inset');\n  const margin = fromTheme('margin');\n  const opacity = fromTheme('opacity');\n  const padding = fromTheme('padding');\n  const saturate = fromTheme('saturate');\n  const scale = fromTheme('scale');\n  const sepia = fromTheme('sepia');\n  const skew = fromTheme('skew');\n  const space = fromTheme('space');\n  const translate = fromTheme('translate');\n  const getOverscroll = () => ['auto', 'contain', 'none'];\n  const getOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const getSpacingWithAutoAndArbitrary = () => ['auto', isArbitraryValue, spacing];\n  const getSpacingWithArbitrary = () => [isArbitraryValue, spacing];\n  const getLengthWithEmptyAndArbitrary = () => ['', isLength, isArbitraryLength];\n  const getNumberWithAutoAndArbitrary = () => ['auto', isNumber, isArbitraryValue];\n  const getPositions = () => ['bottom', 'center', 'left', 'left-bottom', 'left-top', 'right', 'right-bottom', 'right-top', 'top'];\n  const getLineStyles = () => ['solid', 'dashed', 'dotted', 'double', 'none'];\n  const getBlendModes = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const getAlign = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch'];\n  const getZeroAndEmpty = () => ['', '0', isArbitraryValue];\n  const getBreaks = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const getNumberAndArbitrary = () => [isNumber, isArbitraryValue];\n  return {\n    cacheSize: 500,\n    separator: ':',\n    theme: {\n      colors: [isAny],\n      spacing: [isLength, isArbitraryLength],\n      blur: ['none', '', isTshirtSize, isArbitraryValue],\n      brightness: getNumberAndArbitrary(),\n      borderColor: [colors],\n      borderRadius: ['none', '', 'full', isTshirtSize, isArbitraryValue],\n      borderSpacing: getSpacingWithArbitrary(),\n      borderWidth: getLengthWithEmptyAndArbitrary(),\n      contrast: getNumberAndArbitrary(),\n      grayscale: getZeroAndEmpty(),\n      hueRotate: getNumberAndArbitrary(),\n      invert: getZeroAndEmpty(),\n      gap: getSpacingWithArbitrary(),\n      gradientColorStops: [colors],\n      gradientColorStopPositions: [isPercent, isArbitraryLength],\n      inset: getSpacingWithAutoAndArbitrary(),\n      margin: getSpacingWithAutoAndArbitrary(),\n      opacity: getNumberAndArbitrary(),\n      padding: getSpacingWithArbitrary(),\n      saturate: getNumberAndArbitrary(),\n      scale: getNumberAndArbitrary(),\n      sepia: getZeroAndEmpty(),\n      skew: getNumberAndArbitrary(),\n      space: getSpacingWithArbitrary(),\n      translate: getSpacingWithArbitrary()\n    },\n    classGroups: {\n      // Layout\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', 'video', isArbitraryValue]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isTshirtSize]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': getBreaks()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': getBreaks()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: [...getPositions(), isArbitraryValue]\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: getOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': getOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': getOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: getOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': getOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': getOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: [inset]\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': [inset]\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': [inset]\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: [inset]\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: [inset]\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: [inset]\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: [inset]\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: [inset]\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: [inset]\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: ['auto', isInteger, isArbitraryValue]\n      }],\n      // Flexbox and Grid\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: getSpacingWithAutoAndArbitrary()\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['wrap', 'wrap-reverse', 'nowrap']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: ['1', 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: getZeroAndEmpty()\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: getZeroAndEmpty()\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: ['first', 'last', 'none', isInteger, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': [isAny]\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: ['auto', {\n          span: ['full', isInteger, isArbitraryValue]\n        }, isArbitraryValue]\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': [isAny]\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: ['auto', {\n          span: [isInteger, isArbitraryValue]\n        }, isArbitraryValue]\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': ['auto', 'min', 'max', 'fr', isArbitraryValue]\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': ['auto', 'min', 'max', 'fr', isArbitraryValue]\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: [gap]\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': [gap]\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': [gap]\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: ['normal', ...getAlign()]\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': ['start', 'end', 'center', 'stretch']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', 'start', 'end', 'center', 'stretch']\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...getAlign(), 'baseline']\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: ['start', 'end', 'center', 'baseline', 'stretch']\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', 'start', 'end', 'center', 'stretch', 'baseline']\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': [...getAlign(), 'baseline']\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': ['start', 'end', 'center', 'baseline', 'stretch']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', 'start', 'end', 'center', 'stretch']\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: [padding]\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: [padding]\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: [padding]\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: [padding]\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: [padding]\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: [padding]\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: [padding]\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: [padding]\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: [padding]\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: [margin]\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: [margin]\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: [margin]\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: [margin]\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: [margin]\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: [margin]\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: [margin]\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: [margin]\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: [margin]\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-x': [{\n        'space-x': [space]\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-y': [{\n        'space-y': [space]\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // Sizing\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: ['auto', 'min', 'max', 'fit', 'svw', 'lvw', 'dvw', isArbitraryValue, spacing]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [isArbitraryValue, spacing, 'min', 'max', 'fit']\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [isArbitraryValue, spacing, 'none', 'full', 'min', 'max', 'fit', 'prose', {\n          screen: [isTshirtSize]\n        }, isTshirtSize]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/size\n       */\n      size: [{\n        size: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit']\n      }],\n      // Typography\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', isTshirtSize, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black', isArbitraryNumber]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isAny]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractons'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest', isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': ['none', isNumber, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose', isLength, isArbitraryValue]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryValue]\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['none', 'disc', 'decimal', isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: [colors]\n      }],\n      /**\n       * Placeholder Opacity\n       * @see https://tailwindcss.com/docs/placeholder-opacity\n       */\n      'placeholder-opacity': [{\n        'placeholder-opacity': [opacity]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: [colors]\n      }],\n      /**\n       * Text Opacity\n       * @see https://tailwindcss.com/docs/text-opacity\n       */\n      'text-opacity': [{\n        'text-opacity': [opacity]\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...getLineStyles(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: ['auto', 'from-font', isLength, isArbitraryLength]\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': ['auto', isLength, isArbitraryValue]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: [colors]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: getSpacingWithArbitrary()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryValue]\n      }],\n      // Backgrounds\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Opacity\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/background-opacity\n       */\n      'bg-opacity': [{\n        'bg-opacity': [opacity]\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: [...getPositions(), isArbitraryPosition]\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: ['no-repeat', {\n          repeat: ['', 'x', 'y', 'round', 'space']\n        }]\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: ['auto', 'cover', 'contain', isArbitrarySize]\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          'gradient-to': ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n        }, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: [colors]\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: [gradientColorStops]\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: [gradientColorStops]\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: [gradientColorStops]\n      }],\n      // Borders\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: [borderRadius]\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': [borderRadius]\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': [borderRadius]\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': [borderRadius]\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': [borderRadius]\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': [borderRadius]\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': [borderRadius]\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': [borderRadius]\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': [borderRadius]\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': [borderRadius]\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': [borderRadius]\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': [borderRadius]\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: [borderWidth]\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': [borderWidth]\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': [borderWidth]\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': [borderWidth]\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': [borderWidth]\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': [borderWidth]\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': [borderWidth]\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': [borderWidth]\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': [borderWidth]\n      }],\n      /**\n       * Border Opacity\n       * @see https://tailwindcss.com/docs/border-opacity\n       */\n      'border-opacity': [{\n        'border-opacity': [opacity]\n      }],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...getLineStyles(), 'hidden']\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-x': [{\n        'divide-x': [borderWidth]\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-y': [{\n        'divide-y': [borderWidth]\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Divide Opacity\n       * @see https://tailwindcss.com/docs/divide-opacity\n       */\n      'divide-opacity': [{\n        'divide-opacity': [opacity]\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/divide-style\n       */\n      'divide-style': [{\n        divide: getLineStyles()\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: [borderColor]\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': [borderColor]\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': [borderColor]\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': [borderColor]\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': [borderColor]\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': [borderColor]\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': [borderColor]\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: [borderColor]\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: ['', ...getLineStyles()]\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isLength, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: [isLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: [colors]\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/ring-width\n       */\n      'ring-w': [{\n        ring: getLengthWithEmptyAndArbitrary()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://tailwindcss.com/docs/ring-width\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/ring-color\n       */\n      'ring-color': [{\n        ring: [colors]\n      }],\n      /**\n       * Ring Opacity\n       * @see https://tailwindcss.com/docs/ring-opacity\n       */\n      'ring-opacity': [{\n        'ring-opacity': [opacity]\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://tailwindcss.com/docs/ring-offset-width\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isLength, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://tailwindcss.com/docs/ring-offset-color\n       */\n      'ring-offset-color': [{\n        'ring-offset': [colors]\n      }],\n      // Effects\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: ['', 'inner', 'none', isTshirtSize, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow-color\n       */\n      'shadow-color': [{\n        shadow: [isAny]\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [opacity]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...getBlendModes(), 'plus-lighter', 'plus-darker']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': getBlendModes()\n      }],\n      // Filters\n      /**\n       * Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: ['', 'none']\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: [blur]\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [brightness]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [contrast]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': ['', 'none', isTshirtSize, isArbitraryValue]\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: [grayscale]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [hueRotate]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: [invert]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [saturate]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: [sepia]\n      }],\n      /**\n       * Backdrop Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': ['', 'none']\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': [blur]\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [brightness]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [contrast]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': [grayscale]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [hueRotate]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': [invert]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [opacity]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [saturate]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': [sepia]\n      }],\n      // Tables\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': [borderSpacing]\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': [borderSpacing]\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': [borderSpacing]\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // Transitions and Animation\n      /**\n       * Tranisition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['none', 'all', '', 'colors', 'opacity', 'shadow', 'transform', isArbitraryValue]\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: getNumberAndArbitrary()\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'in', 'out', 'in-out', isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: getNumberAndArbitrary()\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', 'spin', 'ping', 'pulse', 'bounce', isArbitraryValue]\n      }],\n      // Transforms\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: ['', 'gpu', 'none']\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: [scale]\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': [scale]\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': [scale]\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: [isInteger, isArbitraryValue]\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': [translate]\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': [translate]\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': [skew]\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': [skew]\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: ['center', 'top', 'top-right', 'right', 'bottom-right', 'bottom', 'bottom-left', 'left', 'top-left', isArbitraryValue]\n      }],\n      // Interactivity\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: ['auto', colors]\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryValue]\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: [colors]\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['none', 'auto']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', 'y', 'x', '']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryValue]\n      }],\n      // SVG\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: [colors, 'none']\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: [colors, 'none']\n      }],\n      // Accessibility\n      /**\n       * Screen Readers\n       * @see https://tailwindcss.com/docs/screen-readers\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    }\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  separator,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'separator', separator);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  for (const configKey in override) {\n    overrideConfigProperties(baseConfig[configKey], override[configKey]);\n  }\n  for (const key in extend) {\n    mergeConfigProperties(baseConfig[key], extend[key]);\n  }\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      const mergeValue = mergeObject[key];\n      if (mergeValue !== undefined) {\n        baseObject[key] = (baseObject[key] || []).concat(mergeValue);\n      }\n    }\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\n\n//# sourceMappingURL=bundle-mjs.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\n");

/***/ })

};
;