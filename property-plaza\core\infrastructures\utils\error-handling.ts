import { ACCESS_TOKEN } from "@/lib/constanta/constant";
import axios from "axios";
import Cookies from "js-cookie";
export function errorHandling(e: any) {
  if (axios.isAxiosError(e)) {
    // Provide custom error messages based on the Axios error
    if (e.response?.status === 401) {
      // Cookies.remove(ACCESS_TOKEN);
      throw new Error("Unauthorized: Invalid token or missing credentials");
    } else if (e.response?.status === 404) {
      throw new Error("Not Found: The requested resource could not be found");
    } else if (e.response) {
      throw new Error(
        `Request failed with status code ${e.response.status}: ${e.response.statusText}`
      );
    } else if (e.request) {
      throw new Error(
        "No response received: Possible network error or wrong endpoint"
      );
    } else {
      throw new Error(`Error during request setup: ${e.message}`);
    }
  } else {
    // Handle other unknown errors
    throw new Error(e as any);
  }
}
