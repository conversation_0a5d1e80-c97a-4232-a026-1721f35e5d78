import { GetSeekersListingDto } from "@/core/infrastructures/listing/dto"
import { getSeekersListingService } from "@/core/infrastructures/listing/service"
import { useQuery } from "@tanstack/react-query"

export const SEEKERS_LISTING_QUERY_KEY = "seekers-listing"
export function useGetSeekersListing(data:GetSeekersListingDto,isEnabled:boolean = false){
  const {category,limit,location,section} = data
  const { failureCount,...query} = useQuery({
    queryKey: [SEEKERS_LISTING_QUERY_KEY,category,limit,location,section],
    queryFn: async () => {
      const request: GetSeekersListingDto = {
        ...data,
        category: data.category?.includes("all") ? undefined : data.category,
        location: data.location == "all" ? undefined : data.location,
      }
      const response = await getSeekersListingService(request)
      return response
    },
    enabled:isEnabled,
    retry:false,
  })
  return query
}