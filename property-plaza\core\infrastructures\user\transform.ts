import {
  membershipMaximumChatBasedOnSubscription,
  membershipTypeFormatter,
  membershipZoomFeatureBasedOnSubscription,
  User,
  UserDetail,
  UserType,
} from "@/core/domain/users/user";
import { UserDetailDto, UserDto } from "./dto";

export function transformUser(dto: UserDto[]): User[] {
  const data: User[] = dto.map((item) => ({
    accounts: {
      firstName: item.accounts.first_name,
      createdAt: item.accounts.created_at,
      isSubscriber: item.accounts.is_subscriber,
      lastName: item.accounts.last_name,
      membership: membershipTypeFormatter(
        item.accounts.membership?.detail.name || ""
      ),
    },
    email: item.email,
    code: item.code,
    isActive: item.is_active,
    phoneNumber: item.phone_number,
    type: item.type as UserType,
  }));
  return data;
}

export function transformUserDetail(dto: UserDetailDto): UserDetail {
  const membership = membershipTypeFormatter(
    dto.accounts.subscription?.detail.name || ""
  );
  const data: UserDetail = {
    accounts: {
      about: dto.accounts.about,
      citizenship: dto.accounts.citizenship || "",
      credit: {
        amount: dto.accounts.credit?.amount || 0,
        updatedAt: dto.accounts.credit?.updated_at || "",
      },
      facebookSocial: dto.accounts.facebook_social || "",
      firstName: dto.accounts.first_name,
      image: dto.accounts.image,
      isSubscriber: dto.accounts.is_subscriber,
      language: dto.accounts.language,
      lastName: dto.accounts.last_name,
      membership: membership,
      twitterSocial: dto.accounts.twitter_social || "",
      address: dto.accounts.address || "",

      chat: {
        current: 0, // TODO: Add data based on Services
        max: membershipMaximumChatBasedOnSubscription(membership),
      },
      zoomFeature: membershipZoomFeatureBasedOnSubscription(membership),
    },
    has2FA: dto.is_2fa,
    email: dto.email,
    code: dto.code,
    isActive: dto.is_active,
    phoneNumber: dto.phone_number,
    phoneCode: dto.phone_code,
    type: dto.type as UserType,
    setting: {
      messageNotif: dto.accounts.settings?.message_notif,
      newsletterNotif: dto.accounts.settings?.newsletter_notif,
      priceAlertNotif: dto.accounts.settings?.price_alert_notif,
      propertyNotif: dto.accounts.settings?.property_notif,
      soundNotif: dto.accounts.settings?.sound_notif,
      specialOfferNotif: dto.accounts.settings?.special_offer_notif,
      surveyNotif: dto.accounts.settings?.survey_notif,
    },
  };
  return data;
}
