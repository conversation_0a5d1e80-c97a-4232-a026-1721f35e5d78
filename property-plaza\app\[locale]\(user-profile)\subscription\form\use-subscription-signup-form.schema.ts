import { MAX_NAME_LENGTH, MIN_NAME_LENGTH } from "@/lib/constanta/constant";
import { useTranslations } from "next-intl";
import { z } from "zod";
export function useSubscriptionSignUpFormSchema() {
  const t = useTranslations("seeker");
  const formSchema = z.object({
    firstName: z
      .string()
      .min(MIN_NAME_LENGTH, {
        message: t("form.utility.minimumLength", {
          field: t("form.field.firstName"),
          length: MIN_NAME_LENGTH,
        }),
      })
      .max(MAX_NAME_LENGTH, {
        message: t("form.utility.maximumLength", {
          field: t("form.field.firstName"),
          length: MAX_NAME_LENGTH,
        }),
      }),
    lastName: z
      .string()
      .min(MIN_NAME_LENGTH, {
        message: t("form.utility.minimumLength", {
          field: t("form.field.lastName"),
          length: MIN_NAME_LENGTH,
        }),
      })
      .max(MAX_NAME_LENGTH, {
        message: t("form.utility.maximumLength", {
          field: t("form.field.lastName"),
          length: MAX_NAME_LENGTH,
        }),
      }),
    contact: z.string().email({
      message: t("form.utility.enterValidField", {
        field: ` ${t("form.field.email")}`,
      }),
    }),
  });
  return formSchema;
}
