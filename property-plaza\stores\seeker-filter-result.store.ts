import { ListingListSeekers } from "@/core/domain/listing/listing-seekers";
import { create } from "zustand";

interface SeekersFilterResult {
  data: ListingListSeekers[]
  setData:(data:ListingListSeekers[]) => void
  total:number,
  setTotal:(total:number) => void,
  isLoading:boolean,
  setIsLoading:(isLoading?:boolean) => void
}

export const useSeekerFilterResultStore = create<SeekersFilterResult>(set => ({
  data: [],
  setData:(data) => set(({data})),
  total:0,
  setTotal:(total) => set(({total})),
  isLoading:true,
  setIsLoading:(isLoading?:boolean) => set(({isLoading}))
}))