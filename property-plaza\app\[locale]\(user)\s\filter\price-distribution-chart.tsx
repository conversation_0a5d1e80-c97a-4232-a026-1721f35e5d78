import { Chart<PERSON>ontainer } from "@/components/ui/chart";
import { FilterPriceDistribution } from "@/core/domain/listing/listing-seekers";
import { type ChartConfig } from "@/components/ui/chart"
import { Bar, BarChart, Cell } from "recharts";
export default function PriceDistributionChart({ data, range }: { data: FilterPriceDistribution[], range: number[] }) {
  const chartConfig = {
    amount: {
      label: "property",
      color: "#A88851"
    }
  } satisfies ChartConfig
  return <ChartContainer config={chartConfig} className="h-[95px] w-full">
    <BarChart accessibilityLayer data={data} className="min-w-full min-h-[95px]">
      <Bar
        isAnimationActive={false}
        dataKey="amount"
        fill="var(--color-amount)"
        className="min-w-full min-h-[95px]"
      >
        {/* Use Cell to conditionally style each bar */}
        {data.map((entry, index) => (
          <Cell
            key={`cell-${index}`}
            fill={
              Number(entry.price) >= range[0] &&
                Number(entry.price) <= range[1]
                ? "var(--color-amount)" // Highlighted color
                : "#d3d3d3" // Dimmed color
            }
            opacity={
              Number(entry.price) >= range[0] &&
                Number(entry.price) <= range[1]
                ? 1
                : 0.3 // Reduced opacity for dimmed bars
            }
          />
        ))}
      </Bar>
    </BarChart>
  </ChartContainer>
}