import { GetLocationSuggestionDto } from "@/core/infrastructures/listing/dto";
import { getLocationSuggestionService } from "@/core/infrastructures/listing/service";
import { useQuery } from "@tanstack/react-query";

export const LOCATION_SUGGESTION_QUERY_KEY = "location-suggestion"
export function useGetLocationSuggestion(data:GetLocationSuggestionDto){
const {search} = data
const query = useQuery({
  queryKey: [LOCATION_SUGGESTION_QUERY_KEY,search],
  queryFn: async () => {
    const response = await getLocationSuggestionService(data)
    return response 
  },
  retry:false,
})
return query
}