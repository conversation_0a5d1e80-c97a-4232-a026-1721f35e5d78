import { cookies } from "next/headers";
import HeroTermsOfUseSection from "./hero";
import { getTranslations } from "next-intl/server";
import { getPrivacyPolictContent, getTermsOfUseContent } from "@/core/services/sanity/services";
import Content from "./content";
import { Metadata } from "next";
import { privacySeekerUrl } from "@/lib/constanta/route";

export async function generateMetadata(): Promise<Metadata> {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const t = await getTranslations("seeker")
  return {
    title: t('metadata.privacyPolicy.title'),
    description: t('metadata.privacyPolicy.description'),
    alternates: {
      languages: {
        "id": process.env.USER_DOMAIN + `/id/${privacySeekerUrl}`,
        "en": process.env.USER_DOMAIN + `/en/${privacySeekerUrl}`,
      },

    }
  }
}

export default async function PrivacyPololicyPage() {
  const cookiesStore = cookies()
  const locale = cookiesStore.get('NEXT_LOCALE')?.value
  const t = await getTranslations("seeker")
  // const termOfUseContent = await getTermsOfUseContent(locale?.toLocaleLowerCase() || "en") // use this when locale for content are implemented
  const termOfUseContent = await getPrivacyPolictContent("en")
  return <>
    <HeroTermsOfUseSection />
    <Content content={termOfUseContent[0]} />
  </>
}