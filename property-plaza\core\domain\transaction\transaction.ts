export type TypeTransaction =
  | "CREDIT_DEDUCTION"
  | "CREDIT_TOP_UP"
  | "CREDIT_REFUND"
  | "SUBSCRIBE";

export interface Transaction {
  code: string;
  grandTotal: number;
  credit: number;
  type: TypeTransaction;
  status: string;
  requestAt: string;
  PayedAt: string;
  url: string;
  productName: string;
  isActive: boolean;
  nextBilling: string;
}

export interface TransactionDetail {
  code: string;
  grandTotal: number;
  credit: number;
  type: string;
  status: string;
  requestAt: string;
  PayedAt: string;
  url: string;
  productName: string;
  price: string;
}
export interface BillingInformation {
  name: string;
  addressOne: string;
  addressTwo: string;
  city: string;
  country: string;
  postalCode: string;
  state: string;
}

export interface SeekerTransaction extends Transaction {
  date: string;
  invoiceNumber: string;
  plan: string;
  amount: string;
  downloadUrl: string;
}

export interface PaymentMethod {
  id: string;
  brand: string;
  cardNumber: string;
  expiredMonth: string;
  expiredYear: number;
  isDefault: boolean;
}
