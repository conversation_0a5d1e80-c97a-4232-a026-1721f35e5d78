import { uploadFile } from "@/core/infrastructures/file-upload/api";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

export function useUploadFile(){
  const {toast} = useToast()
  const t = useTranslations()
  const mutation = useMutation({
    mutationFn: (data:FormData) => uploadFile(data),
    onSuccess: (response) => {
      const data = response.data.data
      return data.url
    },
    onError:(error) => {
      const data: any = (error as any).response.data
      toast({
        title: t('misc.foundError'),
        description: data.message,
        variant: "destructive"
      })
    },
  })
  return mutation
}