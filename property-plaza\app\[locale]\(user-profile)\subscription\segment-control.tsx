"use client"

import type * as React from "react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"

interface SegmentControlProps extends React.HTMLAttributes<HTMLDivElement> {
  value: string
  onValueChange: (value: string) => void
  options: {
    value: string
    label: string
    badge?: string
  }[]
}

export default function SegmentControl({ value, onValueChange, options, className, ...props }: SegmentControlProps) {
  return (
    <div className={cn("inline-flex rounded-lg bg-seekers-primary p-1", className)} {...props}>
      {options.map((option) => (
        <div key={option.value} className="relative">
          <button
            onClick={() => onValueChange(option.value)}
            className={cn(
              "relative px-8 py-2 text-sm font-medium transition-colors rounded-md",
              value === option.value ? "bg-white text-seekers-primary" : "text-white hover:bg-white/10",
            )}
          >
            {option.label}
          </button>
          {option.badge && (
            <Badge className="absolute -right-2 -top-2 rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-700">
              {option.badge}
            </Badge>
          )}
        </div>
      ))}
    </div>
  )
}

