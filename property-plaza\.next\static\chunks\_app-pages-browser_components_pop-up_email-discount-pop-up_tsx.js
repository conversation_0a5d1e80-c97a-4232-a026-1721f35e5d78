"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_components_pop-up_email-discount-pop-up_tsx"],{

/***/ "(app-pages-browser)/./public/pop-up-background.jpeg":
/*!***************************************!*\
  !*** ./public/pop-up-background.jpeg ***!
  \***************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/pop-up-background.07e123db.jpeg\",\"height\":640,\"width\":960,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fpop-up-background.07e123db.jpeg&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3B1YmxpYy9wb3AtdXAtYmFja2dyb3VuZC5qcGVnIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLHdOQUF3TiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wdWJsaWMvcG9wLXVwLWJhY2tncm91bmQuanBlZz8xOGNlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9wb3AtdXAtYmFja2dyb3VuZC4wN2UxMjNkYi5qcGVnXCIsXCJoZWlnaHRcIjo2NDAsXCJ3aWR0aFwiOjk2MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZwb3AtdXAtYmFja2dyb3VuZC4wN2UxMjNkYi5qcGVnJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./public/pop-up-background.jpeg\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/[locale]/create-password/form/use-email-form.schema.ts":
/*!********************************************************************!*\
  !*** ./app/[locale]/create-password/form/use-email-form.schema.ts ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEmailFormSchema: function() { return /* binding */ useEmailFormSchema; }\n/* harmony export */ });\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n\n\nfunction useEmailFormSchema() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_0__.useTranslations)(\"universal\");\n    const formSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n        email: zod__WEBPACK_IMPORTED_MODULE_1__.z.string({\n            message: t(\"form.utility.fieldRequired\", {\n                field: t(\"form.field.email\")\n            })\n        }).email()\n    });\n    return formSchema;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9bbG9jYWxlXS9jcmVhdGUtcGFzc3dvcmQvZm9ybS91c2UtZW1haWwtZm9ybS5zY2hlbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0QztBQUNwQjtBQUVqQixTQUFTRTtJQUNkLE1BQU1DLElBQUlILDBEQUFlQSxDQUFDO0lBQzFCLE1BQU1JLGFBQWFILGtDQUFDQSxDQUFDSSxNQUFNLENBQUM7UUFDMUJDLE9BQU9MLGtDQUFDQSxDQUFDTSxNQUFNLENBQUM7WUFBQ0MsU0FBU0wsRUFBRSw4QkFBOEI7Z0JBQUNNLE9BQU9OLEVBQUU7WUFBbUI7UUFBRSxHQUFHRyxLQUFLO0lBQ25HO0lBQ0EsT0FBT0Y7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvW2xvY2FsZV0vY3JlYXRlLXBhc3N3b3JkL2Zvcm0vdXNlLWVtYWlsLWZvcm0uc2NoZW1hLnRzPzg1MmMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlVHJhbnNsYXRpb25zIH0gZnJvbSBcIm5leHQtaW50bFwiO1xyXG5pbXBvcnQgeyB6IH0gZnJvbSBcInpvZFwiO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHVzZUVtYWlsRm9ybVNjaGVtYSgpe1xyXG4gIGNvbnN0IHQgPSB1c2VUcmFuc2xhdGlvbnMoXCJ1bml2ZXJzYWxcIilcclxuICBjb25zdCBmb3JtU2NoZW1hID0gei5vYmplY3Qoe1xyXG4gICAgZW1haWw6IHouc3RyaW5nKHttZXNzYWdlOiB0KFwiZm9ybS51dGlsaXR5LmZpZWxkUmVxdWlyZWRcIiwge2ZpZWxkOiB0KFwiZm9ybS5maWVsZC5lbWFpbFwiKX0pfSkuZW1haWwoKVxyXG4gIH0pXHJcbiAgcmV0dXJuIGZvcm1TY2hlbWFcclxufSJdLCJuYW1lcyI6WyJ1c2VUcmFuc2xhdGlvbnMiLCJ6IiwidXNlRW1haWxGb3JtU2NoZW1hIiwidCIsImZvcm1TY2hlbWEiLCJvYmplY3QiLCJlbWFpbCIsInN0cmluZyIsIm1lc3NhZ2UiLCJmaWVsZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/create-password/form/use-email-form.schema.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/pop-up/email-discount-pop-up.tsx":
/*!*****************************************************!*\
  !*** ./components/pop-up/email-discount-pop-up.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EmailDiscountPopup; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dialog_wrapper_dialog_wrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../dialog-wrapper/dialog-wrapper */ \"(app-pages-browser)/./components/dialog-wrapper/dialog-wrapper.tsx\");\n/* harmony import */ var _dialog_wrapper_dialog_header_wrapper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../dialog-wrapper/dialog-header-wrapper */ \"(app-pages-browser)/./components/dialog-wrapper/dialog-header-wrapper.tsx\");\n/* harmony import */ var _email_discount_pop_up_email_input_discount_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./email-discount-pop-up/email-input-discount.form */ \"(app-pages-browser)/./components/pop-up/email-discount-pop-up/email-input-discount.form.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-motion-value-event.mjs\");\n/* harmony import */ var _stores_user_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/stores/user.store */ \"(app-pages-browser)/./stores/user.store.ts\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _lib_constanta_route__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/constanta/route */ \"(app-pages-browser)/./lib/constanta/route.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _public_pop_up_background_jpeg__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/public/pop-up-background.jpeg */ \"(app-pages-browser)/./public/pop-up-background.jpeg\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst DISCOUNT_CODE = \"WELCOME25\";\nconst DISCOUNT_PERCENTAGE = 25;\nfunction EmailDiscountPopup() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_12__.useTranslations)(\"seeker\");\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_12__.useLocale)();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isValidScroll, setIsValidScroll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_13__.useScroll)();\n    const { seekers, hydrated } = (0,_stores_user_store__WEBPACK_IMPORTED_MODULE_5__.useUserStore)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    (0,framer_motion__WEBPACK_IMPORTED_MODULE_14__.useMotionValueEvent)(scrollYProgress, \"change\", (latest)=>{\n        if (latest > 0.3) {\n            setIsValidScroll(true);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!hydrated) return;\n        if (seekers.email) return;\n        if (isValidScroll) {\n            const timer = setTimeout(()=>setOpen(true), 500);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        hydrated,\n        seekers.email,\n        isValidScroll\n    ]);\n    const [isSubmittedEmail, setSubmittedEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dialog_wrapper_dialog_wrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        open: open,\n        setOpen: setOpen,\n        openTrigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n        dialogClassName: \"overflow-hidden\",\n        drawerClassName: \"overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dialog_wrapper_dialog_header_wrapper__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"relative h-48\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-base font-bold text-seekers-text hidden\",\n                    children: t(\"promotion.popUp.title\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                lineNumber: 49,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-52 overflow-hidden rounded-t-lg -z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    src: _public_pop_up_background_jpeg__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    alt: \"pop-up-background\",\n                    className: \"object-cover\",\n                    fill: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                lineNumber: 55,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-bold text-seekers-text\",\n                        children: t(\"promotion.popUp.title\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 7\n                    }, this),\n                    isSubmittedEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-seekers-text-light\",\n                                children: t.rich(\"promotion.popUp.couponCodeDescription\", {\n                                    code: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"b\", {\n                                            children: DISCOUNT_CODE\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 29\n                                        }, this)\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                asChild: true,\n                                onClick: ()=>{\n                                    navigator.clipboard.writeText(DISCOUNT_CODE);\n                                    toast({\n                                        title: t(\"misc.copy.successCopyContent\", {\n                                            content: t(\"misc.promoCode\")\n                                        })\n                                    });\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-full\",\n                                    href: _lib_constanta_route__WEBPACK_IMPORTED_MODULE_8__.noLoginPlanUrl,\n                                    hrefLang: locale,\n                                    children: t(\"cta.useDiscountCode\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-seekers-text-light\",\n                                children: t(\"promotion.popUp.description\", {\n                                    count: DISCOUNT_PERCENTAGE\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_email_discount_pop_up_email_input_discount_form__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                setIsSubmitted: setSubmittedEmail\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"link\",\n                                className: \"w-full\",\n                                onClick: ()=>setOpen(false),\n                                children: t(\"misc.maybeLater\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-center text-seekers-text-light\",\n                                children: t.rich(\"promotion.popUp.termsAndCondition\", {\n                                    term: (chunk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-seekers-text\",\n                                            children: chunk\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 32\n                                        }, this),\n                                    privacy: (chunk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-seekers-text\",\n                                            children: chunk\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 35\n                                        }, this)\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n                lineNumber: 58,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up.tsx\",\n        lineNumber: 42,\n        columnNumber: 10\n    }, this);\n}\n_s(EmailDiscountPopup, \"Bv1spn2PM5w+xsyYjVYKbK0hvSk=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_12__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_12__.useLocale,\n        framer_motion__WEBPACK_IMPORTED_MODULE_13__.useScroll,\n        _stores_user_store__WEBPACK_IMPORTED_MODULE_5__.useUserStore,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast,\n        framer_motion__WEBPACK_IMPORTED_MODULE_14__.useMotionValueEvent\n    ];\n});\n_c = EmailDiscountPopup;\nvar _c;\n$RefreshReg$(_c, \"EmailDiscountPopup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pop-up/email-discount-pop-up.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/pop-up/email-discount-pop-up/email-input-discount.form.tsx":
/*!*******************************************************************************!*\
  !*** ./components/pop-up/email-discount-pop-up/email-input-discount.form.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EmailInputDiscountForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_locale_create_password_form_use_email_form_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/[locale]/create-password/form/use-email-form.schema */ \"(app-pages-browser)/./app/[locale]/create-password/form/use-email-form.schema.ts\");\n/* harmony import */ var _components_input_form_default_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/input-form/default-input */ \"(app-pages-browser)/./components/input-form/default-input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _core_applications_mutations_waiting_list_use_join_waiting_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/core/applications/mutations/waiting-list/use-join-waiting-list */ \"(app-pages-browser)/./core/applications/mutations/waiting-list/use-join-waiting-list.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction EmailInputDiscountForm(param) {\n    let { setIsSubmitted } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"seeker\");\n    const formSchema = (0,_app_locale_create_password_form_use_email_form_schema__WEBPACK_IMPORTED_MODULE_1__.useEmailFormSchema)();\n    const useWaitingJoinMutation = (0,_core_applications_mutations_waiting_list_use_join_waiting_list__WEBPACK_IMPORTED_MODULE_5__.useJoinWaitingList)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_7__.zodResolver)(formSchema),\n        defaultValues: {\n            email: \"\"\n        }\n    });\n    const onSubmit = async (values)=>{\n        const data = {\n            email: values.email,\n            name: \"no-name\"\n        };\n        try {\n            await useWaitingJoinMutation.mutateAsync(data);\n            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.gtagEvent)({\n                action: \"lead_magnet_form_submit\",\n                category: \"Lead Magnet\",\n                label: \"Email Capture for get discount code\",\n                value: \"1\"\n            });\n        } catch (e) {} finally{\n            setIsSubmitted(true);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.Form, {\n        ...form,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: form.handleSubmit(onSubmit),\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_input_form_default_input__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    type: \"email\",\n                    form: form,\n                    name: \"email\",\n                    variant: \"float\",\n                    label: t(\"form.label.email\"),\n                    labelClassName: \"text-xs text-seekers-text-light font-normal\",\n                    placeholder: \"\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up\\\\email-input-discount.form.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    type: \"submit\",\n                    className: \"w-full\",\n                    loading: useWaitingJoinMutation.isPending,\n                    children: t(\"cta.getDiscount\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up\\\\email-input-discount.form.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up\\\\email-input-discount.form.tsx\",\n            lineNumber: 43,\n            columnNumber: 5\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza DEV\\\\property-plaza\\\\components\\\\pop-up\\\\email-discount-pop-up\\\\email-input-discount.form.tsx\",\n        lineNumber: 42,\n        columnNumber: 10\n    }, this);\n}\n_s(EmailInputDiscountForm, \"2o8wMn1xvGb3oiGseIJsTRDmsLE=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        _app_locale_create_password_form_use_email_form_schema__WEBPACK_IMPORTED_MODULE_1__.useEmailFormSchema,\n        _core_applications_mutations_waiting_list_use_join_waiting_list__WEBPACK_IMPORTED_MODULE_5__.useJoinWaitingList,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm\n    ];\n});\n_c = EmailInputDiscountForm;\nvar _c;\n$RefreshReg$(_c, \"EmailInputDiscountForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pop-up/email-discount-pop-up/email-input-discount.form.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./core/applications/mutations/waiting-list/use-join-waiting-list.ts":
/*!***************************************************************************!*\
  !*** ./core/applications/mutations/waiting-list/use-join-waiting-list.ts ***!
  \***************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useJoinWaitingList: function() { return /* binding */ useJoinWaitingList; }\n/* harmony export */ });\n/* harmony import */ var _core_infrastructures_waiting_list_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/core/infrastructures/waiting-list/api */ \"(app-pages-browser)/./core/infrastructures/waiting-list/api.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction useJoinWaitingList() {\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"seeker\");\n    const mutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: (data)=>(0,_core_infrastructures_waiting_list_api__WEBPACK_IMPORTED_MODULE_0__.joinWaitingList)(data),\n        onError: (error)=>{\n            const data = error.response.data;\n            toast({\n                title: t(\"misc.foundError\"),\n                description: data.message,\n                variant: \"destructive\"\n            });\n        }\n    });\n    return mutation;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./core/applications/mutations/waiting-list/use-join-waiting-list.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./core/infrastructures/waiting-list/api.ts":
/*!**************************************************!*\
  !*** ./core/infrastructures/waiting-list/api.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   joinWaitingList: function() { return /* binding */ joinWaitingList; }\n/* harmony export */ });\n/* harmony import */ var _core_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/core/client */ \"(app-pages-browser)/./core/client.ts\");\n\nconst joinWaitingList = (data)=>_core_client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"waiting-list\", data);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvcmUvaW5mcmFzdHJ1Y3R1cmVzL3dhaXRpbmctbGlzdC9hcGkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEM7QUFHbkMsTUFBTUMsa0JBQWtCLENBQUNDLE9BQzlCRixtREFBU0EsQ0FBQ0csSUFBSSxDQUFDLGdCQUFnQkQsTUFBTSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb3JlL2luZnJhc3RydWN0dXJlcy93YWl0aW5nLWxpc3QvYXBpLnRzPzAwNDgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYXBpQ2xpZW50IH0gZnJvbSBcIkAvY29yZS9jbGllbnRcIjtcclxuaW1wb3J0IHsgV2FpdGluZ0xpc3REdG8gfSBmcm9tIFwiLi9kdG9cIjtcclxuXHJcbmV4cG9ydCBjb25zdCBqb2luV2FpdGluZ0xpc3QgPSAoZGF0YTogV2FpdGluZ0xpc3REdG8pID0+XHJcbiAgYXBpQ2xpZW50LnBvc3QoXCJ3YWl0aW5nLWxpc3RcIiwgZGF0YSk7XHJcbiJdLCJuYW1lcyI6WyJhcGlDbGllbnQiLCJqb2luV2FpdGluZ0xpc3QiLCJkYXRhIiwicG9zdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./core/infrastructures/waiting-list/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/batcher.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/frameloop/batcher.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRenderBatcher: function() { return /* binding */ createRenderBatcher; },\n/* harmony export */   stepsOrder: function() { return /* binding */ stepsOrder; }\n/* harmony export */ });\n/* harmony import */ var _utils_GlobalConfig_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/GlobalConfig.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/GlobalConfig.mjs\");\n/* harmony import */ var _render_step_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./render-step.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/render-step.mjs\");\n\n\n\nconst stepsOrder = [\n    \"read\", // Read\n    \"resolveKeyframes\", // Write/Read/Write/Read\n    \"update\", // Compute\n    \"preRender\", // Compute\n    \"render\", // Write\n    \"postRender\", // Compute\n];\nconst maxElapsed = 40;\nfunction createRenderBatcher(scheduleNextBatch, allowKeepAlive) {\n    let runNextFrame = false;\n    let useDefaultElapsed = true;\n    const state = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    const flagRunNextFrame = () => (runNextFrame = true);\n    const steps = stepsOrder.reduce((acc, key) => {\n        acc[key] = (0,_render_step_mjs__WEBPACK_IMPORTED_MODULE_0__.createRenderStep)(flagRunNextFrame);\n        return acc;\n    }, {});\n    const { read, resolveKeyframes, update, preRender, render, postRender } = steps;\n    const processBatch = () => {\n        const timestamp = _utils_GlobalConfig_mjs__WEBPACK_IMPORTED_MODULE_1__.MotionGlobalConfig.useManualTiming\n            ? state.timestamp\n            : performance.now();\n        runNextFrame = false;\n        state.delta = useDefaultElapsed\n            ? 1000 / 60\n            : Math.max(Math.min(timestamp - state.timestamp, maxElapsed), 1);\n        state.timestamp = timestamp;\n        state.isProcessing = true;\n        // Unrolled render loop for better per-frame performance\n        read.process(state);\n        resolveKeyframes.process(state);\n        update.process(state);\n        preRender.process(state);\n        render.process(state);\n        postRender.process(state);\n        state.isProcessing = false;\n        if (runNextFrame && allowKeepAlive) {\n            useDefaultElapsed = false;\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const wake = () => {\n        runNextFrame = true;\n        useDefaultElapsed = true;\n        if (!state.isProcessing) {\n            scheduleNextBatch(processBatch);\n        }\n    };\n    const schedule = stepsOrder.reduce((acc, key) => {\n        const step = steps[key];\n        acc[key] = (process, keepAlive = false, immediate = false) => {\n            if (!runNextFrame)\n                wake();\n            return step.schedule(process, keepAlive, immediate);\n        };\n        return acc;\n    }, {});\n    const cancel = (process) => {\n        for (let i = 0; i < stepsOrder.length; i++) {\n            steps[stepsOrder[i]].cancel(process);\n        }\n    };\n    return { schedule, cancel, state, steps };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/batcher.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/frameloop/frame.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelFrame: function() { return /* binding */ cancelFrame; },\n/* harmony export */   frame: function() { return /* binding */ frame; },\n/* harmony export */   frameData: function() { return /* binding */ frameData; },\n/* harmony export */   frameSteps: function() { return /* binding */ frameSteps; }\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _batcher_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./batcher.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/batcher.mjs\");\n\n\n\nconst { schedule: frame, cancel: cancelFrame, state: frameData, steps: frameSteps, } = (0,_batcher_mjs__WEBPACK_IMPORTED_MODULE_1__.createRenderBatcher)(typeof requestAnimationFrame !== \"undefined\" ? requestAnimationFrame : motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop, true);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZnJhbWVsb29wL2ZyYW1lLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBb0M7QUFDZ0I7O0FBRXBELFFBQVEsNkVBQTZFLEVBQUUsaUVBQW1CLHdFQUF3RSw4Q0FBSTs7QUFFakkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9mcmFtZWxvb3AvZnJhbWUubWpzP2JmMDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbm9vcCB9IGZyb20gJ21vdGlvbi11dGlscyc7XG5pbXBvcnQgeyBjcmVhdGVSZW5kZXJCYXRjaGVyIH0gZnJvbSAnLi9iYXRjaGVyLm1qcyc7XG5cbmNvbnN0IHsgc2NoZWR1bGU6IGZyYW1lLCBjYW5jZWw6IGNhbmNlbEZyYW1lLCBzdGF0ZTogZnJhbWVEYXRhLCBzdGVwczogZnJhbWVTdGVwcywgfSA9IGNyZWF0ZVJlbmRlckJhdGNoZXIodHlwZW9mIHJlcXVlc3RBbmltYXRpb25GcmFtZSAhPT0gXCJ1bmRlZmluZWRcIiA/IHJlcXVlc3RBbmltYXRpb25GcmFtZSA6IG5vb3AsIHRydWUpO1xuXG5leHBvcnQgeyBjYW5jZWxGcmFtZSwgZnJhbWUsIGZyYW1lRGF0YSwgZnJhbWVTdGVwcyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/render-step.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/frameloop/render-step.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRenderStep: function() { return /* binding */ createRenderStep; }\n/* harmony export */ });\nfunction createRenderStep(runNextFrame) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Set();\n    let nextFrame = new Set();\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    let latestFrameData = {\n        delta: 0.0,\n        timestamp: 0.0,\n        isProcessing: false,\n    };\n    function triggerCallback(callback) {\n        if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n        }\n        callback(latestFrameData);\n    }\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (!queue.has(callback))\n                queue.add(callback);\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.delete(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            latestFrameData = frameData;\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Execute this frame\n            thisFrame.forEach(triggerCallback);\n            // Clear the frame so no callbacks remain. This is to avoid\n            // memory leaks should this render step not run for a while.\n            thisFrame.clear();\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/render-step.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/sync-time.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/frameloop/sync-time.mjs ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   time: function() { return /* binding */ time; }\n/* harmony export */ });\n/* harmony import */ var _utils_GlobalConfig_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/GlobalConfig.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/GlobalConfig.mjs\");\n/* harmony import */ var _frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\nlet now;\nfunction clearTime() {\n    now = undefined;\n}\n/**\n * An eventloop-synchronous alternative to performance.now().\n *\n * Ensures that time measurements remain consistent within a synchronous context.\n * Usually calling performance.now() twice within the same synchronous context\n * will return different values which isn't useful for animations when we're usually\n * trying to sync animations to the same frame.\n */\nconst time = {\n    now: () => {\n        if (now === undefined) {\n            time.set(_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.isProcessing || _utils_GlobalConfig_mjs__WEBPACK_IMPORTED_MODULE_1__.MotionGlobalConfig.useManualTiming\n                ? _frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.timestamp\n                : performance.now());\n        }\n        return now;\n    },\n    set: (newTime) => {\n        now = newTime;\n        queueMicrotask(clearTime);\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvZnJhbWVsb29wL3N5bmMtdGltZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStEO0FBQ3ZCOztBQUV4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsaURBQVMsaUJBQWlCLHVFQUFrQjtBQUNqRSxrQkFBa0IsaURBQVM7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9mcmFtZWxvb3Avc3luYy10aW1lLm1qcz8zMWU0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE1vdGlvbkdsb2JhbENvbmZpZyB9IGZyb20gJy4uL3V0aWxzL0dsb2JhbENvbmZpZy5tanMnO1xuaW1wb3J0IHsgZnJhbWVEYXRhIH0gZnJvbSAnLi9mcmFtZS5tanMnO1xuXG5sZXQgbm93O1xuZnVuY3Rpb24gY2xlYXJUaW1lKCkge1xuICAgIG5vdyA9IHVuZGVmaW5lZDtcbn1cbi8qKlxuICogQW4gZXZlbnRsb29wLXN5bmNocm9ub3VzIGFsdGVybmF0aXZlIHRvIHBlcmZvcm1hbmNlLm5vdygpLlxuICpcbiAqIEVuc3VyZXMgdGhhdCB0aW1lIG1lYXN1cmVtZW50cyByZW1haW4gY29uc2lzdGVudCB3aXRoaW4gYSBzeW5jaHJvbm91cyBjb250ZXh0LlxuICogVXN1YWxseSBjYWxsaW5nIHBlcmZvcm1hbmNlLm5vdygpIHR3aWNlIHdpdGhpbiB0aGUgc2FtZSBzeW5jaHJvbm91cyBjb250ZXh0XG4gKiB3aWxsIHJldHVybiBkaWZmZXJlbnQgdmFsdWVzIHdoaWNoIGlzbid0IHVzZWZ1bCBmb3IgYW5pbWF0aW9ucyB3aGVuIHdlJ3JlIHVzdWFsbHlcbiAqIHRyeWluZyB0byBzeW5jIGFuaW1hdGlvbnMgdG8gdGhlIHNhbWUgZnJhbWUuXG4gKi9cbmNvbnN0IHRpbWUgPSB7XG4gICAgbm93OiAoKSA9PiB7XG4gICAgICAgIGlmIChub3cgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgdGltZS5zZXQoZnJhbWVEYXRhLmlzUHJvY2Vzc2luZyB8fCBNb3Rpb25HbG9iYWxDb25maWcudXNlTWFudWFsVGltaW5nXG4gICAgICAgICAgICAgICAgPyBmcmFtZURhdGEudGltZXN0YW1wXG4gICAgICAgICAgICAgICAgOiBwZXJmb3JtYW5jZS5ub3coKSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG5vdztcbiAgICB9LFxuICAgIHNldDogKG5ld1RpbWUpID0+IHtcbiAgICAgICAgbm93ID0gbmV3VGltZTtcbiAgICAgICAgcXVldWVNaWNyb3Rhc2soY2xlYXJUaW1lKTtcbiAgICB9LFxufTtcblxuZXhwb3J0IHsgdGltZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/sync-time.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeElement: function() { return /* binding */ resizeElement; }\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/index.mjs\");\n\n\nconst resizeHandlers = new WeakMap();\nlet observer;\nfunction getElementSize(target, borderBoxSize) {\n    if (borderBoxSize) {\n        const { inlineSize, blockSize } = borderBoxSize[0];\n        return { width: inlineSize, height: blockSize };\n    }\n    else if (target instanceof SVGElement && \"getBBox\" in target) {\n        return target.getBBox();\n    }\n    else {\n        return {\n            width: target.offsetWidth,\n            height: target.offsetHeight,\n        };\n    }\n}\nfunction notifyTarget({ target, contentRect, borderBoxSize, }) {\n    var _a;\n    (_a = resizeHandlers.get(target)) === null || _a === void 0 ? void 0 : _a.forEach((handler) => {\n        handler({\n            target,\n            contentSize: contentRect,\n            get size() {\n                return getElementSize(target, borderBoxSize);\n            },\n        });\n    });\n}\nfunction notifyAll(entries) {\n    entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n    if (typeof ResizeObserver === \"undefined\")\n        return;\n    observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n    if (!observer)\n        createResizeObserver();\n    const elements = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(target);\n    elements.forEach((element) => {\n        let elementHandlers = resizeHandlers.get(element);\n        if (!elementHandlers) {\n            elementHandlers = new Set();\n            resizeHandlers.set(element, elementHandlers);\n        }\n        elementHandlers.add(handler);\n        observer === null || observer === void 0 ? void 0 : observer.observe(element);\n    });\n    return () => {\n        elements.forEach((element) => {\n            const elementHandlers = resizeHandlers.get(element);\n            elementHandlers === null || elementHandlers === void 0 ? void 0 : elementHandlers.delete(handler);\n            if (!(elementHandlers === null || elementHandlers === void 0 ? void 0 : elementHandlers.size)) {\n                observer === null || observer === void 0 ? void 0 : observer.unobserve(element);\n            }\n        });\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeWindow: function() { return /* binding */ resizeWindow; }\n/* harmony export */ });\nconst windowCallbacks = new Set();\nlet windowResizeHandler;\nfunction createWindowResizeHandler() {\n    windowResizeHandler = () => {\n        const size = {\n            width: window.innerWidth,\n            height: window.innerHeight,\n        };\n        const info = {\n            target: window,\n            size,\n            contentSize: size,\n        };\n        windowCallbacks.forEach((callback) => callback(info));\n    };\n    window.addEventListener(\"resize\", windowResizeHandler);\n}\nfunction resizeWindow(callback) {\n    windowCallbacks.add(callback);\n    if (!windowResizeHandler)\n        createWindowResizeHandler();\n    return () => {\n        windowCallbacks.delete(callback);\n        if (!windowCallbacks.size && windowResizeHandler) {\n            windowResizeHandler = undefined;\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9yZXNpemUvaGFuZGxlLXdpbmRvdy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vcmVzaXplL2hhbmRsZS13aW5kb3cubWpzPzg3ZWMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgd2luZG93Q2FsbGJhY2tzID0gbmV3IFNldCgpO1xubGV0IHdpbmRvd1Jlc2l6ZUhhbmRsZXI7XG5mdW5jdGlvbiBjcmVhdGVXaW5kb3dSZXNpemVIYW5kbGVyKCkge1xuICAgIHdpbmRvd1Jlc2l6ZUhhbmRsZXIgPSAoKSA9PiB7XG4gICAgICAgIGNvbnN0IHNpemUgPSB7XG4gICAgICAgICAgICB3aWR0aDogd2luZG93LmlubmVyV2lkdGgsXG4gICAgICAgICAgICBoZWlnaHQ6IHdpbmRvdy5pbm5lckhlaWdodCxcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgaW5mbyA9IHtcbiAgICAgICAgICAgIHRhcmdldDogd2luZG93LFxuICAgICAgICAgICAgc2l6ZSxcbiAgICAgICAgICAgIGNvbnRlbnRTaXplOiBzaXplLFxuICAgICAgICB9O1xuICAgICAgICB3aW5kb3dDYWxsYmFja3MuZm9yRWFjaCgoY2FsbGJhY2spID0+IGNhbGxiYWNrKGluZm8pKTtcbiAgICB9O1xuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIHdpbmRvd1Jlc2l6ZUhhbmRsZXIpO1xufVxuZnVuY3Rpb24gcmVzaXplV2luZG93KGNhbGxiYWNrKSB7XG4gICAgd2luZG93Q2FsbGJhY2tzLmFkZChjYWxsYmFjayk7XG4gICAgaWYgKCF3aW5kb3dSZXNpemVIYW5kbGVyKVxuICAgICAgICBjcmVhdGVXaW5kb3dSZXNpemVIYW5kbGVyKCk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgd2luZG93Q2FsbGJhY2tzLmRlbGV0ZShjYWxsYmFjayk7XG4gICAgICAgIGlmICghd2luZG93Q2FsbGJhY2tzLnNpemUgJiYgd2luZG93UmVzaXplSGFuZGxlcikge1xuICAgICAgICAgICAgd2luZG93UmVzaXplSGFuZGxlciA9IHVuZGVmaW5lZDtcbiAgICAgICAgfVxuICAgIH07XG59XG5cbmV4cG9ydCB7IHJlc2l6ZVdpbmRvdyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resize: function() { return /* binding */ resize; }\n/* harmony export */ });\n/* harmony import */ var _handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./handle-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs\");\n/* harmony import */ var _handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handle-window.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs\");\n\n\n\nfunction resize(a, b) {\n    return typeof a === \"function\" ? (0,_handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__.resizeWindow)(a) : (0,_handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__.resizeElement)(a, b);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9yZXNpemUvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUNGOztBQUVuRDtBQUNBLHFDQUFxQyxnRUFBWSxNQUFNLGtFQUFhO0FBQ3BFOztBQUVrQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vcmVzaXplL2luZGV4Lm1qcz8zODkxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJlc2l6ZUVsZW1lbnQgfSBmcm9tICcuL2hhbmRsZS1lbGVtZW50Lm1qcyc7XG5pbXBvcnQgeyByZXNpemVXaW5kb3cgfSBmcm9tICcuL2hhbmRsZS13aW5kb3cubWpzJztcblxuZnVuY3Rpb24gcmVzaXplKGEsIGIpIHtcbiAgICByZXR1cm4gdHlwZW9mIGEgPT09IFwiZnVuY3Rpb25cIiA/IHJlc2l6ZVdpbmRvdyhhKSA6IHJlc2l6ZUVsZW1lbnQoYSwgYik7XG59XG5cbmV4cG9ydCB7IHJlc2l6ZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scroll: function() { return /* binding */ scroll; }\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _observe_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./observe.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/observe.mjs\");\n/* harmony import */ var _track_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./track.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\");\n\n\n\n\n\nfunction scrollTimelineFallback({ source, container, axis = \"y\", }) {\n    // Support legacy source argument. Deprecate later.\n    if (source)\n        container = source;\n    // ScrollTimeline records progress as a percentage CSSUnitValue\n    const currentTime = { value: 0 };\n    const cancel = (0,_track_mjs__WEBPACK_IMPORTED_MODULE_2__.scrollInfo)((info) => {\n        currentTime.value = info[axis].progress * 100;\n    }, { container, axis });\n    return { currentTime, cancel };\n}\nconst timelineCache = new Map();\nfunction getTimeline({ source, container = document.documentElement, axis = \"y\", } = {}) {\n    // Support legacy source argument. Deprecate later.\n    if (source)\n        container = source;\n    if (!timelineCache.has(container)) {\n        timelineCache.set(container, {});\n    }\n    const elementCache = timelineCache.get(container);\n    if (!elementCache[axis]) {\n        elementCache[axis] = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.supportsScrollTimeline)()\n            ? new ScrollTimeline({ source: container, axis })\n            : scrollTimelineFallback({ source: container, axis });\n    }\n    return elementCache[axis];\n}\n/**\n * If the onScroll function has two arguments, it's expecting\n * more specific information about the scroll from scrollInfo.\n */\nfunction isOnScrollWithInfo(onScroll) {\n    return onScroll.length === 2;\n}\n/**\n * Currently, we only support element tracking with `scrollInfo`, though in\n * the future we can also offer ViewTimeline support.\n */\nfunction needsElementTracking(options) {\n    return options && (options.target || options.offset);\n}\nfunction scrollFunction(onScroll, options) {\n    if (isOnScrollWithInfo(onScroll) || needsElementTracking(options)) {\n        return (0,_track_mjs__WEBPACK_IMPORTED_MODULE_2__.scrollInfo)((info) => {\n            onScroll(info[options.axis].progress, info);\n        }, options);\n    }\n    else {\n        return (0,_observe_mjs__WEBPACK_IMPORTED_MODULE_3__.observeTimeline)(onScroll, getTimeline(options));\n    }\n}\nfunction scrollAnimation(animation, options) {\n    animation.flatten();\n    if (needsElementTracking(options)) {\n        animation.pause();\n        return (0,_track_mjs__WEBPACK_IMPORTED_MODULE_2__.scrollInfo)((info) => {\n            animation.time = animation.duration * info[options.axis].progress;\n        }, options);\n    }\n    else {\n        const timeline = getTimeline(options);\n        if (animation.attachTimeline) {\n            return animation.attachTimeline(timeline, (valueAnimation) => {\n                valueAnimation.pause();\n                return (0,_observe_mjs__WEBPACK_IMPORTED_MODULE_3__.observeTimeline)((progress) => {\n                    valueAnimation.time = valueAnimation.duration * progress;\n                }, timeline);\n            });\n        }\n        else {\n            return motion_utils__WEBPACK_IMPORTED_MODULE_1__.noop;\n        }\n    }\n}\nfunction scroll(onScroll, { axis = \"y\", ...options } = {}) {\n    const optionsWithDefaults = { axis, ...options };\n    return typeof onScroll === \"function\"\n        ? scrollFunction(onScroll, optionsWithDefaults)\n        : scrollAnimation(onScroll, optionsWithDefaults);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createScrollInfo: function() { return /* binding */ createScrollInfo; },\n/* harmony export */   updateScrollInfo: function() { return /* binding */ updateScrollInfo; }\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/velocity-per-second.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs\");\n\n\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n    current: 0,\n    offset: [],\n    progress: 0,\n    scrollLength: 0,\n    targetOffset: 0,\n    targetLength: 0,\n    containerLength: 0,\n    velocity: 0,\n});\nconst createScrollInfo = () => ({\n    time: 0,\n    x: createAxisInfo(),\n    y: createAxisInfo(),\n});\nconst keys = {\n    x: {\n        length: \"Width\",\n        position: \"Left\",\n    },\n    y: {\n        length: \"Height\",\n        position: \"Top\",\n    },\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n    const axis = info[axisName];\n    const { length, position } = keys[axisName];\n    const prev = axis.current;\n    const prevTime = info.time;\n    axis.current = element[`scroll${position}`];\n    axis.scrollLength = element[`scroll${length}`] - element[`client${length}`];\n    axis.offset.length = 0;\n    axis.offset[0] = 0;\n    axis.offset[1] = axis.scrollLength;\n    axis.progress = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.progress)(0, axis.scrollLength, axis.current);\n    const elapsed = time - prevTime;\n    axis.velocity =\n        elapsed > maxElapsed\n            ? 0\n            : (0,_utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_1__.velocityPerSecond)(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n    updateAxisInfo(element, \"x\", info, time);\n    updateAxisInfo(element, \"y\", info, time);\n    info.time = time;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/observe.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/observe.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   observeTimeline: function() { return /* binding */ observeTimeline; }\n/* harmony export */ });\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\nfunction observeTimeline(update, timeline) {\n    let prevProgress;\n    const onFrame = () => {\n        const { currentTime } = timeline;\n        const percentage = currentTime === null ? 0 : currentTime.value;\n        const progress = percentage / 100;\n        if (prevProgress !== progress) {\n            update(progress);\n        }\n        prevProgress = progress;\n    };\n    _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.update(onFrame, true);\n    return () => (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(onFrame);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvb2JzZXJ2ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0U7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixjQUFjO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSx1REFBSztBQUNULGlCQUFpQixpRUFBVztBQUM1Qjs7QUFFMkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9yZW5kZXIvZG9tL3Njcm9sbC9vYnNlcnZlLm1qcz82YTMyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZyYW1lLCBjYW5jZWxGcmFtZSB9IGZyb20gJy4uLy4uLy4uL2ZyYW1lbG9vcC9mcmFtZS5tanMnO1xuXG5mdW5jdGlvbiBvYnNlcnZlVGltZWxpbmUodXBkYXRlLCB0aW1lbGluZSkge1xuICAgIGxldCBwcmV2UHJvZ3Jlc3M7XG4gICAgY29uc3Qgb25GcmFtZSA9ICgpID0+IHtcbiAgICAgICAgY29uc3QgeyBjdXJyZW50VGltZSB9ID0gdGltZWxpbmU7XG4gICAgICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSBjdXJyZW50VGltZSA9PT0gbnVsbCA/IDAgOiBjdXJyZW50VGltZS52YWx1ZTtcbiAgICAgICAgY29uc3QgcHJvZ3Jlc3MgPSBwZXJjZW50YWdlIC8gMTAwO1xuICAgICAgICBpZiAocHJldlByb2dyZXNzICE9PSBwcm9ncmVzcykge1xuICAgICAgICAgICAgdXBkYXRlKHByb2dyZXNzKTtcbiAgICAgICAgfVxuICAgICAgICBwcmV2UHJvZ3Jlc3MgPSBwcm9ncmVzcztcbiAgICB9O1xuICAgIGZyYW1lLnVwZGF0ZShvbkZyYW1lLCB0cnVlKTtcbiAgICByZXR1cm4gKCkgPT4gY2FuY2VsRnJhbWUob25GcmFtZSk7XG59XG5cbmV4cG9ydCB7IG9ic2VydmVUaW1lbGluZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/observe.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   namedEdges: function() { return /* binding */ namedEdges; },\n/* harmony export */   resolveEdge: function() { return /* binding */ resolveEdge; }\n/* harmony export */ });\nconst namedEdges = {\n    start: 0,\n    center: 0.5,\n    end: 1,\n};\nfunction resolveEdge(edge, length, inset = 0) {\n    let delta = 0;\n    /**\n     * If we have this edge defined as a preset, replace the definition\n     * with the numerical value.\n     */\n    if (edge in namedEdges) {\n        edge = namedEdges[edge];\n    }\n    /**\n     * Handle unit values\n     */\n    if (typeof edge === \"string\") {\n        const asNumber = parseFloat(edge);\n        if (edge.endsWith(\"px\")) {\n            delta = asNumber;\n        }\n        else if (edge.endsWith(\"%\")) {\n            edge = asNumber / 100;\n        }\n        else if (edge.endsWith(\"vw\")) {\n            delta = (asNumber / 100) * document.documentElement.clientWidth;\n        }\n        else if (edge.endsWith(\"vh\")) {\n            delta = (asNumber / 100) * document.documentElement.clientHeight;\n        }\n        else {\n            edge = asNumber;\n        }\n    }\n    /**\n     * If the edge is defined as a number, handle as a progress value.\n     */\n    if (typeof edge === \"number\") {\n        delta = length * edge;\n    }\n    return inset + delta;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffsets: function() { return /* binding */ resolveOffsets; }\n/* harmony export */ });\n/* harmony import */ var _utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../utils/clamp.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\");\n/* harmony import */ var _utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../utils/interpolate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs\");\n/* harmony import */ var _utils_offsets_default_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../utils/offsets/default.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/default.mjs\");\n/* harmony import */ var _inset_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./inset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\");\n/* harmony import */ var _offset_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\");\n/* harmony import */ var _presets_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./presets.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\");\n\n\n\n\n\n\n\nconst point = { x: 0, y: 0 };\nfunction getTargetSize(target) {\n    return \"getBBox\" in target && target.tagName !== \"svg\"\n        ? target.getBBox()\n        : { width: target.clientWidth, height: target.clientHeight };\n}\nfunction resolveOffsets(container, info, options) {\n    const { offset: offsetDefinition = _presets_mjs__WEBPACK_IMPORTED_MODULE_0__.ScrollOffset.All } = options;\n    const { target = container, axis = \"y\" } = options;\n    const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n    const inset = target !== container ? (0,_inset_mjs__WEBPACK_IMPORTED_MODULE_1__.calcInset)(target, container) : point;\n    /**\n     * Measure the target and container. If they're the same thing then we\n     * use the container's scrollWidth/Height as the target, from there\n     * all other calculations can remain the same.\n     */\n    const targetSize = target === container\n        ? { width: container.scrollWidth, height: container.scrollHeight }\n        : getTargetSize(target);\n    const containerSize = {\n        width: container.clientWidth,\n        height: container.clientHeight,\n    };\n    /**\n     * Reset the length of the resolved offset array rather than creating a new one.\n     * TODO: More reusable data structures for targetSize/containerSize would also be good.\n     */\n    info[axis].offset.length = 0;\n    /**\n     * Populate the offset array by resolving the user's offset definition into\n     * a list of pixel scroll offets.\n     */\n    let hasChanged = !info[axis].interpolate;\n    const numOffsets = offsetDefinition.length;\n    for (let i = 0; i < numOffsets; i++) {\n        const offset = (0,_offset_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffset)(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset[axis]);\n        if (!hasChanged && offset !== info[axis].interpolatorOffsets[i]) {\n            hasChanged = true;\n        }\n        info[axis].offset[i] = offset;\n    }\n    /**\n     * If the pixel scroll offsets have changed, create a new interpolator function\n     * to map scroll value into a progress.\n     */\n    if (hasChanged) {\n        info[axis].interpolate = (0,_utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_3__.interpolate)(info[axis].offset, (0,_utils_offsets_default_mjs__WEBPACK_IMPORTED_MODULE_4__.defaultOffset)(offsetDefinition), { clamp: false });\n        info[axis].interpolatorOffsets = [...info[axis].offset];\n    }\n    info[axis].progress = (0,_utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_5__.clamp)(0, 1, info[axis].interpolate(info[axis].current));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcInset: function() { return /* binding */ calcInset; }\n/* harmony export */ });\nfunction calcInset(element, container) {\n    const inset = { x: 0, y: 0 };\n    let current = element;\n    while (current && current !== container) {\n        if (current instanceof HTMLElement) {\n            inset.x += current.offsetLeft;\n            inset.y += current.offsetTop;\n            current = current.offsetParent;\n        }\n        else if (current.tagName === \"svg\") {\n            /**\n             * This isn't an ideal approach to measuring the offset of <svg /> tags.\n             * It would be preferable, given they behave like HTMLElements in most ways\n             * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n             * can't use .getBBox() like most SVG elements as these provide the offset\n             * relative to the SVG itself, which for <svg /> is usually 0x0.\n             */\n            const svgBoundingBox = current.getBoundingClientRect();\n            current = current.parentElement;\n            const parentBoundingBox = current.getBoundingClientRect();\n            inset.x += svgBoundingBox.left - parentBoundingBox.left;\n            inset.y += svgBoundingBox.top - parentBoundingBox.top;\n        }\n        else if (current instanceof SVGGraphicsElement) {\n            const { x, y } = current.getBBox();\n            inset.x += x;\n            inset.y += y;\n            let svg = null;\n            let parent = current.parentNode;\n            while (!svg) {\n                if (parent.tagName === \"svg\") {\n                    svg = parent;\n                }\n                parent = current.parentNode;\n            }\n            current = svg;\n        }\n        else {\n            break;\n        }\n    }\n    return inset;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffset: function() { return /* binding */ resolveOffset; }\n/* harmony export */ });\n/* harmony import */ var _edge_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edge.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\");\n\n\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n    let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n    let targetPoint = 0;\n    let containerPoint = 0;\n    if (typeof offset === \"number\") {\n        /**\n         * If we're provided offset: [0, 0.5, 1] then each number x should become\n         * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n         * and container etc.\n         */\n        offsetDefinition = [offset, offset];\n    }\n    else if (typeof offset === \"string\") {\n        offset = offset.trim();\n        if (offset.includes(\" \")) {\n            offsetDefinition = offset.split(\" \");\n        }\n        else {\n            /**\n             * If we're provided a definition like \"100px\" then we want to apply\n             * that only to the top of the target point, leaving the container at 0.\n             * Whereas a named offset like \"end\" should be applied to both.\n             */\n            offsetDefinition = [offset, _edge_mjs__WEBPACK_IMPORTED_MODULE_0__.namedEdges[offset] ? offset : `0`];\n        }\n    }\n    targetPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[0], targetLength, targetInset);\n    containerPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[1], containerLength);\n    return targetPoint - containerPoint;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollOffset: function() { return /* binding */ ScrollOffset; }\n/* harmony export */ });\nconst ScrollOffset = {\n    Enter: [\n        [0, 1],\n        [1, 1],\n    ],\n    Exit: [\n        [0, 0],\n        [1, 0],\n    ],\n    Any: [\n        [1, 0],\n        [0, 1],\n    ],\n    All: [\n        [0, 0],\n        [1, 1],\n    ],\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvb2Zmc2V0cy9wcmVzZXRzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3JlbmRlci9kb20vc2Nyb2xsL29mZnNldHMvcHJlc2V0cy5tanM/MGQ4NiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBTY3JvbGxPZmZzZXQgPSB7XG4gICAgRW50ZXI6IFtcbiAgICAgICAgWzAsIDFdLFxuICAgICAgICBbMSwgMV0sXG4gICAgXSxcbiAgICBFeGl0OiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDBdLFxuICAgIF0sXG4gICAgQW55OiBbXG4gICAgICAgIFsxLCAwXSxcbiAgICAgICAgWzAsIDFdLFxuICAgIF0sXG4gICAgQWxsOiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDFdLFxuICAgIF0sXG59O1xuXG5leHBvcnQgeyBTY3JvbGxPZmZzZXQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOnScrollHandler: function() { return /* binding */ createOnScrollHandler; }\n/* harmony export */ });\n/* harmony import */ var _utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/warn-once.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/warn-once.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offsets/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\");\n\n\n\n\nfunction measure(container, target = container, info) {\n    /**\n     * Find inset of target within scrollable container\n     */\n    info.x.targetOffset = 0;\n    info.y.targetOffset = 0;\n    if (target !== container) {\n        let node = target;\n        while (node && node !== container) {\n            info.x.targetOffset += node.offsetLeft;\n            info.y.targetOffset += node.offsetTop;\n            node = node.offsetParent;\n        }\n    }\n    info.x.targetLength =\n        target === container ? target.scrollWidth : target.clientWidth;\n    info.y.targetLength =\n        target === container ? target.scrollHeight : target.clientHeight;\n    info.x.containerLength = container.clientWidth;\n    info.y.containerLength = container.clientHeight;\n    /**\n     * In development mode ensure scroll containers aren't position: static as this makes\n     * it difficult to measure their relative positions.\n     */\n    if (true) {\n        if (container && target && target !== container) {\n            (0,_utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_0__.warnOnce)(getComputedStyle(container).position !== \"static\", \"Please ensure that the container has a non-static position, like 'relative', 'fixed', or 'absolute' to ensure scroll offset is calculated correctly.\");\n        }\n    }\n}\nfunction createOnScrollHandler(element, onScroll, info, options = {}) {\n    return {\n        measure: () => measure(element, options.target, info),\n        update: (time) => {\n            (0,_info_mjs__WEBPACK_IMPORTED_MODULE_1__.updateScrollInfo)(element, info, time);\n            if (options.offset || options.target) {\n                (0,_offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffsets)(element, info, options);\n            }\n        },\n        notify: () => onScroll(info),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrollInfo: function() { return /* binding */ scrollInfo; }\n/* harmony export */ });\n/* harmony import */ var _resize_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../resize/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./on-scroll-handler.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.documentElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.documentElement, ...options } = {}) {\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = (0,_info_mjs__WEBPACK_IMPORTED_MODULE_0__.createScrollInfo)();\n    const containerHandler = (0,_on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_1__.createOnScrollHandler)(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers)\n                handler.measure();\n        };\n        const updateAll = () => {\n            for (const handler of containerHandlers) {\n                handler.update(_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frameData.timestamp);\n            }\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers)\n                handler.notify();\n        };\n        const listener = () => {\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.read(measureAll, false, true);\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.read(updateAll, false, true);\n            _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.update(notifyAll, false, true);\n        };\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, (0,_resize_index_mjs__WEBPACK_IMPORTED_MODULE_3__.resize)(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n    }\n    const listener = scrollListeners.get(container);\n    _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.read(listener, false, true);\n    return () => {\n        var _a;\n        (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            (_a = resizeListeners.get(container)) === null || _a === void 0 ? void 0 : _a();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isCSSVariableName: function() { return /* binding */ isCSSVariableName; },\n/* harmony export */   isCSSVariableToken: function() { return /* binding */ isCSSVariableToken; }\n/* harmony export */ });\nconst checkStringStartsWith = (token) => (key) => typeof key === \"string\" && key.startsWith(token);\nconst isCSSVariableName = \n/*@__PURE__*/ checkStringStartsWith(\"--\");\nconst startsAsVariableToken = \n/*@__PURE__*/ checkStringStartsWith(\"var(--\");\nconst isCSSVariableToken = (value) => {\n    const startsWithToken = startsAsVariableToken(value);\n    if (!startsWithToken)\n        return false;\n    // Ensure any comments are stripped from the value as this can harm performance of the regex.\n    return singleCssVariableRegex.test(value.split(\"/*\")[0].trim());\n};\nconst singleCssVariableRegex = /var\\(--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)$/iu;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9pcy1jc3MtdmFyaWFibGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWlEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS91dGlscy9pcy1jc3MtdmFyaWFibGUubWpzP2JiMzAiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgY2hlY2tTdHJpbmdTdGFydHNXaXRoID0gKHRva2VuKSA9PiAoa2V5KSA9PiB0eXBlb2Yga2V5ID09PSBcInN0cmluZ1wiICYmIGtleS5zdGFydHNXaXRoKHRva2VuKTtcbmNvbnN0IGlzQ1NTVmFyaWFibGVOYW1lID0gXG4vKkBfX1BVUkVfXyovIGNoZWNrU3RyaW5nU3RhcnRzV2l0aChcIi0tXCIpO1xuY29uc3Qgc3RhcnRzQXNWYXJpYWJsZVRva2VuID0gXG4vKkBfX1BVUkVfXyovIGNoZWNrU3RyaW5nU3RhcnRzV2l0aChcInZhcigtLVwiKTtcbmNvbnN0IGlzQ1NTVmFyaWFibGVUb2tlbiA9ICh2YWx1ZSkgPT4ge1xuICAgIGNvbnN0IHN0YXJ0c1dpdGhUb2tlbiA9IHN0YXJ0c0FzVmFyaWFibGVUb2tlbih2YWx1ZSk7XG4gICAgaWYgKCFzdGFydHNXaXRoVG9rZW4pXG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAvLyBFbnN1cmUgYW55IGNvbW1lbnRzIGFyZSBzdHJpcHBlZCBmcm9tIHRoZSB2YWx1ZSBhcyB0aGlzIGNhbiBoYXJtIHBlcmZvcm1hbmNlIG9mIHRoZSByZWdleC5cbiAgICByZXR1cm4gc2luZ2xlQ3NzVmFyaWFibGVSZWdleC50ZXN0KHZhbHVlLnNwbGl0KFwiLypcIilbMF0udHJpbSgpKTtcbn07XG5jb25zdCBzaW5nbGVDc3NWYXJpYWJsZVJlZ2V4ID0gL3ZhclxcKC0tKD86W1xcdy1dK1xccyp8W1xcdy1dK1xccyosKD86XFxzKlteKShcXHNdfFxccypcXCgoPzpbXikoXXxcXChbXikoXSpcXCkpKlxcKSkrXFxzKilcXCkkL2l1O1xuXG5leHBvcnQgeyBpc0NTU1ZhcmlhYmxlTmFtZSwgaXNDU1NWYXJpYWJsZVRva2VuIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/GlobalConfig.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/GlobalConfig.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionGlobalConfig: function() { return /* binding */ MotionGlobalConfig; }\n/* harmony export */ });\nconst MotionGlobalConfig = {\n    skipAnimations: false,\n    useManualTiming: false,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvR2xvYmFsQ29uZmlnLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvR2xvYmFsQ29uZmlnLm1qcz85YjU0Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IE1vdGlvbkdsb2JhbENvbmZpZyA9IHtcbiAgICBza2lwQW5pbWF0aW9uczogZmFsc2UsXG4gICAgdXNlTWFudWFsVGltaW5nOiBmYWxzZSxcbn07XG5cbmV4cG9ydCB7IE1vdGlvbkdsb2JhbENvbmZpZyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/GlobalConfig.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/array.mjs":
/*!************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/array.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addUniqueItem: function() { return /* binding */ addUniqueItem; },\n/* harmony export */   moveItem: function() { return /* binding */ moveItem; },\n/* harmony export */   removeItem: function() { return /* binding */ removeItem; }\n/* harmony export */ });\nfunction addUniqueItem(arr, item) {\n    if (arr.indexOf(item) === -1)\n        arr.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    if (index > -1)\n        arr.splice(index, 1);\n}\n// Adapted from array-move\nfunction moveItem([...arr], fromIndex, toIndex) {\n    const startIndex = fromIndex < 0 ? arr.length + fromIndex : fromIndex;\n    if (startIndex >= 0 && startIndex < arr.length) {\n        const endIndex = toIndex < 0 ? arr.length + toIndex : toIndex;\n        const [item] = arr.splice(fromIndex, 1);\n        arr.splice(endIndex, 0, item);\n    }\n    return arr;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvYXJyYXkubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUrQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL2FycmF5Lm1qcz9jMjZiIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGFkZFVuaXF1ZUl0ZW0oYXJyLCBpdGVtKSB7XG4gICAgaWYgKGFyci5pbmRleE9mKGl0ZW0pID09PSAtMSlcbiAgICAgICAgYXJyLnB1c2goaXRlbSk7XG59XG5mdW5jdGlvbiByZW1vdmVJdGVtKGFyciwgaXRlbSkge1xuICAgIGNvbnN0IGluZGV4ID0gYXJyLmluZGV4T2YoaXRlbSk7XG4gICAgaWYgKGluZGV4ID4gLTEpXG4gICAgICAgIGFyci5zcGxpY2UoaW5kZXgsIDEpO1xufVxuLy8gQWRhcHRlZCBmcm9tIGFycmF5LW1vdmVcbmZ1bmN0aW9uIG1vdmVJdGVtKFsuLi5hcnJdLCBmcm9tSW5kZXgsIHRvSW5kZXgpIHtcbiAgICBjb25zdCBzdGFydEluZGV4ID0gZnJvbUluZGV4IDwgMCA/IGFyci5sZW5ndGggKyBmcm9tSW5kZXggOiBmcm9tSW5kZXg7XG4gICAgaWYgKHN0YXJ0SW5kZXggPj0gMCAmJiBzdGFydEluZGV4IDwgYXJyLmxlbmd0aCkge1xuICAgICAgICBjb25zdCBlbmRJbmRleCA9IHRvSW5kZXggPCAwID8gYXJyLmxlbmd0aCArIHRvSW5kZXggOiB0b0luZGV4O1xuICAgICAgICBjb25zdCBbaXRlbV0gPSBhcnIuc3BsaWNlKGZyb21JbmRleCwgMSk7XG4gICAgICAgIGFyci5zcGxpY2UoZW5kSW5kZXgsIDAsIGl0ZW0pO1xuICAgIH1cbiAgICByZXR1cm4gYXJyO1xufVxuXG5leHBvcnQgeyBhZGRVbmlxdWVJdGVtLCBtb3ZlSXRlbSwgcmVtb3ZlSXRlbSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/array.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs":
/*!************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/clamp.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: function() { return /* binding */ clamp; }\n/* harmony export */ });\nconst clamp = (min, max, v) => {\n    if (v > max)\n        return max;\n    if (v < min)\n        return min;\n    return v;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvY2xhbXAubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy9jbGFtcC5tanM/ZDQxYSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjbGFtcCA9IChtaW4sIG1heCwgdikgPT4ge1xuICAgIGlmICh2ID4gbWF4KVxuICAgICAgICByZXR1cm4gbWF4O1xuICAgIGlmICh2IDwgbWluKVxuICAgICAgICByZXR1cm4gbWluO1xuICAgIHJldHVybiB2O1xufTtcblxuZXhwb3J0IHsgY2xhbXAgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/hsla-to-rgba.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/hsla-to-rgba.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hslaToRgba: function() { return /* binding */ hslaToRgba; }\n/* harmony export */ });\n// Adapted from https://gist.github.com/mjackson/5311256\nfunction hueToRgb(p, q, t) {\n    if (t < 0)\n        t += 1;\n    if (t > 1)\n        t -= 1;\n    if (t < 1 / 6)\n        return p + (q - p) * 6 * t;\n    if (t < 1 / 2)\n        return q;\n    if (t < 2 / 3)\n        return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n}\nfunction hslaToRgba({ hue, saturation, lightness, alpha }) {\n    hue /= 360;\n    saturation /= 100;\n    lightness /= 100;\n    let red = 0;\n    let green = 0;\n    let blue = 0;\n    if (!saturation) {\n        red = green = blue = lightness;\n    }\n    else {\n        const q = lightness < 0.5\n            ? lightness * (1 + saturation)\n            : lightness + saturation - lightness * saturation;\n        const p = 2 * lightness - q;\n        red = hueToRgb(p, q, hue + 1 / 3);\n        green = hueToRgb(p, q, hue);\n        blue = hueToRgb(p, q, hue - 1 / 3);\n    }\n    return {\n        red: Math.round(red * 255),\n        green: Math.round(green * 255),\n        blue: Math.round(blue * 255),\n        alpha,\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/hsla-to-rgba.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/interpolate.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   interpolate: function() { return /* binding */ interpolate; }\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _clamp_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./clamp.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\");\n/* harmony import */ var _mix_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mix/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/index.mjs\");\n/* harmony import */ var _pipe_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./pipe.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/pipe.mjs\");\n\n\n\n\n\nfunction createMixers(output, ease, customMixer) {\n    const mixers = [];\n    const mixerFactory = customMixer || _mix_index_mjs__WEBPACK_IMPORTED_MODULE_1__.mix;\n    const numMixers = output.length - 1;\n    for (let i = 0; i < numMixers; i++) {\n        let mixer = mixerFactory(output[i], output[i + 1]);\n        if (ease) {\n            const easingFunction = Array.isArray(ease) ? ease[i] || motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop : ease;\n            mixer = (0,_pipe_mjs__WEBPACK_IMPORTED_MODULE_2__.pipe)(easingFunction, mixer);\n        }\n        mixers.push(mixer);\n    }\n    return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revist this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output, { clamp: isClamp = true, ease, mixer } = {}) {\n    const inputLength = input.length;\n    (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.invariant)(inputLength === output.length, \"Both input and output ranges must be the same length\");\n    /**\n     * If we're only provided a single input, we can just make a function\n     * that returns the output.\n     */\n    if (inputLength === 1)\n        return () => output[0];\n    if (inputLength === 2 && output[0] === output[1])\n        return () => output[1];\n    const isZeroDeltaRange = input[0] === input[1];\n    // If input runs highest -> lowest, reverse both arrays\n    if (input[0] > input[inputLength - 1]) {\n        input = [...input].reverse();\n        output = [...output].reverse();\n    }\n    const mixers = createMixers(output, ease, mixer);\n    const numMixers = mixers.length;\n    const interpolator = (v) => {\n        if (isZeroDeltaRange && v < input[0])\n            return output[0];\n        let i = 0;\n        if (numMixers > 1) {\n            for (; i < input.length - 2; i++) {\n                if (v < input[i + 1])\n                    break;\n            }\n        }\n        const progressInRange = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.progress)(input[i], input[i + 1], v);\n        return mixers[i](progressInRange);\n    };\n    return isClamp\n        ? (v) => interpolator((0,_clamp_mjs__WEBPACK_IMPORTED_MODULE_3__.clamp)(input[0], input[inputLength - 1], v))\n        : interpolator;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-browser.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/is-browser.mjs ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: function() { return /* binding */ isBrowser; }\n/* harmony export */ });\nconst isBrowser = typeof window !== \"undefined\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvaXMtYnJvd3Nlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUVxQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL2lzLWJyb3dzZXIubWpzP2YxMDEiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNCcm93c2VyID0gdHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIjtcblxuZXhwb3J0IHsgaXNCcm93c2VyIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-browser.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/color.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/mix/color.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mixColor: function() { return /* binding */ mixColor; },\n/* harmony export */   mixLinearColor: function() { return /* binding */ mixLinearColor; }\n/* harmony export */ });\n/* harmony import */ var _number_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./number.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/number.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _hsla_to_rgba_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hsla-to-rgba.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/hsla-to-rgba.mjs\");\n/* harmony import */ var _value_types_color_hex_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../value/types/color/hex.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hex.mjs\");\n/* harmony import */ var _value_types_color_rgba_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../value/types/color/rgba.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/rgba.mjs\");\n/* harmony import */ var _value_types_color_hsla_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../value/types/color/hsla.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hsla.mjs\");\n/* harmony import */ var _immediate_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./immediate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/immediate.mjs\");\n\n\n\n\n\n\n\n\n// Linear color space blending\n// Explained https://www.youtube.com/watch?v=LKnqECcg6Gw\n// Demonstrated http://codepen.io/osublake/pen/xGVVaN\nconst mixLinearColor = (from, to, v) => {\n    const fromExpo = from * from;\n    const expo = v * (to * to - fromExpo) + fromExpo;\n    return expo < 0 ? 0 : Math.sqrt(expo);\n};\nconst colorTypes = [_value_types_color_hex_mjs__WEBPACK_IMPORTED_MODULE_1__.hex, _value_types_color_rgba_mjs__WEBPACK_IMPORTED_MODULE_2__.rgba, _value_types_color_hsla_mjs__WEBPACK_IMPORTED_MODULE_3__.hsla];\nconst getColorType = (v) => colorTypes.find((type) => type.test(v));\nfunction asRGBA(color) {\n    const type = getColorType(color);\n    (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.warning)(Boolean(type), `'${color}' is not an animatable color. Use the equivalent color code instead.`);\n    if (!Boolean(type))\n        return false;\n    let model = type.parse(color);\n    if (type === _value_types_color_hsla_mjs__WEBPACK_IMPORTED_MODULE_3__.hsla) {\n        // TODO Remove this cast - needed since Motion's stricter typing\n        model = (0,_hsla_to_rgba_mjs__WEBPACK_IMPORTED_MODULE_4__.hslaToRgba)(model);\n    }\n    return model;\n}\nconst mixColor = (from, to) => {\n    const fromRGBA = asRGBA(from);\n    const toRGBA = asRGBA(to);\n    if (!fromRGBA || !toRGBA) {\n        return (0,_immediate_mjs__WEBPACK_IMPORTED_MODULE_5__.mixImmediate)(from, to);\n    }\n    const blended = { ...fromRGBA };\n    return (v) => {\n        blended.red = mixLinearColor(fromRGBA.red, toRGBA.red, v);\n        blended.green = mixLinearColor(fromRGBA.green, toRGBA.green, v);\n        blended.blue = mixLinearColor(fromRGBA.blue, toRGBA.blue, v);\n        blended.alpha = (0,_number_mjs__WEBPACK_IMPORTED_MODULE_6__.mixNumber)(fromRGBA.alpha, toRGBA.alpha, v);\n        return _value_types_color_rgba_mjs__WEBPACK_IMPORTED_MODULE_2__.rgba.transform(blended);\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/color.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/complex.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/mix/complex.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMixer: function() { return /* binding */ getMixer; },\n/* harmony export */   mixArray: function() { return /* binding */ mixArray; },\n/* harmony export */   mixComplex: function() { return /* binding */ mixComplex; },\n/* harmony export */   mixObject: function() { return /* binding */ mixObject; }\n/* harmony export */ });\n/* harmony import */ var _number_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./number.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/number.mjs\");\n/* harmony import */ var _color_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./color.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/color.mjs\");\n/* harmony import */ var _pipe_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../pipe.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/pipe.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../value/types/color/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/index.mjs\");\n/* harmony import */ var _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../value/types/complex/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/index.mjs\");\n/* harmony import */ var _render_dom_utils_is_css_variable_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../render/dom/utils/is-css-variable.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/utils/is-css-variable.mjs\");\n/* harmony import */ var _visibility_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./visibility.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/visibility.mjs\");\n/* harmony import */ var _immediate_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./immediate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/immediate.mjs\");\n\n\n\n\n\n\n\n\n\n\nfunction mixNumber(a, b) {\n    return (p) => (0,_number_mjs__WEBPACK_IMPORTED_MODULE_1__.mixNumber)(a, b, p);\n}\nfunction getMixer(a) {\n    if (typeof a === \"number\") {\n        return mixNumber;\n    }\n    else if (typeof a === \"string\") {\n        return (0,_render_dom_utils_is_css_variable_mjs__WEBPACK_IMPORTED_MODULE_2__.isCSSVariableToken)(a)\n            ? _immediate_mjs__WEBPACK_IMPORTED_MODULE_3__.mixImmediate\n            : _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_4__.color.test(a)\n                ? _color_mjs__WEBPACK_IMPORTED_MODULE_5__.mixColor\n                : mixComplex;\n    }\n    else if (Array.isArray(a)) {\n        return mixArray;\n    }\n    else if (typeof a === \"object\") {\n        return _value_types_color_index_mjs__WEBPACK_IMPORTED_MODULE_4__.color.test(a) ? _color_mjs__WEBPACK_IMPORTED_MODULE_5__.mixColor : mixObject;\n    }\n    return _immediate_mjs__WEBPACK_IMPORTED_MODULE_3__.mixImmediate;\n}\nfunction mixArray(a, b) {\n    const output = [...a];\n    const numValues = output.length;\n    const blendValue = a.map((v, i) => getMixer(v)(v, b[i]));\n    return (p) => {\n        for (let i = 0; i < numValues; i++) {\n            output[i] = blendValue[i](p);\n        }\n        return output;\n    };\n}\nfunction mixObject(a, b) {\n    const output = { ...a, ...b };\n    const blendValue = {};\n    for (const key in output) {\n        if (a[key] !== undefined && b[key] !== undefined) {\n            blendValue[key] = getMixer(a[key])(a[key], b[key]);\n        }\n    }\n    return (v) => {\n        for (const key in blendValue) {\n            output[key] = blendValue[key](v);\n        }\n        return output;\n    };\n}\nfunction matchOrder(origin, target) {\n    var _a;\n    const orderedOrigin = [];\n    const pointers = { color: 0, var: 0, number: 0 };\n    for (let i = 0; i < target.values.length; i++) {\n        const type = target.types[i];\n        const originIndex = origin.indexes[type][pointers[type]];\n        const originValue = (_a = origin.values[originIndex]) !== null && _a !== void 0 ? _a : 0;\n        orderedOrigin[i] = originValue;\n        pointers[type]++;\n    }\n    return orderedOrigin;\n}\nconst mixComplex = (origin, target) => {\n    const template = _value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_6__.complex.createTransformer(target);\n    const originStats = (0,_value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_6__.analyseComplexValue)(origin);\n    const targetStats = (0,_value_types_complex_index_mjs__WEBPACK_IMPORTED_MODULE_6__.analyseComplexValue)(target);\n    const canInterpolate = originStats.indexes.var.length === targetStats.indexes.var.length &&\n        originStats.indexes.color.length === targetStats.indexes.color.length &&\n        originStats.indexes.number.length >= targetStats.indexes.number.length;\n    if (canInterpolate) {\n        if ((_visibility_mjs__WEBPACK_IMPORTED_MODULE_7__.invisibleValues.has(origin) &&\n            !targetStats.values.length) ||\n            (_visibility_mjs__WEBPACK_IMPORTED_MODULE_7__.invisibleValues.has(target) &&\n                !originStats.values.length)) {\n            return (0,_visibility_mjs__WEBPACK_IMPORTED_MODULE_7__.mixVisibility)(origin, target);\n        }\n        return (0,_pipe_mjs__WEBPACK_IMPORTED_MODULE_8__.pipe)(mixArray(matchOrder(originStats, targetStats), targetStats.values), template);\n    }\n    else {\n        (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.warning)(true, `Complex values '${origin}' and '${target}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`);\n        return (0,_immediate_mjs__WEBPACK_IMPORTED_MODULE_3__.mixImmediate)(origin, target);\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/complex.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/immediate.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/mix/immediate.mjs ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mixImmediate: function() { return /* binding */ mixImmediate; }\n/* harmony export */ });\nfunction mixImmediate(a, b) {\n    return (p) => (p > 0 ? b : a);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvbWl4L2ltbWVkaWF0ZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFd0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy9taXgvaW1tZWRpYXRlLm1qcz83NjNhIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIG1peEltbWVkaWF0ZShhLCBiKSB7XG4gICAgcmV0dXJuIChwKSA9PiAocCA+IDAgPyBiIDogYSk7XG59XG5cbmV4cG9ydCB7IG1peEltbWVkaWF0ZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/immediate.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/mix/index.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mix: function() { return /* binding */ mix; }\n/* harmony export */ });\n/* harmony import */ var _complex_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./complex.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/complex.mjs\");\n/* harmony import */ var _number_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./number.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/number.mjs\");\n\n\n\nfunction mix(from, to, p) {\n    if (typeof from === \"number\" &&\n        typeof to === \"number\" &&\n        typeof p === \"number\") {\n        return (0,_number_mjs__WEBPACK_IMPORTED_MODULE_0__.mixNumber)(from, to, p);\n    }\n    const mixer = (0,_complex_mjs__WEBPACK_IMPORTED_MODULE_1__.getMixer)(from);\n    return mixer(from, to);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvbWl4L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBeUM7QUFDQTs7QUFFekM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHNEQUFTO0FBQ3hCO0FBQ0Esa0JBQWtCLHNEQUFRO0FBQzFCO0FBQ0E7O0FBRWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy9taXgvaW5kZXgubWpzP2Q3ZTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0TWl4ZXIgfSBmcm9tICcuL2NvbXBsZXgubWpzJztcbmltcG9ydCB7IG1peE51bWJlciB9IGZyb20gJy4vbnVtYmVyLm1qcyc7XG5cbmZ1bmN0aW9uIG1peChmcm9tLCB0bywgcCkge1xuICAgIGlmICh0eXBlb2YgZnJvbSA9PT0gXCJudW1iZXJcIiAmJlxuICAgICAgICB0eXBlb2YgdG8gPT09IFwibnVtYmVyXCIgJiZcbiAgICAgICAgdHlwZW9mIHAgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgcmV0dXJuIG1peE51bWJlcihmcm9tLCB0bywgcCk7XG4gICAgfVxuICAgIGNvbnN0IG1peGVyID0gZ2V0TWl4ZXIoZnJvbSk7XG4gICAgcmV0dXJuIG1peGVyKGZyb20sIHRvKTtcbn1cblxuZXhwb3J0IHsgbWl4IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/number.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/mix/number.mjs ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mixNumber: function() { return /* binding */ mixNumber; }\n/* harmony export */ });\n/*\n  Value in range from progress\n\n  Given a lower limit and an upper limit, we return the value within\n  that range as expressed by progress (usually a number from 0 to 1)\n\n  So progress = 0.5 would change\n\n  from -------- to\n\n  to\n\n  from ---- to\n\n  E.g. from = 10, to = 20, progress = 0.5 => 15\n\n  @param [number]: Lower limit of range\n  @param [number]: Upper limit of range\n  @param [number]: The progress between lower and upper limits expressed 0-1\n  @return [number]: Value as calculated from progress within range (not limited within range)\n*/\nconst mixNumber = (from, to, progress) => {\n    return from + (to - from) * progress;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvbWl4L251bWJlci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVxQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL21peC9udW1iZXIubWpzPzlmZjciXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAgVmFsdWUgaW4gcmFuZ2UgZnJvbSBwcm9ncmVzc1xuXG4gIEdpdmVuIGEgbG93ZXIgbGltaXQgYW5kIGFuIHVwcGVyIGxpbWl0LCB3ZSByZXR1cm4gdGhlIHZhbHVlIHdpdGhpblxuICB0aGF0IHJhbmdlIGFzIGV4cHJlc3NlZCBieSBwcm9ncmVzcyAodXN1YWxseSBhIG51bWJlciBmcm9tIDAgdG8gMSlcblxuICBTbyBwcm9ncmVzcyA9IDAuNSB3b3VsZCBjaGFuZ2VcblxuICBmcm9tIC0tLS0tLS0tIHRvXG5cbiAgdG9cblxuICBmcm9tIC0tLS0gdG9cblxuICBFLmcuIGZyb20gPSAxMCwgdG8gPSAyMCwgcHJvZ3Jlc3MgPSAwLjUgPT4gMTVcblxuICBAcGFyYW0gW251bWJlcl06IExvd2VyIGxpbWl0IG9mIHJhbmdlXG4gIEBwYXJhbSBbbnVtYmVyXTogVXBwZXIgbGltaXQgb2YgcmFuZ2VcbiAgQHBhcmFtIFtudW1iZXJdOiBUaGUgcHJvZ3Jlc3MgYmV0d2VlbiBsb3dlciBhbmQgdXBwZXIgbGltaXRzIGV4cHJlc3NlZCAwLTFcbiAgQHJldHVybiBbbnVtYmVyXTogVmFsdWUgYXMgY2FsY3VsYXRlZCBmcm9tIHByb2dyZXNzIHdpdGhpbiByYW5nZSAobm90IGxpbWl0ZWQgd2l0aGluIHJhbmdlKVxuKi9cbmNvbnN0IG1peE51bWJlciA9IChmcm9tLCB0bywgcHJvZ3Jlc3MpID0+IHtcbiAgICByZXR1cm4gZnJvbSArICh0byAtIGZyb20pICogcHJvZ3Jlc3M7XG59O1xuXG5leHBvcnQgeyBtaXhOdW1iZXIgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/number.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/visibility.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/mix/visibility.mjs ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invisibleValues: function() { return /* binding */ invisibleValues; },\n/* harmony export */   mixVisibility: function() { return /* binding */ mixVisibility; }\n/* harmony export */ });\nconst invisibleValues = new Set([\"none\", \"hidden\"]);\n/**\n * Returns a function that, when provided a progress value between 0 and 1,\n * will return the \"none\" or \"hidden\" string only when the progress is that of\n * the origin or target.\n */\nfunction mixVisibility(origin, target) {\n    if (invisibleValues.has(origin)) {\n        return (p) => (p <= 0 ? origin : target);\n    }\n    else {\n        return (p) => (p >= 1 ? target : origin);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvbWl4L3Zpc2liaWxpdHkubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFMEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy9taXgvdmlzaWJpbGl0eS5tanM/ZGZmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpbnZpc2libGVWYWx1ZXMgPSBuZXcgU2V0KFtcIm5vbmVcIiwgXCJoaWRkZW5cIl0pO1xuLyoqXG4gKiBSZXR1cm5zIGEgZnVuY3Rpb24gdGhhdCwgd2hlbiBwcm92aWRlZCBhIHByb2dyZXNzIHZhbHVlIGJldHdlZW4gMCBhbmQgMSxcbiAqIHdpbGwgcmV0dXJuIHRoZSBcIm5vbmVcIiBvciBcImhpZGRlblwiIHN0cmluZyBvbmx5IHdoZW4gdGhlIHByb2dyZXNzIGlzIHRoYXQgb2ZcbiAqIHRoZSBvcmlnaW4gb3IgdGFyZ2V0LlxuICovXG5mdW5jdGlvbiBtaXhWaXNpYmlsaXR5KG9yaWdpbiwgdGFyZ2V0KSB7XG4gICAgaWYgKGludmlzaWJsZVZhbHVlcy5oYXMob3JpZ2luKSkge1xuICAgICAgICByZXR1cm4gKHApID0+IChwIDw9IDAgPyBvcmlnaW4gOiB0YXJnZXQpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIChwKSA9PiAocCA+PSAxID8gdGFyZ2V0IDogb3JpZ2luKTtcbiAgICB9XG59XG5cbmV4cG9ydCB7IGludmlzaWJsZVZhbHVlcywgbWl4VmlzaWJpbGl0eSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/visibility.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/default.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/offsets/default.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultOffset: function() { return /* binding */ defaultOffset; }\n/* harmony export */ });\n/* harmony import */ var _fill_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fill.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/fill.mjs\");\n\n\nfunction defaultOffset(arr) {\n    const offset = [0];\n    (0,_fill_mjs__WEBPACK_IMPORTED_MODULE_0__.fillOffset)(offset, arr.length - 1);\n    return offset;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvb2Zmc2V0cy9kZWZhdWx0Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3Qzs7QUFFeEM7QUFDQTtBQUNBLElBQUkscURBQVU7QUFDZDtBQUNBOztBQUV5QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL29mZnNldHMvZGVmYXVsdC5tanM/ZTU0OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmaWxsT2Zmc2V0IH0gZnJvbSAnLi9maWxsLm1qcyc7XG5cbmZ1bmN0aW9uIGRlZmF1bHRPZmZzZXQoYXJyKSB7XG4gICAgY29uc3Qgb2Zmc2V0ID0gWzBdO1xuICAgIGZpbGxPZmZzZXQob2Zmc2V0LCBhcnIubGVuZ3RoIC0gMSk7XG4gICAgcmV0dXJuIG9mZnNldDtcbn1cblxuZXhwb3J0IHsgZGVmYXVsdE9mZnNldCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/default.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/fill.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/offsets/fill.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fillOffset: function() { return /* binding */ fillOffset; }\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _mix_number_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../mix/number.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/mix/number.mjs\");\n\n\n\nfunction fillOffset(offset, remaining) {\n    const min = offset[offset.length - 1];\n    for (let i = 1; i <= remaining; i++) {\n        const offsetProgress = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.progress)(0, remaining, i);\n        offset.push((0,_mix_number_mjs__WEBPACK_IMPORTED_MODULE_1__.mixNumber)(min, 1, offsetProgress));\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvb2Zmc2V0cy9maWxsLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBd0M7QUFDTTs7QUFFOUM7QUFDQTtBQUNBLG9CQUFvQixnQkFBZ0I7QUFDcEMsK0JBQStCLHNEQUFRO0FBQ3ZDLG9CQUFvQiwwREFBUztBQUM3QjtBQUNBOztBQUVzQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL29mZnNldHMvZmlsbC5tanM/NTY0NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBwcm9ncmVzcyB9IGZyb20gJ21vdGlvbi11dGlscyc7XG5pbXBvcnQgeyBtaXhOdW1iZXIgfSBmcm9tICcuLi9taXgvbnVtYmVyLm1qcyc7XG5cbmZ1bmN0aW9uIGZpbGxPZmZzZXQob2Zmc2V0LCByZW1haW5pbmcpIHtcbiAgICBjb25zdCBtaW4gPSBvZmZzZXRbb2Zmc2V0Lmxlbmd0aCAtIDFdO1xuICAgIGZvciAobGV0IGkgPSAxOyBpIDw9IHJlbWFpbmluZzsgaSsrKSB7XG4gICAgICAgIGNvbnN0IG9mZnNldFByb2dyZXNzID0gcHJvZ3Jlc3MoMCwgcmVtYWluaW5nLCBpKTtcbiAgICAgICAgb2Zmc2V0LnB1c2gobWl4TnVtYmVyKG1pbiwgMSwgb2Zmc2V0UHJvZ3Jlc3MpKTtcbiAgICB9XG59XG5cbmV4cG9ydCB7IGZpbGxPZmZzZXQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/offsets/fill.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/pipe.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/pipe.mjs ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pipe: function() { return /* binding */ pipe; }\n/* harmony export */ });\n/**\n * Pipe\n * Compose other transformers to run linearily\n * pipe(min(20), max(40))\n * @param  {...functions} transformers\n * @return {function}\n */\nconst combineFunctions = (a, b) => (v) => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvcGlwZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxjQUFjO0FBQzFCLFlBQVk7QUFDWjtBQUNBO0FBQ0E7O0FBRWdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvcGlwZS5tanM/NDk3NyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFBpcGVcbiAqIENvbXBvc2Ugb3RoZXIgdHJhbnNmb3JtZXJzIHRvIHJ1biBsaW5lYXJpbHlcbiAqIHBpcGUobWluKDIwKSwgbWF4KDQwKSlcbiAqIEBwYXJhbSAgey4uLmZ1bmN0aW9uc30gdHJhbnNmb3JtZXJzXG4gKiBAcmV0dXJuIHtmdW5jdGlvbn1cbiAqL1xuY29uc3QgY29tYmluZUZ1bmN0aW9ucyA9IChhLCBiKSA9PiAodikgPT4gYihhKHYpKTtcbmNvbnN0IHBpcGUgPSAoLi4udHJhbnNmb3JtZXJzKSA9PiB0cmFuc2Zvcm1lcnMucmVkdWNlKGNvbWJpbmVGdW5jdGlvbnMpO1xuXG5leHBvcnQgeyBwaXBlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/pipe.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/subscription-manager.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/subscription-manager.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SubscriptionManager: function() { return /* binding */ SubscriptionManager; }\n/* harmony export */ });\n/* harmony import */ var _array_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/array.mjs\");\n\n\nclass SubscriptionManager {\n    constructor() {\n        this.subscriptions = [];\n    }\n    add(handler) {\n        (0,_array_mjs__WEBPACK_IMPORTED_MODULE_0__.addUniqueItem)(this.subscriptions, handler);\n        return () => (0,_array_mjs__WEBPACK_IMPORTED_MODULE_0__.removeItem)(this.subscriptions, handler);\n    }\n    notify(a, b, c) {\n        const numSubscriptions = this.subscriptions.length;\n        if (!numSubscriptions)\n            return;\n        if (numSubscriptions === 1) {\n            /**\n             * If there's only a single handler we can just call it without invoking a loop.\n             */\n            this.subscriptions[0](a, b, c);\n        }\n        else {\n            for (let i = 0; i < numSubscriptions; i++) {\n                /**\n                 * Check whether the handler exists before firing as it's possible\n                 * the subscriptions were modified during this loop running.\n                 */\n                const handler = this.subscriptions[i];\n                handler && handler(a, b, c);\n            }\n        }\n    }\n    getSize() {\n        return this.subscriptions.length;\n    }\n    clear() {\n        this.subscriptions.length = 0;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/subscription-manager.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-constant.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConstant: function() { return /* binding */ useConstant; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\n/**\n * Creates a constant value over the lifecycle of a component.\n *\n * Even if `useMemo` is provided an empty array as its final argument, it doesn't offer\n * a guarantee that it won't re-run for performance reasons later on. By using `useConstant`\n * you can ensure that initialisers don't execute twice or more.\n */\nfunction useConstant(init) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    if (ref.current === null) {\n        ref.current = init();\n    }\n    return ref.current;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWNvbnN0YW50Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQjs7QUFFL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiw2Q0FBTTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV1QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3VzZS1jb25zdGFudC5tanM/MjI1NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogQ3JlYXRlcyBhIGNvbnN0YW50IHZhbHVlIG92ZXIgdGhlIGxpZmVjeWNsZSBvZiBhIGNvbXBvbmVudC5cbiAqXG4gKiBFdmVuIGlmIGB1c2VNZW1vYCBpcyBwcm92aWRlZCBhbiBlbXB0eSBhcnJheSBhcyBpdHMgZmluYWwgYXJndW1lbnQsIGl0IGRvZXNuJ3Qgb2ZmZXJcbiAqIGEgZ3VhcmFudGVlIHRoYXQgaXQgd29uJ3QgcmUtcnVuIGZvciBwZXJmb3JtYW5jZSByZWFzb25zIGxhdGVyIG9uLiBCeSB1c2luZyBgdXNlQ29uc3RhbnRgXG4gKiB5b3UgY2FuIGVuc3VyZSB0aGF0IGluaXRpYWxpc2VycyBkb24ndCBleGVjdXRlIHR3aWNlIG9yIG1vcmUuXG4gKi9cbmZ1bmN0aW9uIHVzZUNvbnN0YW50KGluaXQpIHtcbiAgICBjb25zdCByZWYgPSB1c2VSZWYobnVsbCk7XG4gICAgaWYgKHJlZi5jdXJyZW50ID09PSBudWxsKSB7XG4gICAgICAgIHJlZi5jdXJyZW50ID0gaW5pdCgpO1xuICAgIH1cbiAgICByZXR1cm4gcmVmLmN1cnJlbnQ7XG59XG5cbmV4cG9ydCB7IHVzZUNvbnN0YW50IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsomorphicLayoutEffect: function() { return /* binding */ useIsomorphicLayoutEffect; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _is_browser_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is-browser.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-browser.mjs\");\n\n\n\nconst useIsomorphicLayoutEffect = _is_browser_mjs__WEBPACK_IMPORTED_MODULE_1__.isBrowser ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWlzb21vcnBoaWMtZWZmZWN0Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUQ7QUFDTjs7QUFFN0Msa0NBQWtDLHNEQUFTLEdBQUcsa0RBQWUsR0FBRyw0Q0FBUzs7QUFFcEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy91c2UtaXNvbW9ycGhpYy1lZmZlY3QubWpzPzg0YmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlTGF5b3V0RWZmZWN0LCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBpc0Jyb3dzZXIgfSBmcm9tICcuL2lzLWJyb3dzZXIubWpzJztcblxuY29uc3QgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCA9IGlzQnJvd3NlciA/IHVzZUxheW91dEVmZmVjdCA6IHVzZUVmZmVjdDtcblxuZXhwb3J0IHsgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-motion-value-event.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-motion-value-event.mjs ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionValueEvent: function() { return /* binding */ useMotionValueEvent; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\nfunction useMotionValueEvent(value, event, callback) {\n    /**\n     * useInsertionEffect will create subscriptions before any other\n     * effects will run. Effects run upwards through the tree so it\n     * can be that binding a useLayoutEffect higher up the tree can\n     * miss changes from lower down the tree.\n     */\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useInsertionEffect)(() => value.on(event, callback), [value, event, callback]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLW1vdGlvbi12YWx1ZS1ldmVudC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkM7O0FBRTNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSx5REFBa0I7QUFDdEI7O0FBRStCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLW1vdGlvbi12YWx1ZS1ldmVudC5tanM/Y2ZiNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VJbnNlcnRpb25FZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbmZ1bmN0aW9uIHVzZU1vdGlvblZhbHVlRXZlbnQodmFsdWUsIGV2ZW50LCBjYWxsYmFjaykge1xuICAgIC8qKlxuICAgICAqIHVzZUluc2VydGlvbkVmZmVjdCB3aWxsIGNyZWF0ZSBzdWJzY3JpcHRpb25zIGJlZm9yZSBhbnkgb3RoZXJcbiAgICAgKiBlZmZlY3RzIHdpbGwgcnVuLiBFZmZlY3RzIHJ1biB1cHdhcmRzIHRocm91Z2ggdGhlIHRyZWUgc28gaXRcbiAgICAgKiBjYW4gYmUgdGhhdCBiaW5kaW5nIGEgdXNlTGF5b3V0RWZmZWN0IGhpZ2hlciB1cCB0aGUgdHJlZSBjYW5cbiAgICAgKiBtaXNzIGNoYW5nZXMgZnJvbSBsb3dlciBkb3duIHRoZSB0cmVlLlxuICAgICAqL1xuICAgIHVzZUluc2VydGlvbkVmZmVjdCgoKSA9PiB2YWx1ZS5vbihldmVudCwgY2FsbGJhY2spLCBbdmFsdWUsIGV2ZW50LCBjYWxsYmFja10pO1xufVxuXG5leHBvcnQgeyB1c2VNb3Rpb25WYWx1ZUV2ZW50IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-motion-value-event.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   velocityPerSecond: function() { return /* binding */ velocityPerSecond; }\n/* harmony export */ });\n/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdmVsb2NpdHktcGVyLXNlY29uZC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU2QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3ZlbG9jaXR5LXBlci1zZWNvbmQubWpzP2I0MDgiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAgQ29udmVydCB2ZWxvY2l0eSBpbnRvIHZlbG9jaXR5IHBlciBzZWNvbmRcblxuICBAcGFyYW0gW251bWJlcl06IFVuaXQgcGVyIGZyYW1lXG4gIEBwYXJhbSBbbnVtYmVyXTogRnJhbWUgZHVyYXRpb24gaW4gbXNcbiovXG5mdW5jdGlvbiB2ZWxvY2l0eVBlclNlY29uZCh2ZWxvY2l0eSwgZnJhbWVEdXJhdGlvbikge1xuICAgIHJldHVybiBmcmFtZUR1cmF0aW9uID8gdmVsb2NpdHkgKiAoMTAwMCAvIGZyYW1lRHVyYXRpb24pIDogMDtcbn1cblxuZXhwb3J0IHsgdmVsb2NpdHlQZXJTZWNvbmQgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/warn-once.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/warn-once.mjs ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   warnOnce: function() { return /* binding */ warnOnce; }\n/* harmony export */ });\nconst warned = new Set();\nfunction warnOnce(condition, message, element) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(message);\n    if (element)\n        console.warn(element);\n    warned.add(message);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvd2Fybi1vbmNlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVvQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3dhcm4tb25jZS5tanM/MWFjZiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB3YXJuZWQgPSBuZXcgU2V0KCk7XG5mdW5jdGlvbiB3YXJuT25jZShjb25kaXRpb24sIG1lc3NhZ2UsIGVsZW1lbnQpIHtcbiAgICBpZiAoY29uZGl0aW9uIHx8IHdhcm5lZC5oYXMobWVzc2FnZSkpXG4gICAgICAgIHJldHVybjtcbiAgICBjb25zb2xlLndhcm4obWVzc2FnZSk7XG4gICAgaWYgKGVsZW1lbnQpXG4gICAgICAgIGNvbnNvbGUud2FybihlbGVtZW50KTtcbiAgICB3YXJuZWQuYWRkKG1lc3NhZ2UpO1xufVxuXG5leHBvcnQgeyB3YXJuT25jZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/warn-once.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/index.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionValue: function() { return /* binding */ MotionValue; },\n/* harmony export */   collectMotionValues: function() { return /* binding */ collectMotionValues; },\n/* harmony export */   motionValue: function() { return /* binding */ motionValue; }\n/* harmony export */ });\n/* harmony import */ var _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../frameloop/sync-time.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/sync-time.mjs\");\n/* harmony import */ var _utils_subscription_manager_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/subscription-manager.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/subscription-manager.mjs\");\n/* harmony import */ var _utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/velocity-per-second.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/velocity-per-second.mjs\");\n/* harmony import */ var _utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/warn-once.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/warn-once.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\n/**\n * Maximum time between the value of two frames, beyond which we\n * assume the velocity has since been 0.\n */\nconst MAX_VELOCITY_DELTA = 30;\nconst isFloat = (value) => {\n    return !isNaN(parseFloat(value));\n};\nconst collectMotionValues = {\n    current: undefined,\n};\n/**\n * `MotionValue` is used to track the state and velocity of motion values.\n *\n * @public\n */\nclass MotionValue {\n    /**\n     * @param init - The initiating value\n     * @param config - Optional configuration options\n     *\n     * -  `transformer`: A function to transform incoming values with.\n     *\n     * @internal\n     */\n    constructor(init, options = {}) {\n        /**\n         * This will be replaced by the build step with the latest version number.\n         * When MotionValues are provided to motion components, warn if versions are mixed.\n         */\n        this.version = \"11.18.2\";\n        /**\n         * Tracks whether this value can output a velocity. Currently this is only true\n         * if the value is numerical, but we might be able to widen the scope here and support\n         * other value types.\n         *\n         * @internal\n         */\n        this.canTrackVelocity = null;\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        this.updateAndNotify = (v, render = true) => {\n            const currentTime = _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_0__.time.now();\n            /**\n             * If we're updating the value during another frame or eventloop\n             * than the previous frame, then the we set the previous frame value\n             * to current.\n             */\n            if (this.updatedAt !== currentTime) {\n                this.setPrevFrameValue();\n            }\n            this.prev = this.current;\n            this.setCurrent(v);\n            // Update update subscribers\n            if (this.current !== this.prev && this.events.change) {\n                this.events.change.notify(this.current);\n            }\n            // Update render subscribers\n            if (render && this.events.renderRequest) {\n                this.events.renderRequest.notify(this.current);\n            }\n        };\n        this.hasAnimated = false;\n        this.setCurrent(init);\n        this.owner = options.owner;\n    }\n    setCurrent(current) {\n        this.current = current;\n        this.updatedAt = _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_0__.time.now();\n        if (this.canTrackVelocity === null && current !== undefined) {\n            this.canTrackVelocity = isFloat(this.current);\n        }\n    }\n    setPrevFrameValue(prevFrameValue = this.current) {\n        this.prevFrameValue = prevFrameValue;\n        this.prevUpdatedAt = this.updatedAt;\n    }\n    /**\n     * Adds a function that will be notified when the `MotionValue` is updated.\n     *\n     * It returns a function that, when called, will cancel the subscription.\n     *\n     * When calling `onChange` inside a React component, it should be wrapped with the\n     * `useEffect` hook. As it returns an unsubscribe function, this should be returned\n     * from the `useEffect` function to ensure you don't add duplicate subscribers..\n     *\n     * ```jsx\n     * export const MyComponent = () => {\n     *   const x = useMotionValue(0)\n     *   const y = useMotionValue(0)\n     *   const opacity = useMotionValue(1)\n     *\n     *   useEffect(() => {\n     *     function updateOpacity() {\n     *       const maxXY = Math.max(x.get(), y.get())\n     *       const newOpacity = transform(maxXY, [0, 100], [1, 0])\n     *       opacity.set(newOpacity)\n     *     }\n     *\n     *     const unsubscribeX = x.on(\"change\", updateOpacity)\n     *     const unsubscribeY = y.on(\"change\", updateOpacity)\n     *\n     *     return () => {\n     *       unsubscribeX()\n     *       unsubscribeY()\n     *     }\n     *   }, [])\n     *\n     *   return <motion.div style={{ x }} />\n     * }\n     * ```\n     *\n     * @param subscriber - A function that receives the latest value.\n     * @returns A function that, when called, will cancel this subscription.\n     *\n     * @deprecated\n     */\n    onChange(subscription) {\n        if (true) {\n            (0,_utils_warn_once_mjs__WEBPACK_IMPORTED_MODULE_1__.warnOnce)(false, `value.onChange(callback) is deprecated. Switch to value.on(\"change\", callback).`);\n        }\n        return this.on(\"change\", subscription);\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new _utils_subscription_manager_mjs__WEBPACK_IMPORTED_MODULE_2__.SubscriptionManager();\n        }\n        const unsubscribe = this.events[eventName].add(callback);\n        if (eventName === \"change\") {\n            return () => {\n                unsubscribe();\n                /**\n                 * If we have no more change listeners by the start\n                 * of the next frame, stop active animations.\n                 */\n                _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_3__.frame.read(() => {\n                    if (!this.events.change.getSize()) {\n                        this.stop();\n                    }\n                });\n            };\n        }\n        return unsubscribe;\n    }\n    clearListeners() {\n        for (const eventManagers in this.events) {\n            this.events[eventManagers].clear();\n        }\n    }\n    /**\n     * Attaches a passive effect to the `MotionValue`.\n     *\n     * @internal\n     */\n    attach(passiveEffect, stopPassiveEffect) {\n        this.passiveEffect = passiveEffect;\n        this.stopPassiveEffect = stopPassiveEffect;\n    }\n    /**\n     * Sets the state of the `MotionValue`.\n     *\n     * @remarks\n     *\n     * ```jsx\n     * const x = useMotionValue(0)\n     * x.set(10)\n     * ```\n     *\n     * @param latest - Latest value to set.\n     * @param render - Whether to notify render subscribers. Defaults to `true`\n     *\n     * @public\n     */\n    set(v, render = true) {\n        if (!render || !this.passiveEffect) {\n            this.updateAndNotify(v, render);\n        }\n        else {\n            this.passiveEffect(v, this.updateAndNotify);\n        }\n    }\n    setWithVelocity(prev, current, delta) {\n        this.set(current);\n        this.prev = undefined;\n        this.prevFrameValue = prev;\n        this.prevUpdatedAt = this.updatedAt - delta;\n    }\n    /**\n     * Set the state of the `MotionValue`, stopping any active animations,\n     * effects, and resets velocity to `0`.\n     */\n    jump(v, endAnimation = true) {\n        this.updateAndNotify(v);\n        this.prev = v;\n        this.prevUpdatedAt = this.prevFrameValue = undefined;\n        endAnimation && this.stop();\n        if (this.stopPassiveEffect)\n            this.stopPassiveEffect();\n    }\n    /**\n     * Returns the latest state of `MotionValue`\n     *\n     * @returns - The latest state of `MotionValue`\n     *\n     * @public\n     */\n    get() {\n        if (collectMotionValues.current) {\n            collectMotionValues.current.push(this);\n        }\n        return this.current;\n    }\n    /**\n     * @public\n     */\n    getPrevious() {\n        return this.prev;\n    }\n    /**\n     * Returns the latest velocity of `MotionValue`\n     *\n     * @returns - The latest velocity of `MotionValue`. Returns `0` if the state is non-numerical.\n     *\n     * @public\n     */\n    getVelocity() {\n        const currentTime = _frameloop_sync_time_mjs__WEBPACK_IMPORTED_MODULE_0__.time.now();\n        if (!this.canTrackVelocity ||\n            this.prevFrameValue === undefined ||\n            currentTime - this.updatedAt > MAX_VELOCITY_DELTA) {\n            return 0;\n        }\n        const delta = Math.min(this.updatedAt - this.prevUpdatedAt, MAX_VELOCITY_DELTA);\n        // Casts because of parseFloat's poor typing\n        return (0,_utils_velocity_per_second_mjs__WEBPACK_IMPORTED_MODULE_4__.velocityPerSecond)(parseFloat(this.current) -\n            parseFloat(this.prevFrameValue), delta);\n    }\n    /**\n     * Registers a new animation to control this `MotionValue`. Only one\n     * animation can drive a `MotionValue` at one time.\n     *\n     * ```jsx\n     * value.start()\n     * ```\n     *\n     * @param animation - A function that starts the provided animation\n     *\n     * @internal\n     */\n    start(startAnimation) {\n        this.stop();\n        return new Promise((resolve) => {\n            this.hasAnimated = true;\n            this.animation = startAnimation(resolve);\n            if (this.events.animationStart) {\n                this.events.animationStart.notify();\n            }\n        }).then(() => {\n            if (this.events.animationComplete) {\n                this.events.animationComplete.notify();\n            }\n            this.clearAnimation();\n        });\n    }\n    /**\n     * Stop the currently active animation.\n     *\n     * @public\n     */\n    stop() {\n        if (this.animation) {\n            this.animation.stop();\n            if (this.events.animationCancel) {\n                this.events.animationCancel.notify();\n            }\n        }\n        this.clearAnimation();\n    }\n    /**\n     * Returns `true` if this value is currently animating.\n     *\n     * @public\n     */\n    isAnimating() {\n        return !!this.animation;\n    }\n    clearAnimation() {\n        delete this.animation;\n    }\n    /**\n     * Destroy and clean up subscribers to this `MotionValue`.\n     *\n     * The `MotionValue` hooks like `useMotionValue` and `useTransform` automatically\n     * handle the lifecycle of the returned `MotionValue`, so this method is only necessary if you've manually\n     * created a `MotionValue` via the `motionValue` function.\n     *\n     * @public\n     */\n    destroy() {\n        this.clearListeners();\n        this.stop();\n        if (this.stopPassiveEffect) {\n            this.stopPassiveEffect();\n        }\n    }\n}\nfunction motionValue(init, options) {\n    return new MotionValue(init, options);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hex.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/color/hex.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hex: function() { return /* binding */ hex; }\n/* harmony export */ });\n/* harmony import */ var _rgba_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rgba.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/rgba.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/utils.mjs\");\n\n\n\nfunction parseHex(v) {\n    let r = \"\";\n    let g = \"\";\n    let b = \"\";\n    let a = \"\";\n    // If we have 6 characters, ie #FF0000\n    if (v.length > 5) {\n        r = v.substring(1, 3);\n        g = v.substring(3, 5);\n        b = v.substring(5, 7);\n        a = v.substring(7, 9);\n        // Or we have 3 characters, ie #F00\n    }\n    else {\n        r = v.substring(1, 2);\n        g = v.substring(2, 3);\n        b = v.substring(3, 4);\n        a = v.substring(4, 5);\n        r += r;\n        g += g;\n        b += b;\n        a += a;\n    }\n    return {\n        red: parseInt(r, 16),\n        green: parseInt(g, 16),\n        blue: parseInt(b, 16),\n        alpha: a ? parseInt(a, 16) / 255 : 1,\n    };\n}\nconst hex = {\n    test: /*@__PURE__*/ (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.isColorString)(\"#\"),\n    parse: parseHex,\n    transform: _rgba_mjs__WEBPACK_IMPORTED_MODULE_1__.rgba.transform,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29sb3IvaGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0M7QUFDVTs7QUFFNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseURBQWE7QUFDckM7QUFDQSxlQUFlLDJDQUFJO0FBQ25COztBQUVlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29sb3IvaGV4Lm1qcz82MWU5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHJnYmEgfSBmcm9tICcuL3JnYmEubWpzJztcbmltcG9ydCB7IGlzQ29sb3JTdHJpbmcgfSBmcm9tICcuL3V0aWxzLm1qcyc7XG5cbmZ1bmN0aW9uIHBhcnNlSGV4KHYpIHtcbiAgICBsZXQgciA9IFwiXCI7XG4gICAgbGV0IGcgPSBcIlwiO1xuICAgIGxldCBiID0gXCJcIjtcbiAgICBsZXQgYSA9IFwiXCI7XG4gICAgLy8gSWYgd2UgaGF2ZSA2IGNoYXJhY3RlcnMsIGllICNGRjAwMDBcbiAgICBpZiAodi5sZW5ndGggPiA1KSB7XG4gICAgICAgIHIgPSB2LnN1YnN0cmluZygxLCAzKTtcbiAgICAgICAgZyA9IHYuc3Vic3RyaW5nKDMsIDUpO1xuICAgICAgICBiID0gdi5zdWJzdHJpbmcoNSwgNyk7XG4gICAgICAgIGEgPSB2LnN1YnN0cmluZyg3LCA5KTtcbiAgICAgICAgLy8gT3Igd2UgaGF2ZSAzIGNoYXJhY3RlcnMsIGllICNGMDBcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHIgPSB2LnN1YnN0cmluZygxLCAyKTtcbiAgICAgICAgZyA9IHYuc3Vic3RyaW5nKDIsIDMpO1xuICAgICAgICBiID0gdi5zdWJzdHJpbmcoMywgNCk7XG4gICAgICAgIGEgPSB2LnN1YnN0cmluZyg0LCA1KTtcbiAgICAgICAgciArPSByO1xuICAgICAgICBnICs9IGc7XG4gICAgICAgIGIgKz0gYjtcbiAgICAgICAgYSArPSBhO1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICByZWQ6IHBhcnNlSW50KHIsIDE2KSxcbiAgICAgICAgZ3JlZW46IHBhcnNlSW50KGcsIDE2KSxcbiAgICAgICAgYmx1ZTogcGFyc2VJbnQoYiwgMTYpLFxuICAgICAgICBhbHBoYTogYSA/IHBhcnNlSW50KGEsIDE2KSAvIDI1NSA6IDEsXG4gICAgfTtcbn1cbmNvbnN0IGhleCA9IHtcbiAgICB0ZXN0OiAvKkBfX1BVUkVfXyovIGlzQ29sb3JTdHJpbmcoXCIjXCIpLFxuICAgIHBhcnNlOiBwYXJzZUhleCxcbiAgICB0cmFuc2Zvcm06IHJnYmEudHJhbnNmb3JtLFxufTtcblxuZXhwb3J0IHsgaGV4IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hex.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hsla.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/color/hsla.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hsla: function() { return /* binding */ hsla; }\n/* harmony export */ });\n/* harmony import */ var _numbers_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../numbers/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs\");\n/* harmony import */ var _numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../numbers/units.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\");\n/* harmony import */ var _utils_sanitize_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/sanitize.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/sanitize.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/utils.mjs\");\n\n\n\n\n\nconst hsla = {\n    test: /*@__PURE__*/ (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.isColorString)(\"hsl\", \"hue\"),\n    parse: /*@__PURE__*/ (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_0__.splitColor)(\"hue\", \"saturation\", \"lightness\"),\n    transform: ({ hue, saturation, lightness, alpha: alpha$1 = 1 }) => {\n        return (\"hsla(\" +\n            Math.round(hue) +\n            \", \" +\n            _numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__.percent.transform((0,_utils_sanitize_mjs__WEBPACK_IMPORTED_MODULE_2__.sanitize)(saturation)) +\n            \", \" +\n            _numbers_units_mjs__WEBPACK_IMPORTED_MODULE_1__.percent.transform((0,_utils_sanitize_mjs__WEBPACK_IMPORTED_MODULE_2__.sanitize)(lightness)) +\n            \", \" +\n            (0,_utils_sanitize_mjs__WEBPACK_IMPORTED_MODULE_2__.sanitize)(_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_3__.alpha.transform(alpha$1)) +\n            \")\");\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29sb3IvaHNsYS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNkM7QUFDRTtBQUNFO0FBQ087O0FBRXhEO0FBQ0Esd0JBQXdCLHlEQUFhO0FBQ3JDLHlCQUF5QixzREFBVTtBQUNuQyxrQkFBa0IsZ0RBQWdEO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBLFlBQVksdURBQU8sV0FBVyw2REFBUTtBQUN0QztBQUNBLFlBQVksdURBQU8sV0FBVyw2REFBUTtBQUN0QztBQUNBLFlBQVksNkRBQVEsQ0FBQyxxREFBSztBQUMxQjtBQUNBLEtBQUs7QUFDTDs7QUFFZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS90eXBlcy9jb2xvci9oc2xhLm1qcz9mYmY2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFscGhhIH0gZnJvbSAnLi4vbnVtYmVycy9pbmRleC5tanMnO1xuaW1wb3J0IHsgcGVyY2VudCB9IGZyb20gJy4uL251bWJlcnMvdW5pdHMubWpzJztcbmltcG9ydCB7IHNhbml0aXplIH0gZnJvbSAnLi4vdXRpbHMvc2FuaXRpemUubWpzJztcbmltcG9ydCB7IGlzQ29sb3JTdHJpbmcsIHNwbGl0Q29sb3IgfSBmcm9tICcuL3V0aWxzLm1qcyc7XG5cbmNvbnN0IGhzbGEgPSB7XG4gICAgdGVzdDogLypAX19QVVJFX18qLyBpc0NvbG9yU3RyaW5nKFwiaHNsXCIsIFwiaHVlXCIpLFxuICAgIHBhcnNlOiAvKkBfX1BVUkVfXyovIHNwbGl0Q29sb3IoXCJodWVcIiwgXCJzYXR1cmF0aW9uXCIsIFwibGlnaHRuZXNzXCIpLFxuICAgIHRyYW5zZm9ybTogKHsgaHVlLCBzYXR1cmF0aW9uLCBsaWdodG5lc3MsIGFscGhhOiBhbHBoYSQxID0gMSB9KSA9PiB7XG4gICAgICAgIHJldHVybiAoXCJoc2xhKFwiICtcbiAgICAgICAgICAgIE1hdGgucm91bmQoaHVlKSArXG4gICAgICAgICAgICBcIiwgXCIgK1xuICAgICAgICAgICAgcGVyY2VudC50cmFuc2Zvcm0oc2FuaXRpemUoc2F0dXJhdGlvbikpICtcbiAgICAgICAgICAgIFwiLCBcIiArXG4gICAgICAgICAgICBwZXJjZW50LnRyYW5zZm9ybShzYW5pdGl6ZShsaWdodG5lc3MpKSArXG4gICAgICAgICAgICBcIiwgXCIgK1xuICAgICAgICAgICAgc2FuaXRpemUoYWxwaGEudHJhbnNmb3JtKGFscGhhJDEpKSArXG4gICAgICAgICAgICBcIilcIik7XG4gICAgfSxcbn07XG5cbmV4cG9ydCB7IGhzbGEgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hsla.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/color/index.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   color: function() { return /* binding */ color; }\n/* harmony export */ });\n/* harmony import */ var _hex_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hex.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hex.mjs\");\n/* harmony import */ var _hsla_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hsla.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/hsla.mjs\");\n/* harmony import */ var _rgba_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rgba.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/rgba.mjs\");\n\n\n\n\nconst color = {\n    test: (v) => _rgba_mjs__WEBPACK_IMPORTED_MODULE_0__.rgba.test(v) || _hex_mjs__WEBPACK_IMPORTED_MODULE_1__.hex.test(v) || _hsla_mjs__WEBPACK_IMPORTED_MODULE_2__.hsla.test(v),\n    parse: (v) => {\n        if (_rgba_mjs__WEBPACK_IMPORTED_MODULE_0__.rgba.test(v)) {\n            return _rgba_mjs__WEBPACK_IMPORTED_MODULE_0__.rgba.parse(v);\n        }\n        else if (_hsla_mjs__WEBPACK_IMPORTED_MODULE_2__.hsla.test(v)) {\n            return _hsla_mjs__WEBPACK_IMPORTED_MODULE_2__.hsla.parse(v);\n        }\n        else {\n            return _hex_mjs__WEBPACK_IMPORTED_MODULE_1__.hex.parse(v);\n        }\n    },\n    transform: (v) => {\n        return typeof v === \"string\"\n            ? v\n            : v.hasOwnProperty(\"red\")\n                ? _rgba_mjs__WEBPACK_IMPORTED_MODULE_0__.rgba.transform(v)\n                : _hsla_mjs__WEBPACK_IMPORTED_MODULE_2__.hsla.transform(v);\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29sb3IvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZ0M7QUFDRTtBQUNBOztBQUVsQztBQUNBLGlCQUFpQiwyQ0FBSSxZQUFZLHlDQUFHLFlBQVksMkNBQUk7QUFDcEQ7QUFDQSxZQUFZLDJDQUFJO0FBQ2hCLG1CQUFtQiwyQ0FBSTtBQUN2QjtBQUNBLGlCQUFpQiwyQ0FBSTtBQUNyQixtQkFBbUIsMkNBQUk7QUFDdkI7QUFDQTtBQUNBLG1CQUFtQix5Q0FBRztBQUN0QjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiwyQ0FBSTtBQUN0QixrQkFBa0IsMkNBQUk7QUFDdEIsS0FBSztBQUNMOztBQUVpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3ZhbHVlL3R5cGVzL2NvbG9yL2luZGV4Lm1qcz8xYmRlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGhleCB9IGZyb20gJy4vaGV4Lm1qcyc7XG5pbXBvcnQgeyBoc2xhIH0gZnJvbSAnLi9oc2xhLm1qcyc7XG5pbXBvcnQgeyByZ2JhIH0gZnJvbSAnLi9yZ2JhLm1qcyc7XG5cbmNvbnN0IGNvbG9yID0ge1xuICAgIHRlc3Q6ICh2KSA9PiByZ2JhLnRlc3QodikgfHwgaGV4LnRlc3QodikgfHwgaHNsYS50ZXN0KHYpLFxuICAgIHBhcnNlOiAodikgPT4ge1xuICAgICAgICBpZiAocmdiYS50ZXN0KHYpKSB7XG4gICAgICAgICAgICByZXR1cm4gcmdiYS5wYXJzZSh2KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChoc2xhLnRlc3QodikpIHtcbiAgICAgICAgICAgIHJldHVybiBoc2xhLnBhcnNlKHYpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIGhleC5wYXJzZSh2KTtcbiAgICAgICAgfVxuICAgIH0sXG4gICAgdHJhbnNmb3JtOiAodikgPT4ge1xuICAgICAgICByZXR1cm4gdHlwZW9mIHYgPT09IFwic3RyaW5nXCJcbiAgICAgICAgICAgID8gdlxuICAgICAgICAgICAgOiB2Lmhhc093blByb3BlcnR5KFwicmVkXCIpXG4gICAgICAgICAgICAgICAgPyByZ2JhLnRyYW5zZm9ybSh2KVxuICAgICAgICAgICAgICAgIDogaHNsYS50cmFuc2Zvcm0odik7XG4gICAgfSxcbn07XG5cbmV4cG9ydCB7IGNvbG9yIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/rgba.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/color/rgba.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rgbUnit: function() { return /* binding */ rgbUnit; },\n/* harmony export */   rgba: function() { return /* binding */ rgba; }\n/* harmony export */ });\n/* harmony import */ var _utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/clamp.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\");\n/* harmony import */ var _numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../numbers/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs\");\n/* harmony import */ var _utils_sanitize_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/sanitize.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/sanitize.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/utils.mjs\");\n\n\n\n\n\nconst clampRgbUnit = (v) => (0,_utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_0__.clamp)(0, 255, v);\nconst rgbUnit = {\n    ..._numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__.number,\n    transform: (v) => Math.round(clampRgbUnit(v)),\n};\nconst rgba = {\n    test: /*@__PURE__*/ (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.isColorString)(\"rgb\", \"red\"),\n    parse: /*@__PURE__*/ (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_2__.splitColor)(\"red\", \"green\", \"blue\"),\n    transform: ({ red, green, blue, alpha: alpha$1 = 1 }) => \"rgba(\" +\n        rgbUnit.transform(red) +\n        \", \" +\n        rgbUnit.transform(green) +\n        \", \" +\n        rgbUnit.transform(blue) +\n        \", \" +\n        (0,_utils_sanitize_mjs__WEBPACK_IMPORTED_MODULE_3__.sanitize)(_numbers_index_mjs__WEBPACK_IMPORTED_MODULE_1__.alpha.transform(alpha$1)) +\n        \")\",\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29sb3IvcmdiYS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQWlEO0FBQ0k7QUFDSjtBQUNPOztBQUV4RCw0QkFBNEIsdURBQUs7QUFDakM7QUFDQSxPQUFPLHNEQUFNO0FBQ2I7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlEQUFhO0FBQ3JDLHlCQUF5QixzREFBVTtBQUNuQyxrQkFBa0Isc0NBQXNDO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsNkRBQVEsQ0FBQyxxREFBSztBQUN0QjtBQUNBOztBQUV5QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3ZhbHVlL3R5cGVzL2NvbG9yL3JnYmEubWpzPzBlODkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xhbXAgfSBmcm9tICcuLi8uLi8uLi91dGlscy9jbGFtcC5tanMnO1xuaW1wb3J0IHsgYWxwaGEsIG51bWJlciB9IGZyb20gJy4uL251bWJlcnMvaW5kZXgubWpzJztcbmltcG9ydCB7IHNhbml0aXplIH0gZnJvbSAnLi4vdXRpbHMvc2FuaXRpemUubWpzJztcbmltcG9ydCB7IGlzQ29sb3JTdHJpbmcsIHNwbGl0Q29sb3IgfSBmcm9tICcuL3V0aWxzLm1qcyc7XG5cbmNvbnN0IGNsYW1wUmdiVW5pdCA9ICh2KSA9PiBjbGFtcCgwLCAyNTUsIHYpO1xuY29uc3QgcmdiVW5pdCA9IHtcbiAgICAuLi5udW1iZXIsXG4gICAgdHJhbnNmb3JtOiAodikgPT4gTWF0aC5yb3VuZChjbGFtcFJnYlVuaXQodikpLFxufTtcbmNvbnN0IHJnYmEgPSB7XG4gICAgdGVzdDogLypAX19QVVJFX18qLyBpc0NvbG9yU3RyaW5nKFwicmdiXCIsIFwicmVkXCIpLFxuICAgIHBhcnNlOiAvKkBfX1BVUkVfXyovIHNwbGl0Q29sb3IoXCJyZWRcIiwgXCJncmVlblwiLCBcImJsdWVcIiksXG4gICAgdHJhbnNmb3JtOiAoeyByZWQsIGdyZWVuLCBibHVlLCBhbHBoYTogYWxwaGEkMSA9IDEgfSkgPT4gXCJyZ2JhKFwiICtcbiAgICAgICAgcmdiVW5pdC50cmFuc2Zvcm0ocmVkKSArXG4gICAgICAgIFwiLCBcIiArXG4gICAgICAgIHJnYlVuaXQudHJhbnNmb3JtKGdyZWVuKSArXG4gICAgICAgIFwiLCBcIiArXG4gICAgICAgIHJnYlVuaXQudHJhbnNmb3JtKGJsdWUpICtcbiAgICAgICAgXCIsIFwiICtcbiAgICAgICAgc2FuaXRpemUoYWxwaGEudHJhbnNmb3JtKGFscGhhJDEpKSArXG4gICAgICAgIFwiKVwiLFxufTtcblxuZXhwb3J0IHsgcmdiVW5pdCwgcmdiYSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/rgba.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/utils.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/color/utils.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isColorString: function() { return /* binding */ isColorString; },\n/* harmony export */   splitColor: function() { return /* binding */ splitColor; }\n/* harmony export */ });\n/* harmony import */ var _utils_float_regex_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/float-regex.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/float-regex.mjs\");\n/* harmony import */ var _utils_is_nullish_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/is-nullish.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/is-nullish.mjs\");\n/* harmony import */ var _utils_single_color_regex_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/single-color-regex.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/single-color-regex.mjs\");\n\n\n\n\n/**\n * Returns true if the provided string is a color, ie rgba(0,0,0,0) or #000,\n * but false if a number or multiple colors\n */\nconst isColorString = (type, testProp) => (v) => {\n    return Boolean((typeof v === \"string\" &&\n        _utils_single_color_regex_mjs__WEBPACK_IMPORTED_MODULE_0__.singleColorRegex.test(v) &&\n        v.startsWith(type)) ||\n        (testProp &&\n            !(0,_utils_is_nullish_mjs__WEBPACK_IMPORTED_MODULE_1__.isNullish)(v) &&\n            Object.prototype.hasOwnProperty.call(v, testProp)));\n};\nconst splitColor = (aName, bName, cName) => (v) => {\n    if (typeof v !== \"string\")\n        return v;\n    const [a, b, c, alpha] = v.match(_utils_float_regex_mjs__WEBPACK_IMPORTED_MODULE_2__.floatRegex);\n    return {\n        [aName]: parseFloat(a),\n        [bName]: parseFloat(b),\n        [cName]: parseFloat(c),\n        alpha: alpha !== undefined ? parseFloat(alpha) : 1,\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29sb3IvdXRpbHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXNEO0FBQ0Y7QUFDZTs7QUFFbkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSwyRUFBZ0I7QUFDeEI7QUFDQTtBQUNBLGFBQWEsZ0VBQVM7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyw4REFBVTtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFcUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS90eXBlcy9jb2xvci91dGlscy5tanM/NGViZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmbG9hdFJlZ2V4IH0gZnJvbSAnLi4vdXRpbHMvZmxvYXQtcmVnZXgubWpzJztcbmltcG9ydCB7IGlzTnVsbGlzaCB9IGZyb20gJy4uL3V0aWxzL2lzLW51bGxpc2gubWpzJztcbmltcG9ydCB7IHNpbmdsZUNvbG9yUmVnZXggfSBmcm9tICcuLi91dGlscy9zaW5nbGUtY29sb3ItcmVnZXgubWpzJztcblxuLyoqXG4gKiBSZXR1cm5zIHRydWUgaWYgdGhlIHByb3ZpZGVkIHN0cmluZyBpcyBhIGNvbG9yLCBpZSByZ2JhKDAsMCwwLDApIG9yICMwMDAsXG4gKiBidXQgZmFsc2UgaWYgYSBudW1iZXIgb3IgbXVsdGlwbGUgY29sb3JzXG4gKi9cbmNvbnN0IGlzQ29sb3JTdHJpbmcgPSAodHlwZSwgdGVzdFByb3ApID0+ICh2KSA9PiB7XG4gICAgcmV0dXJuIEJvb2xlYW4oKHR5cGVvZiB2ID09PSBcInN0cmluZ1wiICYmXG4gICAgICAgIHNpbmdsZUNvbG9yUmVnZXgudGVzdCh2KSAmJlxuICAgICAgICB2LnN0YXJ0c1dpdGgodHlwZSkpIHx8XG4gICAgICAgICh0ZXN0UHJvcCAmJlxuICAgICAgICAgICAgIWlzTnVsbGlzaCh2KSAmJlxuICAgICAgICAgICAgT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHYsIHRlc3RQcm9wKSkpO1xufTtcbmNvbnN0IHNwbGl0Q29sb3IgPSAoYU5hbWUsIGJOYW1lLCBjTmFtZSkgPT4gKHYpID0+IHtcbiAgICBpZiAodHlwZW9mIHYgIT09IFwic3RyaW5nXCIpXG4gICAgICAgIHJldHVybiB2O1xuICAgIGNvbnN0IFthLCBiLCBjLCBhbHBoYV0gPSB2Lm1hdGNoKGZsb2F0UmVnZXgpO1xuICAgIHJldHVybiB7XG4gICAgICAgIFthTmFtZV06IHBhcnNlRmxvYXQoYSksXG4gICAgICAgIFtiTmFtZV06IHBhcnNlRmxvYXQoYiksXG4gICAgICAgIFtjTmFtZV06IHBhcnNlRmxvYXQoYyksXG4gICAgICAgIGFscGhhOiBhbHBoYSAhPT0gdW5kZWZpbmVkID8gcGFyc2VGbG9hdChhbHBoYSkgOiAxLFxuICAgIH07XG59O1xuXG5leHBvcnQgeyBpc0NvbG9yU3RyaW5nLCBzcGxpdENvbG9yIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/utils.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/complex/index.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyseComplexValue: function() { return /* binding */ analyseComplexValue; },\n/* harmony export */   complex: function() { return /* binding */ complex; }\n/* harmony export */ });\n/* harmony import */ var _color_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../color/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/color/index.mjs\");\n/* harmony import */ var _utils_color_regex_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/color-regex.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/color-regex.mjs\");\n/* harmony import */ var _utils_float_regex_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/float-regex.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/float-regex.mjs\");\n/* harmony import */ var _utils_sanitize_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/sanitize.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/sanitize.mjs\");\n\n\n\n\n\nfunction test(v) {\n    var _a, _b;\n    return (isNaN(v) &&\n        typeof v === \"string\" &&\n        (((_a = v.match(_utils_float_regex_mjs__WEBPACK_IMPORTED_MODULE_0__.floatRegex)) === null || _a === void 0 ? void 0 : _a.length) || 0) +\n            (((_b = v.match(_utils_color_regex_mjs__WEBPACK_IMPORTED_MODULE_1__.colorRegex)) === null || _b === void 0 ? void 0 : _b.length) || 0) >\n            0);\n}\nconst NUMBER_TOKEN = \"number\";\nconst COLOR_TOKEN = \"color\";\nconst VAR_TOKEN = \"var\";\nconst VAR_FUNCTION_TOKEN = \"var(\";\nconst SPLIT_TOKEN = \"${}\";\n// this regex consists of the `singleCssVariableRegex|rgbHSLValueRegex|digitRegex`\nconst complexRegex = /var\\s*\\(\\s*--(?:[\\w-]+\\s*|[\\w-]+\\s*,(?:\\s*[^)(\\s]|\\s*\\((?:[^)(]|\\([^)(]*\\))*\\))+\\s*)\\)|#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\)|-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/giu;\nfunction analyseComplexValue(value) {\n    const originalValue = value.toString();\n    const values = [];\n    const indexes = {\n        color: [],\n        number: [],\n        var: [],\n    };\n    const types = [];\n    let i = 0;\n    const tokenised = originalValue.replace(complexRegex, (parsedValue) => {\n        if (_color_index_mjs__WEBPACK_IMPORTED_MODULE_2__.color.test(parsedValue)) {\n            indexes.color.push(i);\n            types.push(COLOR_TOKEN);\n            values.push(_color_index_mjs__WEBPACK_IMPORTED_MODULE_2__.color.parse(parsedValue));\n        }\n        else if (parsedValue.startsWith(VAR_FUNCTION_TOKEN)) {\n            indexes.var.push(i);\n            types.push(VAR_TOKEN);\n            values.push(parsedValue);\n        }\n        else {\n            indexes.number.push(i);\n            types.push(NUMBER_TOKEN);\n            values.push(parseFloat(parsedValue));\n        }\n        ++i;\n        return SPLIT_TOKEN;\n    });\n    const split = tokenised.split(SPLIT_TOKEN);\n    return { values, split, indexes, types };\n}\nfunction parseComplexValue(v) {\n    return analyseComplexValue(v).values;\n}\nfunction createTransformer(source) {\n    const { split, types } = analyseComplexValue(source);\n    const numSections = split.length;\n    return (v) => {\n        let output = \"\";\n        for (let i = 0; i < numSections; i++) {\n            output += split[i];\n            if (v[i] !== undefined) {\n                const type = types[i];\n                if (type === NUMBER_TOKEN) {\n                    output += (0,_utils_sanitize_mjs__WEBPACK_IMPORTED_MODULE_3__.sanitize)(v[i]);\n                }\n                else if (type === COLOR_TOKEN) {\n                    output += _color_index_mjs__WEBPACK_IMPORTED_MODULE_2__.color.transform(v[i]);\n                }\n                else {\n                    output += v[i];\n                }\n            }\n        }\n        return output;\n    };\n}\nconst convertNumbersToZero = (v) => typeof v === \"number\" ? 0 : v;\nfunction getAnimatableNone(v) {\n    const parsed = parseComplexValue(v);\n    const transformer = createTransformer(v);\n    return transformer(parsed.map(convertNumbersToZero));\n}\nconst complex = {\n    test,\n    parse: parseComplexValue,\n    createTransformer,\n    getAnimatableNone,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29tcGxleC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTJDO0FBQ1c7QUFDQTtBQUNMOztBQUVqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qiw4REFBVTtBQUNsQyw0QkFBNEIsOERBQVU7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCO0FBQ0Esc0hBQXNILElBQUkscUNBQXFDLEVBQUU7QUFDaks7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksbURBQUs7QUFDakI7QUFDQTtBQUNBLHdCQUF3QixtREFBSztBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxlQUFlO0FBQzNCO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixpQkFBaUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsNkRBQVE7QUFDdEM7QUFDQTtBQUNBLDhCQUE4QixtREFBSztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvY29tcGxleC9pbmRleC5tanM/MTE1NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb2xvciB9IGZyb20gJy4uL2NvbG9yL2luZGV4Lm1qcyc7XG5pbXBvcnQgeyBjb2xvclJlZ2V4IH0gZnJvbSAnLi4vdXRpbHMvY29sb3ItcmVnZXgubWpzJztcbmltcG9ydCB7IGZsb2F0UmVnZXggfSBmcm9tICcuLi91dGlscy9mbG9hdC1yZWdleC5tanMnO1xuaW1wb3J0IHsgc2FuaXRpemUgfSBmcm9tICcuLi91dGlscy9zYW5pdGl6ZS5tanMnO1xuXG5mdW5jdGlvbiB0ZXN0KHYpIHtcbiAgICB2YXIgX2EsIF9iO1xuICAgIHJldHVybiAoaXNOYU4odikgJiZcbiAgICAgICAgdHlwZW9mIHYgPT09IFwic3RyaW5nXCIgJiZcbiAgICAgICAgKCgoX2EgPSB2Lm1hdGNoKGZsb2F0UmVnZXgpKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EubGVuZ3RoKSB8fCAwKSArXG4gICAgICAgICAgICAoKChfYiA9IHYubWF0Y2goY29sb3JSZWdleCkpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5sZW5ndGgpIHx8IDApID5cbiAgICAgICAgICAgIDApO1xufVxuY29uc3QgTlVNQkVSX1RPS0VOID0gXCJudW1iZXJcIjtcbmNvbnN0IENPTE9SX1RPS0VOID0gXCJjb2xvclwiO1xuY29uc3QgVkFSX1RPS0VOID0gXCJ2YXJcIjtcbmNvbnN0IFZBUl9GVU5DVElPTl9UT0tFTiA9IFwidmFyKFwiO1xuY29uc3QgU1BMSVRfVE9LRU4gPSBcIiR7fVwiO1xuLy8gdGhpcyByZWdleCBjb25zaXN0cyBvZiB0aGUgYHNpbmdsZUNzc1ZhcmlhYmxlUmVnZXh8cmdiSFNMVmFsdWVSZWdleHxkaWdpdFJlZ2V4YFxuY29uc3QgY29tcGxleFJlZ2V4ID0gL3ZhclxccypcXChcXHMqLS0oPzpbXFx3LV0rXFxzKnxbXFx3LV0rXFxzKiwoPzpcXHMqW14pKFxcc118XFxzKlxcKCg/OlteKShdfFxcKFteKShdKlxcKSkqXFwpKStcXHMqKVxcKXwjW1xcZGEtZl17Myw4fXwoPzpyZ2J8aHNsKWE/XFwoKD86LT9bXFxkLl0rJT9bLFxcc10rKXsyfS0/W1xcZC5dKyU/XFxzKig/OlssL11cXHMqKT8oPzpcXGJcXGQrKD86XFwuXFxkKyk/fFxcLlxcZCspPyU/XFwpfC0/KD86XFxkKyg/OlxcLlxcZCspP3xcXC5cXGQrKS9naXU7XG5mdW5jdGlvbiBhbmFseXNlQ29tcGxleFZhbHVlKHZhbHVlKSB7XG4gICAgY29uc3Qgb3JpZ2luYWxWYWx1ZSA9IHZhbHVlLnRvU3RyaW5nKCk7XG4gICAgY29uc3QgdmFsdWVzID0gW107XG4gICAgY29uc3QgaW5kZXhlcyA9IHtcbiAgICAgICAgY29sb3I6IFtdLFxuICAgICAgICBudW1iZXI6IFtdLFxuICAgICAgICB2YXI6IFtdLFxuICAgIH07XG4gICAgY29uc3QgdHlwZXMgPSBbXTtcbiAgICBsZXQgaSA9IDA7XG4gICAgY29uc3QgdG9rZW5pc2VkID0gb3JpZ2luYWxWYWx1ZS5yZXBsYWNlKGNvbXBsZXhSZWdleCwgKHBhcnNlZFZhbHVlKSA9PiB7XG4gICAgICAgIGlmIChjb2xvci50ZXN0KHBhcnNlZFZhbHVlKSkge1xuICAgICAgICAgICAgaW5kZXhlcy5jb2xvci5wdXNoKGkpO1xuICAgICAgICAgICAgdHlwZXMucHVzaChDT0xPUl9UT0tFTik7XG4gICAgICAgICAgICB2YWx1ZXMucHVzaChjb2xvci5wYXJzZShwYXJzZWRWYWx1ZSkpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHBhcnNlZFZhbHVlLnN0YXJ0c1dpdGgoVkFSX0ZVTkNUSU9OX1RPS0VOKSkge1xuICAgICAgICAgICAgaW5kZXhlcy52YXIucHVzaChpKTtcbiAgICAgICAgICAgIHR5cGVzLnB1c2goVkFSX1RPS0VOKTtcbiAgICAgICAgICAgIHZhbHVlcy5wdXNoKHBhcnNlZFZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGluZGV4ZXMubnVtYmVyLnB1c2goaSk7XG4gICAgICAgICAgICB0eXBlcy5wdXNoKE5VTUJFUl9UT0tFTik7XG4gICAgICAgICAgICB2YWx1ZXMucHVzaChwYXJzZUZsb2F0KHBhcnNlZFZhbHVlKSk7XG4gICAgICAgIH1cbiAgICAgICAgKytpO1xuICAgICAgICByZXR1cm4gU1BMSVRfVE9LRU47XG4gICAgfSk7XG4gICAgY29uc3Qgc3BsaXQgPSB0b2tlbmlzZWQuc3BsaXQoU1BMSVRfVE9LRU4pO1xuICAgIHJldHVybiB7IHZhbHVlcywgc3BsaXQsIGluZGV4ZXMsIHR5cGVzIH07XG59XG5mdW5jdGlvbiBwYXJzZUNvbXBsZXhWYWx1ZSh2KSB7XG4gICAgcmV0dXJuIGFuYWx5c2VDb21wbGV4VmFsdWUodikudmFsdWVzO1xufVxuZnVuY3Rpb24gY3JlYXRlVHJhbnNmb3JtZXIoc291cmNlKSB7XG4gICAgY29uc3QgeyBzcGxpdCwgdHlwZXMgfSA9IGFuYWx5c2VDb21wbGV4VmFsdWUoc291cmNlKTtcbiAgICBjb25zdCBudW1TZWN0aW9ucyA9IHNwbGl0Lmxlbmd0aDtcbiAgICByZXR1cm4gKHYpID0+IHtcbiAgICAgICAgbGV0IG91dHB1dCA9IFwiXCI7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbnVtU2VjdGlvbnM7IGkrKykge1xuICAgICAgICAgICAgb3V0cHV0ICs9IHNwbGl0W2ldO1xuICAgICAgICAgICAgaWYgKHZbaV0gIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHR5cGUgPSB0eXBlc1tpXTtcbiAgICAgICAgICAgICAgICBpZiAodHlwZSA9PT0gTlVNQkVSX1RPS0VOKSB7XG4gICAgICAgICAgICAgICAgICAgIG91dHB1dCArPSBzYW5pdGl6ZSh2W2ldKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAodHlwZSA9PT0gQ09MT1JfVE9LRU4pIHtcbiAgICAgICAgICAgICAgICAgICAgb3V0cHV0ICs9IGNvbG9yLnRyYW5zZm9ybSh2W2ldKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIG91dHB1dCArPSB2W2ldO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gb3V0cHV0O1xuICAgIH07XG59XG5jb25zdCBjb252ZXJ0TnVtYmVyc1RvWmVybyA9ICh2KSA9PiB0eXBlb2YgdiA9PT0gXCJudW1iZXJcIiA/IDAgOiB2O1xuZnVuY3Rpb24gZ2V0QW5pbWF0YWJsZU5vbmUodikge1xuICAgIGNvbnN0IHBhcnNlZCA9IHBhcnNlQ29tcGxleFZhbHVlKHYpO1xuICAgIGNvbnN0IHRyYW5zZm9ybWVyID0gY3JlYXRlVHJhbnNmb3JtZXIodik7XG4gICAgcmV0dXJuIHRyYW5zZm9ybWVyKHBhcnNlZC5tYXAoY29udmVydE51bWJlcnNUb1plcm8pKTtcbn1cbmNvbnN0IGNvbXBsZXggPSB7XG4gICAgdGVzdCxcbiAgICBwYXJzZTogcGFyc2VDb21wbGV4VmFsdWUsXG4gICAgY3JlYXRlVHJhbnNmb3JtZXIsXG4gICAgZ2V0QW5pbWF0YWJsZU5vbmUsXG59O1xuXG5leHBvcnQgeyBhbmFseXNlQ29tcGxleFZhbHVlLCBjb21wbGV4IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/complex/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alpha: function() { return /* binding */ alpha; },\n/* harmony export */   number: function() { return /* binding */ number; },\n/* harmony export */   scale: function() { return /* binding */ scale; }\n/* harmony export */ });\n/* harmony import */ var _utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/clamp.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\");\n\n\nconst number = {\n    test: (v) => typeof v === \"number\",\n    parse: parseFloat,\n    transform: (v) => v,\n};\nconst alpha = {\n    ...number,\n    transform: (v) => (0,_utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_0__.clamp)(0, 1, v),\n};\nconst scale = {\n    ...number,\n    default: 1,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvbnVtYmVycy9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpRDs7QUFFakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsdURBQUs7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS90eXBlcy9udW1iZXJzL2luZGV4Lm1qcz8yYTdlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsYW1wIH0gZnJvbSAnLi4vLi4vLi4vdXRpbHMvY2xhbXAubWpzJztcblxuY29uc3QgbnVtYmVyID0ge1xuICAgIHRlc3Q6ICh2KSA9PiB0eXBlb2YgdiA9PT0gXCJudW1iZXJcIixcbiAgICBwYXJzZTogcGFyc2VGbG9hdCxcbiAgICB0cmFuc2Zvcm06ICh2KSA9PiB2LFxufTtcbmNvbnN0IGFscGhhID0ge1xuICAgIC4uLm51bWJlcixcbiAgICB0cmFuc2Zvcm06ICh2KSA9PiBjbGFtcCgwLCAxLCB2KSxcbn07XG5jb25zdCBzY2FsZSA9IHtcbiAgICAuLi5udW1iZXIsXG4gICAgZGVmYXVsdDogMSxcbn07XG5cbmV4cG9ydCB7IGFscGhhLCBudW1iZXIsIHNjYWxlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   degrees: function() { return /* binding */ degrees; },\n/* harmony export */   percent: function() { return /* binding */ percent; },\n/* harmony export */   progressPercentage: function() { return /* binding */ progressPercentage; },\n/* harmony export */   px: function() { return /* binding */ px; },\n/* harmony export */   vh: function() { return /* binding */ vh; },\n/* harmony export */   vw: function() { return /* binding */ vw; }\n/* harmony export */ });\nconst createUnitType = (unit) => ({\n    test: (v) => typeof v === \"string\" && v.endsWith(unit) && v.split(\" \").length === 1,\n    parse: parseFloat,\n    transform: (v) => `${v}${unit}`,\n});\nconst degrees = /*@__PURE__*/ createUnitType(\"deg\");\nconst percent = /*@__PURE__*/ createUnitType(\"%\");\nconst px = /*@__PURE__*/ createUnitType(\"px\");\nconst vh = /*@__PURE__*/ createUnitType(\"vh\");\nconst vw = /*@__PURE__*/ createUnitType(\"vw\");\nconst progressPercentage = {\n    ...percent,\n    parse: (v) => percent.parse(v) / 100,\n    transform: (v) => percent.transform(v * 100),\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvbnVtYmVycy91bml0cy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLEVBQUUsRUFBRSxLQUFLO0FBQ2xDLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFNEQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS90eXBlcy9udW1iZXJzL3VuaXRzLm1qcz80ZDQzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGNyZWF0ZVVuaXRUeXBlID0gKHVuaXQpID0+ICh7XG4gICAgdGVzdDogKHYpID0+IHR5cGVvZiB2ID09PSBcInN0cmluZ1wiICYmIHYuZW5kc1dpdGgodW5pdCkgJiYgdi5zcGxpdChcIiBcIikubGVuZ3RoID09PSAxLFxuICAgIHBhcnNlOiBwYXJzZUZsb2F0LFxuICAgIHRyYW5zZm9ybTogKHYpID0+IGAke3Z9JHt1bml0fWAsXG59KTtcbmNvbnN0IGRlZ3JlZXMgPSAvKkBfX1BVUkVfXyovIGNyZWF0ZVVuaXRUeXBlKFwiZGVnXCIpO1xuY29uc3QgcGVyY2VudCA9IC8qQF9fUFVSRV9fKi8gY3JlYXRlVW5pdFR5cGUoXCIlXCIpO1xuY29uc3QgcHggPSAvKkBfX1BVUkVfXyovIGNyZWF0ZVVuaXRUeXBlKFwicHhcIik7XG5jb25zdCB2aCA9IC8qQF9fUFVSRV9fKi8gY3JlYXRlVW5pdFR5cGUoXCJ2aFwiKTtcbmNvbnN0IHZ3ID0gLypAX19QVVJFX18qLyBjcmVhdGVVbml0VHlwZShcInZ3XCIpO1xuY29uc3QgcHJvZ3Jlc3NQZXJjZW50YWdlID0ge1xuICAgIC4uLnBlcmNlbnQsXG4gICAgcGFyc2U6ICh2KSA9PiBwZXJjZW50LnBhcnNlKHYpIC8gMTAwLFxuICAgIHRyYW5zZm9ybTogKHYpID0+IHBlcmNlbnQudHJhbnNmb3JtKHYgKiAxMDApLFxufTtcblxuZXhwb3J0IHsgZGVncmVlcywgcGVyY2VudCwgcHJvZ3Jlc3NQZXJjZW50YWdlLCBweCwgdmgsIHZ3IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/numbers/units.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/color-regex.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/utils/color-regex.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   colorRegex: function() { return /* binding */ colorRegex; }\n/* harmony export */ });\nconst colorRegex = /(?:#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\))/giu;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvdXRpbHMvY29sb3ItcmVnZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxnQ0FBZ0MsSUFBSSxxQ0FBcUMsRUFBRTs7QUFFckQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS90eXBlcy91dGlscy9jb2xvci1yZWdleC5tanM/ZTY3MyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjb2xvclJlZ2V4ID0gLyg/OiNbXFxkYS1mXXszLDh9fCg/OnJnYnxoc2wpYT9cXCgoPzotP1tcXGQuXSslP1ssXFxzXSspezJ9LT9bXFxkLl0rJT9cXHMqKD86WywvXVxccyopPyg/OlxcYlxcZCsoPzpcXC5cXGQrKT98XFwuXFxkKyk/JT9cXCkpL2dpdTtcblxuZXhwb3J0IHsgY29sb3JSZWdleCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/color-regex.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/float-regex.mjs":
/*!******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/utils/float-regex.mjs ***!
  \******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   floatRegex: function() { return /* binding */ floatRegex; }\n/* harmony export */ });\nconst floatRegex = /-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/gu;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvdXRpbHMvZmxvYXQtcmVnZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS90eXBlcy91dGlscy9mbG9hdC1yZWdleC5tanM/YjlhMyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBmbG9hdFJlZ2V4ID0gLy0/KD86XFxkKyg/OlxcLlxcZCspP3xcXC5cXGQrKS9ndTtcblxuZXhwb3J0IHsgZmxvYXRSZWdleCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/float-regex.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/is-nullish.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/utils/is-nullish.mjs ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNullish: function() { return /* binding */ isNullish; }\n/* harmony export */ });\nfunction isNullish(v) {\n    return v == null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvdXRpbHMvaXMtbnVsbGlzaC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy92YWx1ZS90eXBlcy91dGlscy9pcy1udWxsaXNoLm1qcz81NTYyIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGlzTnVsbGlzaCh2KSB7XG4gICAgcmV0dXJuIHYgPT0gbnVsbDtcbn1cblxuZXhwb3J0IHsgaXNOdWxsaXNoIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/is-nullish.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/sanitize.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/utils/sanitize.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sanitize: function() { return /* binding */ sanitize; }\n/* harmony export */ });\n// If this number is a decimal, make it just five decimal places\n// to avoid exponents\nconst sanitize = (v) => Math.round(v * 100000) / 100000;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvdXRpbHMvc2FuaXRpemUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7O0FBRW9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvdXRpbHMvc2FuaXRpemUubWpzPzQ0YjEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gSWYgdGhpcyBudW1iZXIgaXMgYSBkZWNpbWFsLCBtYWtlIGl0IGp1c3QgZml2ZSBkZWNpbWFsIHBsYWNlc1xuLy8gdG8gYXZvaWQgZXhwb25lbnRzXG5jb25zdCBzYW5pdGl6ZSA9ICh2KSA9PiBNYXRoLnJvdW5kKHYgKiAxMDAwMDApIC8gMTAwMDAwO1xuXG5leHBvcnQgeyBzYW5pdGl6ZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/sanitize.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/single-color-regex.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/types/utils/single-color-regex.mjs ***!
  \*************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   singleColorRegex: function() { return /* binding */ singleColorRegex; }\n/* harmony export */ });\nconst singleColorRegex = /^(?:#[\\da-f]{3,8}|(?:rgb|hsl)a?\\((?:-?[\\d.]+%?[,\\s]+){2}-?[\\d.]+%?\\s*(?:[,/]\\s*)?(?:\\b\\d+(?:\\.\\d+)?|\\.\\d+)?%?\\))$/iu;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvdXRpbHMvc2luZ2xlLWNvbG9yLXJlZ2V4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsdUNBQXVDLElBQUkscUNBQXFDLEVBQUU7O0FBRXREIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdHlwZXMvdXRpbHMvc2luZ2xlLWNvbG9yLXJlZ2V4Lm1qcz80YTI3Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHNpbmdsZUNvbG9yUmVnZXggPSAvXig/OiNbXFxkYS1mXXszLDh9fCg/OnJnYnxoc2wpYT9cXCgoPzotP1tcXGQuXSslP1ssXFxzXSspezJ9LT9bXFxkLl0rJT9cXHMqKD86WywvXVxccyopPyg/OlxcYlxcZCsoPzpcXC5cXGQrKT98XFwuXFxkKyk/JT9cXCkpJC9pdTtcblxuZXhwb3J0IHsgc2luZ2xlQ29sb3JSZWdleCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/types/utils/single-color-regex.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-scroll.mjs ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useScroll: function() { return /* binding */ useScroll; }\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/index.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _render_dom_scroll_index_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../render/dom/scroll/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n\n\n\n\n\n\n\nfunction refWarning(name, ref) {\n    (0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.warning)(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollY: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollXProgress: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollYProgress: (0,_index_mjs__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__.useConstant)(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__.useIsomorphicLayoutEffect\n        : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return (0,_render_dom_scroll_index_mjs__WEBPACK_IMPORTED_MODULE_5__.scroll)((_progress, { x, y }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: (container === null || container === void 0 ? void 0 : container.current) || undefined,\n            target: (target === null || target === void 0 ? void 0 : target.current) || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/controls/BaseGroup.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/controls/BaseGroup.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BaseGroupPlaybackControls: function() { return /* binding */ BaseGroupPlaybackControls; }\n/* harmony export */ });\n/* harmony import */ var _utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/supports/scroll-timeline.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\");\n\n\nclass BaseGroupPlaybackControls {\n    constructor(animations) {\n        // Bound to accomodate common `return animation.stop` pattern\n        this.stop = () => this.runAll(\"stop\");\n        this.animations = animations.filter(Boolean);\n    }\n    get finished() {\n        // Support for new finished Promise and legacy thennable API\n        return Promise.all(this.animations.map((animation) => \"finished\" in animation ? animation.finished : animation));\n    }\n    /**\n     * TODO: Filter out cancelled or stopped animations before returning\n     */\n    getAll(propName) {\n        return this.animations[0][propName];\n    }\n    setAll(propName, newValue) {\n        for (let i = 0; i < this.animations.length; i++) {\n            this.animations[i][propName] = newValue;\n        }\n    }\n    attachTimeline(timeline, fallback) {\n        const subscriptions = this.animations.map((animation) => {\n            if ((0,_utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__.supportsScrollTimeline)() && animation.attachTimeline) {\n                return animation.attachTimeline(timeline);\n            }\n            else if (typeof fallback === \"function\") {\n                return fallback(animation);\n            }\n        });\n        return () => {\n            subscriptions.forEach((cancel, i) => {\n                cancel && cancel();\n                this.animations[i].stop();\n            });\n        };\n    }\n    get time() {\n        return this.getAll(\"time\");\n    }\n    set time(time) {\n        this.setAll(\"time\", time);\n    }\n    get speed() {\n        return this.getAll(\"speed\");\n    }\n    set speed(speed) {\n        this.setAll(\"speed\", speed);\n    }\n    get startTime() {\n        return this.getAll(\"startTime\");\n    }\n    get duration() {\n        let max = 0;\n        for (let i = 0; i < this.animations.length; i++) {\n            max = Math.max(max, this.animations[i].duration);\n        }\n        return max;\n    }\n    runAll(methodName) {\n        this.animations.forEach((controls) => controls[methodName]());\n    }\n    flatten() {\n        this.runAll(\"flatten\");\n    }\n    play() {\n        this.runAll(\"play\");\n    }\n    pause() {\n        this.runAll(\"pause\");\n    }\n    cancel() {\n        this.runAll(\"cancel\");\n    }\n    complete() {\n        this.runAll(\"complete\");\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/controls/BaseGroup.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/controls/Group.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/controls/Group.mjs ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroupPlaybackControls: function() { return /* binding */ GroupPlaybackControls; }\n/* harmony export */ });\n/* harmony import */ var _BaseGroup_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BaseGroup.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/controls/BaseGroup.mjs\");\n\n\n/**\n * TODO: This is a temporary class to support the legacy\n * thennable API\n */\nclass GroupPlaybackControls extends _BaseGroup_mjs__WEBPACK_IMPORTED_MODULE_0__.BaseGroupPlaybackControls {\n    then(onResolve, onReject) {\n        return Promise.all(this.animations).then(onResolve).catch(onReject);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL2NvbnRyb2xzL0dyb3VwLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0RDs7QUFFNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MscUVBQXlCO0FBQzdEO0FBQ0E7QUFDQTtBQUNBOztBQUVpQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9jb250cm9scy9Hcm91cC5tanM/ZGRlNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCYXNlR3JvdXBQbGF5YmFja0NvbnRyb2xzIH0gZnJvbSAnLi9CYXNlR3JvdXAubWpzJztcblxuLyoqXG4gKiBUT0RPOiBUaGlzIGlzIGEgdGVtcG9yYXJ5IGNsYXNzIHRvIHN1cHBvcnQgdGhlIGxlZ2FjeVxuICogdGhlbm5hYmxlIEFQSVxuICovXG5jbGFzcyBHcm91cFBsYXliYWNrQ29udHJvbHMgZXh0ZW5kcyBCYXNlR3JvdXBQbGF5YmFja0NvbnRyb2xzIHtcbiAgICB0aGVuKG9uUmVzb2x2ZSwgb25SZWplY3QpIHtcbiAgICAgICAgcmV0dXJuIFByb21pc2UuYWxsKHRoaXMuYW5pbWF0aW9ucykudGhlbihvblJlc29sdmUpLmNhdGNoKG9uUmVqZWN0KTtcbiAgICB9XG59XG5cbmV4cG9ydCB7IEdyb3VwUGxheWJhY2tDb250cm9scyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/controls/Group.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcGeneratorDuration: function() { return /* binding */ calcGeneratorDuration; },\n/* harmony export */   maxGeneratorDuration: function() { return /* binding */ maxGeneratorDuration; }\n/* harmony export */ });\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxGeneratorDuration = 20000;\nfunction calcGeneratorDuration(generator) {\n    let duration = 0;\n    const timeStep = 50;\n    let state = generator.next(duration);\n    while (!state.done && duration < maxGeneratorDuration) {\n        duration += timeStep;\n        state = generator.next(duration);\n    }\n    return duration >= maxGeneratorDuration ? Infinity : duration;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL2dlbmVyYXRvcnMvdXRpbHMvY2FsYy1kdXJhdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL2dlbmVyYXRvcnMvdXRpbHMvY2FsYy1kdXJhdGlvbi5tanM/NjI0NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEltcGxlbWVudCBhIHByYWN0aWNhbCBtYXggZHVyYXRpb24gZm9yIGtleWZyYW1lIGdlbmVyYXRpb25cbiAqIHRvIHByZXZlbnQgaW5maW5pdGUgbG9vcHNcbiAqL1xuY29uc3QgbWF4R2VuZXJhdG9yRHVyYXRpb24gPSAyMDAwMDtcbmZ1bmN0aW9uIGNhbGNHZW5lcmF0b3JEdXJhdGlvbihnZW5lcmF0b3IpIHtcbiAgICBsZXQgZHVyYXRpb24gPSAwO1xuICAgIGNvbnN0IHRpbWVTdGVwID0gNTA7XG4gICAgbGV0IHN0YXRlID0gZ2VuZXJhdG9yLm5leHQoZHVyYXRpb24pO1xuICAgIHdoaWxlICghc3RhdGUuZG9uZSAmJiBkdXJhdGlvbiA8IG1heEdlbmVyYXRvckR1cmF0aW9uKSB7XG4gICAgICAgIGR1cmF0aW9uICs9IHRpbWVTdGVwO1xuICAgICAgICBzdGF0ZSA9IGdlbmVyYXRvci5uZXh0KGR1cmF0aW9uKTtcbiAgICB9XG4gICAgcmV0dXJuIGR1cmF0aW9uID49IG1heEdlbmVyYXRvckR1cmF0aW9uID8gSW5maW5pdHkgOiBkdXJhdGlvbjtcbn1cblxuZXhwb3J0IHsgY2FsY0dlbmVyYXRvckR1cmF0aW9uLCBtYXhHZW5lcmF0b3JEdXJhdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs":
/*!************************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs ***!
  \************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createGeneratorEasing: function() { return /* binding */ createGeneratorEasing; }\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _calc_duration_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./calc-duration.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs\");\n\n\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100, createGenerator) {\n    const generator = createGenerator({ ...options, keyframes: [0, scale] });\n    const duration = Math.min((0,_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_1__.calcGeneratorDuration)(generator), _calc_duration_mjs__WEBPACK_IMPORTED_MODULE_1__.maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => {\n            return generator.next(duration * progress).value / scale;\n        },\n        duration: (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds)(duration),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL2dlbmVyYXRvcnMvdXRpbHMvY3JlYXRlLWdlbmVyYXRvci1lYXNpbmcubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUM2Qjs7QUFFbEY7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsbUNBQW1DO0FBQzNFLDhCQUE4Qix5RUFBcUIsYUFBYSxvRUFBb0I7QUFDcEY7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Qsa0JBQWtCLG1FQUFxQjtBQUN2QztBQUNBOztBQUVpQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9nZW5lcmF0b3JzL3V0aWxzL2NyZWF0ZS1nZW5lcmF0b3ItZWFzaW5nLm1qcz8zMmU2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1pbGxpc2Vjb25kc1RvU2Vjb25kcyB9IGZyb20gJ21vdGlvbi11dGlscyc7XG5pbXBvcnQgeyBjYWxjR2VuZXJhdG9yRHVyYXRpb24sIG1heEdlbmVyYXRvckR1cmF0aW9uIH0gZnJvbSAnLi9jYWxjLWR1cmF0aW9uLm1qcyc7XG5cbi8qKlxuICogQ3JlYXRlIGEgcHJvZ3Jlc3MgPT4gcHJvZ3Jlc3MgZWFzaW5nIGZ1bmN0aW9uIGZyb20gYSBnZW5lcmF0b3IuXG4gKi9cbmZ1bmN0aW9uIGNyZWF0ZUdlbmVyYXRvckVhc2luZyhvcHRpb25zLCBzY2FsZSA9IDEwMCwgY3JlYXRlR2VuZXJhdG9yKSB7XG4gICAgY29uc3QgZ2VuZXJhdG9yID0gY3JlYXRlR2VuZXJhdG9yKHsgLi4ub3B0aW9ucywga2V5ZnJhbWVzOiBbMCwgc2NhbGVdIH0pO1xuICAgIGNvbnN0IGR1cmF0aW9uID0gTWF0aC5taW4oY2FsY0dlbmVyYXRvckR1cmF0aW9uKGdlbmVyYXRvciksIG1heEdlbmVyYXRvckR1cmF0aW9uKTtcbiAgICByZXR1cm4ge1xuICAgICAgICB0eXBlOiBcImtleWZyYW1lc1wiLFxuICAgICAgICBlYXNlOiAocHJvZ3Jlc3MpID0+IHtcbiAgICAgICAgICAgIHJldHVybiBnZW5lcmF0b3IubmV4dChkdXJhdGlvbiAqIHByb2dyZXNzKS52YWx1ZSAvIHNjYWxlO1xuICAgICAgICB9LFxuICAgICAgICBkdXJhdGlvbjogbWlsbGlzZWNvbmRzVG9TZWNvbmRzKGR1cmF0aW9uKSxcbiAgICB9O1xufVxuXG5leHBvcnQgeyBjcmVhdGVHZW5lcmF0b3JFYXNpbmcgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs ***!
  \*************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isGenerator: function() { return /* binding */ isGenerator; }\n/* harmony export */ });\nfunction isGenerator(type) {\n    return typeof type === \"function\";\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL2dlbmVyYXRvcnMvdXRpbHMvaXMtZ2VuZXJhdG9yLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBOztBQUV1QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi9nZW5lcmF0b3JzL3V0aWxzL2lzLWdlbmVyYXRvci5tanM/NTY4NCJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBpc0dlbmVyYXRvcih0eXBlKSB7XG4gICAgcmV0dXJuIHR5cGVvZiB0eXBlID09PSBcImZ1bmN0aW9uXCI7XG59XG5cbmV4cG9ydCB7IGlzR2VuZXJhdG9yIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getValueTransition: function() { return /* binding */ getValueTransition; }\n/* harmony export */ });\nfunction getValueTransition(transition, key) {\n    return transition\n        ? transition[key] ||\n            transition[\"default\"] ||\n            transition\n        : undefined;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL3V0aWxzL2dldC12YWx1ZS10cmFuc2l0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL3V0aWxzL2dldC12YWx1ZS10cmFuc2l0aW9uLm1qcz8wMWY3Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGdldFZhbHVlVHJhbnNpdGlvbih0cmFuc2l0aW9uLCBrZXkpIHtcbiAgICByZXR1cm4gdHJhbnNpdGlvblxuICAgICAgICA/IHRyYW5zaXRpb25ba2V5XSB8fFxuICAgICAgICAgICAgdHJhbnNpdGlvbltcImRlZmF1bHRcIl0gfHxcbiAgICAgICAgICAgIHRyYW5zaXRpb25cbiAgICAgICAgOiB1bmRlZmluZWQ7XG59XG5cbmV4cG9ydCB7IGdldFZhbHVlVHJhbnNpdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs ***!
  \*************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NativeAnimationControls: function() { return /* binding */ NativeAnimationControls; }\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _utils_attach_timeline_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/attach-timeline.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs\");\n\n\n\nclass NativeAnimationControls {\n    constructor(animation) {\n        this.animation = animation;\n    }\n    get duration() {\n        var _a, _b, _c;\n        const durationInMs = ((_b = (_a = this.animation) === null || _a === void 0 ? void 0 : _a.effect) === null || _b === void 0 ? void 0 : _b.getComputedTiming().duration) ||\n            ((_c = this.options) === null || _c === void 0 ? void 0 : _c.duration) ||\n            300;\n        return (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds)(Number(durationInMs));\n    }\n    get time() {\n        var _a;\n        if (this.animation) {\n            return (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds)(((_a = this.animation) === null || _a === void 0 ? void 0 : _a.currentTime) || 0);\n        }\n        return 0;\n    }\n    set time(newTime) {\n        if (this.animation) {\n            this.animation.currentTime = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)(newTime);\n        }\n    }\n    get speed() {\n        return this.animation ? this.animation.playbackRate : 1;\n    }\n    set speed(newSpeed) {\n        if (this.animation) {\n            this.animation.playbackRate = newSpeed;\n        }\n    }\n    get state() {\n        return this.animation ? this.animation.playState : \"finished\";\n    }\n    get startTime() {\n        return this.animation ? this.animation.startTime : null;\n    }\n    get finished() {\n        return this.animation ? this.animation.finished : Promise.resolve();\n    }\n    play() {\n        this.animation && this.animation.play();\n    }\n    pause() {\n        this.animation && this.animation.pause();\n    }\n    stop() {\n        if (!this.animation ||\n            this.state === \"idle\" ||\n            this.state === \"finished\") {\n            return;\n        }\n        if (this.animation.commitStyles) {\n            this.animation.commitStyles();\n        }\n        this.cancel();\n    }\n    flatten() {\n        var _a;\n        if (!this.animation)\n            return;\n        (_a = this.animation.effect) === null || _a === void 0 ? void 0 : _a.updateTiming({ easing: \"linear\" });\n    }\n    attachTimeline(timeline) {\n        if (this.animation)\n            (0,_utils_attach_timeline_mjs__WEBPACK_IMPORTED_MODULE_1__.attachTimeline)(this.animation, timeline);\n        return motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n    }\n    complete() {\n        this.animation && this.animation.finish();\n    }\n    cancel() {\n        try {\n            this.animation && this.animation.cancel();\n        }\n        catch (e) { }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/PseudoAnimation.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/PseudoAnimation.mjs ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PseudoAnimation: function() { return /* binding */ PseudoAnimation; }\n/* harmony export */ });\n/* harmony import */ var _NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NativeAnimationControls.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs\");\n/* harmony import */ var _utils_convert_options_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/convert-options.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/convert-options.mjs\");\n\n\n\nclass PseudoAnimation extends _NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_0__.NativeAnimationControls {\n    constructor(target, pseudoElement, valueName, keyframes, options) {\n        const animationOptions = (0,_utils_convert_options_mjs__WEBPACK_IMPORTED_MODULE_1__.convertMotionOptionsToNative)(valueName, keyframes, options);\n        const animation = target.animate(animationOptions.keyframes, {\n            pseudoElement,\n            ...animationOptions.options,\n        });\n        super(animation);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL3dhYXBpL1BzZXVkb0FuaW1hdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXdFO0FBQ0c7O0FBRTNFLDhCQUE4QixpRkFBdUI7QUFDckQ7QUFDQSxpQ0FBaUMsd0ZBQTRCO0FBQzdEO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7O0FBRTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL3dhYXBpL1BzZXVkb0FuaW1hdGlvbi5tanM/ZGVhNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOYXRpdmVBbmltYXRpb25Db250cm9scyB9IGZyb20gJy4vTmF0aXZlQW5pbWF0aW9uQ29udHJvbHMubWpzJztcbmltcG9ydCB7IGNvbnZlcnRNb3Rpb25PcHRpb25zVG9OYXRpdmUgfSBmcm9tICcuL3V0aWxzL2NvbnZlcnQtb3B0aW9ucy5tanMnO1xuXG5jbGFzcyBQc2V1ZG9BbmltYXRpb24gZXh0ZW5kcyBOYXRpdmVBbmltYXRpb25Db250cm9scyB7XG4gICAgY29uc3RydWN0b3IodGFyZ2V0LCBwc2V1ZG9FbGVtZW50LCB2YWx1ZU5hbWUsIGtleWZyYW1lcywgb3B0aW9ucykge1xuICAgICAgICBjb25zdCBhbmltYXRpb25PcHRpb25zID0gY29udmVydE1vdGlvbk9wdGlvbnNUb05hdGl2ZSh2YWx1ZU5hbWUsIGtleWZyYW1lcywgb3B0aW9ucyk7XG4gICAgICAgIGNvbnN0IGFuaW1hdGlvbiA9IHRhcmdldC5hbmltYXRlKGFuaW1hdGlvbk9wdGlvbnMua2V5ZnJhbWVzLCB7XG4gICAgICAgICAgICBwc2V1ZG9FbGVtZW50LFxuICAgICAgICAgICAgLi4uYW5pbWF0aW9uT3B0aW9ucy5vcHRpb25zLFxuICAgICAgICB9KTtcbiAgICAgICAgc3VwZXIoYW5pbWF0aW9uKTtcbiAgICB9XG59XG5cbmV4cG9ydCB7IFBzZXVkb0FuaW1hdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/PseudoAnimation.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachTimeline: function() { return /* binding */ attachTimeline; }\n/* harmony export */ });\nfunction attachTimeline(animation, timeline) {\n    animation.timeline = timeline;\n    animation.onfinish = null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL3dhYXBpL3V0aWxzL2F0dGFjaC10aW1lbGluZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUUwQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS91dGlscy9hdHRhY2gtdGltZWxpbmUubWpzP2Y2ZmEiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gYXR0YWNoVGltZWxpbmUoYW5pbWF0aW9uLCB0aW1lbGluZSkge1xuICAgIGFuaW1hdGlvbi50aW1lbGluZSA9IHRpbWVsaW5lO1xuICAgIGFuaW1hdGlvbi5vbmZpbmlzaCA9IG51bGw7XG59XG5cbmV4cG9ydCB7IGF0dGFjaFRpbWVsaW5lIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/convert-options.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/convert-options.mjs ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyGeneratorOptions: function() { return /* binding */ applyGeneratorOptions; },\n/* harmony export */   convertMotionOptionsToNative: function() { return /* binding */ convertMotionOptionsToNative; }\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/supports/linear-easing.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\");\n/* harmony import */ var _generators_utils_create_generator_easing_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../generators/utils/create-generator-easing.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs\");\n/* harmony import */ var _generators_utils_is_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../generators/utils/is-generator.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs\");\n/* harmony import */ var _easing_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./easing.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs\");\n\n\n\n\n\n\nconst defaultEasing = \"easeOut\";\nfunction applyGeneratorOptions(options) {\n    var _a;\n    if ((0,_generators_utils_is_generator_mjs__WEBPACK_IMPORTED_MODULE_3__.isGenerator)(options.type)) {\n        const generatorOptions = (0,_generators_utils_create_generator_easing_mjs__WEBPACK_IMPORTED_MODULE_2__.createGeneratorEasing)(options, 100, options.type);\n        options.ease = (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsLinearEasing)()\n            ? generatorOptions.ease\n            : defaultEasing;\n        options.duration = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)(generatorOptions.duration);\n        options.type = \"keyframes\";\n    }\n    else {\n        options.duration = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)((_a = options.duration) !== null && _a !== void 0 ? _a : 0.3);\n        options.ease = options.ease || defaultEasing;\n    }\n}\n// TODO: Reuse for NativeAnimation\nfunction convertMotionOptionsToNative(valueName, keyframes, options) {\n    var _a;\n    const nativeKeyframes = {};\n    const nativeOptions = {\n        fill: \"both\",\n        easing: \"linear\",\n        composite: \"replace\",\n    };\n    nativeOptions.delay = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)((_a = options.delay) !== null && _a !== void 0 ? _a : 0);\n    applyGeneratorOptions(options);\n    nativeOptions.duration = options.duration;\n    const { ease, times } = options;\n    if (times)\n        nativeKeyframes.offset = times;\n    nativeKeyframes[valueName] = keyframes;\n    const easing = (0,_easing_mjs__WEBPACK_IMPORTED_MODULE_4__.mapEasingToNativeEasing)(ease, options.duration);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing)) {\n        nativeKeyframes.easing = easing;\n    }\n    else {\n        nativeOptions.easing = easing;\n    }\n    return {\n        keyframes: nativeKeyframes,\n        options: nativeOptions,\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/convert-options.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cubicBezierAsString: function() { return /* binding */ cubicBezierAsString; },\n/* harmony export */   isWaapiSupportedEasing: function() { return /* binding */ isWaapiSupportedEasing; },\n/* harmony export */   mapEasingToNativeEasing: function() { return /* binding */ mapEasingToNativeEasing; },\n/* harmony export */   supportedWaapiEasing: function() { return /* binding */ supportedWaapiEasing; }\n/* harmony export */ });\n/* harmony import */ var _utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/is-bezier-definition.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\");\n/* harmony import */ var _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/supports/linear-easing.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\");\n/* harmony import */ var _linear_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./linear.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs\");\n\n\n\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean((typeof easing === \"function\" && (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsLinearEasing)()) ||\n        !easing ||\n        (typeof easing === \"string\" &&\n            (easing in supportedWaapiEasing || (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsLinearEasing)())) ||\n        (0,_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__.isBezierDefinition)(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: /*@__PURE__*/ cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: /*@__PURE__*/ cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: /*@__PURE__*/ cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: /*@__PURE__*/ cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\nfunction mapEasingToNativeEasing(easing, duration) {\n    if (!easing) {\n        return undefined;\n    }\n    else if (typeof easing === \"function\" && (0,_utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsLinearEasing)()) {\n        return (0,_linear_mjs__WEBPACK_IMPORTED_MODULE_2__.generateLinearEasing)(easing, duration);\n    }\n    else if ((0,_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__.isBezierDefinition)(easing)) {\n        return cubicBezierAsString(easing);\n    }\n    else if (Array.isArray(easing)) {\n        return easing.map((segmentEasing) => mapEasingToNativeEasing(segmentEasing, duration) ||\n            supportedWaapiEasing.easeOut);\n    }\n    else {\n        return supportedWaapiEasing[easing];\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateLinearEasing: function() { return /* binding */ generateLinearEasing; }\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n\n\nconst generateLinearEasing = (easing, duration, // as milliseconds\nresolution = 10 // as milliseconds\n) => {\n    let points = \"\";\n    const numPoints = Math.max(Math.round(duration / resolution), 2);\n    for (let i = 0; i < numPoints; i++) {\n        points += easing((0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.progress)(0, numPoints - 1, i)) + \", \";\n    }\n    return `linear(${points.substring(0, points.length - 2)})`;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvYW5pbWF0aW9uL3dhYXBpL3V0aWxzL2xpbmVhci5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0M7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsZUFBZTtBQUNuQyx5QkFBeUIsc0RBQVE7QUFDakM7QUFDQSxxQkFBcUIsdUNBQXVDO0FBQzVEOztBQUVnQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL2FuaW1hdGlvbi93YWFwaS91dGlscy9saW5lYXIubWpzPzYxNTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcHJvZ3Jlc3MgfSBmcm9tICdtb3Rpb24tdXRpbHMnO1xuXG5jb25zdCBnZW5lcmF0ZUxpbmVhckVhc2luZyA9IChlYXNpbmcsIGR1cmF0aW9uLCAvLyBhcyBtaWxsaXNlY29uZHNcbnJlc29sdXRpb24gPSAxMCAvLyBhcyBtaWxsaXNlY29uZHNcbikgPT4ge1xuICAgIGxldCBwb2ludHMgPSBcIlwiO1xuICAgIGNvbnN0IG51bVBvaW50cyA9IE1hdGgubWF4KE1hdGgucm91bmQoZHVyYXRpb24gLyByZXNvbHV0aW9uKSwgMik7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBudW1Qb2ludHM7IGkrKykge1xuICAgICAgICBwb2ludHMgKz0gZWFzaW5nKHByb2dyZXNzKDAsIG51bVBvaW50cyAtIDEsIGkpKSArIFwiLCBcIjtcbiAgICB9XG4gICAgcmV0dXJuIGBsaW5lYXIoJHtwb2ludHMuc3Vic3RyaW5nKDAsIHBvaW50cy5sZW5ndGggLSAyKX0pYDtcbn07XG5cbmV4cG9ydCB7IGdlbmVyYXRlTGluZWFyRWFzaW5nIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDragActive: function() { return /* binding */ isDragActive; },\n/* harmony export */   isDragging: function() { return /* binding */ isDragging; }\n/* harmony export */ });\nconst isDragging = {\n    x: false,\n    y: false,\n};\nfunction isDragActive() {\n    return isDragging.x || isDragging.y;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvZHJhZy9zdGF0ZS9pcy1hY3RpdmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvZHJhZy9zdGF0ZS9pcy1hY3RpdmUubWpzPzA1NTQiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNEcmFnZ2luZyA9IHtcbiAgICB4OiBmYWxzZSxcbiAgICB5OiBmYWxzZSxcbn07XG5mdW5jdGlvbiBpc0RyYWdBY3RpdmUoKSB7XG4gICAgcmV0dXJuIGlzRHJhZ2dpbmcueCB8fCBpc0RyYWdnaW5nLnk7XG59XG5cbmV4cG9ydCB7IGlzRHJhZ0FjdGl2ZSwgaXNEcmFnZ2luZyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setDragLock: function() { return /* binding */ setDragLock; }\n/* harmony export */ });\n/* harmony import */ var _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is-active.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n\n\nfunction setDragLock(axis) {\n    if (axis === \"x\" || axis === \"y\") {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis]) {\n            return null;\n        }\n        else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = true;\n            return () => {\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging[axis] = false;\n            };\n        }\n    }\n    else {\n        if (_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x || _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y) {\n            return null;\n        }\n        else {\n            _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = true;\n            return () => {\n                _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.x = _is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragging.y = false;\n            };\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvZHJhZy9zdGF0ZS9zZXQtYWN0aXZlLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE2Qzs7QUFFN0M7QUFDQTtBQUNBLFlBQVksc0RBQVU7QUFDdEI7QUFDQTtBQUNBO0FBQ0EsWUFBWSxzREFBVTtBQUN0QjtBQUNBLGdCQUFnQixzREFBVTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksc0RBQVUsTUFBTSxzREFBVTtBQUN0QztBQUNBO0FBQ0E7QUFDQSxZQUFZLHNEQUFVLEtBQUssc0RBQVU7QUFDckM7QUFDQSxnQkFBZ0Isc0RBQVUsS0FBSyxzREFBVTtBQUN6QztBQUNBO0FBQ0E7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21vdGlvbi1kb20vZGlzdC9lcy9nZXN0dXJlcy9kcmFnL3N0YXRlL3NldC1hY3RpdmUubWpzPzk5YWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNEcmFnZ2luZyB9IGZyb20gJy4vaXMtYWN0aXZlLm1qcyc7XG5cbmZ1bmN0aW9uIHNldERyYWdMb2NrKGF4aXMpIHtcbiAgICBpZiAoYXhpcyA9PT0gXCJ4XCIgfHwgYXhpcyA9PT0gXCJ5XCIpIHtcbiAgICAgICAgaWYgKGlzRHJhZ2dpbmdbYXhpc10pIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaXNEcmFnZ2luZ1theGlzXSA9IHRydWU7XG4gICAgICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlzRHJhZ2dpbmdbYXhpc10gPSBmYWxzZTtcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGlmIChpc0RyYWdnaW5nLnggfHwgaXNEcmFnZ2luZy55KSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGlzRHJhZ2dpbmcueCA9IGlzRHJhZ2dpbmcueSA9IHRydWU7XG4gICAgICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlzRHJhZ2dpbmcueCA9IGlzRHJhZ2dpbmcueSA9IGZhbHNlO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgIH1cbn1cblxuZXhwb3J0IHsgc2V0RHJhZ0xvY2sgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/hover.mjs":
/*!************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/hover.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hover: function() { return /* binding */ hover; }\n/* harmony export */ });\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./drag/state/is-active.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _utils_setup_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/setup.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\");\n\n\n\n/**\n * Filter out events that are not pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction filterEvents(callback) {\n    return (event) => {\n        if (event.pointerType === \"touch\" || (0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragActive)())\n            return;\n        callback(event);\n    };\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */\nfunction hover(elementOrSelector, onHoverStart, options = {}) {\n    const [elements, eventOptions, cancel] = (0,_utils_setup_mjs__WEBPACK_IMPORTED_MODULE_1__.setupGesture)(elementOrSelector, options);\n    const onPointerEnter = filterEvents((enterEvent) => {\n        const { target } = enterEvent;\n        const onHoverEnd = onHoverStart(enterEvent);\n        if (typeof onHoverEnd !== \"function\" || !target)\n            return;\n        const onPointerLeave = filterEvents((leaveEvent) => {\n            onHoverEnd(leaveEvent);\n            target.removeEventListener(\"pointerleave\", onPointerLeave);\n        });\n        target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n    });\n    elements.forEach((element) => {\n        element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n    });\n    return cancel;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/hover.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/index.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   press: function() { return /* binding */ press; }\n/* harmony export */ });\n/* harmony import */ var _drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../drag/state/is-active.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/is-node-or-child.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\");\n/* harmony import */ var _utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/is-primary-pointer.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\");\n/* harmony import */ var _utils_setup_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/setup.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\");\n/* harmony import */ var _utils_is_keyboard_accessible_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/is-keyboard-accessible.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs\");\n/* harmony import */ var _utils_keyboard_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/keyboard.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs\");\n/* harmony import */ var _utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/state.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\");\n\n\n\n\n\n\n\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n    return (0,_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_2__.isPrimaryPointer)(event) && !(0,_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_0__.isDragActive)();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(elementOrSelector, onPressStart, options = {}) {\n    const [elements, eventOptions, cancelEvents] = (0,_utils_setup_mjs__WEBPACK_IMPORTED_MODULE_3__.setupGesture)(elementOrSelector, options);\n    const startPress = (startEvent) => {\n        const element = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent) || _utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__.isPressing.has(element))\n            return;\n        _utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__.isPressing.add(element);\n        const onPressEnd = onPressStart(startEvent);\n        const onPointerEnd = (endEvent, success) => {\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (!isValidPressEvent(endEvent) || !_utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__.isPressing.has(element)) {\n                return;\n            }\n            _utils_state_mjs__WEBPACK_IMPORTED_MODULE_6__.isPressing.delete(element);\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, { success });\n            }\n        };\n        const onPointerUp = (upEvent) => {\n            onPointerEnd(upEvent, options.useGlobalTarget ||\n                (0,_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_1__.isNodeOrChild)(element, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent) => {\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    elements.forEach((element) => {\n        if (!(0,_utils_is_keyboard_accessible_mjs__WEBPACK_IMPORTED_MODULE_4__.isElementKeyboardAccessible)(element) &&\n            element.getAttribute(\"tabindex\") === null) {\n            element.tabIndex = 0;\n        }\n        const target = options.useGlobalTarget ? window : element;\n        target.addEventListener(\"pointerdown\", startPress, eventOptions);\n        element.addEventListener(\"focus\", (event) => (0,_utils_keyboard_mjs__WEBPACK_IMPORTED_MODULE_5__.enableKeyboardPress)(event, eventOptions), eventOptions);\n    });\n    return cancelEvents;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isElementKeyboardAccessible: function() { return /* binding */ isElementKeyboardAccessible; }\n/* harmony export */ });\nconst focusableElements = new Set([\n    \"BUTTON\",\n    \"INPUT\",\n    \"SELECT\",\n    \"TEXTAREA\",\n    \"A\",\n]);\nfunction isElementKeyboardAccessible(element) {\n    return (focusableElements.has(element.tagName) ||\n        element.tabIndex !== -1);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvcHJlc3MvdXRpbHMvaXMta2V5Ym9hcmQtYWNjZXNzaWJsZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXVDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvcHJlc3MvdXRpbHMvaXMta2V5Ym9hcmQtYWNjZXNzaWJsZS5tanM/YzU4YSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBmb2N1c2FibGVFbGVtZW50cyA9IG5ldyBTZXQoW1xuICAgIFwiQlVUVE9OXCIsXG4gICAgXCJJTlBVVFwiLFxuICAgIFwiU0VMRUNUXCIsXG4gICAgXCJURVhUQVJFQVwiLFxuICAgIFwiQVwiLFxuXSk7XG5mdW5jdGlvbiBpc0VsZW1lbnRLZXlib2FyZEFjY2Vzc2libGUoZWxlbWVudCkge1xuICAgIHJldHVybiAoZm9jdXNhYmxlRWxlbWVudHMuaGFzKGVsZW1lbnQudGFnTmFtZSkgfHxcbiAgICAgICAgZWxlbWVudC50YWJJbmRleCAhPT0gLTEpO1xufVxuXG5leHBvcnQgeyBpc0VsZW1lbnRLZXlib2FyZEFjY2Vzc2libGUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs":
/*!***************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs ***!
  \***************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enableKeyboardPress: function() { return /* binding */ enableKeyboardPress; }\n/* harmony export */ });\n/* harmony import */ var _state_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\");\n\n\n/**\n * Filter out events that are not \"Enter\" keys.\n */\nfunction filterEvents(callback) {\n    return (event) => {\n        if (event.key !== \"Enter\")\n            return;\n        callback(event);\n    };\n}\nfunction firePointerEvent(target, type) {\n    target.dispatchEvent(new PointerEvent(\"pointer\" + type, { isPrimary: true, bubbles: true }));\n}\nconst enableKeyboardPress = (focusEvent, eventOptions) => {\n    const element = focusEvent.currentTarget;\n    if (!element)\n        return;\n    const handleKeydown = filterEvents(() => {\n        if (_state_mjs__WEBPACK_IMPORTED_MODULE_0__.isPressing.has(element))\n            return;\n        firePointerEvent(element, \"down\");\n        const handleKeyup = filterEvents(() => {\n            firePointerEvent(element, \"up\");\n        });\n        const handleBlur = () => firePointerEvent(element, \"cancel\");\n        element.addEventListener(\"keyup\", handleKeyup, eventOptions);\n        element.addEventListener(\"blur\", handleBlur, eventOptions);\n    });\n    element.addEventListener(\"keydown\", handleKeydown, eventOptions);\n    /**\n     * Add an event listener that fires on blur to remove the keydown events.\n     */\n    element.addEventListener(\"blur\", () => element.removeEventListener(\"keydown\", handleKeydown), eventOptions);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPressing: function() { return /* binding */ isPressing; }\n/* harmony export */ });\nconst isPressing = new WeakSet();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvcHJlc3MvdXRpbHMvc3RhdGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFc0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21vdGlvbi1kb20vZGlzdC9lcy9nZXN0dXJlcy9wcmVzcy91dGlscy9zdGF0ZS5tanM/N2U3NSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc1ByZXNzaW5nID0gbmV3IFdlYWtTZXQoKTtcblxuZXhwb3J0IHsgaXNQcmVzc2luZyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isNodeOrChild: function() { return /* binding */ isNodeOrChild; }\n/* harmony export */ });\n/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */\nconst isNodeOrChild = (parent, child) => {\n    if (!child) {\n        return false;\n    }\n    else if (parent === child) {\n        return true;\n    }\n    else {\n        return isNodeOrChild(parent, child.parentElement);\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvdXRpbHMvaXMtbm9kZS1vci1jaGlsZC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21vdGlvbi1kb20vZGlzdC9lcy9nZXN0dXJlcy91dGlscy9pcy1ub2RlLW9yLWNoaWxkLm1qcz83MTU0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUmVjdXJzaXZlbHkgdHJhdmVyc2UgdXAgdGhlIHRyZWUgdG8gY2hlY2sgd2hldGhlciB0aGUgcHJvdmlkZWQgY2hpbGQgbm9kZVxuICogaXMgdGhlIHBhcmVudCBvciBhIGRlc2NlbmRhbnQgb2YgaXQuXG4gKlxuICogQHBhcmFtIHBhcmVudCAtIEVsZW1lbnQgdG8gZmluZFxuICogQHBhcmFtIGNoaWxkIC0gRWxlbWVudCB0byB0ZXN0IGFnYWluc3QgcGFyZW50XG4gKi9cbmNvbnN0IGlzTm9kZU9yQ2hpbGQgPSAocGFyZW50LCBjaGlsZCkgPT4ge1xuICAgIGlmICghY2hpbGQpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBlbHNlIGlmIChwYXJlbnQgPT09IGNoaWxkKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGlzTm9kZU9yQ2hpbGQocGFyZW50LCBjaGlsZC5wYXJlbnRFbGVtZW50KTtcbiAgICB9XG59O1xuXG5leHBvcnQgeyBpc05vZGVPckNoaWxkIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isPrimaryPointer: function() { return /* binding */ isPrimaryPointer; }\n/* harmony export */ });\nconst isPrimaryPointer = (event) => {\n    if (event.pointerType === \"mouse\") {\n        return typeof event.button !== \"number\" || event.button <= 0;\n    }\n    else {\n        /**\n         * isPrimary is true for all mice buttons, whereas every touch point\n         * is regarded as its own input. So subsequent concurrent touch points\n         * will be false.\n         *\n         * Specifically match against false here as incomplete versions of\n         * PointerEvents in very old browser might have it set as undefined.\n         */\n        return event.isPrimary !== false;\n    }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvdXRpbHMvaXMtcHJpbWFyeS1wb2ludGVyLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvdXRpbHMvaXMtcHJpbWFyeS1wb2ludGVyLm1qcz8wNGIwIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzUHJpbWFyeVBvaW50ZXIgPSAoZXZlbnQpID0+IHtcbiAgICBpZiAoZXZlbnQucG9pbnRlclR5cGUgPT09IFwibW91c2VcIikge1xuICAgICAgICByZXR1cm4gdHlwZW9mIGV2ZW50LmJ1dHRvbiAhPT0gXCJudW1iZXJcIiB8fCBldmVudC5idXR0b24gPD0gMDtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBpc1ByaW1hcnkgaXMgdHJ1ZSBmb3IgYWxsIG1pY2UgYnV0dG9ucywgd2hlcmVhcyBldmVyeSB0b3VjaCBwb2ludFxuICAgICAgICAgKiBpcyByZWdhcmRlZCBhcyBpdHMgb3duIGlucHV0LiBTbyBzdWJzZXF1ZW50IGNvbmN1cnJlbnQgdG91Y2ggcG9pbnRzXG4gICAgICAgICAqIHdpbGwgYmUgZmFsc2UuXG4gICAgICAgICAqXG4gICAgICAgICAqIFNwZWNpZmljYWxseSBtYXRjaCBhZ2FpbnN0IGZhbHNlIGhlcmUgYXMgaW5jb21wbGV0ZSB2ZXJzaW9ucyBvZlxuICAgICAgICAgKiBQb2ludGVyRXZlbnRzIGluIHZlcnkgb2xkIGJyb3dzZXIgbWlnaHQgaGF2ZSBpdCBzZXQgYXMgdW5kZWZpbmVkLlxuICAgICAgICAgKi9cbiAgICAgICAgcmV0dXJuIGV2ZW50LmlzUHJpbWFyeSAhPT0gZmFsc2U7XG4gICAgfVxufTtcblxuZXhwb3J0IHsgaXNQcmltYXJ5UG9pbnRlciB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setupGesture: function() { return /* binding */ setupGesture; }\n/* harmony export */ });\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/resolve-elements.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n\n\nfunction setupGesture(elementOrSelector, options) {\n    const elements = (0,_utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal,\n    };\n    const cancel = () => gestureAbortController.abort();\n    return [elements, eventOptions, cancel];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvdXRpbHMvc2V0dXAubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1FOztBQUVuRTtBQUNBLHFCQUFxQiw0RUFBZTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvZ2VzdHVyZXMvdXRpbHMvc2V0dXAubWpzPzNhM2MiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmVzb2x2ZUVsZW1lbnRzIH0gZnJvbSAnLi4vLi4vdXRpbHMvcmVzb2x2ZS1lbGVtZW50cy5tanMnO1xuXG5mdW5jdGlvbiBzZXR1cEdlc3R1cmUoZWxlbWVudE9yU2VsZWN0b3IsIG9wdGlvbnMpIHtcbiAgICBjb25zdCBlbGVtZW50cyA9IHJlc29sdmVFbGVtZW50cyhlbGVtZW50T3JTZWxlY3Rvcik7XG4gICAgY29uc3QgZ2VzdHVyZUFib3J0Q29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgICBjb25zdCBldmVudE9wdGlvbnMgPSB7XG4gICAgICAgIHBhc3NpdmU6IHRydWUsXG4gICAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAgIHNpZ25hbDogZ2VzdHVyZUFib3J0Q29udHJvbGxlci5zaWduYWwsXG4gICAgfTtcbiAgICBjb25zdCBjYW5jZWwgPSAoKSA9PiBnZXN0dXJlQWJvcnRDb250cm9sbGVyLmFib3J0KCk7XG4gICAgcmV0dXJuIFtlbGVtZW50cywgZXZlbnRPcHRpb25zLCBjYW5jZWxdO1xufVxuXG5leHBvcnQgeyBzZXR1cEdlc3R1cmUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/utils/setup.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/index.mjs ***!
  \***************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroupPlaybackControls: function() { return /* reexport safe */ _animation_controls_Group_mjs__WEBPACK_IMPORTED_MODULE_0__.GroupPlaybackControls; },\n/* harmony export */   NativeAnimationControls: function() { return /* reexport safe */ _animation_waapi_NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_5__.NativeAnimationControls; },\n/* harmony export */   ViewTransitionBuilder: function() { return /* reexport safe */ _view_index_mjs__WEBPACK_IMPORTED_MODULE_16__.ViewTransitionBuilder; },\n/* harmony export */   attachTimeline: function() { return /* reexport safe */ _animation_waapi_utils_attach_timeline_mjs__WEBPACK_IMPORTED_MODULE_6__.attachTimeline; },\n/* harmony export */   calcGeneratorDuration: function() { return /* reexport safe */ _animation_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_2__.calcGeneratorDuration; },\n/* harmony export */   createGeneratorEasing: function() { return /* reexport safe */ _animation_generators_utils_create_generator_easing_mjs__WEBPACK_IMPORTED_MODULE_3__.createGeneratorEasing; },\n/* harmony export */   cubicBezierAsString: function() { return /* reexport safe */ _animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_7__.cubicBezierAsString; },\n/* harmony export */   generateLinearEasing: function() { return /* reexport safe */ _animation_waapi_utils_linear_mjs__WEBPACK_IMPORTED_MODULE_8__.generateLinearEasing; },\n/* harmony export */   getValueTransition: function() { return /* reexport safe */ _animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_1__.getValueTransition; },\n/* harmony export */   hover: function() { return /* reexport safe */ _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_9__.hover; },\n/* harmony export */   isBezierDefinition: function() { return /* reexport safe */ _utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_11__.isBezierDefinition; },\n/* harmony export */   isDragActive: function() { return /* reexport safe */ _gestures_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_17__.isDragActive; },\n/* harmony export */   isDragging: function() { return /* reexport safe */ _gestures_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_17__.isDragging; },\n/* harmony export */   isGenerator: function() { return /* reexport safe */ _animation_generators_utils_is_generator_mjs__WEBPACK_IMPORTED_MODULE_4__.isGenerator; },\n/* harmony export */   isNodeOrChild: function() { return /* reexport safe */ _gestures_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_19__.isNodeOrChild; },\n/* harmony export */   isPrimaryPointer: function() { return /* reexport safe */ _gestures_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_20__.isPrimaryPointer; },\n/* harmony export */   isWaapiSupportedEasing: function() { return /* reexport safe */ _animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_7__.isWaapiSupportedEasing; },\n/* harmony export */   mapEasingToNativeEasing: function() { return /* reexport safe */ _animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_7__.mapEasingToNativeEasing; },\n/* harmony export */   maxGeneratorDuration: function() { return /* reexport safe */ _animation_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_2__.maxGeneratorDuration; },\n/* harmony export */   press: function() { return /* reexport safe */ _gestures_press_index_mjs__WEBPACK_IMPORTED_MODULE_10__.press; },\n/* harmony export */   resolveElements: function() { return /* reexport safe */ _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_12__.resolveElements; },\n/* harmony export */   setDragLock: function() { return /* reexport safe */ _gestures_drag_state_set_active_mjs__WEBPACK_IMPORTED_MODULE_18__.setDragLock; },\n/* harmony export */   supportedWaapiEasing: function() { return /* reexport safe */ _animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_7__.supportedWaapiEasing; },\n/* harmony export */   supportsFlags: function() { return /* reexport safe */ _utils_supports_flags_mjs__WEBPACK_IMPORTED_MODULE_13__.supportsFlags; },\n/* harmony export */   supportsLinearEasing: function() { return /* reexport safe */ _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_14__.supportsLinearEasing; },\n/* harmony export */   supportsScrollTimeline: function() { return /* reexport safe */ _utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_15__.supportsScrollTimeline; },\n/* harmony export */   view: function() { return /* reexport safe */ _view_index_mjs__WEBPACK_IMPORTED_MODULE_16__.view; }\n/* harmony export */ });\n/* harmony import */ var _animation_controls_Group_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./animation/controls/Group.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/controls/Group.mjs\");\n/* harmony import */ var _animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./animation/utils/get-value-transition.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs\");\n/* harmony import */ var _animation_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./animation/generators/utils/calc-duration.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs\");\n/* harmony import */ var _animation_generators_utils_create_generator_easing_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./animation/generators/utils/create-generator-easing.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs\");\n/* harmony import */ var _animation_generators_utils_is_generator_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./animation/generators/utils/is-generator.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs\");\n/* harmony import */ var _animation_waapi_NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./animation/waapi/NativeAnimationControls.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs\");\n/* harmony import */ var _animation_waapi_utils_attach_timeline_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./animation/waapi/utils/attach-timeline.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs\");\n/* harmony import */ var _animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./animation/waapi/utils/easing.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs\");\n/* harmony import */ var _animation_waapi_utils_linear_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./animation/waapi/utils/linear.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs\");\n/* harmony import */ var _gestures_hover_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./gestures/hover.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/hover.mjs\");\n/* harmony import */ var _gestures_press_index_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./gestures/press/index.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/press/index.mjs\");\n/* harmony import */ var _utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./utils/is-bezier-definition.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\");\n/* harmony import */ var _utils_resolve_elements_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./utils/resolve-elements.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n/* harmony import */ var _utils_supports_flags_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./utils/supports/flags.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs\");\n/* harmony import */ var _utils_supports_linear_easing_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./utils/supports/linear-easing.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\");\n/* harmony import */ var _utils_supports_scroll_timeline_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./utils/supports/scroll-timeline.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\");\n/* harmony import */ var _view_index_mjs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./view/index.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/view/index.mjs\");\n/* harmony import */ var _gestures_drag_state_is_active_mjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./gestures/drag/state/is-active.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs\");\n/* harmony import */ var _gestures_drag_state_set_active_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./gestures/drag/state/set-active.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs\");\n/* harmony import */ var _gestures_utils_is_node_or_child_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./gestures/utils/is-node-or-child.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs\");\n/* harmony import */ var _gestures_utils_is_primary_pointer_mjs__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./gestures/utils/is-primary-pointer.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs ***!
  \************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBezierDefinition: function() { return /* binding */ isBezierDefinition; }\n/* harmony export */ });\nconst isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvaXMtYmV6aWVyLWRlZmluaXRpb24ubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7QUFFOEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21vdGlvbi1kb20vZGlzdC9lcy91dGlscy9pcy1iZXppZXItZGVmaW5pdGlvbi5tanM/NWMzOCJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBpc0JlemllckRlZmluaXRpb24gPSAoZWFzaW5nKSA9PiBBcnJheS5pc0FycmF5KGVhc2luZykgJiYgdHlwZW9mIGVhc2luZ1swXSA9PT0gXCJudW1iZXJcIjtcblxuZXhwb3J0IHsgaXNCZXppZXJEZWZpbml0aW9uIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs ***!
  \********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveElements: function() { return /* binding */ resolveElements; }\n/* harmony export */ });\nfunction resolveElements(elementOrSelector, scope, selectorCache) {\n    var _a;\n    if (elementOrSelector instanceof Element) {\n        return [elementOrSelector];\n    }\n    else if (typeof elementOrSelector === \"string\") {\n        let root = document;\n        if (scope) {\n            // TODO: Refactor to utils package\n            // invariant(\n            //     Boolean(scope.current),\n            //     \"Scope provided, but no element detected.\"\n            // )\n            root = scope.current;\n        }\n        const elements = (_a = selectorCache === null || selectorCache === void 0 ? void 0 : selectorCache[elementOrSelector]) !== null && _a !== void 0 ? _a : root.querySelectorAll(elementOrSelector);\n        return elements ? Array.from(elements) : [];\n    }\n    return Array.from(elementOrSelector);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvcmVzb2x2ZS1lbGVtZW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvcmVzb2x2ZS1lbGVtZW50cy5tanM/NDljMCJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiByZXNvbHZlRWxlbWVudHMoZWxlbWVudE9yU2VsZWN0b3IsIHNjb3BlLCBzZWxlY3RvckNhY2hlKSB7XG4gICAgdmFyIF9hO1xuICAgIGlmIChlbGVtZW50T3JTZWxlY3RvciBpbnN0YW5jZW9mIEVsZW1lbnQpIHtcbiAgICAgICAgcmV0dXJuIFtlbGVtZW50T3JTZWxlY3Rvcl07XG4gICAgfVxuICAgIGVsc2UgaWYgKHR5cGVvZiBlbGVtZW50T3JTZWxlY3RvciA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICBsZXQgcm9vdCA9IGRvY3VtZW50O1xuICAgICAgICBpZiAoc2NvcGUpIHtcbiAgICAgICAgICAgIC8vIFRPRE86IFJlZmFjdG9yIHRvIHV0aWxzIHBhY2thZ2VcbiAgICAgICAgICAgIC8vIGludmFyaWFudChcbiAgICAgICAgICAgIC8vICAgICBCb29sZWFuKHNjb3BlLmN1cnJlbnQpLFxuICAgICAgICAgICAgLy8gICAgIFwiU2NvcGUgcHJvdmlkZWQsIGJ1dCBubyBlbGVtZW50IGRldGVjdGVkLlwiXG4gICAgICAgICAgICAvLyApXG4gICAgICAgICAgICByb290ID0gc2NvcGUuY3VycmVudDtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBlbGVtZW50cyA9IChfYSA9IHNlbGVjdG9yQ2FjaGUgPT09IG51bGwgfHwgc2VsZWN0b3JDYWNoZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogc2VsZWN0b3JDYWNoZVtlbGVtZW50T3JTZWxlY3Rvcl0pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IHJvb3QucXVlcnlTZWxlY3RvckFsbChlbGVtZW50T3JTZWxlY3Rvcik7XG4gICAgICAgIHJldHVybiBlbGVtZW50cyA/IEFycmF5LmZyb20oZWxlbWVudHMpIDogW107XG4gICAgfVxuICAgIHJldHVybiBBcnJheS5mcm9tKGVsZW1lbnRPclNlbGVjdG9yKTtcbn1cblxuZXhwb3J0IHsgcmVzb2x2ZUVsZW1lbnRzIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/flags.mjs ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsFlags: function() { return /* binding */ supportsFlags; }\n/* harmony export */ });\n/**\n * Add the ability for test suites to manually set support flags\n * to better test more environments.\n */\nconst supportsFlags = {\n    linearEasing: undefined,\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvc3VwcG9ydHMvZmxhZ3MubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21vdGlvbi1kb20vZGlzdC9lcy91dGlscy9zdXBwb3J0cy9mbGFncy5tanM/NTliZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEFkZCB0aGUgYWJpbGl0eSBmb3IgdGVzdCBzdWl0ZXMgdG8gbWFudWFsbHkgc2V0IHN1cHBvcnQgZmxhZ3NcbiAqIHRvIGJldHRlciB0ZXN0IG1vcmUgZW52aXJvbm1lbnRzLlxuICovXG5jb25zdCBzdXBwb3J0c0ZsYWdzID0ge1xuICAgIGxpbmVhckVhc2luZzogdW5kZWZpbmVkLFxufTtcblxuZXhwb3J0IHsgc3VwcG9ydHNGbGFncyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsLinearEasing: function() { return /* binding */ supportsLinearEasing; }\n/* harmony export */ });\n/* harmony import */ var _memo_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./memo.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/memo.mjs\");\n\n\nconst supportsLinearEasing = /*@__PURE__*/ (0,_memo_mjs__WEBPACK_IMPORTED_MODULE_0__.memoSupports)(() => {\n    try {\n        document\n            .createElement(\"div\")\n            .animate({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n}, \"linearEasing\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvc3VwcG9ydHMvbGluZWFyLWVhc2luZy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEM7O0FBRTFDLDJDQUEyQyx1REFBWTtBQUN2RDtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsWUFBWSxJQUFJLHdCQUF3QjtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFK0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21vdGlvbi1kb20vZGlzdC9lcy91dGlscy9zdXBwb3J0cy9saW5lYXItZWFzaW5nLm1qcz8zZmY4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1lbW9TdXBwb3J0cyB9IGZyb20gJy4vbWVtby5tanMnO1xuXG5jb25zdCBzdXBwb3J0c0xpbmVhckVhc2luZyA9IC8qQF9fUFVSRV9fKi8gbWVtb1N1cHBvcnRzKCgpID0+IHtcbiAgICB0cnkge1xuICAgICAgICBkb2N1bWVudFxuICAgICAgICAgICAgLmNyZWF0ZUVsZW1lbnQoXCJkaXZcIilcbiAgICAgICAgICAgIC5hbmltYXRlKHsgb3BhY2l0eTogMCB9LCB7IGVhc2luZzogXCJsaW5lYXIoMCwgMSlcIiB9KTtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn0sIFwibGluZWFyRWFzaW5nXCIpO1xuXG5leHBvcnQgeyBzdXBwb3J0c0xpbmVhckVhc2luZyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/memo.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/memo.mjs ***!
  \*****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoSupports: function() { return /* binding */ memoSupports; }\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _flags_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./flags.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/flags.mjs\");\n\n\n\nfunction memoSupports(callback, supportsFlag) {\n    const memoized = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.memo)(callback);\n    return () => { var _a; return (_a = _flags_mjs__WEBPACK_IMPORTED_MODULE_1__.supportsFlags[supportsFlag]) !== null && _a !== void 0 ? _a : memoized(); };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvc3VwcG9ydHMvbWVtby5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9DO0FBQ1E7O0FBRTVDO0FBQ0EscUJBQXFCLGtEQUFJO0FBQ3pCLG1CQUFtQixRQUFRLGFBQWEscURBQWE7QUFDckQ7O0FBRXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvc3VwcG9ydHMvbWVtby5tanM/YzQ2NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBtZW1vIH0gZnJvbSAnbW90aW9uLXV0aWxzJztcbmltcG9ydCB7IHN1cHBvcnRzRmxhZ3MgfSBmcm9tICcuL2ZsYWdzLm1qcyc7XG5cbmZ1bmN0aW9uIG1lbW9TdXBwb3J0cyhjYWxsYmFjaywgc3VwcG9ydHNGbGFnKSB7XG4gICAgY29uc3QgbWVtb2l6ZWQgPSBtZW1vKGNhbGxiYWNrKTtcbiAgICByZXR1cm4gKCkgPT4geyB2YXIgX2E7IHJldHVybiAoX2EgPSBzdXBwb3J0c0ZsYWdzW3N1cHBvcnRzRmxhZ10pICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IG1lbW9pemVkKCk7IH07XG59XG5cbmV4cG9ydCB7IG1lbW9TdXBwb3J0cyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/memo.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supportsScrollTimeline: function() { return /* binding */ supportsScrollTimeline; }\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n\n\nconst supportsScrollTimeline = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.memo)(() => window.ScrollTimeline !== undefined);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvc3VwcG9ydHMvc2Nyb2xsLXRpbWVsaW5lLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvQzs7QUFFcEMsK0JBQStCLGtEQUFJOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvc3VwcG9ydHMvc2Nyb2xsLXRpbWVsaW5lLm1qcz84ZGJjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1lbW8gfSBmcm9tICdtb3Rpb24tdXRpbHMnO1xuXG5jb25zdCBzdXBwb3J0c1Njcm9sbFRpbWVsaW5lID0gbWVtbygoKSA9PiB3aW5kb3cuU2Nyb2xsVGltZWxpbmUgIT09IHVuZGVmaW5lZCk7XG5cbmV4cG9ydCB7IHN1cHBvcnRzU2Nyb2xsVGltZWxpbmUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/view/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/index.mjs ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ViewTransitionBuilder: function() { return /* binding */ ViewTransitionBuilder; },\n/* harmony export */   view: function() { return /* binding */ view; }\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _start_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./start.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/view/start.mjs\");\n\n\n\n/**\n * TODO:\n * - Create view transition on next tick\n * - Replace animations with Motion animations\n * - Return GroupAnimation on next tick\n */\nclass ViewTransitionBuilder {\n    constructor(update, options = {}) {\n        this.currentTarget = \"root\";\n        this.targets = new Map();\n        this.notifyReady = motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n        this.readyPromise = new Promise((resolve) => {\n            this.notifyReady = resolve;\n        });\n        queueMicrotask(() => {\n            (0,_start_mjs__WEBPACK_IMPORTED_MODULE_1__.startViewAnimation)(update, options, this.targets).then((animation) => this.notifyReady(animation));\n        });\n    }\n    get(selector) {\n        this.currentTarget = selector;\n        return this;\n    }\n    layout(keyframes, options) {\n        this.updateTarget(\"layout\", keyframes, options);\n        return this;\n    }\n    new(keyframes, options) {\n        this.updateTarget(\"new\", keyframes, options);\n        return this;\n    }\n    old(keyframes, options) {\n        this.updateTarget(\"old\", keyframes, options);\n        return this;\n    }\n    enter(keyframes, options) {\n        this.updateTarget(\"enter\", keyframes, options);\n        return this;\n    }\n    exit(keyframes, options) {\n        this.updateTarget(\"exit\", keyframes, options);\n        return this;\n    }\n    crossfade(options) {\n        this.updateTarget(\"enter\", { opacity: 1 }, options);\n        this.updateTarget(\"exit\", { opacity: 0 }, options);\n        return this;\n    }\n    updateTarget(target, keyframes, options = {}) {\n        const { currentTarget, targets } = this;\n        if (!targets.has(currentTarget)) {\n            targets.set(currentTarget, {});\n        }\n        const targetData = targets.get(currentTarget);\n        targetData[target] = { keyframes, options };\n    }\n    then(resolve, reject) {\n        return this.readyPromise.then(resolve, reject);\n    }\n}\nfunction view(update, defaultOptions = {}) {\n    return new ViewTransitionBuilder(update, defaultOptions);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/view/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/view/start.mjs":
/*!********************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/start.mjs ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   startViewAnimation: function() { return /* binding */ startViewAnimation; }\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\");\n/* harmony import */ var _animation_controls_BaseGroup_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../animation/controls/BaseGroup.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/controls/BaseGroup.mjs\");\n/* harmony import */ var _animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../animation/utils/get-value-transition.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs\");\n/* harmony import */ var _animation_waapi_NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../animation/waapi/NativeAnimationControls.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs\");\n/* harmony import */ var _animation_waapi_PseudoAnimation_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../animation/waapi/PseudoAnimation.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/PseudoAnimation.mjs\");\n/* harmony import */ var _animation_waapi_utils_convert_options_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../animation/waapi/utils/convert-options.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/convert-options.mjs\");\n/* harmony import */ var _animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../animation/waapi/utils/easing.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs\");\n/* harmony import */ var _utils_choose_layer_type_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/choose-layer-type.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs\");\n/* harmony import */ var _utils_css_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/css.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/css.mjs\");\n/* harmony import */ var _utils_get_layer_name_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/get-layer-name.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs\");\n/* harmony import */ var _utils_get_view_animations_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./utils/get-view-animations.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs\");\n/* harmony import */ var _utils_has_target_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./utils/has-target.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/has-target.mjs\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst definitionNames = [\"layout\", \"enter\", \"exit\", \"new\", \"old\"];\nfunction startViewAnimation(update, defaultOptions, targets) {\n    if (!document.startViewTransition) {\n        return new Promise(async (resolve) => {\n            await update();\n            resolve(new _animation_controls_BaseGroup_mjs__WEBPACK_IMPORTED_MODULE_1__.BaseGroupPlaybackControls([]));\n        });\n    }\n    // TODO: Go over existing targets and ensure they all have ids\n    /**\n     * If we don't have any animations defined for the root target,\n     * remove it from being captured.\n     */\n    if (!(0,_utils_has_target_mjs__WEBPACK_IMPORTED_MODULE_11__.hasTarget)(\"root\", targets)) {\n        _utils_css_mjs__WEBPACK_IMPORTED_MODULE_8__.css.set(\":root\", {\n            \"view-transition-name\": \"none\",\n        });\n    }\n    /**\n     * Set the timing curve to linear for all view transition layers.\n     * This gets baked into the keyframes, which can't be changed\n     * without breaking the generated animation.\n     *\n     * This allows us to set easing via updateTiming - which can be changed.\n     */\n    _utils_css_mjs__WEBPACK_IMPORTED_MODULE_8__.css.set(\"::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)\", { \"animation-timing-function\": \"linear !important\" });\n    _utils_css_mjs__WEBPACK_IMPORTED_MODULE_8__.css.commit(); // Write\n    const transition = document.startViewTransition(async () => {\n        await update();\n        // TODO: Go over new targets and ensure they all have ids\n    });\n    transition.finished.finally(() => {\n        _utils_css_mjs__WEBPACK_IMPORTED_MODULE_8__.css.remove(); // Write\n    });\n    return new Promise((resolve) => {\n        transition.ready.then(() => {\n            var _a;\n            const generatedViewAnimations = (0,_utils_get_view_animations_mjs__WEBPACK_IMPORTED_MODULE_10__.getViewAnimations)();\n            const animations = [];\n            /**\n             * Create animations for our definitions\n             */\n            targets.forEach((definition, target) => {\n                // TODO: If target is not \"root\", resolve elements\n                // and iterate over each\n                for (const key of definitionNames) {\n                    if (!definition[key])\n                        continue;\n                    const { keyframes, options } = definition[key];\n                    for (let [valueName, valueKeyframes] of Object.entries(keyframes)) {\n                        if (!valueKeyframes)\n                            continue;\n                        const valueOptions = {\n                            ...(0,_animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_2__.getValueTransition)(defaultOptions, valueName),\n                            ...(0,_animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_2__.getValueTransition)(options, valueName),\n                        };\n                        const type = (0,_utils_choose_layer_type_mjs__WEBPACK_IMPORTED_MODULE_7__.chooseLayerType)(key);\n                        /**\n                         * If this is an opacity animation, and keyframes are not an array,\n                         * we need to convert them into an array and set an initial value.\n                         */\n                        if (valueName === \"opacity\" &&\n                            !Array.isArray(valueKeyframes)) {\n                            const initialValue = type === \"new\" ? 0 : 1;\n                            valueKeyframes = [initialValue, valueKeyframes];\n                        }\n                        /**\n                         * Resolve stagger function if provided.\n                         */\n                        if (typeof valueOptions.delay === \"function\") {\n                            valueOptions.delay = valueOptions.delay(0, 1);\n                        }\n                        const animation = new _animation_waapi_PseudoAnimation_mjs__WEBPACK_IMPORTED_MODULE_4__.PseudoAnimation(document.documentElement, `::view-transition-${type}(${target})`, valueName, valueKeyframes, valueOptions);\n                        animations.push(animation);\n                    }\n                }\n            });\n            /**\n             * Handle browser generated animations\n             */\n            for (const animation of generatedViewAnimations) {\n                if (animation.playState === \"finished\")\n                    continue;\n                const { effect } = animation;\n                if (!effect || !(effect instanceof KeyframeEffect))\n                    continue;\n                const { pseudoElement } = effect;\n                if (!pseudoElement)\n                    continue;\n                const name = (0,_utils_get_layer_name_mjs__WEBPACK_IMPORTED_MODULE_9__.getLayerName)(pseudoElement);\n                if (!name)\n                    continue;\n                const targetDefinition = targets.get(name.layer);\n                if (!targetDefinition) {\n                    /**\n                     * If transition name is group then update the timing of the animation\n                     * whereas if it's old or new then we could possibly replace it using\n                     * the above method.\n                     */\n                    const transitionName = name.type === \"group\" ? \"layout\" : \"\";\n                    const animationTransition = {\n                        ...(0,_animation_utils_get_value_transition_mjs__WEBPACK_IMPORTED_MODULE_2__.getValueTransition)(defaultOptions, transitionName),\n                    };\n                    (0,_animation_waapi_utils_convert_options_mjs__WEBPACK_IMPORTED_MODULE_5__.applyGeneratorOptions)(animationTransition);\n                    const easing = (0,_animation_waapi_utils_easing_mjs__WEBPACK_IMPORTED_MODULE_6__.mapEasingToNativeEasing)(animationTransition.ease, animationTransition.duration);\n                    effect.updateTiming({\n                        delay: (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.secondsToMilliseconds)((_a = animationTransition.delay) !== null && _a !== void 0 ? _a : 0),\n                        duration: animationTransition.duration,\n                        easing,\n                    });\n                    animations.push(new _animation_waapi_NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_3__.NativeAnimationControls(animation));\n                }\n                else if (hasOpacity(targetDefinition, \"enter\") &&\n                    hasOpacity(targetDefinition, \"exit\") &&\n                    effect\n                        .getKeyframes()\n                        .some((keyframe) => keyframe.mixBlendMode)) {\n                    animations.push(new _animation_waapi_NativeAnimationControls_mjs__WEBPACK_IMPORTED_MODULE_3__.NativeAnimationControls(animation));\n                }\n                else {\n                    animation.cancel();\n                }\n            }\n            resolve(new _animation_controls_BaseGroup_mjs__WEBPACK_IMPORTED_MODULE_1__.BaseGroupPlaybackControls(animations));\n        });\n    });\n}\nfunction hasOpacity(target, key) {\n    var _a;\n    return (_a = target === null || target === void 0 ? void 0 : target[key]) === null || _a === void 0 ? void 0 : _a.keyframes.opacity;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/view/start.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs ***!
  \**************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chooseLayerType: function() { return /* binding */ chooseLayerType; }\n/* harmony export */ });\nfunction chooseLayerType(valueName) {\n    if (valueName === \"layout\")\n        return \"group\";\n    if (valueName === \"enter\" || valueName === \"new\")\n        return \"new\";\n    if (valueName === \"exit\" || valueName === \"old\")\n        return \"old\";\n    return \"group\";\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdmlldy91dGlscy9jaG9vc2UtbGF5ZXItdHlwZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFMkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21vdGlvbi1kb20vZGlzdC9lcy92aWV3L3V0aWxzL2Nob29zZS1sYXllci10eXBlLm1qcz9lZmJiIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGNob29zZUxheWVyVHlwZSh2YWx1ZU5hbWUpIHtcbiAgICBpZiAodmFsdWVOYW1lID09PSBcImxheW91dFwiKVxuICAgICAgICByZXR1cm4gXCJncm91cFwiO1xuICAgIGlmICh2YWx1ZU5hbWUgPT09IFwiZW50ZXJcIiB8fCB2YWx1ZU5hbWUgPT09IFwibmV3XCIpXG4gICAgICAgIHJldHVybiBcIm5ld1wiO1xuICAgIGlmICh2YWx1ZU5hbWUgPT09IFwiZXhpdFwiIHx8IHZhbHVlTmFtZSA9PT0gXCJvbGRcIilcbiAgICAgICAgcmV0dXJuIFwib2xkXCI7XG4gICAgcmV0dXJuIFwiZ3JvdXBcIjtcbn1cblxuZXhwb3J0IHsgY2hvb3NlTGF5ZXJUeXBlIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/css.mjs":
/*!************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/css.mjs ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: function() { return /* binding */ css; }\n/* harmony export */ });\nlet pendingRules = {};\nlet style = null;\nconst css = {\n    set: (selector, values) => {\n        pendingRules[selector] = values;\n    },\n    commit: () => {\n        if (!style) {\n            style = document.createElement(\"style\");\n            style.id = \"motion-view\";\n        }\n        let cssText = \"\";\n        for (const selector in pendingRules) {\n            const rule = pendingRules[selector];\n            cssText += `${selector} {\\n`;\n            for (const [property, value] of Object.entries(rule)) {\n                cssText += `  ${property}: ${value};\\n`;\n            }\n            cssText += \"}\\n\";\n        }\n        style.textContent = cssText;\n        document.head.appendChild(style);\n        pendingRules = {};\n    },\n    remove: () => {\n        if (style && style.parentElement) {\n            style.parentElement.removeChild(style);\n        }\n    },\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdmlldy91dGlscy9jc3MubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsV0FBVztBQUNyQztBQUNBLGdDQUFnQyxTQUFTLElBQUksT0FBTztBQUNwRDtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFZSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvY3NzLm1qcz9mZWY4Il0sInNvdXJjZXNDb250ZW50IjpbImxldCBwZW5kaW5nUnVsZXMgPSB7fTtcbmxldCBzdHlsZSA9IG51bGw7XG5jb25zdCBjc3MgPSB7XG4gICAgc2V0OiAoc2VsZWN0b3IsIHZhbHVlcykgPT4ge1xuICAgICAgICBwZW5kaW5nUnVsZXNbc2VsZWN0b3JdID0gdmFsdWVzO1xuICAgIH0sXG4gICAgY29tbWl0OiAoKSA9PiB7XG4gICAgICAgIGlmICghc3R5bGUpIHtcbiAgICAgICAgICAgIHN0eWxlID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcInN0eWxlXCIpO1xuICAgICAgICAgICAgc3R5bGUuaWQgPSBcIm1vdGlvbi12aWV3XCI7XG4gICAgICAgIH1cbiAgICAgICAgbGV0IGNzc1RleHQgPSBcIlwiO1xuICAgICAgICBmb3IgKGNvbnN0IHNlbGVjdG9yIGluIHBlbmRpbmdSdWxlcykge1xuICAgICAgICAgICAgY29uc3QgcnVsZSA9IHBlbmRpbmdSdWxlc1tzZWxlY3Rvcl07XG4gICAgICAgICAgICBjc3NUZXh0ICs9IGAke3NlbGVjdG9yfSB7XFxuYDtcbiAgICAgICAgICAgIGZvciAoY29uc3QgW3Byb3BlcnR5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMocnVsZSkpIHtcbiAgICAgICAgICAgICAgICBjc3NUZXh0ICs9IGAgICR7cHJvcGVydHl9OiAke3ZhbHVlfTtcXG5gO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY3NzVGV4dCArPSBcIn1cXG5cIjtcbiAgICAgICAgfVxuICAgICAgICBzdHlsZS50ZXh0Q29udGVudCA9IGNzc1RleHQ7XG4gICAgICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQoc3R5bGUpO1xuICAgICAgICBwZW5kaW5nUnVsZXMgPSB7fTtcbiAgICB9LFxuICAgIHJlbW92ZTogKCkgPT4ge1xuICAgICAgICBpZiAoc3R5bGUgJiYgc3R5bGUucGFyZW50RWxlbWVudCkge1xuICAgICAgICAgICAgc3R5bGUucGFyZW50RWxlbWVudC5yZW1vdmVDaGlsZChzdHlsZSk7XG4gICAgICAgIH1cbiAgICB9LFxufTtcblxuZXhwb3J0IHsgY3NzIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/css.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLayerName: function() { return /* binding */ getLayerName; }\n/* harmony export */ });\nfunction getLayerName(pseudoElement) {\n    const match = pseudoElement.match(/::view-transition-(old|new|group|image-pair)\\((.*?)\\)/);\n    if (!match)\n        return null;\n    return { layer: match[2], type: match[1] };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdmlldy91dGlscy9nZXQtbGF5ZXItbmFtZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiOztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvZ2V0LWxheWVyLW5hbWUubWpzPzcyMjYiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZ2V0TGF5ZXJOYW1lKHBzZXVkb0VsZW1lbnQpIHtcbiAgICBjb25zdCBtYXRjaCA9IHBzZXVkb0VsZW1lbnQubWF0Y2goLzo6dmlldy10cmFuc2l0aW9uLShvbGR8bmV3fGdyb3VwfGltYWdlLXBhaXIpXFwoKC4qPylcXCkvKTtcbiAgICBpZiAoIW1hdGNoKVxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICByZXR1cm4geyBsYXllcjogbWF0Y2hbMl0sIHR5cGU6IG1hdGNoWzFdIH07XG59XG5cbmV4cG9ydCB7IGdldExheWVyTmFtZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getViewAnimations: function() { return /* binding */ getViewAnimations; }\n/* harmony export */ });\nfunction filterViewAnimations(animation) {\n    var _a;\n    const { effect } = animation;\n    if (!effect)\n        return false;\n    return (effect.target === document.documentElement &&\n        ((_a = effect.pseudoElement) === null || _a === void 0 ? void 0 : _a.startsWith(\"::view-transition\")));\n}\nfunction getViewAnimations() {\n    return document.getAnimations().filter(filterViewAnimations);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdmlldy91dGlscy9nZXQtdmlldy1hbmltYXRpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBLFlBQVksU0FBUztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU2QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvZ2V0LXZpZXctYW5pbWF0aW9ucy5tanM/MDQxYSJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBmaWx0ZXJWaWV3QW5pbWF0aW9ucyhhbmltYXRpb24pIHtcbiAgICB2YXIgX2E7XG4gICAgY29uc3QgeyBlZmZlY3QgfSA9IGFuaW1hdGlvbjtcbiAgICBpZiAoIWVmZmVjdClcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIHJldHVybiAoZWZmZWN0LnRhcmdldCA9PT0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50ICYmXG4gICAgICAgICgoX2EgPSBlZmZlY3QucHNldWRvRWxlbWVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnN0YXJ0c1dpdGgoXCI6OnZpZXctdHJhbnNpdGlvblwiKSkpO1xufVxuZnVuY3Rpb24gZ2V0Vmlld0FuaW1hdGlvbnMoKSB7XG4gICAgcmV0dXJuIGRvY3VtZW50LmdldEFuaW1hdGlvbnMoKS5maWx0ZXIoZmlsdGVyVmlld0FuaW1hdGlvbnMpO1xufVxuXG5leHBvcnQgeyBnZXRWaWV3QW5pbWF0aW9ucyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/has-target.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/view/utils/has-target.mjs ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasTarget: function() { return /* binding */ hasTarget; }\n/* harmony export */ });\nfunction hasTarget(target, targets) {\n    return targets.has(target) && Object.keys(targets.get(target)).length > 0;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdmlldy91dGlscy9oYXMtdGFyZ2V0Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBOztBQUVxQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLWRvbS9kaXN0L2VzL3ZpZXcvdXRpbHMvaGFzLXRhcmdldC5tanM/ZWNhNiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiBoYXNUYXJnZXQodGFyZ2V0LCB0YXJnZXRzKSB7XG4gICAgcmV0dXJuIHRhcmdldHMuaGFzKHRhcmdldCkgJiYgT2JqZWN0LmtleXModGFyZ2V0cy5nZXQodGFyZ2V0KSkubGVuZ3RoID4gMDtcbn1cblxuZXhwb3J0IHsgaGFzVGFyZ2V0IH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/view/utils/has-target.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-utils/dist/es/errors.mjs":
/*!******************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/errors.mjs ***!
  \******************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: function() { return /* binding */ invariant; },\n/* harmony export */   warning: function() { return /* binding */ warning; }\n/* harmony export */ });\n/* harmony import */ var _noop_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./noop.mjs */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/noop.mjs\");\n\n\nlet warning = _noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop;\nlet invariant = _noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop;\nif (true) {\n    warning = (check, message) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(message);\n        }\n    };\n    invariant = (check, message) => {\n        if (!check) {\n            throw new Error(message);\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy9lcnJvcnMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrQzs7QUFFbEMsY0FBYywyQ0FBSTtBQUNsQixnQkFBZ0IsMkNBQUk7QUFDcEIsSUFBSSxJQUFxQztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU4QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvZXJyb3JzLm1qcz9jZjg2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vb3AgfSBmcm9tICcuL25vb3AubWpzJztcblxubGV0IHdhcm5pbmcgPSBub29wO1xubGV0IGludmFyaWFudCA9IG5vb3A7XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgd2FybmluZyA9IChjaGVjaywgbWVzc2FnZSkgPT4ge1xuICAgICAgICBpZiAoIWNoZWNrICYmIHR5cGVvZiBjb25zb2xlICE9PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4obWVzc2FnZSk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGludmFyaWFudCA9IChjaGVjaywgbWVzc2FnZSkgPT4ge1xuICAgICAgICBpZiAoIWNoZWNrKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IobWVzc2FnZSk7XG4gICAgICAgIH1cbiAgICB9O1xufVxuXG5leHBvcnQgeyBpbnZhcmlhbnQsIHdhcm5pbmcgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-utils/dist/es/errors.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/index.mjs ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: function() { return /* reexport safe */ _errors_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant; },\n/* harmony export */   memo: function() { return /* reexport safe */ _memo_mjs__WEBPACK_IMPORTED_MODULE_1__.memo; },\n/* harmony export */   millisecondsToSeconds: function() { return /* reexport safe */ _time_conversion_mjs__WEBPACK_IMPORTED_MODULE_4__.millisecondsToSeconds; },\n/* harmony export */   noop: function() { return /* reexport safe */ _noop_mjs__WEBPACK_IMPORTED_MODULE_2__.noop; },\n/* harmony export */   progress: function() { return /* reexport safe */ _progress_mjs__WEBPACK_IMPORTED_MODULE_3__.progress; },\n/* harmony export */   secondsToMilliseconds: function() { return /* reexport safe */ _time_conversion_mjs__WEBPACK_IMPORTED_MODULE_4__.secondsToMilliseconds; },\n/* harmony export */   warning: function() { return /* reexport safe */ _errors_mjs__WEBPACK_IMPORTED_MODULE_0__.warning; }\n/* harmony export */ });\n/* harmony import */ var _errors_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./errors.mjs */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/errors.mjs\");\n/* harmony import */ var _memo_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./memo.mjs */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/memo.mjs\");\n/* harmony import */ var _noop_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./noop.mjs */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/noop.mjs\");\n/* harmony import */ var _progress_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./progress.mjs */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/progress.mjs\");\n/* harmony import */ var _time_conversion_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./time-conversion.mjs */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/time-conversion.mjs\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2hCO0FBQ0E7QUFDUTtBQUMyQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvaW5kZXgubWpzP2U5MjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgaW52YXJpYW50LCB3YXJuaW5nIH0gZnJvbSAnLi9lcnJvcnMubWpzJztcbmV4cG9ydCB7IG1lbW8gfSBmcm9tICcuL21lbW8ubWpzJztcbmV4cG9ydCB7IG5vb3AgfSBmcm9tICcuL25vb3AubWpzJztcbmV4cG9ydCB7IHByb2dyZXNzIH0gZnJvbSAnLi9wcm9ncmVzcy5tanMnO1xuZXhwb3J0IHsgbWlsbGlzZWNvbmRzVG9TZWNvbmRzLCBzZWNvbmRzVG9NaWxsaXNlY29uZHMgfSBmcm9tICcuL3RpbWUtY29udmVyc2lvbi5tanMnO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-utils/dist/es/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-utils/dist/es/memo.mjs":
/*!****************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/memo.mjs ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memo: function() { return /* binding */ memo; }\n/* harmony export */ });\n/*#__NO_SIDE_EFFECTS__*/\nfunction memo(callback) {\n    let result;\n    return () => {\n        if (result === undefined)\n            result = callback();\n        return result;\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy9tZW1vLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVnQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvbWVtby5tanM/NDlmZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiNfX05PX1NJREVfRUZGRUNUU19fKi9cbmZ1bmN0aW9uIG1lbW8oY2FsbGJhY2spIHtcbiAgICBsZXQgcmVzdWx0O1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGlmIChyZXN1bHQgPT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgIHJlc3VsdCA9IGNhbGxiYWNrKCk7XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfTtcbn1cblxuZXhwb3J0IHsgbWVtbyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-utils/dist/es/memo.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-utils/dist/es/noop.mjs":
/*!****************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/noop.mjs ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   noop: function() { return /* binding */ noop; }\n/* harmony export */ });\n/*#__NO_SIDE_EFFECTS__*/\nconst noop = (any) => any;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy9ub29wLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTs7QUFFZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL21vdGlvbi11dGlscy9kaXN0L2VzL25vb3AubWpzPzllYjkiXSwic291cmNlc0NvbnRlbnQiOlsiLyojX19OT19TSURFX0VGRkVDVFNfXyovXG5jb25zdCBub29wID0gKGFueSkgPT4gYW55O1xuXG5leHBvcnQgeyBub29wIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-utils/dist/es/noop.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-utils/dist/es/progress.mjs":
/*!********************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/progress.mjs ***!
  \********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   progress: function() { return /* binding */ progress; }\n/* harmony export */ });\n/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\n/*#__NO_SIDE_EFFECTS__*/\nconst progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy9wcm9ncmVzcy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVvQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvcHJvZ3Jlc3MubWpzPzlkNGEiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbiAgUHJvZ3Jlc3Mgd2l0aGluIGdpdmVuIHJhbmdlXG5cbiAgR2l2ZW4gYSBsb3dlciBsaW1pdCBhbmQgYW4gdXBwZXIgbGltaXQsIHdlIHJldHVybiB0aGUgcHJvZ3Jlc3NcbiAgKGV4cHJlc3NlZCBhcyBhIG51bWJlciAwLTEpIHJlcHJlc2VudGVkIGJ5IHRoZSBnaXZlbiB2YWx1ZSwgYW5kXG4gIGxpbWl0IHRoYXQgcHJvZ3Jlc3MgdG8gd2l0aGluIDAtMS5cblxuICBAcGFyYW0gW251bWJlcl06IExvd2VyIGxpbWl0XG4gIEBwYXJhbSBbbnVtYmVyXTogVXBwZXIgbGltaXRcbiAgQHBhcmFtIFtudW1iZXJdOiBWYWx1ZSB0byBmaW5kIHByb2dyZXNzIHdpdGhpbiBnaXZlbiByYW5nZVxuICBAcmV0dXJuIFtudW1iZXJdOiBQcm9ncmVzcyBvZiB2YWx1ZSB3aXRoaW4gcmFuZ2UgYXMgZXhwcmVzc2VkIDAtMVxuKi9cbi8qI19fTk9fU0lERV9FRkZFQ1RTX18qL1xuY29uc3QgcHJvZ3Jlc3MgPSAoZnJvbSwgdG8sIHZhbHVlKSA9PiB7XG4gICAgY29uc3QgdG9Gcm9tRGlmZmVyZW5jZSA9IHRvIC0gZnJvbTtcbiAgICByZXR1cm4gdG9Gcm9tRGlmZmVyZW5jZSA9PT0gMCA/IDEgOiAodmFsdWUgLSBmcm9tKSAvIHRvRnJvbURpZmZlcmVuY2U7XG59O1xuXG5leHBvcnQgeyBwcm9ncmVzcyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-utils/dist/es/progress.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-utils/dist/es/time-conversion.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/time-conversion.mjs ***!
  \***************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   millisecondsToSeconds: function() { return /* binding */ millisecondsToSeconds; },\n/* harmony export */   secondsToMilliseconds: function() { return /* binding */ secondsToMilliseconds; }\n/* harmony export */ });\n/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\n/*#__NO_SIDE_EFFECTS__*/\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\n/*#__NO_SIDE_EFFECTS__*/\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy90aW1lLWNvbnZlcnNpb24ubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXdEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tdXRpbHMvZGlzdC9lcy90aW1lLWNvbnZlcnNpb24ubWpzPzFkMGEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb252ZXJ0cyBzZWNvbmRzIHRvIG1pbGxpc2Vjb25kc1xuICpcbiAqIEBwYXJhbSBzZWNvbmRzIC0gVGltZSBpbiBzZWNvbmRzLlxuICogQHJldHVybiBtaWxsaXNlY29uZHMgLSBDb252ZXJ0ZWQgdGltZSBpbiBtaWxsaXNlY29uZHMuXG4gKi9cbi8qI19fTk9fU0lERV9FRkZFQ1RTX18qL1xuY29uc3Qgc2Vjb25kc1RvTWlsbGlzZWNvbmRzID0gKHNlY29uZHMpID0+IHNlY29uZHMgKiAxMDAwO1xuLyojX19OT19TSURFX0VGRkVDVFNfXyovXG5jb25zdCBtaWxsaXNlY29uZHNUb1NlY29uZHMgPSAobWlsbGlzZWNvbmRzKSA9PiBtaWxsaXNlY29uZHMgLyAxMDAwO1xuXG5leHBvcnQgeyBtaWxsaXNlY29uZHNUb1NlY29uZHMsIHNlY29uZHNUb01pbGxpc2Vjb25kcyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-utils/dist/es/time-conversion.mjs\n"));

/***/ })

}]);