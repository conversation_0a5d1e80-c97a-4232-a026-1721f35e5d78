"use client"
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import MainContentLayout from "../seekers-content-layout/main-content-layout";
import DefaultLayoutContent from "../seekers-content-layout/default-layout-content";
import SeekerSeoArticleContent from "./seeker-seo-article-content";
import { SeoContent } from "@/core/services/sanity/types";
import Link from "next/link";

interface ContentProps {
  id: string
  title: string,
  content: string
  url: string,

}
export default function SeekersSeoContent({ content }: { content: SeoContent }) {
  const t = useTranslations("seeker")
  const SellsPropertyContent: ContentProps[] = [
    {
      id: "luxury-villas",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.one.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.one.description'),
      url: "/s/all?t=VILLA&c=PLACE_TO_LIVE&sc=VILLA"
    },
    {
      id: "affordable-homes",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.two.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.two.description'),
      url: "/s/all?t=APARTMENT&c=PLACE_TO_LIVE&sc=APARTMENT&maxp=500000000"
    },
    {
      id: "ocean-view-villas",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.three.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.three.description'),
      url: "/s/all?t=VILLA&v=OCEAN"
    },
    {
      id: "mountain-retreat-villas",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.four.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.four.description'),
      url: "/s/all?t=VILLA&v=MOUNTAIN"
    },
    {
      id: "ricefield-view-homes",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.five.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.five.description'),
      url: "/s/all?t=VILLA&v=RICEFIELD"
    },
    {
      id: "jungle-escape-villas",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.six.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.six.description'),
      url: "/s/all?t=VILLA&v=JUNGLE"
    },
    {
      id: "pet-friendly-houses",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.seven.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.seven.description'),
      url: "/s/all?t=VILLA&feat=PET_ALLOWED"
    },
    {
      id: "balcony-view-apartments",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.eight.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.eight.description'),
      url: "/s/all?t=APARTMENT&feat=BALCONY"
    },
    {
      id: "spacious-land",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.nine.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.nine.description'),
      url: "/s/all?t=LAND"
    },
    {
      id: "beachfront-properties",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.ten.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.ten.description'),
      url: "/s/all?t=VILLA&v=OCEAN"
    },
    {
      id: "furnished-homes",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.eleven.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.eleven.description'),
      url: "/s/all?t=VILLA&fs=FURNISHED"
    },
    {
      id: "private-garden-villas",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.twelve.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.twelve.description'),
      url: "/s/all?t=VILLA&feat=GARDEN_BACKYARD"
    },
    {
      id: "rooftop-terrace-homes",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.thirteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.thirteen.description'),
      url: "/s/all?t=VILLA&feat=ROOFTOP_TERRACE"
    },
    {
      id: "business-property",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.fourteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.fourteen.description'),
      url: "/s/all?t=COMMERCIAL_SPACE"
    },
    {
      id: "small-business-space",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.fifteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.fifteen.description'),
      url: "/s/all?t=COMMERCIAL_SPACE&sc=SMALL"
    },
    {
      id: "medium-office-space",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.sixteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.sixteen.description'),
      url: "/s/all?t=OFFICE&sc=MEDIUM"
    },
    {
      id: "large-commercial-units",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.seventeen.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.seventeen.description'),
      url: "/s/all?t=COMMERCIAL_SPACE&sc=LARGE"
    },
    {
      id: "recently-renovated-homes",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.eighteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.eighteen.description'),
      url: "/s/all?t=VILLA&pc=RECENTLY_RENOVATED"
    },
    {
      id: "sublease-allowed-villas",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.nineteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.nineteen.description'),
      url: "/s/all?t=VILLA&pc=SUBLEASE_ALLOWED"
    },
    {
      id: "municipal-water-homes",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.twenty.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.twenty.description'),
      url: "/s/all?t=VILLA&pc=MUNICIPAL_WATERWORK"
    },
    {
      id: "newly-built-villas",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.twentyOne.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.twentyOne.description'),
      url: "/s/all?t=VILLA&yob=CURRENT"
    },
    {
      id: "homes-with-bathtub",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.twentyTwo.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.twentyTwo.description'),
      url: "/s/all?t=VILLA&feat=BATHUB"
    },
    {
      id: "private-parking-space",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.twentyThree.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.twentyThree.description'),
      url: "/s/all?t=VILLA&pk=PRIVATE"
    },
    {
      id: "swimming-pool-homes",
      title: t('seekersLandingPage.seo.tabs.optionOne.content.twentyFour.title'),
      content: t('seekersLandingPage.seo.tabs.optionOne.content.twentyFour.description'),
      url: "/s/all?t=VILLA&sp=AVAILABLE"
    }
  ];
  ;
  const shortTermHolidayRentalContent: ContentProps[] = [
    {
      id: "affordable-rentals",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.one.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.one.description'),
      url: "/s/all?t=APARTMENT&c=PLACE_TO_LIVE"
    },
    {
      id: "furnished-rentals",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.two.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.two.description'),
      url: "/s/all?t=VILLA&fs=FURNISHED"
    },
    {
      id: "pet-friendly-rentals",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.three.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.three.description'),
      url: "/s/all?t=VILLA&feat=PET_ALLOWED"
    },
    {
      id: "monthly-rental-homes",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.four.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.four.description'),
      url: "/s/all?t=VILLA&minc=BETWEEN_1_3"
    },
    {
      id: "one-year-lease",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.five.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.five.description'),
      url: "/s/all?t=VILLA&minc=BETWEEN_1_3"
    },
    {
      id: "long-term-villa",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.six.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.six.description'),
      url: "/s/all?t=VILLA&minc=GREATER_THAN_5"
    },
    {
      id: "yearly-lease-apartments",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.seven.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.seven.description'),
      url: "/s/all?t=APARTMENT&minc=BETWEEN_1_3"
    },
    {
      id: "affordable-studio",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.eight.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.eight.description'),
      url: "/s/all?t=ROOM&c=PLACE_TO_LIVE"
    },
    {
      id: "garden-view-apartments",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.nine.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.nine.description'),
      url: "/s/all?t=APARTMENT&feat=GARDEN_BACKYARD"
    },
    {
      id: "rooftop-terrace-rentals",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.ten.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.ten.description'),
      url: "/s/all?t=APARTMENT&feat=ROOFTOP_TERRACE"
    },
    {
      id: "ricefield-view-rentals",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.eleven.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.eleven.description'),
      url: "/s/all?t=VILLA&v=RICEFIELD"
    },
    {
      id: "large-family-villas",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.twelve.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.twelve.description'),
      url: "/s/all?t=VILLA&bedt=4"
    },
    {
      id: "budget-friendly-rentals",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.thirteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.thirteen.description'),
      url: "/s/all?t=APARTMENT&maxp=500000000"
    },
    {
      id: "private-pool-rentals",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.fourteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.fourteen.description'),
      url: "/s/all?t=VILLA&sp=AVAILABLE"
    },
    {
      id: "parking-included",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.fifteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.fifteen.description'),
      url: "/s/all?t=VILLA&pk=PRIVATE"
    },
    {
      id: "sublease-allowed",
      title: t('seekersLandingPage.seo.tabs.optionTwo.content.sixteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionTwo.content.sixteen.description'),
      url: "/s/all?t=VILLA&pc=SUBLEASE_ALLOWED"
    }
  ];

  const digitalNomadInvestmentContent: ContentProps[] = [
    {
      id: "wifi-ready-rentals",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.one.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.one.description'),
      url: "/s/all?t=VILLA&ro=WIFI"
    },
    {
      id: "coworking-nearby",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.two.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.two.description'),
      url: "/s/all?t=OFFICE&sc=SMALL"
    },
    {
      id: "remote-work-villas",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.three.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.three.description'),
      url: "/s/all?t=VILLA&ro=WIFI"
    },
    {
      id: "digital-nomad-studios",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.four.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.four.description'),
      url: "/s/all?t=ROOM&ro=WIFI"
    },
    {
      id: "monthly-coworking-rentals",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.five.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.five.description'),
      url: "/s/all?t=APARTMENT&ro=WIFI"
    },
    {
      id: "beachfront-workspaces",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.six.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.six.description'),
      url: "/s/all?t=OFFICE&v=OCEAN"
    },
    {
      id: "jungle-office-spaces",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.seven.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.seven.description'),
      url: "/s/all?t=OFFICE&v=JUNGLE"
    },
    {
      id: "private-office-rentals",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.eight.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.eight.description'),
      url: "/s/all?t=OFFICE&sc=SMALL"
    },
    {
      id: "bali-rooftop-workspaces",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.nine.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.nine.description'),
      url: "/s/all?t=OFFICE&feat=ROOFTOP_TERRACE"
    },
    {
      id: "nomad-friendly-apartments",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.ten.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.ten.description'),
      url: "/s/all?t=APARTMENT&ro=WIFI"
    },
    {
      id: "co-living-spaces",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.eleven.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.eleven.description'),
      url: "/s/all?t=APARTMENT"
    },
    {
      id: "minimalist-work-homes",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.twelve.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.twelve.description'),
      url: "/s/all?t=VILLA&fs=FURNISHED"
    },
    {
      id: "business-hub-spaces",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.thirteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.thirteen.description'),
      url: "/s/all?t=COMMERCIAL_SPACE&sc=MEDIUM"
    },
    {
      id: "creative-studio-rentals",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.fourteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.fourteen.description'),
      url: "/s/all?t=OFFICE&sc=SMALL"
    },
    {
      id: "long-term-nomad-stays",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.fifteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.fifteen.description'),
      url: "/s/all?t=APARTMENT&minc=BETWEEN_1_3"
    },
    {
      id: "quiet-work-retreats",
      title: t('seekersLandingPage.seo.tabs.optionThree.content.sixteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionThree.content.sixteen.description'),
      url: "/s/all?t=VILLA&v=MOUNTAIN"
    }
  ];

  const offPlanDevelopmentsContent: ContentProps[] = [
    {
      id: "future-developments",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.one.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.one.description'),
      url: "/s/all?t=VILLA&yob=CURRENT"
    },
    {
      id: "investment-properties",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.two.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.two.description'),
      url: "/s/all?t=COMMERCIAL_SPACE"
    },
    {
      id: "pre-construction-villas",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.three.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.three.description'),
      url: "/s/all?t=VILLA&yob=CURRENT"
    },
    {
      id: "off-plan-luxury-homes",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.four.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.four.description'),
      url: "/s/all?t=VILLA&yob=CURRENT&sc=VILLA"
    },
    {
      id: "future-apartments",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.five.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.five.description'),
      url: "/s/all?t=APARTMENT&yob=CURRENT"
    },
    {
      id: "beachfront-off-plan",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.six.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.six.description'),
      url: "/s/all?t=VILLA&yob=CURRENT&v=OCEAN"
    },
    {
      id: "mountain-view-developments",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.seven.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.seven.description'),
      url: "/s/all?t=VILLA&yob=CURRENT&v=MOUNTAIN"
    },
    {
      id: "ricefield-view-developments",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.eight.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.eight.description'),
      url: "/s/all?t=VILLA&yob=CURRENT&v=RICEFIELD"
    },
    {
      id: "jungle-off-plan",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.nine.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.nine.description'),
      url: "/s/all?t=VILLA&yob=CURRENT&v=JUNGLE"
    },
    {
      id: "private-garden-estates",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.ten.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.ten.description'),
      url: "/s/all?t=VILLA&yob=CURRENT&feat=GARDEN_BACKYARD"
    },
    {
      id: "smart-homes",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.eleven.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.eleven.description'),
      url: "/s/all?t=VILLA&yob=CURRENT&feat=SMART_HOME"
    },
    {
      id: "rooftop-terrace-developments",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.twelve.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.twelve.description'),
      url: "/s/all?t=VILLA&yob=CURRENT&feat=ROOFTOP_TERRACE"
    },
    {
      id: "off-plan-business",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.thirteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.thirteen.description'),
      url: "/s/all?t=COMMERCIAL_SPACE&yob=CURRENT"
    },
    {
      id: "sustainable-projects",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.fourteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.fourteen.description'),
      url: "/s/all?t=VILLA&yob=CURRENT&feat=SOLAR_ENERGY"
    },
    {
      id: "high-rise-developments",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.fifteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.fifteen.description'),
      url: "/s/all?t=APARTMENT&yob=CURRENT"
    },
    {
      id: "sublease-developments",
      title: t('seekersLandingPage.seo.tabs.optionFour.content.sixteen.title'),
      content: t('seekersLandingPage.seo.tabs.optionFour.content.sixteen.description'),
      url: "/s/all?t=VILLA&yob=CURRENT&pc=SUBLEASE_ALLOWED"
    }
  ];

  const overrideClassName = "text-sm text-seekers-text-light font-normal data-[state=active]:text-seekers-primary data-[state=active]:border-b-seekers-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:font-semibold data-[state=active]:border-b h-full rounded-b-none p-0"
  return <div className=" text-black w-full border-b border-seekers-text-lighter mt-12">
    <MainContentLayout>
      <DefaultLayoutContent title={t('seekersLandingPage.seo.title')}>
        <Tabs defaultValue="sell-property" className="w-full overflow-hidden">
          <ScrollArea>
            <TabsList className="bg-transparent justify-start overflow-y-hidden border-b w-full rounded-b-none gap-6 p-0">
              <TabsTrigger className={overrideClassName} value="sell-property">{t('seekersLandingPage.seo.tabs.optionOne.title')}</TabsTrigger>
              <TabsTrigger className={overrideClassName} value="short-term-and-holiday-rental">{t('seekersLandingPage.seo.tabs.optionTwo.title')}</TabsTrigger>
              <TabsTrigger className={overrideClassName} value="investment-for-digital-nomads">{t('seekersLandingPage.seo.tabs.optionThree.title')}</TabsTrigger>
              <TabsTrigger className={overrideClassName} value="off-plan-development">{t('seekersLandingPage.seo.tabs.optionFour.title')}</TabsTrigger>
            </TabsList>
            <div className="h-2"></div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
          <TabsContent value="sell-property" >
            <Content urls={SellsPropertyContent} content={content} />
          </TabsContent>
          <TabsContent value="investment-for-digital-nomads">
            <Content urls={digitalNomadInvestmentContent} content={content} />
          </TabsContent>
          <TabsContent value="off-plan-development">
            <Content urls={offPlanDevelopmentsContent} content={content} />
          </TabsContent>
          <TabsContent value="short-term-and-holiday-rental">
            <Content urls={shortTermHolidayRentalContent} content={content} />

          </TabsContent>
        </Tabs>
      </DefaultLayoutContent>
    </MainContentLayout>
  </div>
}

function Content({ urls, content }: { urls: ContentProps[], content: SeoContent }) {
  const t = useTranslations()
  const isMobile = useMediaQuery("(max-width:480px)")
  const [formattedUrl, setFormattedUrl] = useState(urls)
  const [isExpanded, setIsExpanded] = useState(false)
  const locale = useLocale()
  useEffect(() => {
    if (isMobile) {
      const newUrl = urls.slice(0, 8)
      setFormattedUrl(newUrl)
    } else {
      setFormattedUrl(urls)
    }
  }, [isMobile, urls])

  return (
    <>
      <div className="w-full grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-2 md:gap-x-6 gap-y-8 py-6">
        {formattedUrl.map((item, idx) => (
          <Button
            key={idx}
            variant={"link"}
            size={"sm"}
            className="text-start w-full overflow-hidden justify-start pl-0 text-black"
            asChild
          >
            <Link href={item.url} hrefLang={locale}>
              <div className="flex flex-col w-full overflow-hidden">
                <p className="text-sm font-semibold text-seekers-text">{item.title}</p>
                <p className="text-seekers-text-light font-normal line-clamp-1 w-full">{item.content}</p>
              </div>
            </Link>
          </Button>
        ))}
      </div>

      <SeekerSeoArticleContent content={content} />
    </>
  )
}