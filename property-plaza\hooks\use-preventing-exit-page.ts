import { useSettingStore } from "@/stores/setting.store";
import { useTranslations } from "next-intl";

export default function usePreventingExitPage(){
  const t = useTranslations()
  const { editingStatus,removeEditingStatus } = useSettingStore(state => state)
  const handlePreventingExitPage = () => {
    if(editingStatus.length > 0){
      const userConfirmed = confirm("Are you sure you want to leave the page?");
      if(userConfirmed){
        removeEditingStatus()
      }
      return userConfirmed
    }else{
      return true
    }
  }
  return {handlePreventingExitPage}
}