import { usePathname, useSearchParams } from "next/navigation";
import { useCallback } from "react";
import { useRouter } from "nextjs-toploader/app";
export default function useSearchParamWrapper() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Get a new searchParams string by merging the current
  // searchParams with a provided key/value pair
  const createQueryString = (name: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set(name, value);
    router.push(pathname + "?" + params.toString());
  };
  const updateQuery = (name: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set(name, value);
    router.push(pathname + "?" + params.toString());
  };
  const createMultipleQueryString = useCallback(
    (params: { name: string; value: string }[]) => {
      const param = new URLSearchParams(searchParams.toString());
      params.forEach((item) => param.set(item.name, item.value));
      router.push(pathname + "?" + param.toString());
    },
    [searchParams, router, pathname]
  );
  // return a new searchParams string by merging the current
  const generateQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set(name, value);
      return params.toString();
    },
    [searchParams]
  );
  const removeQueryParam = (keys: string[], hardReload?: boolean) => {
    const params = new URLSearchParams(searchParams.toString());
    keys.forEach((element) => {
      params.delete(element);
    });
    const newUrl = `${window.location.pathname}?${params.toString()}`;

    if (hardReload) {
      return (window.location.href = newUrl);
    }
    // Navigate to the new URL
    router.push(newUrl);
  };

  return {
    searchParams,
    createQueryString,
    generateQueryString,
    removeQueryParam,
    createMultipleQueryString,
    pathname,
    updateQuery,
  };
}
