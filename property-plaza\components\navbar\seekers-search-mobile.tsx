import useSeekersSearch from "@/hooks/use-seekers-search"
import { Search } from "lucide-react"
import { useTranslations } from "next-intl"
import { Button } from "../ui/button"
import LocationSearch from "./location-search/location-search-form"
import { Input } from "../ui/input"
import { useSeekerSearchStore } from "@/stores/seeker-search.store"

export default function SeekersSearchMobile() {
  const { query } = useSeekerSearchStore(state => state)

  const { handleSetQuery, handleSearch } = useSeekersSearch()

  const t = useTranslations()
  return <div className="w-full border h-10 pl-4 pr-1 flex items-center text-seekers-text-light text-xs rounded-full border-seekers-text-lighter shadow-md">
    <LocationSearch
      isUseAnimation={false}
      customTrigger={<Input
        onChange={(e) => handleSetQuery(e.target.value)}
        value={query}
        placeholder={t('seeker.listing.search.placeholder')}
        className="border-0 placeholder:text-seekers-text-lighter focus:outline-none shadow-none focus-visible:ring-0 focus-visible:border-b rounded-none pb-2 !p-0 h-fit"
        onKeyDown={(e) => {
          e.stopPropagation()
          if (e.key === "Enter") {
            handleSearch()
          }
        }}
      />
      }
    />
    <Button variant={"default-seekers"} onClick={() => handleSearch()} className="rounded-full !h-8 !w-[2.25rem]" size={"icon"}>
      <Search className="!w-4 !h-4" strokeWidth={3} />
    </Button>
  </div>
}