import { TypeTransaction } from "@/core/domain/transaction/transaction";
import { BasePaginationRequest } from "@/types/base";

export interface TopUpDto {
  description?: string;
  id: string;
  name: string;
  point: number;
  price: number;
}

export interface PromoDto {
  code: string;
  package_id: string;
  quantity: number;
}

export interface CreateTopUpCreditDto {
  package_id: string;
  quantity: number;
  given_names: string;
  email: string;
  phone_number: string;
  streat_line1: string;
  postal_code: string;
  promo_code?: string;
}

export interface TransactionDto {
  code: string;
  grand_total: number;
  date: string;
  type: string;
  status: string;
  created_at: string;
  updated_at: string;
  items: {
    name: string;
    price: string;
    quantity: number;
    detail: {
      cost?: number;
      name: string;
      point?: number;
    };
  }[];
  url?: string;
}

export interface TransactionMetadataDto {
  currency: string;
  created_at: number;
  invoice_url: string;
  customer_name: string;
  customer_email: string;
  invoice_ref_id: string;
  customer_ref_id: string;
  period_end_date: number;
  period_start_date: number;
  subscription_ref_id: string;
  subscription_status?: string | null;
  period_end_date_text: string;
  customer_billing_city: string;
  customer_phone_number: string;
  subscription_metadata: {
    user_id: string;
    price_id: string;
    condition: string;
    account_id: string;
    product_id: string;
    billing_cycle: string;
    internal_invoice: string;
  };
  customer_billing_line1: string;
  customer_billing_line2: string;
  customer_billing_state: string;
  period_start_date_text: string;
  customer_billing_reason: string;
  customer_billing_country: string;
  customer_billing_postal_code: string;
}
export interface TransactionSeekerDto {
  code: string;
  grand_total: number;
  date: string;
  type: string;
  status: string;
  url: string;
  promo_code: string | null;
  promo_value: string | null;
  promo_type: string | null;
  created_at: string;
  metadata: TransactionMetadataDto;
  updated_at: string;
  items: {
    ref_id: string;
    name: string;
    quantity: number;
    price: number;
    point: number;
  }[];
}

export interface TransactionItemDto {
  ref_id: string;
  name: string;
  quantity: number;
  total: number;
}
export interface TransactionDetailDto {
  code: string;
  grand_total: number;
  date: string;
  type: string;
  status: string;
  created_at: string;
  updated_at: string;
  items: TransactionItemDto[];
  url: string;
}

export interface GetAllTransactionDto extends BasePaginationRequest {
  start_date?: string;
  end_date?: string;
  type?: string;
}

export interface TopUpDetailDto {
  id: string;
  name: string;
  description: string;
  point: number;
  price: number;
}

export interface CardBillingAddressDto {
  city: string;
  country: string;
  line1: string;
  line2: string;
  postal_code: string;
  state: string;
}

export interface PutCardBillingAddressDto {
  city: string;
  country: string;
  line1: string;
  line2?: string;
  postal_code: string;
  state: string;
  name: string;
}

export interface PaymentMethodDto {
  id: string;
  brand: string;
  display_brand: string;
  type: string;
  card_number: string;
  card_country: string;
  card_exp_month: number;
  card_exp_year: number;
  card_funding: string;
  card_billing: {
    address: {
      city: string;
      country: string;
      line1: string;
      line2: string;
      postal_code: number;
      state: string;
    };
    email: string;
    name: string;
    phone: string | null;
  };
  card_check: {
    address_line1_check: string;
    address_postal_code_check: string;
    cvc_check: string;
  };
  is_default: boolean;
}

export interface PutPaymentMethod {
  payment_method_id: string;
  request_for: "SET_DEFAULT" | "ADD" | "REMOVE";
}
