import {getRequestConfig} from 'next-intl/server';
import {routing} from './routing';

export const locales = ['en', 'id'] as const;
export const supportedLocales = ['en', 'id']
export const localePrefix = 'as-needed';

export default getRequestConfig(async ({requestLocale}) => {
  const locale = await requestLocale 
  return {
    locale: await requestLocale,
    messages: (await import(`../../locales/${locale}.json`)).default,
    defaultLocale: routing.defaultLocale,
    locales: routing.locales
  };
});