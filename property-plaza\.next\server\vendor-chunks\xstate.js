"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xstate";
exports.ids = ["vendor-chunks/xstate"];
exports.modules = {

/***/ "(ssr)/./node_modules/xstate/actors/dist/xstate-actors.development.esm.js":
/*!**************************************************************************!*\
  !*** ./node_modules/xstate/actors/dist/xstate-actors.development.esm.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEmptyActor: () => (/* binding */ createEmptyActor),\n/* harmony export */   fromCallback: () => (/* binding */ fromCallback),\n/* harmony export */   fromEventObservable: () => (/* binding */ fromEventObservable),\n/* harmony export */   fromObservable: () => (/* binding */ fromObservable),\n/* harmony export */   fromPromise: () => (/* binding */ fromPromise),\n/* harmony export */   fromTransition: () => (/* binding */ fromTransition)\n/* harmony export */ });\n/* harmony import */ var _dist_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../dist/raise-1db27a82.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/raise-1db27a82.development.esm.js\");\n/* harmony import */ var _dev_dist_xstate_dev_development_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../dev/dist/xstate-dev.development.esm.js */ \"(ssr)/./node_modules/xstate/dev/dist/xstate-dev.development.esm.js\");\n\n\n\n/**\n * Represents an actor created by `fromTransition`.\n *\n * The type of `self` within the actor's logic.\n *\n * @example\n *\n * ```ts\n * import {\n *   fromTransition,\n *   createActor,\n *   type AnyActorSystem\n * } from 'xstate';\n *\n * //* The actor's stored context.\n * type Context = {\n *   // The current count.\n *   count: number;\n *   // The amount to increase `count` by.\n *   step: number;\n * };\n * // The events the actor receives.\n * type Event = { type: 'increment' };\n * // The actor's input.\n * type Input = { step?: number };\n *\n * // Actor logic that increments `count` by `step` when it receives an event of\n * // type `increment`.\n * const logic = fromTransition<Context, Event, AnyActorSystem, Input>(\n *   (state, event, actorScope) => {\n *     actorScope.self;\n *     //         ^? TransitionActorRef<Context, Event>\n *\n *     if (event.type === 'increment') {\n *       return {\n *         ...state,\n *         count: state.count + state.step\n *       };\n *     }\n *     return state;\n *   },\n *   ({ input, self }) => {\n *     self;\n *     // ^? TransitionActorRef<Context, Event>\n *\n *     return {\n *       count: 0,\n *       step: input.step ?? 1\n *     };\n *   }\n * );\n *\n * const actor = createActor(logic, { input: { step: 10 } });\n * //    ^? TransitionActorRef<Context, Event>\n * ```\n *\n * @see {@link fromTransition}\n */\n\n/**\n * Returns actor logic given a transition function and its initial state.\n *\n * A “transition function” is a function that takes the current `state` and\n * received `event` object as arguments, and returns the next state, similar to\n * a reducer.\n *\n * Actors created from transition logic (“transition actors”) can:\n *\n * - Receive events\n * - Emit snapshots of its state\n *\n * The transition function’s `state` is used as its transition actor’s\n * `context`.\n *\n * Note that the \"state\" for a transition function is provided by the initial\n * state argument, and is not the same as the State object of an actor or a\n * state within a machine configuration.\n *\n * @example\n *\n * ```ts\n * const transitionLogic = fromTransition(\n *   (state, event) => {\n *     if (event.type === 'increment') {\n *       return {\n *         ...state,\n *         count: state.count + 1\n *       };\n *     }\n *     return state;\n *   },\n *   { count: 0 }\n * );\n *\n * const transitionActor = createActor(transitionLogic);\n * transitionActor.subscribe((snapshot) => {\n *   console.log(snapshot);\n * });\n * transitionActor.start();\n * // => {\n * //   status: 'active',\n * //   context: { count: 0 },\n * //   ...\n * // }\n *\n * transitionActor.send({ type: 'increment' });\n * // => {\n * //   status: 'active',\n * //   context: { count: 1 },\n * //   ...\n * // }\n * ```\n *\n * @param transition The transition function used to describe the transition\n *   logic. It should return the next state given the current state and event.\n *   It receives the following arguments:\n *\n *   - `state` - the current state.\n *   - `event` - the received event.\n *   - `actorScope` - the actor scope object, with properties like `self` and\n *       `system`.\n *\n * @param initialContext The initial state of the transition function, either an\n *   object representing the state, or a function which returns a state object.\n *   If a function, it will receive as its only argument an object with the\n *   following properties:\n *\n *   - `input` - the `input` provided to its parent transition actor.\n *   - `self` - a reference to its parent transition actor.\n *\n * @returns Actor logic\n * @see {@link https://stately.ai/docs/input | Input docs} for more information about how input is passed\n */\nfunction fromTransition(transition, initialContext) {\n  return {\n    config: transition,\n    transition: (snapshot, event, actorScope) => {\n      return {\n        ...snapshot,\n        context: transition(snapshot.context, event, actorScope)\n      };\n    },\n    getInitialSnapshot: (_, input) => {\n      return {\n        status: 'active',\n        output: undefined,\n        error: undefined,\n        context: typeof initialContext === 'function' ? initialContext({\n          input\n        }) : initialContext\n      };\n    },\n    getPersistedSnapshot: snapshot => snapshot,\n    restoreSnapshot: snapshot => snapshot\n  };\n}\n\nconst instanceStates = /* #__PURE__ */new WeakMap();\n\n/**\n * Represents an actor created by `fromCallback`.\n *\n * The type of `self` within the actor's logic.\n *\n * @example\n *\n * ```ts\n * import { fromCallback, createActor } from 'xstate';\n *\n * // The events the actor receives.\n * type Event = { type: 'someEvent' };\n * // The actor's input.\n * type Input = { name: string };\n *\n * // Actor logic that logs whenever it receives an event of type `someEvent`.\n * const logic = fromCallback<Event, Input>(({ self, input, receive }) => {\n *   self;\n *   // ^? CallbackActorRef<Event, Input>\n *\n *   receive((event) => {\n *     if (event.type === 'someEvent') {\n *       console.log(`${input.name}: received \"someEvent\" event`);\n *       // logs 'myActor: received \"someEvent\" event'\n *     }\n *   });\n * });\n *\n * const actor = createActor(logic, { input: { name: 'myActor' } });\n * //    ^? CallbackActorRef<Event, Input>\n * ```\n *\n * @see {@link fromCallback}\n */\n\n/**\n * An actor logic creator which returns callback logic as defined by a callback\n * function.\n *\n * @remarks\n * Useful for subscription-based or other free-form logic that can send events\n * back to the parent actor.\n *\n * Actors created from callback logic (“callback actors”) can:\n *\n * - Receive events via the `receive` function\n * - Send events to the parent actor via the `sendBack` function\n *\n * Callback actors are a bit different from other actors in that they:\n *\n * - Do not work with `onDone`\n * - Do not produce a snapshot using `.getSnapshot()`\n * - Do not emit values when used with `.subscribe()`\n * - Can not be stopped with `.stop()`\n *\n * @example\n *\n * ```typescript\n * const callbackLogic = fromCallback(({ sendBack, receive }) => {\n *   let lockStatus = 'unlocked';\n *\n *   const handler = (event) => {\n *     if (lockStatus === 'locked') {\n *       return;\n *     }\n *     sendBack(event);\n *   };\n *\n *   receive((event) => {\n *     if (event.type === 'lock') {\n *       lockStatus = 'locked';\n *     } else if (event.type === 'unlock') {\n *       lockStatus = 'unlocked';\n *     }\n *   });\n *\n *   document.body.addEventListener('click', handler);\n *\n *   return () => {\n *     document.body.removeEventListener('click', handler);\n *   };\n * });\n * ```\n *\n * @param callback - The callback function used to describe the callback logic\n *   The callback function is passed an object with the following properties:\n *\n *   - `receive` - A function that can send events back to the parent actor; the\n *       listener is then called whenever events are received by the callback\n *       actor\n *   - `sendBack` - A function that can send events back to the parent actor\n *   - `input` - Data that was provided to the callback actor\n *   - `self` - The parent actor of the callback actor\n *   - `system` - The actor system to which the callback actor belongs The callback\n *       function can (optionally) return a cleanup function, which is called\n *       when the actor is stopped.\n *\n * @returns Callback logic\n * @see {@link CallbackLogicFunction} for more information about the callback function and its object argument\n * @see {@link https://stately.ai/docs/input | Input docs} for more information about how input is passed\n */\nfunction fromCallback(callback) {\n  const logic = {\n    config: callback,\n    start: (state, actorScope) => {\n      const {\n        self,\n        system,\n        emit\n      } = actorScope;\n      const callbackState = {\n        receivers: undefined,\n        dispose: undefined\n      };\n      instanceStates.set(self, callbackState);\n      callbackState.dispose = callback({\n        input: state.input,\n        system,\n        self,\n        sendBack: event => {\n          if (self.getSnapshot().status === 'stopped') {\n            return;\n          }\n          if (self._parent) {\n            system._relay(self, self._parent, event);\n          }\n        },\n        receive: listener => {\n          callbackState.receivers ??= new Set();\n          callbackState.receivers.add(listener);\n        },\n        emit\n      });\n    },\n    transition: (state, event, actorScope) => {\n      const callbackState = instanceStates.get(actorScope.self);\n      if (event.type === _dist_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.X) {\n        state = {\n          ...state,\n          status: 'stopped',\n          error: undefined\n        };\n        callbackState.dispose?.();\n        return state;\n      }\n      callbackState.receivers?.forEach(receiver => receiver(event));\n      return state;\n    },\n    getInitialSnapshot: (_, input) => {\n      return {\n        status: 'active',\n        output: undefined,\n        error: undefined,\n        input\n      };\n    },\n    getPersistedSnapshot: snapshot => snapshot,\n    restoreSnapshot: snapshot => snapshot\n  };\n  return logic;\n}\n\nconst XSTATE_OBSERVABLE_NEXT = 'xstate.observable.next';\nconst XSTATE_OBSERVABLE_ERROR = 'xstate.observable.error';\nconst XSTATE_OBSERVABLE_COMPLETE = 'xstate.observable.complete';\n\n/**\n * Represents an actor created by `fromObservable` or `fromEventObservable`.\n *\n * The type of `self` within the actor's logic.\n *\n * @example\n *\n * ```ts\n * import { fromObservable, createActor } from 'xstate';\n * import { interval } from 'rxjs';\n *\n * // The type of the value observed by the actor's logic.\n * type Context = number;\n * // The actor's input.\n * type Input = { period?: number };\n *\n * // Actor logic that observes a number incremented every `input.period`\n * // milliseconds (default: 1_000).\n * const logic = fromObservable<Context, Input>(({ input, self }) => {\n *   self;\n *   // ^? ObservableActorRef<Event, Input>\n *\n *   return interval(input.period ?? 1_000);\n * });\n *\n * const actor = createActor(logic, { input: { period: 2_000 } });\n * //    ^? ObservableActorRef<Event, Input>\n * ```\n *\n * @see {@link fromObservable}\n * @see {@link fromEventObservable}\n */\n\n/**\n * Observable actor logic is described by an observable stream of values. Actors\n * created from observable logic (“observable actors”) can:\n *\n * - Emit snapshots of the observable’s emitted value\n *\n * The observable’s emitted value is used as its observable actor’s `context`.\n *\n * Sending events to observable actors will have no effect.\n *\n * @example\n *\n * ```ts\n * import { fromObservable, createActor } from 'xstate';\n * import { interval } from 'rxjs';\n *\n * const logic = fromObservable((obj) => interval(1000));\n *\n * const actor = createActor(logic);\n *\n * actor.subscribe((snapshot) => {\n *   console.log(snapshot.context);\n * });\n *\n * actor.start();\n * // At every second:\n * // Logs 0\n * // Logs 1\n * // Logs 2\n * // ...\n * ```\n *\n * @param observableCreator A function that creates an observable. It receives\n *   one argument, an object with the following properties:\n *\n *   - `input` - Data that was provided to the observable actor\n *   - `self` - The parent actor\n *   - `system` - The actor system to which the observable actor belongs\n *\n *   It should return a {@link Subscribable}, which is compatible with an RxJS\n *   Observable, although RxJS is not required to create them.\n * @see {@link https://rxjs.dev} for documentation on RxJS Observable and observable creators.\n * @see {@link Subscribable} interface in XState, which is based on and compatible with RxJS Observable.\n */\nfunction fromObservable(observableCreator) {\n  // TODO: add event types\n  const logic = {\n    config: observableCreator,\n    transition: (snapshot, event) => {\n      if (snapshot.status !== 'active') {\n        return snapshot;\n      }\n      switch (event.type) {\n        case XSTATE_OBSERVABLE_NEXT:\n          {\n            const newSnapshot = {\n              ...snapshot,\n              context: event.data\n            };\n            return newSnapshot;\n          }\n        case XSTATE_OBSERVABLE_ERROR:\n          return {\n            ...snapshot,\n            status: 'error',\n            error: event.data,\n            input: undefined,\n            _subscription: undefined\n          };\n        case XSTATE_OBSERVABLE_COMPLETE:\n          return {\n            ...snapshot,\n            status: 'done',\n            input: undefined,\n            _subscription: undefined\n          };\n        case _dist_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.X:\n          snapshot._subscription.unsubscribe();\n          return {\n            ...snapshot,\n            status: 'stopped',\n            input: undefined,\n            _subscription: undefined\n          };\n        default:\n          return snapshot;\n      }\n    },\n    getInitialSnapshot: (_, input) => {\n      return {\n        status: 'active',\n        output: undefined,\n        error: undefined,\n        context: undefined,\n        input,\n        _subscription: undefined\n      };\n    },\n    start: (state, {\n      self,\n      system,\n      emit\n    }) => {\n      if (state.status === 'done') {\n        // Do not restart a completed observable\n        return;\n      }\n      state._subscription = observableCreator({\n        input: state.input,\n        system,\n        self,\n        emit\n      }).subscribe({\n        next: value => {\n          system._relay(self, self, {\n            type: XSTATE_OBSERVABLE_NEXT,\n            data: value\n          });\n        },\n        error: err => {\n          system._relay(self, self, {\n            type: XSTATE_OBSERVABLE_ERROR,\n            data: err\n          });\n        },\n        complete: () => {\n          system._relay(self, self, {\n            type: XSTATE_OBSERVABLE_COMPLETE\n          });\n        }\n      });\n    },\n    getPersistedSnapshot: ({\n      _subscription,\n      ...state\n    }) => state,\n    restoreSnapshot: state => ({\n      ...state,\n      _subscription: undefined\n    })\n  };\n  return logic;\n}\n\n/**\n * Creates event observable logic that listens to an observable that delivers\n * event objects.\n *\n * Event observable actor logic is described by an observable stream of\n * {@link https://stately.ai/docs/transitions#event-objects | event objects}.\n * Actors created from event observable logic (“event observable actors”) can:\n *\n * - Implicitly send events to its parent actor\n * - Emit snapshots of its emitted event objects\n *\n * Sending events to event observable actors will have no effect.\n *\n * @example\n *\n * ```ts\n * import {\n *   fromEventObservable,\n *   Subscribable,\n *   EventObject,\n *   createMachine,\n *   createActor\n * } from 'xstate';\n * import { fromEvent } from 'rxjs';\n *\n * const mouseClickLogic = fromEventObservable(\n *   () => fromEvent(document.body, 'click') as Subscribable<EventObject>\n * );\n *\n * const canvasMachine = createMachine({\n *   invoke: {\n *     // Will send mouse `click` events to the canvas actor\n *     src: mouseClickLogic\n *   }\n * });\n *\n * const canvasActor = createActor(canvasMachine);\n * canvasActor.start();\n * ```\n *\n * @param lazyObservable A function that creates an observable that delivers\n *   event objects. It receives one argument, an object with the following\n *   properties:\n *\n *   - `input` - Data that was provided to the event observable actor\n *   - `self` - The parent actor\n *   - `system` - The actor system to which the event observable actor belongs.\n *\n *   It should return a {@link Subscribable}, which is compatible with an RxJS\n *   Observable, although RxJS is not required to create them.\n */\nfunction fromEventObservable(lazyObservable) {\n  // TODO: event types\n  const logic = {\n    config: lazyObservable,\n    transition: (state, event) => {\n      if (state.status !== 'active') {\n        return state;\n      }\n      switch (event.type) {\n        case XSTATE_OBSERVABLE_ERROR:\n          return {\n            ...state,\n            status: 'error',\n            error: event.data,\n            input: undefined,\n            _subscription: undefined\n          };\n        case XSTATE_OBSERVABLE_COMPLETE:\n          return {\n            ...state,\n            status: 'done',\n            input: undefined,\n            _subscription: undefined\n          };\n        case _dist_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.X:\n          state._subscription.unsubscribe();\n          return {\n            ...state,\n            status: 'stopped',\n            input: undefined,\n            _subscription: undefined\n          };\n        default:\n          return state;\n      }\n    },\n    getInitialSnapshot: (_, input) => {\n      return {\n        status: 'active',\n        output: undefined,\n        error: undefined,\n        context: undefined,\n        input,\n        _subscription: undefined\n      };\n    },\n    start: (state, {\n      self,\n      system,\n      emit\n    }) => {\n      if (state.status === 'done') {\n        // Do not restart a completed observable\n        return;\n      }\n      state._subscription = lazyObservable({\n        input: state.input,\n        system,\n        self,\n        emit\n      }).subscribe({\n        next: value => {\n          if (self._parent) {\n            system._relay(self, self._parent, value);\n          }\n        },\n        error: err => {\n          system._relay(self, self, {\n            type: XSTATE_OBSERVABLE_ERROR,\n            data: err\n          });\n        },\n        complete: () => {\n          system._relay(self, self, {\n            type: XSTATE_OBSERVABLE_COMPLETE\n          });\n        }\n      });\n    },\n    getPersistedSnapshot: ({\n      _subscription,\n      ...snapshot\n    }) => snapshot,\n    restoreSnapshot: snapshot => ({\n      ...snapshot,\n      _subscription: undefined\n    })\n  };\n  return logic;\n}\n\nconst XSTATE_PROMISE_RESOLVE = 'xstate.promise.resolve';\nconst XSTATE_PROMISE_REJECT = 'xstate.promise.reject';\n\n/**\n * Represents an actor created by `fromPromise`.\n *\n * The type of `self` within the actor's logic.\n *\n * @example\n *\n * ```ts\n * import { fromPromise, createActor } from 'xstate';\n *\n * // The actor's resolved output\n * type Output = string;\n * // The actor's input.\n * type Input = { message: string };\n *\n * // Actor logic that fetches the url of an image of a cat saying `input.message`.\n * const logic = fromPromise<Output, Input>(async ({ input, self }) => {\n *   self;\n *   // ^? PromiseActorRef<Output, Input>\n *\n *   const data = await fetch(\n *     `https://cataas.com/cat/says/${input.message}`\n *   );\n *   const url = await data.json();\n *   return url;\n * });\n *\n * const actor = createActor(logic, { input: { message: 'hello world' } });\n * //    ^? PromiseActorRef<Output, Input>\n * ```\n *\n * @see {@link fromPromise}\n */\n\nconst controllerMap = new WeakMap();\n\n/**\n * An actor logic creator which returns promise logic as defined by an async\n * process that resolves or rejects after some time.\n *\n * Actors created from promise actor logic (“promise actors”) can:\n *\n * - Emit the resolved value of the promise\n * - Output the resolved value of the promise\n *\n * Sending events to promise actors will have no effect.\n *\n * @example\n *\n * ```ts\n * const promiseLogic = fromPromise(async () => {\n *   const result = await fetch('https://example.com/...').then((data) =>\n *     data.json()\n *   );\n *\n *   return result;\n * });\n *\n * const promiseActor = createActor(promiseLogic);\n * promiseActor.subscribe((snapshot) => {\n *   console.log(snapshot);\n * });\n * promiseActor.start();\n * // => {\n * //   output: undefined,\n * //   status: 'active'\n * //   ...\n * // }\n *\n * // After promise resolves\n * // => {\n * //   output: { ... },\n * //   status: 'done',\n * //   ...\n * // }\n * ```\n *\n * @param promiseCreator A function which returns a Promise, and accepts an\n *   object with the following properties:\n *\n *   - `input` - Data that was provided to the promise actor\n *   - `self` - The parent actor of the promise actor\n *   - `system` - The actor system to which the promise actor belongs\n *\n * @see {@link https://stately.ai/docs/input | Input docs} for more information about how input is passed\n */\nfunction fromPromise(promiseCreator) {\n  const logic = {\n    config: promiseCreator,\n    transition: (state, event, scope) => {\n      if (state.status !== 'active') {\n        return state;\n      }\n      switch (event.type) {\n        case XSTATE_PROMISE_RESOLVE:\n          {\n            const resolvedValue = event.data;\n            return {\n              ...state,\n              status: 'done',\n              output: resolvedValue,\n              input: undefined\n            };\n          }\n        case XSTATE_PROMISE_REJECT:\n          return {\n            ...state,\n            status: 'error',\n            error: event.data,\n            input: undefined\n          };\n        case _dist_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.X:\n          {\n            controllerMap.get(scope.self)?.abort();\n            return {\n              ...state,\n              status: 'stopped',\n              input: undefined\n            };\n          }\n        default:\n          return state;\n      }\n    },\n    start: (state, {\n      self,\n      system,\n      emit\n    }) => {\n      // TODO: determine how to allow customizing this so that promises\n      // can be restarted if necessary\n      if (state.status !== 'active') {\n        return;\n      }\n      const controller = new AbortController();\n      controllerMap.set(self, controller);\n      const resolvedPromise = Promise.resolve(promiseCreator({\n        input: state.input,\n        system,\n        self,\n        signal: controller.signal,\n        emit\n      }));\n      resolvedPromise.then(response => {\n        if (self.getSnapshot().status !== 'active') {\n          return;\n        }\n        controllerMap.delete(self);\n        system._relay(self, self, {\n          type: XSTATE_PROMISE_RESOLVE,\n          data: response\n        });\n      }, errorData => {\n        if (self.getSnapshot().status !== 'active') {\n          return;\n        }\n        controllerMap.delete(self);\n        system._relay(self, self, {\n          type: XSTATE_PROMISE_REJECT,\n          data: errorData\n        });\n      });\n    },\n    getInitialSnapshot: (_, input) => {\n      return {\n        status: 'active',\n        output: undefined,\n        error: undefined,\n        input\n      };\n    },\n    getPersistedSnapshot: snapshot => snapshot,\n    restoreSnapshot: snapshot => snapshot\n  };\n  return logic;\n}\n\nconst emptyLogic = fromTransition(_ => undefined, undefined);\nfunction createEmptyActor() {\n  return (0,_dist_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.A)(emptyLogic);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xstate/actors/dist/xstate-actors.development.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xstate/dev/dist/xstate-dev.development.esm.js":
/*!********************************************************************!*\
  !*** ./node_modules/xstate/dev/dist/xstate-dev.development.esm.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   devToolsAdapter: () => (/* binding */ devToolsAdapter),\n/* harmony export */   getGlobal: () => (/* binding */ getGlobal),\n/* harmony export */   registerService: () => (/* binding */ registerService)\n/* harmony export */ });\n// From https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/globalThis\nfunction getGlobal() {\n  if (typeof globalThis !== 'undefined') {\n    return globalThis;\n  }\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  {\n    console.warn('XState could not find a global object in this environment. Please let the maintainers know and raise an issue here: https://github.com/statelyai/xstate/issues');\n  }\n}\nfunction getDevTools() {\n  const w = getGlobal();\n  if (w.__xstate__) {\n    return w.__xstate__;\n  }\n  return undefined;\n}\nfunction registerService(service) {\n  if (typeof window === 'undefined') {\n    return;\n  }\n  const devTools = getDevTools();\n  if (devTools) {\n    devTools.register(service);\n  }\n}\nconst devToolsAdapter = service => {\n  if (typeof window === 'undefined') {\n    return;\n  }\n  const devTools = getDevTools();\n  if (devTools) {\n    devTools.register(service);\n  }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xstate/dev/dist/xstate-dev.development.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xstate/dist/log-0acd9069.development.esm.js":
/*!******************************************************************!*\
  !*** ./node_modules/xstate/dist/log-0acd9069.development.esm.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   S: () => (/* binding */ SpecialTargets),\n/* harmony export */   a: () => (/* binding */ assign),\n/* harmony export */   b: () => (/* binding */ enqueueActions),\n/* harmony export */   c: () => (/* binding */ sendTo),\n/* harmony export */   e: () => (/* binding */ emit),\n/* harmony export */   f: () => (/* binding */ forwardTo),\n/* harmony export */   l: () => (/* binding */ log),\n/* harmony export */   s: () => (/* binding */ sendParent)\n/* harmony export */ });\n/* harmony import */ var _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./raise-1db27a82.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/raise-1db27a82.development.esm.js\");\n\n\nfunction createSpawner(actorScope, {\n  machine,\n  context\n}, event, spawnedChildren) {\n  const spawn = (src, options) => {\n    if (typeof src === 'string') {\n      const logic = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.z)(machine, src);\n      if (!logic) {\n        throw new Error(`Actor logic '${src}' not implemented in machine '${machine.id}'`);\n      }\n      const actorRef = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.A)(logic, {\n        id: options?.id,\n        parent: actorScope.self,\n        syncSnapshot: options?.syncSnapshot,\n        input: typeof options?.input === 'function' ? options.input({\n          context,\n          event,\n          self: actorScope.self\n        }) : options?.input,\n        src,\n        systemId: options?.systemId\n      });\n      spawnedChildren[actorRef.id] = actorRef;\n      return actorRef;\n    } else {\n      const actorRef = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.A)(src, {\n        id: options?.id,\n        parent: actorScope.self,\n        syncSnapshot: options?.syncSnapshot,\n        input: options?.input,\n        src,\n        systemId: options?.systemId\n      });\n      return actorRef;\n    }\n  };\n  return (src, options) => {\n    const actorRef = spawn(src, options); // TODO: fix types\n    spawnedChildren[actorRef.id] = actorRef;\n    actorScope.defer(() => {\n      if (actorRef._processingStatus === _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.T.Stopped) {\n        return;\n      }\n      actorRef.start();\n    });\n    return actorRef;\n  };\n}\n\nfunction resolveAssign(actorScope, snapshot, actionArgs, actionParams, {\n  assignment\n}) {\n  if (!snapshot.context) {\n    throw new Error('Cannot assign to undefined `context`. Ensure that `context` is defined in the machine config.');\n  }\n  const spawnedChildren = {};\n  const assignArgs = {\n    context: snapshot.context,\n    event: actionArgs.event,\n    spawn: createSpawner(actorScope, snapshot, actionArgs.event, spawnedChildren),\n    self: actorScope.self,\n    system: actorScope.system\n  };\n  let partialUpdate = {};\n  if (typeof assignment === 'function') {\n    partialUpdate = assignment(assignArgs, actionParams);\n  } else {\n    for (const key of Object.keys(assignment)) {\n      const propAssignment = assignment[key];\n      partialUpdate[key] = typeof propAssignment === 'function' ? propAssignment(assignArgs, actionParams) : propAssignment;\n    }\n  }\n  const updatedContext = Object.assign({}, snapshot.context, partialUpdate);\n  return [(0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.U)(snapshot, {\n    context: updatedContext,\n    children: Object.keys(spawnedChildren).length ? {\n      ...snapshot.children,\n      ...spawnedChildren\n    } : snapshot.children\n  }), undefined, undefined];\n}\n/**\n * Updates the current context of the machine.\n *\n * @example\n *\n * ```ts\n * import { createMachine, assign } from 'xstate';\n *\n * const countMachine = createMachine({\n *   context: {\n *     count: 0,\n *     message: ''\n *   },\n *   on: {\n *     inc: {\n *       actions: assign({\n *         count: ({ context }) => context.count + 1\n *       })\n *     },\n *     updateMessage: {\n *       actions: assign(({ context, event }) => {\n *         return {\n *           message: event.message.trim()\n *         };\n *       })\n *     }\n *   }\n * });\n * ```\n *\n * @param assignment An object that represents the partial context to update, or\n *   a function that returns an object that represents the partial context to\n *   update.\n */\nfunction assign(assignment) {\n  if (_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.V) {\n    console.warn('Custom actions should not call `assign()` directly, as it is not imperative. See https://stately.ai/docs/actions#built-in-actions for more details.');\n  }\n  function assign(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  assign.type = 'xstate.assign';\n  assign.assignment = assignment;\n  assign.resolve = resolveAssign;\n  return assign;\n}\n\nfunction resolveEmit(_, snapshot, args, actionParams, {\n  event: eventOrExpr\n}) {\n  const resolvedEvent = typeof eventOrExpr === 'function' ? eventOrExpr(args, actionParams) : eventOrExpr;\n  return [snapshot, {\n    event: resolvedEvent\n  }, undefined];\n}\nfunction executeEmit(actorScope, {\n  event\n}) {\n  actorScope.defer(() => actorScope.emit(event));\n}\n/**\n * Emits an event to event handlers registered on the actor via `actor.on(event,\n * handler)`.\n *\n * @example\n *\n * ```ts\n * import { emit } from 'xstate';\n *\n * const machine = createMachine({\n *   // ...\n *   on: {\n *     something: {\n *       actions: emit({\n *         type: 'emitted',\n *         some: 'data'\n *       })\n *     }\n *   }\n *   // ...\n * });\n *\n * const actor = createActor(machine).start();\n *\n * actor.on('emitted', (event) => {\n *   console.log(event);\n * });\n *\n * actor.send({ type: 'something' });\n * // logs:\n * // {\n * //   type: 'emitted',\n * //   some: 'data'\n * // }\n * ```\n */\nfunction emit(/** The event to emit, or an expression that returns an event to emit. */\neventOrExpr) {\n  if (_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.V) {\n    console.warn('Custom actions should not call `emit()` directly, as it is not imperative. See https://stately.ai/docs/actions#built-in-actions for more details.');\n  }\n  function emit(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  emit.type = 'xstate.emit';\n  emit.event = eventOrExpr;\n  emit.resolve = resolveEmit;\n  emit.execute = executeEmit;\n  return emit;\n}\n\n/**\n * @remarks\n * `T | unknown` reduces to `unknown` and that can be problematic when it comes\n * to contextual typing. It especially is a problem when the union has a\n * function member, like here:\n *\n * ```ts\n * declare function test(\n *   cbOrVal: ((arg: number) => unknown) | unknown\n * ): void;\n * test((arg) => {}); // oops, implicit any\n * ```\n *\n * This type can be used to avoid this problem. This union represents the same\n * value space as `unknown`.\n */\n\n// https://github.com/microsoft/TypeScript/issues/23182#issuecomment-379091887\n\n// @TODO: Replace with native `NoInfer` when TS issue gets fixed:\n// https://github.com/microsoft/TypeScript/pull/57673\n\n/** @deprecated Use the built-in `NoInfer` type instead */\n\n/** The full definition of an event, with a string `type`. */\n\n/**\n * The string or object representing the state value relative to the parent\n * state node.\n *\n * @remarks\n * - For a child atomic state node, this is a string, e.g., `\"pending\"`.\n * - For complex state nodes, this is an object, e.g., `{ success:\n *   \"someChildState\" }`.\n */\n\n/** @deprecated Use `AnyMachineSnapshot` instead */\n\n// TODO: possibly refactor this somehow, use even a simpler type, and maybe even make `machine.options` private or something\n/** @ignore */\n\nlet SpecialTargets = /*#__PURE__*/function (SpecialTargets) {\n  SpecialTargets[\"Parent\"] = \"#_parent\";\n  SpecialTargets[\"Internal\"] = \"#_internal\";\n  return SpecialTargets;\n}({});\n\n/** @deprecated Use `AnyActor` instead. */\n\n// Based on RxJS types\n\n// TODO: in v6, this should only accept AnyActorLogic, like ActorRefFromLogic\n\n/** @deprecated Use `Actor<T>` instead. */\n\n/**\n * Represents logic which can be used by an actor.\n *\n * @template TSnapshot - The type of the snapshot.\n * @template TEvent - The type of the event object.\n * @template TInput - The type of the input.\n * @template TSystem - The type of the actor system.\n */\n\n/** @deprecated */\n\n// TODO: cover all that can be actually returned\n\nfunction resolveSendTo(actorScope, snapshot, args, actionParams, {\n  to,\n  event: eventOrExpr,\n  id,\n  delay\n}, extra) {\n  const delaysMap = snapshot.machine.implementations.delays;\n  if (typeof eventOrExpr === 'string') {\n    throw new Error(\n    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n    `Only event objects may be used with sendTo; use sendTo({ type: \"${eventOrExpr}\" }) instead`);\n  }\n  const resolvedEvent = typeof eventOrExpr === 'function' ? eventOrExpr(args, actionParams) : eventOrExpr;\n  let resolvedDelay;\n  if (typeof delay === 'string') {\n    const configDelay = delaysMap && delaysMap[delay];\n    resolvedDelay = typeof configDelay === 'function' ? configDelay(args, actionParams) : configDelay;\n  } else {\n    resolvedDelay = typeof delay === 'function' ? delay(args, actionParams) : delay;\n  }\n  const resolvedTarget = typeof to === 'function' ? to(args, actionParams) : to;\n  let targetActorRef;\n  if (typeof resolvedTarget === 'string') {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison\n    if (resolvedTarget === SpecialTargets.Parent) {\n      targetActorRef = actorScope.self._parent;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison\n    else if (resolvedTarget === SpecialTargets.Internal) {\n      targetActorRef = actorScope.self;\n    } else if (resolvedTarget.startsWith('#_')) {\n      // SCXML compatibility: https://www.w3.org/TR/scxml/#SCXMLEventProcessor\n      // #_invokeid. If the target is the special term '#_invokeid', where invokeid is the invokeid of an SCXML session that the sending session has created by <invoke>, the Processor must add the event to the external queue of that session.\n      targetActorRef = snapshot.children[resolvedTarget.slice(2)];\n    } else {\n      targetActorRef = extra.deferredActorIds?.includes(resolvedTarget) ? resolvedTarget : snapshot.children[resolvedTarget];\n    }\n    if (!targetActorRef) {\n      throw new Error(`Unable to send event to actor '${resolvedTarget}' from machine '${snapshot.machine.id}'.`);\n    }\n  } else {\n    targetActorRef = resolvedTarget || actorScope.self;\n  }\n  return [snapshot, {\n    to: targetActorRef,\n    targetId: typeof resolvedTarget === 'string' ? resolvedTarget : undefined,\n    event: resolvedEvent,\n    id,\n    delay: resolvedDelay\n  }, undefined];\n}\nfunction retryResolveSendTo(_, snapshot, params) {\n  if (typeof params.to === 'string') {\n    params.to = snapshot.children[params.to];\n  }\n}\nfunction executeSendTo(actorScope, params) {\n  // this forms an outgoing events queue\n  // thanks to that the recipient actors are able to read the *updated* snapshot value of the sender\n  actorScope.defer(() => {\n    const {\n      to,\n      event,\n      delay,\n      id\n    } = params;\n    if (typeof delay === 'number') {\n      actorScope.system.scheduler.schedule(actorScope.self, to, event, delay, id);\n      return;\n    }\n    actorScope.system._relay(actorScope.self,\n    // at this point, in a deferred task, it should already be mutated by retryResolveSendTo\n    // if it initially started as a string\n    to, event.type === _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.W ? (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.Y)(actorScope.self.id, event.data) : event);\n  });\n}\n/**\n * Sends an event to an actor.\n *\n * @param actor The `ActorRef` to send the event to.\n * @param event The event to send, or an expression that evaluates to the event\n *   to send\n * @param options Send action options\n *\n *   - `id` - The unique send event identifier (used with `cancel()`).\n *   - `delay` - The number of milliseconds to delay the sending of the event.\n */\nfunction sendTo(to, eventOrExpr, options) {\n  if (_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.V) {\n    console.warn('Custom actions should not call `sendTo()` directly, as it is not imperative. See https://stately.ai/docs/actions#built-in-actions for more details.');\n  }\n  function sendTo(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  sendTo.type = 'xstate.sendTo';\n  sendTo.to = to;\n  sendTo.event = eventOrExpr;\n  sendTo.id = options?.id;\n  sendTo.delay = options?.delay;\n  sendTo.resolve = resolveSendTo;\n  sendTo.retryResolve = retryResolveSendTo;\n  sendTo.execute = executeSendTo;\n  return sendTo;\n}\n\n/**\n * Sends an event to this machine's parent.\n *\n * @param event The event to send to the parent machine.\n * @param options Options to pass into the send event.\n */\nfunction sendParent(event, options) {\n  return sendTo(SpecialTargets.Parent, event, options);\n}\n/**\n * Forwards (sends) an event to the `target` actor.\n *\n * @param target The target actor to forward the event to.\n * @param options Options to pass into the send action creator.\n */\nfunction forwardTo(target, options) {\n  if ((!target || typeof target === 'function')) {\n    const originalTarget = target;\n    target = (...args) => {\n      const resolvedTarget = typeof originalTarget === 'function' ? originalTarget(...args) : originalTarget;\n      if (!resolvedTarget) {\n        throw new Error(`Attempted to forward event to undefined actor. This risks an infinite loop in the sender.`);\n      }\n      return resolvedTarget;\n    };\n  }\n  return sendTo(target, ({\n    event\n  }) => event, options);\n}\n\nfunction resolveEnqueueActions(actorScope, snapshot, args, actionParams, {\n  collect\n}) {\n  const actions = [];\n  const enqueue = function enqueue(action) {\n    actions.push(action);\n  };\n  enqueue.assign = (...args) => {\n    actions.push(assign(...args));\n  };\n  enqueue.cancel = (...args) => {\n    actions.push((0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.M)(...args));\n  };\n  enqueue.raise = (...args) => {\n    // for some reason it fails to infer `TDelay` from `...args` here and picks its default (`never`)\n    // then it fails to typecheck that because `...args` use `string` in place of `TDelay`\n    actions.push((0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.O)(...args));\n  };\n  enqueue.sendTo = (...args) => {\n    // for some reason it fails to infer `TDelay` from `...args` here and picks its default (`never`)\n    // then it fails to typecheck that because `...args` use `string` in place of `TDelay\n    actions.push(sendTo(...args));\n  };\n  enqueue.sendParent = (...args) => {\n    actions.push(sendParent(...args));\n  };\n  enqueue.spawnChild = (...args) => {\n    actions.push((0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.P)(...args));\n  };\n  enqueue.stopChild = (...args) => {\n    actions.push((0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.R)(...args));\n  };\n  enqueue.emit = (...args) => {\n    actions.push(emit(...args));\n  };\n  collect({\n    context: args.context,\n    event: args.event,\n    enqueue,\n    check: guard => (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.e)(guard, snapshot.context, args.event, snapshot),\n    self: actorScope.self,\n    system: actorScope.system\n  }, actionParams);\n  return [snapshot, undefined, actions];\n}\n/**\n * Creates an action object that will execute actions that are queued by the\n * `enqueue(action)` function.\n *\n * @example\n *\n * ```ts\n * import { createMachine, enqueueActions } from 'xstate';\n *\n * const machine = createMachine({\n *   entry: enqueueActions(({ enqueue, check }) => {\n *     enqueue.assign({ count: 0 });\n *\n *     if (check('someGuard')) {\n *       enqueue.assign({ count: 1 });\n *     }\n *\n *     enqueue('someAction');\n *   })\n * });\n * ```\n */\nfunction enqueueActions(collect) {\n  function enqueueActions(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  enqueueActions.type = 'xstate.enqueueActions';\n  enqueueActions.collect = collect;\n  enqueueActions.resolve = resolveEnqueueActions;\n  return enqueueActions;\n}\n\nfunction resolveLog(_, snapshot, actionArgs, actionParams, {\n  value,\n  label\n}) {\n  return [snapshot, {\n    value: typeof value === 'function' ? value(actionArgs, actionParams) : value,\n    label\n  }, undefined];\n}\nfunction executeLog({\n  logger\n}, {\n  value,\n  label\n}) {\n  if (label) {\n    logger(label, value);\n  } else {\n    logger(value);\n  }\n}\n/**\n * @param expr The expression function to evaluate which will be logged. Takes\n *   in 2 arguments:\n *\n *   - `ctx` - the current state context\n *   - `event` - the event that caused this action to be executed.\n *\n * @param label The label to give to the logged expression.\n */\nfunction log(value = ({\n  context,\n  event\n}) => ({\n  context,\n  event\n}), label) {\n  function log(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  log.type = 'xstate.log';\n  log.value = value;\n  log.label = label;\n  log.resolve = resolveLog;\n  log.execute = executeLog;\n  return log;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveHN0YXRlL2Rpc3QvbG9nLTBhY2Q5MDY5LmRldmVsb3BtZW50LmVzbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBK1M7O0FBRS9TO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0Esb0JBQW9CLHFFQUFzQjtBQUMxQztBQUNBLHdDQUF3QyxJQUFJLGdDQUFnQyxXQUFXO0FBQ3ZGO0FBQ0EsdUJBQXVCLHFFQUFXO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLE1BQU07QUFDTix1QkFBdUIscUVBQVc7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDO0FBQzFDO0FBQ0E7QUFDQSx5Q0FBeUMsaUVBQWdCO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUM7QUFDekMsVUFBVSxxRUFBb0I7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ04sR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSx3QkFBd0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsU0FBUztBQUM5QixVQUFVO0FBQ1YsUUFBUTtBQUNSO0FBQ0EsNEJBQTRCLGdCQUFnQjtBQUM1QztBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0saUVBQXFCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQSxnQkFBZ0IsbUJBQW1CO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0saUVBQXFCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLEdBQUc7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBEQUEwRDtBQUMxRCx1QkFBdUI7QUFDdkI7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsR0FBRzs7QUFFSjs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsYUFBYSxTQUFTLFlBQVksR0FBRztBQUN0RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSx3REFBd0QsZUFBZSxrQkFBa0Isb0JBQW9CO0FBQzdHO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGlFQUFZLEdBQUcscUVBQXFCO0FBQzNELEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0saUVBQXFCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLHFFQUFNO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLHFFQUFLO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLHFFQUFVO0FBQzNCO0FBQ0E7QUFDQSxpQkFBaUIscUVBQVM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixxRUFBYTtBQUNqQztBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksZ0NBQWdDO0FBQzVDO0FBQ0E7QUFDQSw4QkFBOEIsZ0JBQWdCO0FBQzlDLHdCQUF3QixVQUFVO0FBQ2xDO0FBQ0E7QUFDQSwwQkFBMEIsVUFBVTtBQUNwQztBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ04sSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW9JIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMveHN0YXRlL2Rpc3QvbG9nLTBhY2Q5MDY5LmRldmVsb3BtZW50LmVzbS5qcz9jMmQ5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFQgYXMgUHJvY2Vzc2luZ1N0YXR1cywgeiBhcyByZXNvbHZlUmVmZXJlbmNlZEFjdG9yLCBBIGFzIGNyZWF0ZUFjdG9yLCBVIGFzIGNsb25lTWFjaGluZVNuYXBzaG90LCBWIGFzIGV4ZWN1dGluZ0N1c3RvbUFjdGlvbiwgVyBhcyBYU1RBVEVfRVJST1IsIFkgYXMgY3JlYXRlRXJyb3JBY3RvckV2ZW50LCBlIGFzIGV2YWx1YXRlR3VhcmQsIE0gYXMgY2FuY2VsLCBPIGFzIHJhaXNlLCBQIGFzIHNwYXduQ2hpbGQsIFIgYXMgc3RvcENoaWxkIH0gZnJvbSAnLi9yYWlzZS0xZGIyN2E4Mi5kZXZlbG9wbWVudC5lc20uanMnO1xuXG5mdW5jdGlvbiBjcmVhdGVTcGF3bmVyKGFjdG9yU2NvcGUsIHtcbiAgbWFjaGluZSxcbiAgY29udGV4dFxufSwgZXZlbnQsIHNwYXduZWRDaGlsZHJlbikge1xuICBjb25zdCBzcGF3biA9IChzcmMsIG9wdGlvbnMpID0+IHtcbiAgICBpZiAodHlwZW9mIHNyYyA9PT0gJ3N0cmluZycpIHtcbiAgICAgIGNvbnN0IGxvZ2ljID0gcmVzb2x2ZVJlZmVyZW5jZWRBY3RvcihtYWNoaW5lLCBzcmMpO1xuICAgICAgaWYgKCFsb2dpYykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEFjdG9yIGxvZ2ljICcke3NyY30nIG5vdCBpbXBsZW1lbnRlZCBpbiBtYWNoaW5lICcke21hY2hpbmUuaWR9J2ApO1xuICAgICAgfVxuICAgICAgY29uc3QgYWN0b3JSZWYgPSBjcmVhdGVBY3Rvcihsb2dpYywge1xuICAgICAgICBpZDogb3B0aW9ucz8uaWQsXG4gICAgICAgIHBhcmVudDogYWN0b3JTY29wZS5zZWxmLFxuICAgICAgICBzeW5jU25hcHNob3Q6IG9wdGlvbnM/LnN5bmNTbmFwc2hvdCxcbiAgICAgICAgaW5wdXQ6IHR5cGVvZiBvcHRpb25zPy5pbnB1dCA9PT0gJ2Z1bmN0aW9uJyA/IG9wdGlvbnMuaW5wdXQoe1xuICAgICAgICAgIGNvbnRleHQsXG4gICAgICAgICAgZXZlbnQsXG4gICAgICAgICAgc2VsZjogYWN0b3JTY29wZS5zZWxmXG4gICAgICAgIH0pIDogb3B0aW9ucz8uaW5wdXQsXG4gICAgICAgIHNyYyxcbiAgICAgICAgc3lzdGVtSWQ6IG9wdGlvbnM/LnN5c3RlbUlkXG4gICAgICB9KTtcbiAgICAgIHNwYXduZWRDaGlsZHJlblthY3RvclJlZi5pZF0gPSBhY3RvclJlZjtcbiAgICAgIHJldHVybiBhY3RvclJlZjtcbiAgICB9IGVsc2Uge1xuICAgICAgY29uc3QgYWN0b3JSZWYgPSBjcmVhdGVBY3RvcihzcmMsIHtcbiAgICAgICAgaWQ6IG9wdGlvbnM/LmlkLFxuICAgICAgICBwYXJlbnQ6IGFjdG9yU2NvcGUuc2VsZixcbiAgICAgICAgc3luY1NuYXBzaG90OiBvcHRpb25zPy5zeW5jU25hcHNob3QsXG4gICAgICAgIGlucHV0OiBvcHRpb25zPy5pbnB1dCxcbiAgICAgICAgc3JjLFxuICAgICAgICBzeXN0ZW1JZDogb3B0aW9ucz8uc3lzdGVtSWRcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIGFjdG9yUmVmO1xuICAgIH1cbiAgfTtcbiAgcmV0dXJuIChzcmMsIG9wdGlvbnMpID0+IHtcbiAgICBjb25zdCBhY3RvclJlZiA9IHNwYXduKHNyYywgb3B0aW9ucyk7IC8vIFRPRE86IGZpeCB0eXBlc1xuICAgIHNwYXduZWRDaGlsZHJlblthY3RvclJlZi5pZF0gPSBhY3RvclJlZjtcbiAgICBhY3RvclNjb3BlLmRlZmVyKCgpID0+IHtcbiAgICAgIGlmIChhY3RvclJlZi5fcHJvY2Vzc2luZ1N0YXR1cyA9PT0gUHJvY2Vzc2luZ1N0YXR1cy5TdG9wcGVkKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGFjdG9yUmVmLnN0YXJ0KCk7XG4gICAgfSk7XG4gICAgcmV0dXJuIGFjdG9yUmVmO1xuICB9O1xufVxuXG5mdW5jdGlvbiByZXNvbHZlQXNzaWduKGFjdG9yU2NvcGUsIHNuYXBzaG90LCBhY3Rpb25BcmdzLCBhY3Rpb25QYXJhbXMsIHtcbiAgYXNzaWdubWVudFxufSkge1xuICBpZiAoIXNuYXBzaG90LmNvbnRleHQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0Nhbm5vdCBhc3NpZ24gdG8gdW5kZWZpbmVkIGBjb250ZXh0YC4gRW5zdXJlIHRoYXQgYGNvbnRleHRgIGlzIGRlZmluZWQgaW4gdGhlIG1hY2hpbmUgY29uZmlnLicpO1xuICB9XG4gIGNvbnN0IHNwYXduZWRDaGlsZHJlbiA9IHt9O1xuICBjb25zdCBhc3NpZ25BcmdzID0ge1xuICAgIGNvbnRleHQ6IHNuYXBzaG90LmNvbnRleHQsXG4gICAgZXZlbnQ6IGFjdGlvbkFyZ3MuZXZlbnQsXG4gICAgc3Bhd246IGNyZWF0ZVNwYXduZXIoYWN0b3JTY29wZSwgc25hcHNob3QsIGFjdGlvbkFyZ3MuZXZlbnQsIHNwYXduZWRDaGlsZHJlbiksXG4gICAgc2VsZjogYWN0b3JTY29wZS5zZWxmLFxuICAgIHN5c3RlbTogYWN0b3JTY29wZS5zeXN0ZW1cbiAgfTtcbiAgbGV0IHBhcnRpYWxVcGRhdGUgPSB7fTtcbiAgaWYgKHR5cGVvZiBhc3NpZ25tZW50ID09PSAnZnVuY3Rpb24nKSB7XG4gICAgcGFydGlhbFVwZGF0ZSA9IGFzc2lnbm1lbnQoYXNzaWduQXJncywgYWN0aW9uUGFyYW1zKTtcbiAgfSBlbHNlIHtcbiAgICBmb3IgKGNvbnN0IGtleSBvZiBPYmplY3Qua2V5cyhhc3NpZ25tZW50KSkge1xuICAgICAgY29uc3QgcHJvcEFzc2lnbm1lbnQgPSBhc3NpZ25tZW50W2tleV07XG4gICAgICBwYXJ0aWFsVXBkYXRlW2tleV0gPSB0eXBlb2YgcHJvcEFzc2lnbm1lbnQgPT09ICdmdW5jdGlvbicgPyBwcm9wQXNzaWdubWVudChhc3NpZ25BcmdzLCBhY3Rpb25QYXJhbXMpIDogcHJvcEFzc2lnbm1lbnQ7XG4gICAgfVxuICB9XG4gIGNvbnN0IHVwZGF0ZWRDb250ZXh0ID0gT2JqZWN0LmFzc2lnbih7fSwgc25hcHNob3QuY29udGV4dCwgcGFydGlhbFVwZGF0ZSk7XG4gIHJldHVybiBbY2xvbmVNYWNoaW5lU25hcHNob3Qoc25hcHNob3QsIHtcbiAgICBjb250ZXh0OiB1cGRhdGVkQ29udGV4dCxcbiAgICBjaGlsZHJlbjogT2JqZWN0LmtleXMoc3Bhd25lZENoaWxkcmVuKS5sZW5ndGggPyB7XG4gICAgICAuLi5zbmFwc2hvdC5jaGlsZHJlbixcbiAgICAgIC4uLnNwYXduZWRDaGlsZHJlblxuICAgIH0gOiBzbmFwc2hvdC5jaGlsZHJlblxuICB9KSwgdW5kZWZpbmVkLCB1bmRlZmluZWRdO1xufVxuLyoqXG4gKiBVcGRhdGVzIHRoZSBjdXJyZW50IGNvbnRleHQgb2YgdGhlIG1hY2hpbmUuXG4gKlxuICogQGV4YW1wbGVcbiAqXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgY3JlYXRlTWFjaGluZSwgYXNzaWduIH0gZnJvbSAneHN0YXRlJztcbiAqXG4gKiBjb25zdCBjb3VudE1hY2hpbmUgPSBjcmVhdGVNYWNoaW5lKHtcbiAqICAgY29udGV4dDoge1xuICogICAgIGNvdW50OiAwLFxuICogICAgIG1lc3NhZ2U6ICcnXG4gKiAgIH0sXG4gKiAgIG9uOiB7XG4gKiAgICAgaW5jOiB7XG4gKiAgICAgICBhY3Rpb25zOiBhc3NpZ24oe1xuICogICAgICAgICBjb3VudDogKHsgY29udGV4dCB9KSA9PiBjb250ZXh0LmNvdW50ICsgMVxuICogICAgICAgfSlcbiAqICAgICB9LFxuICogICAgIHVwZGF0ZU1lc3NhZ2U6IHtcbiAqICAgICAgIGFjdGlvbnM6IGFzc2lnbigoeyBjb250ZXh0LCBldmVudCB9KSA9PiB7XG4gKiAgICAgICAgIHJldHVybiB7XG4gKiAgICAgICAgICAgbWVzc2FnZTogZXZlbnQubWVzc2FnZS50cmltKClcbiAqICAgICAgICAgfTtcbiAqICAgICAgIH0pXG4gKiAgICAgfVxuICogICB9XG4gKiB9KTtcbiAqIGBgYFxuICpcbiAqIEBwYXJhbSBhc3NpZ25tZW50IEFuIG9iamVjdCB0aGF0IHJlcHJlc2VudHMgdGhlIHBhcnRpYWwgY29udGV4dCB0byB1cGRhdGUsIG9yXG4gKiAgIGEgZnVuY3Rpb24gdGhhdCByZXR1cm5zIGFuIG9iamVjdCB0aGF0IHJlcHJlc2VudHMgdGhlIHBhcnRpYWwgY29udGV4dCB0b1xuICogICB1cGRhdGUuXG4gKi9cbmZ1bmN0aW9uIGFzc2lnbihhc3NpZ25tZW50KSB7XG4gIGlmIChleGVjdXRpbmdDdXN0b21BY3Rpb24pIHtcbiAgICBjb25zb2xlLndhcm4oJ0N1c3RvbSBhY3Rpb25zIHNob3VsZCBub3QgY2FsbCBgYXNzaWduKClgIGRpcmVjdGx5LCBhcyBpdCBpcyBub3QgaW1wZXJhdGl2ZS4gU2VlIGh0dHBzOi8vc3RhdGVseS5haS9kb2NzL2FjdGlvbnMjYnVpbHQtaW4tYWN0aW9ucyBmb3IgbW9yZSBkZXRhaWxzLicpO1xuICB9XG4gIGZ1bmN0aW9uIGFzc2lnbihfYXJncywgX3BhcmFtcykge1xuICAgIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVGhpcyBpc24ndCBzdXBwb3NlZCB0byBiZSBjYWxsZWRgKTtcbiAgICB9XG4gIH1cbiAgYXNzaWduLnR5cGUgPSAneHN0YXRlLmFzc2lnbic7XG4gIGFzc2lnbi5hc3NpZ25tZW50ID0gYXNzaWdubWVudDtcbiAgYXNzaWduLnJlc29sdmUgPSByZXNvbHZlQXNzaWduO1xuICByZXR1cm4gYXNzaWduO1xufVxuXG5mdW5jdGlvbiByZXNvbHZlRW1pdChfLCBzbmFwc2hvdCwgYXJncywgYWN0aW9uUGFyYW1zLCB7XG4gIGV2ZW50OiBldmVudE9yRXhwclxufSkge1xuICBjb25zdCByZXNvbHZlZEV2ZW50ID0gdHlwZW9mIGV2ZW50T3JFeHByID09PSAnZnVuY3Rpb24nID8gZXZlbnRPckV4cHIoYXJncywgYWN0aW9uUGFyYW1zKSA6IGV2ZW50T3JFeHByO1xuICByZXR1cm4gW3NuYXBzaG90LCB7XG4gICAgZXZlbnQ6IHJlc29sdmVkRXZlbnRcbiAgfSwgdW5kZWZpbmVkXTtcbn1cbmZ1bmN0aW9uIGV4ZWN1dGVFbWl0KGFjdG9yU2NvcGUsIHtcbiAgZXZlbnRcbn0pIHtcbiAgYWN0b3JTY29wZS5kZWZlcigoKSA9PiBhY3RvclNjb3BlLmVtaXQoZXZlbnQpKTtcbn1cbi8qKlxuICogRW1pdHMgYW4gZXZlbnQgdG8gZXZlbnQgaGFuZGxlcnMgcmVnaXN0ZXJlZCBvbiB0aGUgYWN0b3IgdmlhIGBhY3Rvci5vbihldmVudCxcbiAqIGhhbmRsZXIpYC5cbiAqXG4gKiBAZXhhbXBsZVxuICpcbiAqIGBgYHRzXG4gKiBpbXBvcnQgeyBlbWl0IH0gZnJvbSAneHN0YXRlJztcbiAqXG4gKiBjb25zdCBtYWNoaW5lID0gY3JlYXRlTWFjaGluZSh7XG4gKiAgIC8vIC4uLlxuICogICBvbjoge1xuICogICAgIHNvbWV0aGluZzoge1xuICogICAgICAgYWN0aW9uczogZW1pdCh7XG4gKiAgICAgICAgIHR5cGU6ICdlbWl0dGVkJyxcbiAqICAgICAgICAgc29tZTogJ2RhdGEnXG4gKiAgICAgICB9KVxuICogICAgIH1cbiAqICAgfVxuICogICAvLyAuLi5cbiAqIH0pO1xuICpcbiAqIGNvbnN0IGFjdG9yID0gY3JlYXRlQWN0b3IobWFjaGluZSkuc3RhcnQoKTtcbiAqXG4gKiBhY3Rvci5vbignZW1pdHRlZCcsIChldmVudCkgPT4ge1xuICogICBjb25zb2xlLmxvZyhldmVudCk7XG4gKiB9KTtcbiAqXG4gKiBhY3Rvci5zZW5kKHsgdHlwZTogJ3NvbWV0aGluZycgfSk7XG4gKiAvLyBsb2dzOlxuICogLy8ge1xuICogLy8gICB0eXBlOiAnZW1pdHRlZCcsXG4gKiAvLyAgIHNvbWU6ICdkYXRhJ1xuICogLy8gfVxuICogYGBgXG4gKi9cbmZ1bmN0aW9uIGVtaXQoLyoqIFRoZSBldmVudCB0byBlbWl0LCBvciBhbiBleHByZXNzaW9uIHRoYXQgcmV0dXJucyBhbiBldmVudCB0byBlbWl0LiAqL1xuZXZlbnRPckV4cHIpIHtcbiAgaWYgKGV4ZWN1dGluZ0N1c3RvbUFjdGlvbikge1xuICAgIGNvbnNvbGUud2FybignQ3VzdG9tIGFjdGlvbnMgc2hvdWxkIG5vdCBjYWxsIGBlbWl0KClgIGRpcmVjdGx5LCBhcyBpdCBpcyBub3QgaW1wZXJhdGl2ZS4gU2VlIGh0dHBzOi8vc3RhdGVseS5haS9kb2NzL2FjdGlvbnMjYnVpbHQtaW4tYWN0aW9ucyBmb3IgbW9yZSBkZXRhaWxzLicpO1xuICB9XG4gIGZ1bmN0aW9uIGVtaXQoX2FyZ3MsIF9wYXJhbXMpIHtcbiAgICB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRoaXMgaXNuJ3Qgc3VwcG9zZWQgdG8gYmUgY2FsbGVkYCk7XG4gICAgfVxuICB9XG4gIGVtaXQudHlwZSA9ICd4c3RhdGUuZW1pdCc7XG4gIGVtaXQuZXZlbnQgPSBldmVudE9yRXhwcjtcbiAgZW1pdC5yZXNvbHZlID0gcmVzb2x2ZUVtaXQ7XG4gIGVtaXQuZXhlY3V0ZSA9IGV4ZWN1dGVFbWl0O1xuICByZXR1cm4gZW1pdDtcbn1cblxuLyoqXG4gKiBAcmVtYXJrc1xuICogYFQgfCB1bmtub3duYCByZWR1Y2VzIHRvIGB1bmtub3duYCBhbmQgdGhhdCBjYW4gYmUgcHJvYmxlbWF0aWMgd2hlbiBpdCBjb21lc1xuICogdG8gY29udGV4dHVhbCB0eXBpbmcuIEl0IGVzcGVjaWFsbHkgaXMgYSBwcm9ibGVtIHdoZW4gdGhlIHVuaW9uIGhhcyBhXG4gKiBmdW5jdGlvbiBtZW1iZXIsIGxpa2UgaGVyZTpcbiAqXG4gKiBgYGB0c1xuICogZGVjbGFyZSBmdW5jdGlvbiB0ZXN0KFxuICogICBjYk9yVmFsOiAoKGFyZzogbnVtYmVyKSA9PiB1bmtub3duKSB8IHVua25vd25cbiAqICk6IHZvaWQ7XG4gKiB0ZXN0KChhcmcpID0+IHt9KTsgLy8gb29wcywgaW1wbGljaXQgYW55XG4gKiBgYGBcbiAqXG4gKiBUaGlzIHR5cGUgY2FuIGJlIHVzZWQgdG8gYXZvaWQgdGhpcyBwcm9ibGVtLiBUaGlzIHVuaW9uIHJlcHJlc2VudHMgdGhlIHNhbWVcbiAqIHZhbHVlIHNwYWNlIGFzIGB1bmtub3duYC5cbiAqL1xuXG4vLyBodHRwczovL2dpdGh1Yi5jb20vbWljcm9zb2Z0L1R5cGVTY3JpcHQvaXNzdWVzLzIzMTgyI2lzc3VlY29tbWVudC0zNzkwOTE4ODdcblxuLy8gQFRPRE86IFJlcGxhY2Ugd2l0aCBuYXRpdmUgYE5vSW5mZXJgIHdoZW4gVFMgaXNzdWUgZ2V0cyBmaXhlZDpcbi8vIGh0dHBzOi8vZ2l0aHViLmNvbS9taWNyb3NvZnQvVHlwZVNjcmlwdC9wdWxsLzU3NjczXG5cbi8qKiBAZGVwcmVjYXRlZCBVc2UgdGhlIGJ1aWx0LWluIGBOb0luZmVyYCB0eXBlIGluc3RlYWQgKi9cblxuLyoqIFRoZSBmdWxsIGRlZmluaXRpb24gb2YgYW4gZXZlbnQsIHdpdGggYSBzdHJpbmcgYHR5cGVgLiAqL1xuXG4vKipcbiAqIFRoZSBzdHJpbmcgb3Igb2JqZWN0IHJlcHJlc2VudGluZyB0aGUgc3RhdGUgdmFsdWUgcmVsYXRpdmUgdG8gdGhlIHBhcmVudFxuICogc3RhdGUgbm9kZS5cbiAqXG4gKiBAcmVtYXJrc1xuICogLSBGb3IgYSBjaGlsZCBhdG9taWMgc3RhdGUgbm9kZSwgdGhpcyBpcyBhIHN0cmluZywgZS5nLiwgYFwicGVuZGluZ1wiYC5cbiAqIC0gRm9yIGNvbXBsZXggc3RhdGUgbm9kZXMsIHRoaXMgaXMgYW4gb2JqZWN0LCBlLmcuLCBgeyBzdWNjZXNzOlxuICogICBcInNvbWVDaGlsZFN0YXRlXCIgfWAuXG4gKi9cblxuLyoqIEBkZXByZWNhdGVkIFVzZSBgQW55TWFjaGluZVNuYXBzaG90YCBpbnN0ZWFkICovXG5cbi8vIFRPRE86IHBvc3NpYmx5IHJlZmFjdG9yIHRoaXMgc29tZWhvdywgdXNlIGV2ZW4gYSBzaW1wbGVyIHR5cGUsIGFuZCBtYXliZSBldmVuIG1ha2UgYG1hY2hpbmUub3B0aW9uc2AgcHJpdmF0ZSBvciBzb21ldGhpbmdcbi8qKiBAaWdub3JlICovXG5cbmxldCBTcGVjaWFsVGFyZ2V0cyA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoU3BlY2lhbFRhcmdldHMpIHtcbiAgU3BlY2lhbFRhcmdldHNbXCJQYXJlbnRcIl0gPSBcIiNfcGFyZW50XCI7XG4gIFNwZWNpYWxUYXJnZXRzW1wiSW50ZXJuYWxcIl0gPSBcIiNfaW50ZXJuYWxcIjtcbiAgcmV0dXJuIFNwZWNpYWxUYXJnZXRzO1xufSh7fSk7XG5cbi8qKiBAZGVwcmVjYXRlZCBVc2UgYEFueUFjdG9yYCBpbnN0ZWFkLiAqL1xuXG4vLyBCYXNlZCBvbiBSeEpTIHR5cGVzXG5cbi8vIFRPRE86IGluIHY2LCB0aGlzIHNob3VsZCBvbmx5IGFjY2VwdCBBbnlBY3RvckxvZ2ljLCBsaWtlIEFjdG9yUmVmRnJvbUxvZ2ljXG5cbi8qKiBAZGVwcmVjYXRlZCBVc2UgYEFjdG9yPFQ+YCBpbnN0ZWFkLiAqL1xuXG4vKipcbiAqIFJlcHJlc2VudHMgbG9naWMgd2hpY2ggY2FuIGJlIHVzZWQgYnkgYW4gYWN0b3IuXG4gKlxuICogQHRlbXBsYXRlIFRTbmFwc2hvdCAtIFRoZSB0eXBlIG9mIHRoZSBzbmFwc2hvdC5cbiAqIEB0ZW1wbGF0ZSBURXZlbnQgLSBUaGUgdHlwZSBvZiB0aGUgZXZlbnQgb2JqZWN0LlxuICogQHRlbXBsYXRlIFRJbnB1dCAtIFRoZSB0eXBlIG9mIHRoZSBpbnB1dC5cbiAqIEB0ZW1wbGF0ZSBUU3lzdGVtIC0gVGhlIHR5cGUgb2YgdGhlIGFjdG9yIHN5c3RlbS5cbiAqL1xuXG4vKiogQGRlcHJlY2F0ZWQgKi9cblxuLy8gVE9ETzogY292ZXIgYWxsIHRoYXQgY2FuIGJlIGFjdHVhbGx5IHJldHVybmVkXG5cbmZ1bmN0aW9uIHJlc29sdmVTZW5kVG8oYWN0b3JTY29wZSwgc25hcHNob3QsIGFyZ3MsIGFjdGlvblBhcmFtcywge1xuICB0byxcbiAgZXZlbnQ6IGV2ZW50T3JFeHByLFxuICBpZCxcbiAgZGVsYXlcbn0sIGV4dHJhKSB7XG4gIGNvbnN0IGRlbGF5c01hcCA9IHNuYXBzaG90Lm1hY2hpbmUuaW1wbGVtZW50YXRpb25zLmRlbGF5cztcbiAgaWYgKHR5cGVvZiBldmVudE9yRXhwciA9PT0gJ3N0cmluZycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9yZXN0cmljdC10ZW1wbGF0ZS1leHByZXNzaW9uc1xuICAgIGBPbmx5IGV2ZW50IG9iamVjdHMgbWF5IGJlIHVzZWQgd2l0aCBzZW5kVG87IHVzZSBzZW5kVG8oeyB0eXBlOiBcIiR7ZXZlbnRPckV4cHJ9XCIgfSkgaW5zdGVhZGApO1xuICB9XG4gIGNvbnN0IHJlc29sdmVkRXZlbnQgPSB0eXBlb2YgZXZlbnRPckV4cHIgPT09ICdmdW5jdGlvbicgPyBldmVudE9yRXhwcihhcmdzLCBhY3Rpb25QYXJhbXMpIDogZXZlbnRPckV4cHI7XG4gIGxldCByZXNvbHZlZERlbGF5O1xuICBpZiAodHlwZW9mIGRlbGF5ID09PSAnc3RyaW5nJykge1xuICAgIGNvbnN0IGNvbmZpZ0RlbGF5ID0gZGVsYXlzTWFwICYmIGRlbGF5c01hcFtkZWxheV07XG4gICAgcmVzb2x2ZWREZWxheSA9IHR5cGVvZiBjb25maWdEZWxheSA9PT0gJ2Z1bmN0aW9uJyA/IGNvbmZpZ0RlbGF5KGFyZ3MsIGFjdGlvblBhcmFtcykgOiBjb25maWdEZWxheTtcbiAgfSBlbHNlIHtcbiAgICByZXNvbHZlZERlbGF5ID0gdHlwZW9mIGRlbGF5ID09PSAnZnVuY3Rpb24nID8gZGVsYXkoYXJncywgYWN0aW9uUGFyYW1zKSA6IGRlbGF5O1xuICB9XG4gIGNvbnN0IHJlc29sdmVkVGFyZ2V0ID0gdHlwZW9mIHRvID09PSAnZnVuY3Rpb24nID8gdG8oYXJncywgYWN0aW9uUGFyYW1zKSA6IHRvO1xuICBsZXQgdGFyZ2V0QWN0b3JSZWY7XG4gIGlmICh0eXBlb2YgcmVzb2x2ZWRUYXJnZXQgPT09ICdzdHJpbmcnKSB7XG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11bnNhZmUtZW51bS1jb21wYXJpc29uXG4gICAgaWYgKHJlc29sdmVkVGFyZ2V0ID09PSBTcGVjaWFsVGFyZ2V0cy5QYXJlbnQpIHtcbiAgICAgIHRhcmdldEFjdG9yUmVmID0gYWN0b3JTY29wZS5zZWxmLl9wYXJlbnQ7XG4gICAgfVxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdW5zYWZlLWVudW0tY29tcGFyaXNvblxuICAgIGVsc2UgaWYgKHJlc29sdmVkVGFyZ2V0ID09PSBTcGVjaWFsVGFyZ2V0cy5JbnRlcm5hbCkge1xuICAgICAgdGFyZ2V0QWN0b3JSZWYgPSBhY3RvclNjb3BlLnNlbGY7XG4gICAgfSBlbHNlIGlmIChyZXNvbHZlZFRhcmdldC5zdGFydHNXaXRoKCcjXycpKSB7XG4gICAgICAvLyBTQ1hNTCBjb21wYXRpYmlsaXR5OiBodHRwczovL3d3dy53My5vcmcvVFIvc2N4bWwvI1NDWE1MRXZlbnRQcm9jZXNzb3JcbiAgICAgIC8vICNfaW52b2tlaWQuIElmIHRoZSB0YXJnZXQgaXMgdGhlIHNwZWNpYWwgdGVybSAnI19pbnZva2VpZCcsIHdoZXJlIGludm9rZWlkIGlzIHRoZSBpbnZva2VpZCBvZiBhbiBTQ1hNTCBzZXNzaW9uIHRoYXQgdGhlIHNlbmRpbmcgc2Vzc2lvbiBoYXMgY3JlYXRlZCBieSA8aW52b2tlPiwgdGhlIFByb2Nlc3NvciBtdXN0IGFkZCB0aGUgZXZlbnQgdG8gdGhlIGV4dGVybmFsIHF1ZXVlIG9mIHRoYXQgc2Vzc2lvbi5cbiAgICAgIHRhcmdldEFjdG9yUmVmID0gc25hcHNob3QuY2hpbGRyZW5bcmVzb2x2ZWRUYXJnZXQuc2xpY2UoMildO1xuICAgIH0gZWxzZSB7XG4gICAgICB0YXJnZXRBY3RvclJlZiA9IGV4dHJhLmRlZmVycmVkQWN0b3JJZHM/LmluY2x1ZGVzKHJlc29sdmVkVGFyZ2V0KSA/IHJlc29sdmVkVGFyZ2V0IDogc25hcHNob3QuY2hpbGRyZW5bcmVzb2x2ZWRUYXJnZXRdO1xuICAgIH1cbiAgICBpZiAoIXRhcmdldEFjdG9yUmVmKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuYWJsZSB0byBzZW5kIGV2ZW50IHRvIGFjdG9yICcke3Jlc29sdmVkVGFyZ2V0fScgZnJvbSBtYWNoaW5lICcke3NuYXBzaG90Lm1hY2hpbmUuaWR9Jy5gKTtcbiAgICB9XG4gIH0gZWxzZSB7XG4gICAgdGFyZ2V0QWN0b3JSZWYgPSByZXNvbHZlZFRhcmdldCB8fCBhY3RvclNjb3BlLnNlbGY7XG4gIH1cbiAgcmV0dXJuIFtzbmFwc2hvdCwge1xuICAgIHRvOiB0YXJnZXRBY3RvclJlZixcbiAgICB0YXJnZXRJZDogdHlwZW9mIHJlc29sdmVkVGFyZ2V0ID09PSAnc3RyaW5nJyA/IHJlc29sdmVkVGFyZ2V0IDogdW5kZWZpbmVkLFxuICAgIGV2ZW50OiByZXNvbHZlZEV2ZW50LFxuICAgIGlkLFxuICAgIGRlbGF5OiByZXNvbHZlZERlbGF5XG4gIH0sIHVuZGVmaW5lZF07XG59XG5mdW5jdGlvbiByZXRyeVJlc29sdmVTZW5kVG8oXywgc25hcHNob3QsIHBhcmFtcykge1xuICBpZiAodHlwZW9mIHBhcmFtcy50byA9PT0gJ3N0cmluZycpIHtcbiAgICBwYXJhbXMudG8gPSBzbmFwc2hvdC5jaGlsZHJlbltwYXJhbXMudG9dO1xuICB9XG59XG5mdW5jdGlvbiBleGVjdXRlU2VuZFRvKGFjdG9yU2NvcGUsIHBhcmFtcykge1xuICAvLyB0aGlzIGZvcm1zIGFuIG91dGdvaW5nIGV2ZW50cyBxdWV1ZVxuICAvLyB0aGFua3MgdG8gdGhhdCB0aGUgcmVjaXBpZW50IGFjdG9ycyBhcmUgYWJsZSB0byByZWFkIHRoZSAqdXBkYXRlZCogc25hcHNob3QgdmFsdWUgb2YgdGhlIHNlbmRlclxuICBhY3RvclNjb3BlLmRlZmVyKCgpID0+IHtcbiAgICBjb25zdCB7XG4gICAgICB0byxcbiAgICAgIGV2ZW50LFxuICAgICAgZGVsYXksXG4gICAgICBpZFxuICAgIH0gPSBwYXJhbXM7XG4gICAgaWYgKHR5cGVvZiBkZWxheSA9PT0gJ251bWJlcicpIHtcbiAgICAgIGFjdG9yU2NvcGUuc3lzdGVtLnNjaGVkdWxlci5zY2hlZHVsZShhY3RvclNjb3BlLnNlbGYsIHRvLCBldmVudCwgZGVsYXksIGlkKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgYWN0b3JTY29wZS5zeXN0ZW0uX3JlbGF5KGFjdG9yU2NvcGUuc2VsZixcbiAgICAvLyBhdCB0aGlzIHBvaW50LCBpbiBhIGRlZmVycmVkIHRhc2ssIGl0IHNob3VsZCBhbHJlYWR5IGJlIG11dGF0ZWQgYnkgcmV0cnlSZXNvbHZlU2VuZFRvXG4gICAgLy8gaWYgaXQgaW5pdGlhbGx5IHN0YXJ0ZWQgYXMgYSBzdHJpbmdcbiAgICB0bywgZXZlbnQudHlwZSA9PT0gWFNUQVRFX0VSUk9SID8gY3JlYXRlRXJyb3JBY3RvckV2ZW50KGFjdG9yU2NvcGUuc2VsZi5pZCwgZXZlbnQuZGF0YSkgOiBldmVudCk7XG4gIH0pO1xufVxuLyoqXG4gKiBTZW5kcyBhbiBldmVudCB0byBhbiBhY3Rvci5cbiAqXG4gKiBAcGFyYW0gYWN0b3IgVGhlIGBBY3RvclJlZmAgdG8gc2VuZCB0aGUgZXZlbnQgdG8uXG4gKiBAcGFyYW0gZXZlbnQgVGhlIGV2ZW50IHRvIHNlbmQsIG9yIGFuIGV4cHJlc3Npb24gdGhhdCBldmFsdWF0ZXMgdG8gdGhlIGV2ZW50XG4gKiAgIHRvIHNlbmRcbiAqIEBwYXJhbSBvcHRpb25zIFNlbmQgYWN0aW9uIG9wdGlvbnNcbiAqXG4gKiAgIC0gYGlkYCAtIFRoZSB1bmlxdWUgc2VuZCBldmVudCBpZGVudGlmaWVyICh1c2VkIHdpdGggYGNhbmNlbCgpYCkuXG4gKiAgIC0gYGRlbGF5YCAtIFRoZSBudW1iZXIgb2YgbWlsbGlzZWNvbmRzIHRvIGRlbGF5IHRoZSBzZW5kaW5nIG9mIHRoZSBldmVudC5cbiAqL1xuZnVuY3Rpb24gc2VuZFRvKHRvLCBldmVudE9yRXhwciwgb3B0aW9ucykge1xuICBpZiAoZXhlY3V0aW5nQ3VzdG9tQWN0aW9uKSB7XG4gICAgY29uc29sZS53YXJuKCdDdXN0b20gYWN0aW9ucyBzaG91bGQgbm90IGNhbGwgYHNlbmRUbygpYCBkaXJlY3RseSwgYXMgaXQgaXMgbm90IGltcGVyYXRpdmUuIFNlZSBodHRwczovL3N0YXRlbHkuYWkvZG9jcy9hY3Rpb25zI2J1aWx0LWluLWFjdGlvbnMgZm9yIG1vcmUgZGV0YWlscy4nKTtcbiAgfVxuICBmdW5jdGlvbiBzZW5kVG8oX2FyZ3MsIF9wYXJhbXMpIHtcbiAgICB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRoaXMgaXNuJ3Qgc3VwcG9zZWQgdG8gYmUgY2FsbGVkYCk7XG4gICAgfVxuICB9XG4gIHNlbmRUby50eXBlID0gJ3hzdGF0ZS5zZW5kVG8nO1xuICBzZW5kVG8udG8gPSB0bztcbiAgc2VuZFRvLmV2ZW50ID0gZXZlbnRPckV4cHI7XG4gIHNlbmRUby5pZCA9IG9wdGlvbnM/LmlkO1xuICBzZW5kVG8uZGVsYXkgPSBvcHRpb25zPy5kZWxheTtcbiAgc2VuZFRvLnJlc29sdmUgPSByZXNvbHZlU2VuZFRvO1xuICBzZW5kVG8ucmV0cnlSZXNvbHZlID0gcmV0cnlSZXNvbHZlU2VuZFRvO1xuICBzZW5kVG8uZXhlY3V0ZSA9IGV4ZWN1dGVTZW5kVG87XG4gIHJldHVybiBzZW5kVG87XG59XG5cbi8qKlxuICogU2VuZHMgYW4gZXZlbnQgdG8gdGhpcyBtYWNoaW5lJ3MgcGFyZW50LlxuICpcbiAqIEBwYXJhbSBldmVudCBUaGUgZXZlbnQgdG8gc2VuZCB0byB0aGUgcGFyZW50IG1hY2hpbmUuXG4gKiBAcGFyYW0gb3B0aW9ucyBPcHRpb25zIHRvIHBhc3MgaW50byB0aGUgc2VuZCBldmVudC5cbiAqL1xuZnVuY3Rpb24gc2VuZFBhcmVudChldmVudCwgb3B0aW9ucykge1xuICByZXR1cm4gc2VuZFRvKFNwZWNpYWxUYXJnZXRzLlBhcmVudCwgZXZlbnQsIG9wdGlvbnMpO1xufVxuLyoqXG4gKiBGb3J3YXJkcyAoc2VuZHMpIGFuIGV2ZW50IHRvIHRoZSBgdGFyZ2V0YCBhY3Rvci5cbiAqXG4gKiBAcGFyYW0gdGFyZ2V0IFRoZSB0YXJnZXQgYWN0b3IgdG8gZm9yd2FyZCB0aGUgZXZlbnQgdG8uXG4gKiBAcGFyYW0gb3B0aW9ucyBPcHRpb25zIHRvIHBhc3MgaW50byB0aGUgc2VuZCBhY3Rpb24gY3JlYXRvci5cbiAqL1xuZnVuY3Rpb24gZm9yd2FyZFRvKHRhcmdldCwgb3B0aW9ucykge1xuICBpZiAoKCF0YXJnZXQgfHwgdHlwZW9mIHRhcmdldCA9PT0gJ2Z1bmN0aW9uJykpIHtcbiAgICBjb25zdCBvcmlnaW5hbFRhcmdldCA9IHRhcmdldDtcbiAgICB0YXJnZXQgPSAoLi4uYXJncykgPT4ge1xuICAgICAgY29uc3QgcmVzb2x2ZWRUYXJnZXQgPSB0eXBlb2Ygb3JpZ2luYWxUYXJnZXQgPT09ICdmdW5jdGlvbicgPyBvcmlnaW5hbFRhcmdldCguLi5hcmdzKSA6IG9yaWdpbmFsVGFyZ2V0O1xuICAgICAgaWYgKCFyZXNvbHZlZFRhcmdldCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEF0dGVtcHRlZCB0byBmb3J3YXJkIGV2ZW50IHRvIHVuZGVmaW5lZCBhY3Rvci4gVGhpcyByaXNrcyBhbiBpbmZpbml0ZSBsb29wIGluIHRoZSBzZW5kZXIuYCk7XG4gICAgICB9XG4gICAgICByZXR1cm4gcmVzb2x2ZWRUYXJnZXQ7XG4gICAgfTtcbiAgfVxuICByZXR1cm4gc2VuZFRvKHRhcmdldCwgKHtcbiAgICBldmVudFxuICB9KSA9PiBldmVudCwgb3B0aW9ucyk7XG59XG5cbmZ1bmN0aW9uIHJlc29sdmVFbnF1ZXVlQWN0aW9ucyhhY3RvclNjb3BlLCBzbmFwc2hvdCwgYXJncywgYWN0aW9uUGFyYW1zLCB7XG4gIGNvbGxlY3Rcbn0pIHtcbiAgY29uc3QgYWN0aW9ucyA9IFtdO1xuICBjb25zdCBlbnF1ZXVlID0gZnVuY3Rpb24gZW5xdWV1ZShhY3Rpb24pIHtcbiAgICBhY3Rpb25zLnB1c2goYWN0aW9uKTtcbiAgfTtcbiAgZW5xdWV1ZS5hc3NpZ24gPSAoLi4uYXJncykgPT4ge1xuICAgIGFjdGlvbnMucHVzaChhc3NpZ24oLi4uYXJncykpO1xuICB9O1xuICBlbnF1ZXVlLmNhbmNlbCA9ICguLi5hcmdzKSA9PiB7XG4gICAgYWN0aW9ucy5wdXNoKGNhbmNlbCguLi5hcmdzKSk7XG4gIH07XG4gIGVucXVldWUucmFpc2UgPSAoLi4uYXJncykgPT4ge1xuICAgIC8vIGZvciBzb21lIHJlYXNvbiBpdCBmYWlscyB0byBpbmZlciBgVERlbGF5YCBmcm9tIGAuLi5hcmdzYCBoZXJlIGFuZCBwaWNrcyBpdHMgZGVmYXVsdCAoYG5ldmVyYClcbiAgICAvLyB0aGVuIGl0IGZhaWxzIHRvIHR5cGVjaGVjayB0aGF0IGJlY2F1c2UgYC4uLmFyZ3NgIHVzZSBgc3RyaW5nYCBpbiBwbGFjZSBvZiBgVERlbGF5YFxuICAgIGFjdGlvbnMucHVzaChyYWlzZSguLi5hcmdzKSk7XG4gIH07XG4gIGVucXVldWUuc2VuZFRvID0gKC4uLmFyZ3MpID0+IHtcbiAgICAvLyBmb3Igc29tZSByZWFzb24gaXQgZmFpbHMgdG8gaW5mZXIgYFREZWxheWAgZnJvbSBgLi4uYXJnc2AgaGVyZSBhbmQgcGlja3MgaXRzIGRlZmF1bHQgKGBuZXZlcmApXG4gICAgLy8gdGhlbiBpdCBmYWlscyB0byB0eXBlY2hlY2sgdGhhdCBiZWNhdXNlIGAuLi5hcmdzYCB1c2UgYHN0cmluZ2AgaW4gcGxhY2Ugb2YgYFREZWxheVxuICAgIGFjdGlvbnMucHVzaChzZW5kVG8oLi4uYXJncykpO1xuICB9O1xuICBlbnF1ZXVlLnNlbmRQYXJlbnQgPSAoLi4uYXJncykgPT4ge1xuICAgIGFjdGlvbnMucHVzaChzZW5kUGFyZW50KC4uLmFyZ3MpKTtcbiAgfTtcbiAgZW5xdWV1ZS5zcGF3bkNoaWxkID0gKC4uLmFyZ3MpID0+IHtcbiAgICBhY3Rpb25zLnB1c2goc3Bhd25DaGlsZCguLi5hcmdzKSk7XG4gIH07XG4gIGVucXVldWUuc3RvcENoaWxkID0gKC4uLmFyZ3MpID0+IHtcbiAgICBhY3Rpb25zLnB1c2goc3RvcENoaWxkKC4uLmFyZ3MpKTtcbiAgfTtcbiAgZW5xdWV1ZS5lbWl0ID0gKC4uLmFyZ3MpID0+IHtcbiAgICBhY3Rpb25zLnB1c2goZW1pdCguLi5hcmdzKSk7XG4gIH07XG4gIGNvbGxlY3Qoe1xuICAgIGNvbnRleHQ6IGFyZ3MuY29udGV4dCxcbiAgICBldmVudDogYXJncy5ldmVudCxcbiAgICBlbnF1ZXVlLFxuICAgIGNoZWNrOiBndWFyZCA9PiBldmFsdWF0ZUd1YXJkKGd1YXJkLCBzbmFwc2hvdC5jb250ZXh0LCBhcmdzLmV2ZW50LCBzbmFwc2hvdCksXG4gICAgc2VsZjogYWN0b3JTY29wZS5zZWxmLFxuICAgIHN5c3RlbTogYWN0b3JTY29wZS5zeXN0ZW1cbiAgfSwgYWN0aW9uUGFyYW1zKTtcbiAgcmV0dXJuIFtzbmFwc2hvdCwgdW5kZWZpbmVkLCBhY3Rpb25zXTtcbn1cbi8qKlxuICogQ3JlYXRlcyBhbiBhY3Rpb24gb2JqZWN0IHRoYXQgd2lsbCBleGVjdXRlIGFjdGlvbnMgdGhhdCBhcmUgcXVldWVkIGJ5IHRoZVxuICogYGVucXVldWUoYWN0aW9uKWAgZnVuY3Rpb24uXG4gKlxuICogQGV4YW1wbGVcbiAqXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgY3JlYXRlTWFjaGluZSwgZW5xdWV1ZUFjdGlvbnMgfSBmcm9tICd4c3RhdGUnO1xuICpcbiAqIGNvbnN0IG1hY2hpbmUgPSBjcmVhdGVNYWNoaW5lKHtcbiAqICAgZW50cnk6IGVucXVldWVBY3Rpb25zKCh7IGVucXVldWUsIGNoZWNrIH0pID0+IHtcbiAqICAgICBlbnF1ZXVlLmFzc2lnbih7IGNvdW50OiAwIH0pO1xuICpcbiAqICAgICBpZiAoY2hlY2soJ3NvbWVHdWFyZCcpKSB7XG4gKiAgICAgICBlbnF1ZXVlLmFzc2lnbih7IGNvdW50OiAxIH0pO1xuICogICAgIH1cbiAqXG4gKiAgICAgZW5xdWV1ZSgnc29tZUFjdGlvbicpO1xuICogICB9KVxuICogfSk7XG4gKiBgYGBcbiAqL1xuZnVuY3Rpb24gZW5xdWV1ZUFjdGlvbnMoY29sbGVjdCkge1xuICBmdW5jdGlvbiBlbnF1ZXVlQWN0aW9ucyhfYXJncywgX3BhcmFtcykge1xuICAgIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVGhpcyBpc24ndCBzdXBwb3NlZCB0byBiZSBjYWxsZWRgKTtcbiAgICB9XG4gIH1cbiAgZW5xdWV1ZUFjdGlvbnMudHlwZSA9ICd4c3RhdGUuZW5xdWV1ZUFjdGlvbnMnO1xuICBlbnF1ZXVlQWN0aW9ucy5jb2xsZWN0ID0gY29sbGVjdDtcbiAgZW5xdWV1ZUFjdGlvbnMucmVzb2x2ZSA9IHJlc29sdmVFbnF1ZXVlQWN0aW9ucztcbiAgcmV0dXJuIGVucXVldWVBY3Rpb25zO1xufVxuXG5mdW5jdGlvbiByZXNvbHZlTG9nKF8sIHNuYXBzaG90LCBhY3Rpb25BcmdzLCBhY3Rpb25QYXJhbXMsIHtcbiAgdmFsdWUsXG4gIGxhYmVsXG59KSB7XG4gIHJldHVybiBbc25hcHNob3QsIHtcbiAgICB2YWx1ZTogdHlwZW9mIHZhbHVlID09PSAnZnVuY3Rpb24nID8gdmFsdWUoYWN0aW9uQXJncywgYWN0aW9uUGFyYW1zKSA6IHZhbHVlLFxuICAgIGxhYmVsXG4gIH0sIHVuZGVmaW5lZF07XG59XG5mdW5jdGlvbiBleGVjdXRlTG9nKHtcbiAgbG9nZ2VyXG59LCB7XG4gIHZhbHVlLFxuICBsYWJlbFxufSkge1xuICBpZiAobGFiZWwpIHtcbiAgICBsb2dnZXIobGFiZWwsIHZhbHVlKTtcbiAgfSBlbHNlIHtcbiAgICBsb2dnZXIodmFsdWUpO1xuICB9XG59XG4vKipcbiAqIEBwYXJhbSBleHByIFRoZSBleHByZXNzaW9uIGZ1bmN0aW9uIHRvIGV2YWx1YXRlIHdoaWNoIHdpbGwgYmUgbG9nZ2VkLiBUYWtlc1xuICogICBpbiAyIGFyZ3VtZW50czpcbiAqXG4gKiAgIC0gYGN0eGAgLSB0aGUgY3VycmVudCBzdGF0ZSBjb250ZXh0XG4gKiAgIC0gYGV2ZW50YCAtIHRoZSBldmVudCB0aGF0IGNhdXNlZCB0aGlzIGFjdGlvbiB0byBiZSBleGVjdXRlZC5cbiAqXG4gKiBAcGFyYW0gbGFiZWwgVGhlIGxhYmVsIHRvIGdpdmUgdG8gdGhlIGxvZ2dlZCBleHByZXNzaW9uLlxuICovXG5mdW5jdGlvbiBsb2codmFsdWUgPSAoe1xuICBjb250ZXh0LFxuICBldmVudFxufSkgPT4gKHtcbiAgY29udGV4dCxcbiAgZXZlbnRcbn0pLCBsYWJlbCkge1xuICBmdW5jdGlvbiBsb2coX2FyZ3MsIF9wYXJhbXMpIHtcbiAgICB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRoaXMgaXNuJ3Qgc3VwcG9zZWQgdG8gYmUgY2FsbGVkYCk7XG4gICAgfVxuICB9XG4gIGxvZy50eXBlID0gJ3hzdGF0ZS5sb2cnO1xuICBsb2cudmFsdWUgPSB2YWx1ZTtcbiAgbG9nLmxhYmVsID0gbGFiZWw7XG4gIGxvZy5yZXNvbHZlID0gcmVzb2x2ZUxvZztcbiAgbG9nLmV4ZWN1dGUgPSBleGVjdXRlTG9nO1xuICByZXR1cm4gbG9nO1xufVxuXG5leHBvcnQgeyBTcGVjaWFsVGFyZ2V0cyBhcyBTLCBhc3NpZ24gYXMgYSwgZW5xdWV1ZUFjdGlvbnMgYXMgYiwgc2VuZFRvIGFzIGMsIGVtaXQgYXMgZSwgZm9yd2FyZFRvIGFzIGYsIGxvZyBhcyBsLCBzZW5kUGFyZW50IGFzIHMgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xstate/dist/log-0acd9069.development.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xstate/dist/raise-1db27a82.development.esm.js":
/*!********************************************************************!*\
  !*** ./node_modules/xstate/dist/raise-1db27a82.development.esm.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $: () => (/* binding */ $$ACTOR_TYPE),\n/* harmony export */   A: () => (/* binding */ createActor),\n/* harmony export */   B: () => (/* binding */ Actor),\n/* harmony export */   C: () => (/* binding */ interpret),\n/* harmony export */   D: () => (/* binding */ and),\n/* harmony export */   E: () => (/* binding */ not),\n/* harmony export */   F: () => (/* binding */ or),\n/* harmony export */   G: () => (/* binding */ stateIn),\n/* harmony export */   H: () => (/* binding */ isMachineSnapshot),\n/* harmony export */   I: () => (/* binding */ getAllOwnEventDescriptors),\n/* harmony export */   J: () => (/* binding */ matchesState),\n/* harmony export */   K: () => (/* binding */ pathToStateValue),\n/* harmony export */   L: () => (/* binding */ toObserver),\n/* harmony export */   M: () => (/* binding */ cancel),\n/* harmony export */   N: () => (/* binding */ NULL_EVENT),\n/* harmony export */   O: () => (/* binding */ raise),\n/* harmony export */   P: () => (/* binding */ spawnChild),\n/* harmony export */   Q: () => (/* binding */ stop),\n/* harmony export */   R: () => (/* binding */ stopChild),\n/* harmony export */   S: () => (/* binding */ STATE_DELIMITER),\n/* harmony export */   T: () => (/* binding */ ProcessingStatus),\n/* harmony export */   U: () => (/* binding */ cloneMachineSnapshot),\n/* harmony export */   V: () => (/* binding */ executingCustomAction),\n/* harmony export */   W: () => (/* binding */ XSTATE_ERROR),\n/* harmony export */   X: () => (/* binding */ XSTATE_STOP),\n/* harmony export */   Y: () => (/* binding */ createErrorActorEvent),\n/* harmony export */   a: () => (/* binding */ toTransitionConfigArray),\n/* harmony export */   b: () => (/* binding */ formatTransition),\n/* harmony export */   c: () => (/* binding */ createInvokeId),\n/* harmony export */   d: () => (/* binding */ formatInitialTransition),\n/* harmony export */   e: () => (/* binding */ evaluateGuard),\n/* harmony export */   f: () => (/* binding */ formatTransitions),\n/* harmony export */   g: () => (/* binding */ getDelayedTransitions),\n/* harmony export */   h: () => (/* binding */ getCandidates),\n/* harmony export */   i: () => (/* binding */ getAllStateNodes),\n/* harmony export */   j: () => (/* binding */ getStateNodes),\n/* harmony export */   k: () => (/* binding */ createMachineSnapshot),\n/* harmony export */   l: () => (/* binding */ isInFinalState),\n/* harmony export */   m: () => (/* binding */ mapValues),\n/* harmony export */   n: () => (/* binding */ macrostep),\n/* harmony export */   o: () => (/* binding */ transitionNode),\n/* harmony export */   p: () => (/* binding */ resolveActionsAndContext),\n/* harmony export */   q: () => (/* binding */ createInitEvent),\n/* harmony export */   r: () => (/* binding */ resolveStateValue),\n/* harmony export */   s: () => (/* binding */ microstep),\n/* harmony export */   t: () => (/* binding */ toArray),\n/* harmony export */   u: () => (/* binding */ getInitialStateNodes),\n/* harmony export */   v: () => (/* binding */ toStatePath),\n/* harmony export */   w: () => (/* binding */ isStateId),\n/* harmony export */   x: () => (/* binding */ getStateNodeByPath),\n/* harmony export */   y: () => (/* binding */ getPersistedSnapshot),\n/* harmony export */   z: () => (/* binding */ resolveReferencedActor)\n/* harmony export */ });\n/* harmony import */ var _dev_dist_xstate_dev_development_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dev/dist/xstate-dev.development.esm.js */ \"(ssr)/./node_modules/xstate/dev/dist/xstate-dev.development.esm.js\");\n\n\nclass Mailbox {\n  constructor(_process) {\n    this._process = _process;\n    this._active = false;\n    this._current = null;\n    this._last = null;\n  }\n  start() {\n    this._active = true;\n    this.flush();\n  }\n  clear() {\n    // we can't set _current to null because we might be currently processing\n    // and enqueue following clear shouldnt start processing the enqueued item immediately\n    if (this._current) {\n      this._current.next = null;\n      this._last = this._current;\n    }\n  }\n  enqueue(event) {\n    const enqueued = {\n      value: event,\n      next: null\n    };\n    if (this._current) {\n      this._last.next = enqueued;\n      this._last = enqueued;\n      return;\n    }\n    this._current = enqueued;\n    this._last = enqueued;\n    if (this._active) {\n      this.flush();\n    }\n  }\n  flush() {\n    while (this._current) {\n      // atm the given _process is responsible for implementing proper try/catch handling\n      // we assume here that this won't throw in a way that can affect this mailbox\n      const consumed = this._current;\n      this._process(consumed.value);\n      this._current = consumed.next;\n    }\n    this._last = null;\n  }\n}\n\nconst STATE_DELIMITER = '.';\nconst TARGETLESS_KEY = '';\nconst NULL_EVENT = '';\nconst STATE_IDENTIFIER = '#';\nconst WILDCARD = '*';\nconst XSTATE_INIT = 'xstate.init';\nconst XSTATE_ERROR = 'xstate.error';\nconst XSTATE_STOP = 'xstate.stop';\n\n/**\n * Returns an event that represents an implicit event that is sent after the\n * specified `delay`.\n *\n * @param delayRef The delay in milliseconds\n * @param id The state node ID where this event is handled\n */\nfunction createAfterEvent(delayRef, id) {\n  return {\n    type: `xstate.after.${delayRef}.${id}`\n  };\n}\n\n/**\n * Returns an event that represents that a final state node has been reached in\n * the parent state node.\n *\n * @param id The final state node's parent state node `id`\n * @param output The data to pass into the event\n */\nfunction createDoneStateEvent(id, output) {\n  return {\n    type: `xstate.done.state.${id}`,\n    output\n  };\n}\n\n/**\n * Returns an event that represents that an invoked service has terminated.\n *\n * An invoked service is terminated when it has reached a top-level final state\n * node, but not when it is canceled.\n *\n * @param invokeId The invoked service ID\n * @param output The data to pass into the event\n */\nfunction createDoneActorEvent(invokeId, output) {\n  return {\n    type: `xstate.done.actor.${invokeId}`,\n    output,\n    actorId: invokeId\n  };\n}\nfunction createErrorActorEvent(id, error) {\n  return {\n    type: `xstate.error.actor.${id}`,\n    error,\n    actorId: id\n  };\n}\nfunction createInitEvent(input) {\n  return {\n    type: XSTATE_INIT,\n    input\n  };\n}\n\n/**\n * This function makes sure that unhandled errors are thrown in a separate\n * macrotask. It allows those errors to be detected by global error handlers and\n * reported to bug tracking services without interrupting our own stack of\n * execution.\n *\n * @param err Error to be thrown\n */\nfunction reportUnhandledError(err) {\n  setTimeout(() => {\n    throw err;\n  });\n}\n\nconst symbolObservable = (() => typeof Symbol === 'function' && Symbol.observable || '@@observable')();\n\nfunction matchesState(parentStateId, childStateId) {\n  const parentStateValue = toStateValue(parentStateId);\n  const childStateValue = toStateValue(childStateId);\n  if (typeof childStateValue === 'string') {\n    if (typeof parentStateValue === 'string') {\n      return childStateValue === parentStateValue;\n    }\n\n    // Parent more specific than child\n    return false;\n  }\n  if (typeof parentStateValue === 'string') {\n    return parentStateValue in childStateValue;\n  }\n  return Object.keys(parentStateValue).every(key => {\n    if (!(key in childStateValue)) {\n      return false;\n    }\n    return matchesState(parentStateValue[key], childStateValue[key]);\n  });\n}\nfunction toStatePath(stateId) {\n  if (isArray(stateId)) {\n    return stateId;\n  }\n  const result = [];\n  let segment = '';\n  for (let i = 0; i < stateId.length; i++) {\n    const char = stateId.charCodeAt(i);\n    switch (char) {\n      // \\\n      case 92:\n        // consume the next character\n        segment += stateId[i + 1];\n        // and skip over it\n        i++;\n        continue;\n      // .\n      case 46:\n        result.push(segment);\n        segment = '';\n        continue;\n    }\n    segment += stateId[i];\n  }\n  result.push(segment);\n  return result;\n}\nfunction toStateValue(stateValue) {\n  if (isMachineSnapshot(stateValue)) {\n    return stateValue.value;\n  }\n  if (typeof stateValue !== 'string') {\n    return stateValue;\n  }\n  const statePath = toStatePath(stateValue);\n  return pathToStateValue(statePath);\n}\nfunction pathToStateValue(statePath) {\n  if (statePath.length === 1) {\n    return statePath[0];\n  }\n  const value = {};\n  let marker = value;\n  for (let i = 0; i < statePath.length - 1; i++) {\n    if (i === statePath.length - 2) {\n      marker[statePath[i]] = statePath[i + 1];\n    } else {\n      const previous = marker;\n      marker = {};\n      previous[statePath[i]] = marker;\n    }\n  }\n  return value;\n}\nfunction mapValues(collection, iteratee) {\n  const result = {};\n  const collectionKeys = Object.keys(collection);\n  for (let i = 0; i < collectionKeys.length; i++) {\n    const key = collectionKeys[i];\n    result[key] = iteratee(collection[key], key, collection, i);\n  }\n  return result;\n}\nfunction toArrayStrict(value) {\n  if (isArray(value)) {\n    return value;\n  }\n  return [value];\n}\nfunction toArray(value) {\n  if (value === undefined) {\n    return [];\n  }\n  return toArrayStrict(value);\n}\nfunction resolveOutput(mapper, context, event, self) {\n  if (typeof mapper === 'function') {\n    return mapper({\n      context,\n      event,\n      self\n    });\n  }\n  if (!!mapper && typeof mapper === 'object' && Object.values(mapper).some(val => typeof val === 'function')) {\n    console.warn(`Dynamically mapping values to individual properties is deprecated. Use a single function that returns the mapped object instead.\\nFound object containing properties whose values are possibly mapping functions: ${Object.entries(mapper).filter(([, value]) => typeof value === 'function').map(([key, value]) => `\\n - ${key}: ${value.toString().replace(/\\n\\s*/g, '')}`).join('')}`);\n  }\n  return mapper;\n}\nfunction isArray(value) {\n  return Array.isArray(value);\n}\nfunction isErrorActorEvent(event) {\n  return event.type.startsWith('xstate.error.actor');\n}\nfunction toTransitionConfigArray(configLike) {\n  return toArrayStrict(configLike).map(transitionLike => {\n    if (typeof transitionLike === 'undefined' || typeof transitionLike === 'string') {\n      return {\n        target: transitionLike\n      };\n    }\n    return transitionLike;\n  });\n}\nfunction normalizeTarget(target) {\n  if (target === undefined || target === TARGETLESS_KEY) {\n    return undefined;\n  }\n  return toArray(target);\n}\nfunction toObserver(nextHandler, errorHandler, completionHandler) {\n  const isObserver = typeof nextHandler === 'object';\n  const self = isObserver ? nextHandler : undefined;\n  return {\n    next: (isObserver ? nextHandler.next : nextHandler)?.bind(self),\n    error: (isObserver ? nextHandler.error : errorHandler)?.bind(self),\n    complete: (isObserver ? nextHandler.complete : completionHandler)?.bind(self)\n  };\n}\nfunction createInvokeId(stateNodeId, index) {\n  return `${index}.${stateNodeId}`;\n}\nfunction resolveReferencedActor(machine, src) {\n  const match = src.match(/^xstate\\.invoke\\.(\\d+)\\.(.*)/);\n  if (!match) {\n    return machine.implementations.actors[src];\n  }\n  const [, indexStr, nodeId] = match;\n  const node = machine.getStateNodeById(nodeId);\n  const invokeConfig = node.config.invoke;\n  return (Array.isArray(invokeConfig) ? invokeConfig[indexStr] : invokeConfig).src;\n}\nfunction getAllOwnEventDescriptors(snapshot) {\n  return [...new Set([...snapshot._nodes.flatMap(sn => sn.ownEvents)])];\n}\n\nfunction createScheduledEventId(actorRef, id) {\n  return `${actorRef.sessionId}.${id}`;\n}\nlet idCounter = 0;\nfunction createSystem(rootActor, options) {\n  const children = new Map();\n  const keyedActors = new Map();\n  const reverseKeyedActors = new WeakMap();\n  const inspectionObservers = new Set();\n  const timerMap = {};\n  const {\n    clock,\n    logger\n  } = options;\n  const scheduler = {\n    schedule: (source, target, event, delay, id = Math.random().toString(36).slice(2)) => {\n      const scheduledEvent = {\n        source,\n        target,\n        event,\n        delay,\n        id,\n        startedAt: Date.now()\n      };\n      const scheduledEventId = createScheduledEventId(source, id);\n      system._snapshot._scheduledEvents[scheduledEventId] = scheduledEvent;\n      const timeout = clock.setTimeout(() => {\n        delete timerMap[scheduledEventId];\n        delete system._snapshot._scheduledEvents[scheduledEventId];\n        system._relay(source, target, event);\n      }, delay);\n      timerMap[scheduledEventId] = timeout;\n    },\n    cancel: (source, id) => {\n      const scheduledEventId = createScheduledEventId(source, id);\n      const timeout = timerMap[scheduledEventId];\n      delete timerMap[scheduledEventId];\n      delete system._snapshot._scheduledEvents[scheduledEventId];\n      if (timeout !== undefined) {\n        clock.clearTimeout(timeout);\n      }\n    },\n    cancelAll: actorRef => {\n      for (const scheduledEventId in system._snapshot._scheduledEvents) {\n        const scheduledEvent = system._snapshot._scheduledEvents[scheduledEventId];\n        if (scheduledEvent.source === actorRef) {\n          scheduler.cancel(actorRef, scheduledEvent.id);\n        }\n      }\n    }\n  };\n  const sendInspectionEvent = event => {\n    if (!inspectionObservers.size) {\n      return;\n    }\n    const resolvedInspectionEvent = {\n      ...event,\n      rootId: rootActor.sessionId\n    };\n    inspectionObservers.forEach(observer => observer.next?.(resolvedInspectionEvent));\n  };\n  const system = {\n    _snapshot: {\n      _scheduledEvents: (options?.snapshot && options.snapshot.scheduler) ?? {}\n    },\n    _bookId: () => `x:${idCounter++}`,\n    _register: (sessionId, actorRef) => {\n      children.set(sessionId, actorRef);\n      return sessionId;\n    },\n    _unregister: actorRef => {\n      children.delete(actorRef.sessionId);\n      const systemId = reverseKeyedActors.get(actorRef);\n      if (systemId !== undefined) {\n        keyedActors.delete(systemId);\n        reverseKeyedActors.delete(actorRef);\n      }\n    },\n    get: systemId => {\n      return keyedActors.get(systemId);\n    },\n    _set: (systemId, actorRef) => {\n      const existing = keyedActors.get(systemId);\n      if (existing && existing !== actorRef) {\n        throw new Error(`Actor with system ID '${systemId}' already exists.`);\n      }\n      keyedActors.set(systemId, actorRef);\n      reverseKeyedActors.set(actorRef, systemId);\n    },\n    inspect: observerOrFn => {\n      const observer = toObserver(observerOrFn);\n      inspectionObservers.add(observer);\n      return {\n        unsubscribe() {\n          inspectionObservers.delete(observer);\n        }\n      };\n    },\n    _sendInspectionEvent: sendInspectionEvent,\n    _relay: (source, target, event) => {\n      system._sendInspectionEvent({\n        type: '@xstate.event',\n        sourceRef: source,\n        actorRef: target,\n        event\n      });\n      target._send(event);\n    },\n    scheduler,\n    getSnapshot: () => {\n      return {\n        _scheduledEvents: {\n          ...system._snapshot._scheduledEvents\n        }\n      };\n    },\n    start: () => {\n      const scheduledEvents = system._snapshot._scheduledEvents;\n      system._snapshot._scheduledEvents = {};\n      for (const scheduledId in scheduledEvents) {\n        const {\n          source,\n          target,\n          event,\n          delay,\n          id\n        } = scheduledEvents[scheduledId];\n        scheduler.schedule(source, target, event, delay, id);\n      }\n    },\n    _clock: clock,\n    _logger: logger\n  };\n  return system;\n}\n\nlet executingCustomAction = false;\nconst $$ACTOR_TYPE = 1;\n\n// those values are currently used by @xstate/react directly so it's important to keep the assigned values in sync\nlet ProcessingStatus = /*#__PURE__*/function (ProcessingStatus) {\n  ProcessingStatus[ProcessingStatus[\"NotStarted\"] = 0] = \"NotStarted\";\n  ProcessingStatus[ProcessingStatus[\"Running\"] = 1] = \"Running\";\n  ProcessingStatus[ProcessingStatus[\"Stopped\"] = 2] = \"Stopped\";\n  return ProcessingStatus;\n}({});\nconst defaultOptions = {\n  clock: {\n    setTimeout: (fn, ms) => {\n      return setTimeout(fn, ms);\n    },\n    clearTimeout: id => {\n      return clearTimeout(id);\n    }\n  },\n  logger: console.log.bind(console),\n  devTools: false\n};\n\n/**\n * An Actor is a running process that can receive events, send events and change\n * its behavior based on the events it receives, which can cause effects outside\n * of the actor. When you run a state machine, it becomes an actor.\n */\nclass Actor {\n  /**\n   * Creates a new actor instance for the given logic with the provided options,\n   * if any.\n   *\n   * @param logic The logic to create an actor from\n   * @param options Actor options\n   */\n  constructor(logic, options) {\n    this.logic = logic;\n    /** The current internal state of the actor. */\n    this._snapshot = void 0;\n    /**\n     * The clock that is responsible for setting and clearing timeouts, such as\n     * delayed events and transitions.\n     */\n    this.clock = void 0;\n    this.options = void 0;\n    /** The unique identifier for this actor relative to its parent. */\n    this.id = void 0;\n    this.mailbox = new Mailbox(this._process.bind(this));\n    this.observers = new Set();\n    this.eventListeners = new Map();\n    this.logger = void 0;\n    /** @internal */\n    this._processingStatus = ProcessingStatus.NotStarted;\n    // Actor Ref\n    this._parent = void 0;\n    /** @internal */\n    this._syncSnapshot = void 0;\n    this.ref = void 0;\n    // TODO: add typings for system\n    this._actorScope = void 0;\n    this._systemId = void 0;\n    /** The globally unique process ID for this invocation. */\n    this.sessionId = void 0;\n    /** The system to which this actor belongs. */\n    this.system = void 0;\n    this._doneEvent = void 0;\n    this.src = void 0;\n    // array of functions to defer\n    this._deferred = [];\n    const resolvedOptions = {\n      ...defaultOptions,\n      ...options\n    };\n    const {\n      clock,\n      logger,\n      parent,\n      syncSnapshot,\n      id,\n      systemId,\n      inspect\n    } = resolvedOptions;\n    this.system = parent ? parent.system : createSystem(this, {\n      clock,\n      logger\n    });\n    if (inspect && !parent) {\n      // Always inspect at the system-level\n      this.system.inspect(toObserver(inspect));\n    }\n    this.sessionId = this.system._bookId();\n    this.id = id ?? this.sessionId;\n    this.logger = options?.logger ?? this.system._logger;\n    this.clock = options?.clock ?? this.system._clock;\n    this._parent = parent;\n    this._syncSnapshot = syncSnapshot;\n    this.options = resolvedOptions;\n    this.src = resolvedOptions.src ?? logic;\n    this.ref = this;\n    this._actorScope = {\n      self: this,\n      id: this.id,\n      sessionId: this.sessionId,\n      logger: this.logger,\n      defer: fn => {\n        this._deferred.push(fn);\n      },\n      system: this.system,\n      stopChild: child => {\n        if (child._parent !== this) {\n          throw new Error(`Cannot stop child actor ${child.id} of ${this.id} because it is not a child`);\n        }\n        child._stop();\n      },\n      emit: emittedEvent => {\n        const listeners = this.eventListeners.get(emittedEvent.type);\n        const wildcardListener = this.eventListeners.get('*');\n        if (!listeners && !wildcardListener) {\n          return;\n        }\n        const allListeners = [...(listeners ? listeners.values() : []), ...(wildcardListener ? wildcardListener.values() : [])];\n        for (const handler of allListeners) {\n          handler(emittedEvent);\n        }\n      },\n      actionExecutor: action => {\n        const exec = () => {\n          this._actorScope.system._sendInspectionEvent({\n            type: '@xstate.action',\n            actorRef: this,\n            action: {\n              type: action.type,\n              params: action.params\n            }\n          });\n          if (!action.exec) {\n            return;\n          }\n          const saveExecutingCustomAction = executingCustomAction;\n          try {\n            executingCustomAction = true;\n            action.exec(action.info, action.params);\n          } finally {\n            executingCustomAction = saveExecutingCustomAction;\n          }\n        };\n        if (this._processingStatus === ProcessingStatus.Running) {\n          exec();\n        } else {\n          this._deferred.push(exec);\n        }\n      }\n    };\n\n    // Ensure that the send method is bound to this Actor instance\n    // if destructured\n    this.send = this.send.bind(this);\n    this.system._sendInspectionEvent({\n      type: '@xstate.actor',\n      actorRef: this\n    });\n    if (systemId) {\n      this._systemId = systemId;\n      this.system._set(systemId, this);\n    }\n    this._initState(options?.snapshot ?? options?.state);\n    if (systemId && this._snapshot.status !== 'active') {\n      this.system._unregister(this);\n    }\n  }\n  _initState(persistedState) {\n    try {\n      this._snapshot = persistedState ? this.logic.restoreSnapshot ? this.logic.restoreSnapshot(persistedState, this._actorScope) : persistedState : this.logic.getInitialSnapshot(this._actorScope, this.options?.input);\n    } catch (err) {\n      // if we get here then it means that we assign a value to this._snapshot that is not of the correct type\n      // we can't get the true `TSnapshot & { status: 'error'; }`, it's impossible\n      // so right now this is a lie of sorts\n      this._snapshot = {\n        status: 'error',\n        output: undefined,\n        error: err\n      };\n    }\n  }\n  update(snapshot, event) {\n    // Update state\n    this._snapshot = snapshot;\n\n    // Execute deferred effects\n    let deferredFn;\n    while (deferredFn = this._deferred.shift()) {\n      try {\n        deferredFn();\n      } catch (err) {\n        // this error can only be caught when executing *initial* actions\n        // it's the only time when we call actions provided by the user through those deferreds\n        // when the actor is already running we always execute them synchronously while transitioning\n        // no \"builtin deferred\" should actually throw an error since they are either safe\n        // or the control flow is passed through the mailbox and errors should be caught by the `_process` used by the mailbox\n        this._deferred.length = 0;\n        this._snapshot = {\n          ...snapshot,\n          status: 'error',\n          error: err\n        };\n      }\n    }\n    switch (this._snapshot.status) {\n      case 'active':\n        for (const observer of this.observers) {\n          try {\n            observer.next?.(snapshot);\n          } catch (err) {\n            reportUnhandledError(err);\n          }\n        }\n        break;\n      case 'done':\n        // next observers are meant to be notified about done snapshots\n        // this can be seen as something that is different from how observable work\n        // but with observables `complete` callback is called without any arguments\n        // it's more ergonomic for XState to treat a done snapshot as a \"next\" value\n        // and the completion event as something that is separate,\n        // something that merely follows emitting that done snapshot\n        for (const observer of this.observers) {\n          try {\n            observer.next?.(snapshot);\n          } catch (err) {\n            reportUnhandledError(err);\n          }\n        }\n        this._stopProcedure();\n        this._complete();\n        this._doneEvent = createDoneActorEvent(this.id, this._snapshot.output);\n        if (this._parent) {\n          this.system._relay(this, this._parent, this._doneEvent);\n        }\n        break;\n      case 'error':\n        this._error(this._snapshot.error);\n        break;\n    }\n    this.system._sendInspectionEvent({\n      type: '@xstate.snapshot',\n      actorRef: this,\n      event,\n      snapshot\n    });\n  }\n\n  /**\n   * Subscribe an observer to an actor’s snapshot values.\n   *\n   * @remarks\n   * The observer will receive the actor’s snapshot value when it is emitted.\n   * The observer can be:\n   *\n   * - A plain function that receives the latest snapshot, or\n   * - An observer object whose `.next(snapshot)` method receives the latest\n   *   snapshot\n   *\n   * @example\n   *\n   * ```ts\n   * // Observer as a plain function\n   * const subscription = actor.subscribe((snapshot) => {\n   *   console.log(snapshot);\n   * });\n   * ```\n   *\n   * @example\n   *\n   * ```ts\n   * // Observer as an object\n   * const subscription = actor.subscribe({\n   *   next(snapshot) {\n   *     console.log(snapshot);\n   *   },\n   *   error(err) {\n   *     // ...\n   *   },\n   *   complete() {\n   *     // ...\n   *   }\n   * });\n   * ```\n   *\n   * The return value of `actor.subscribe(observer)` is a subscription object\n   * that has an `.unsubscribe()` method. You can call\n   * `subscription.unsubscribe()` to unsubscribe the observer:\n   *\n   * @example\n   *\n   * ```ts\n   * const subscription = actor.subscribe((snapshot) => {\n   *   // ...\n   * });\n   *\n   * // Unsubscribe the observer\n   * subscription.unsubscribe();\n   * ```\n   *\n   * When the actor is stopped, all of its observers will automatically be\n   * unsubscribed.\n   *\n   * @param observer - Either a plain function that receives the latest\n   *   snapshot, or an observer object whose `.next(snapshot)` method receives\n   *   the latest snapshot\n   */\n\n  subscribe(nextListenerOrObserver, errorListener, completeListener) {\n    const observer = toObserver(nextListenerOrObserver, errorListener, completeListener);\n    if (this._processingStatus !== ProcessingStatus.Stopped) {\n      this.observers.add(observer);\n    } else {\n      switch (this._snapshot.status) {\n        case 'done':\n          try {\n            observer.complete?.();\n          } catch (err) {\n            reportUnhandledError(err);\n          }\n          break;\n        case 'error':\n          {\n            const err = this._snapshot.error;\n            if (!observer.error) {\n              reportUnhandledError(err);\n            } else {\n              try {\n                observer.error(err);\n              } catch (err) {\n                reportUnhandledError(err);\n              }\n            }\n            break;\n          }\n      }\n    }\n    return {\n      unsubscribe: () => {\n        this.observers.delete(observer);\n      }\n    };\n  }\n  on(type, handler) {\n    let listeners = this.eventListeners.get(type);\n    if (!listeners) {\n      listeners = new Set();\n      this.eventListeners.set(type, listeners);\n    }\n    const wrappedHandler = handler.bind(undefined);\n    listeners.add(wrappedHandler);\n    return {\n      unsubscribe: () => {\n        listeners.delete(wrappedHandler);\n      }\n    };\n  }\n\n  /** Starts the Actor from the initial state */\n  start() {\n    if (this._processingStatus === ProcessingStatus.Running) {\n      // Do not restart the service if it is already started\n      return this;\n    }\n    if (this._syncSnapshot) {\n      this.subscribe({\n        next: snapshot => {\n          if (snapshot.status === 'active') {\n            this.system._relay(this, this._parent, {\n              type: `xstate.snapshot.${this.id}`,\n              snapshot\n            });\n          }\n        },\n        error: () => {}\n      });\n    }\n    this.system._register(this.sessionId, this);\n    if (this._systemId) {\n      this.system._set(this._systemId, this);\n    }\n    this._processingStatus = ProcessingStatus.Running;\n\n    // TODO: this isn't correct when rehydrating\n    const initEvent = createInitEvent(this.options.input);\n    this.system._sendInspectionEvent({\n      type: '@xstate.event',\n      sourceRef: this._parent,\n      actorRef: this,\n      event: initEvent\n    });\n    const status = this._snapshot.status;\n    switch (status) {\n      case 'done':\n        // a state machine can be \"done\" upon initialization (it could reach a final state using initial microsteps)\n        // we still need to complete observers, flush deferreds etc\n        this.update(this._snapshot, initEvent);\n        // TODO: rethink cleanup of observers, mailbox, etc\n        return this;\n      case 'error':\n        this._error(this._snapshot.error);\n        return this;\n    }\n    if (!this._parent) {\n      this.system.start();\n    }\n    if (this.logic.start) {\n      try {\n        this.logic.start(this._snapshot, this._actorScope);\n      } catch (err) {\n        this._snapshot = {\n          ...this._snapshot,\n          status: 'error',\n          error: err\n        };\n        this._error(err);\n        return this;\n      }\n    }\n\n    // TODO: this notifies all subscribers but usually this is redundant\n    // there is no real change happening here\n    // we need to rethink if this needs to be refactored\n    this.update(this._snapshot, initEvent);\n    if (this.options.devTools) {\n      this.attachDevTools();\n    }\n    this.mailbox.start();\n    return this;\n  }\n  _process(event) {\n    let nextState;\n    let caughtError;\n    try {\n      nextState = this.logic.transition(this._snapshot, event, this._actorScope);\n    } catch (err) {\n      // we wrap it in a box so we can rethrow it later even if falsy value gets caught here\n      caughtError = {\n        err\n      };\n    }\n    if (caughtError) {\n      const {\n        err\n      } = caughtError;\n      this._snapshot = {\n        ...this._snapshot,\n        status: 'error',\n        error: err\n      };\n      this._error(err);\n      return;\n    }\n    this.update(nextState, event);\n    if (event.type === XSTATE_STOP) {\n      this._stopProcedure();\n      this._complete();\n    }\n  }\n  _stop() {\n    if (this._processingStatus === ProcessingStatus.Stopped) {\n      return this;\n    }\n    this.mailbox.clear();\n    if (this._processingStatus === ProcessingStatus.NotStarted) {\n      this._processingStatus = ProcessingStatus.Stopped;\n      return this;\n    }\n    this.mailbox.enqueue({\n      type: XSTATE_STOP\n    });\n    return this;\n  }\n\n  /** Stops the Actor and unsubscribe all listeners. */\n  stop() {\n    if (this._parent) {\n      throw new Error('A non-root actor cannot be stopped directly.');\n    }\n    return this._stop();\n  }\n  _complete() {\n    for (const observer of this.observers) {\n      try {\n        observer.complete?.();\n      } catch (err) {\n        reportUnhandledError(err);\n      }\n    }\n    this.observers.clear();\n  }\n  _reportError(err) {\n    if (!this.observers.size) {\n      if (!this._parent) {\n        reportUnhandledError(err);\n      }\n      return;\n    }\n    let reportError = false;\n    for (const observer of this.observers) {\n      const errorListener = observer.error;\n      reportError ||= !errorListener;\n      try {\n        errorListener?.(err);\n      } catch (err2) {\n        reportUnhandledError(err2);\n      }\n    }\n    this.observers.clear();\n    if (reportError) {\n      reportUnhandledError(err);\n    }\n  }\n  _error(err) {\n    this._stopProcedure();\n    this._reportError(err);\n    if (this._parent) {\n      this.system._relay(this, this._parent, createErrorActorEvent(this.id, err));\n    }\n  }\n  // TODO: atm children don't belong entirely to the actor so\n  // in a way - it's not even super aware of them\n  // so we can't stop them from here but we really should!\n  // right now, they are being stopped within the machine's transition\n  // but that could throw and leave us with \"orphaned\" active actors\n  _stopProcedure() {\n    if (this._processingStatus !== ProcessingStatus.Running) {\n      // Actor already stopped; do nothing\n      return this;\n    }\n\n    // Cancel all delayed events\n    this.system.scheduler.cancelAll(this);\n\n    // TODO: mailbox.reset\n    this.mailbox.clear();\n    // TODO: after `stop` we must prepare ourselves for receiving events again\n    // events sent *after* stop signal must be queued\n    // it seems like this should be the common behavior for all of our consumers\n    // so perhaps this should be unified somehow for all of them\n    this.mailbox = new Mailbox(this._process.bind(this));\n    this._processingStatus = ProcessingStatus.Stopped;\n    this.system._unregister(this);\n    return this;\n  }\n\n  /** @internal */\n  _send(event) {\n    if (this._processingStatus === ProcessingStatus.Stopped) {\n      // do nothing\n      {\n        const eventString = JSON.stringify(event);\n        console.warn(`Event \"${event.type}\" was sent to stopped actor \"${this.id} (${this.sessionId})\". This actor has already reached its final state, and will not transition.\\nEvent: ${eventString}`);\n      }\n      return;\n    }\n    this.mailbox.enqueue(event);\n  }\n\n  /**\n   * Sends an event to the running Actor to trigger a transition.\n   *\n   * @param event The event to send\n   */\n  send(event) {\n    if (typeof event === 'string') {\n      throw new Error(`Only event objects may be sent to actors; use .send({ type: \"${event}\" }) instead`);\n    }\n    this.system._relay(undefined, this, event);\n  }\n  attachDevTools() {\n    const {\n      devTools\n    } = this.options;\n    if (devTools) {\n      const resolvedDevToolsAdapter = typeof devTools === 'function' ? devTools : _dev_dist_xstate_dev_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.devToolsAdapter;\n      resolvedDevToolsAdapter(this);\n    }\n  }\n  toJSON() {\n    return {\n      xstate$$type: $$ACTOR_TYPE,\n      id: this.id\n    };\n  }\n\n  /**\n   * Obtain the internal state of the actor, which can be persisted.\n   *\n   * @remarks\n   * The internal state can be persisted from any actor, not only machines.\n   *\n   * Note that the persisted state is not the same as the snapshot from\n   * {@link Actor.getSnapshot}. Persisted state represents the internal state of\n   * the actor, while snapshots represent the actor's last emitted value.\n   *\n   * Can be restored with {@link ActorOptions.state}\n   * @see https://stately.ai/docs/persistence\n   */\n\n  getPersistedSnapshot(options) {\n    return this.logic.getPersistedSnapshot(this._snapshot, options);\n  }\n  [symbolObservable]() {\n    return this;\n  }\n\n  /**\n   * Read an actor’s snapshot synchronously.\n   *\n   * @remarks\n   * The snapshot represent an actor's last emitted value.\n   *\n   * When an actor receives an event, its internal state may change. An actor\n   * may emit a snapshot when a state transition occurs.\n   *\n   * Note that some actors, such as callback actors generated with\n   * `fromCallback`, will not emit snapshots.\n   * @see {@link Actor.subscribe} to subscribe to an actor’s snapshot values.\n   * @see {@link Actor.getPersistedSnapshot} to persist the internal state of an actor (which is more than just a snapshot).\n   */\n  getSnapshot() {\n    if (!this._snapshot) {\n      throw new Error(`Snapshot can't be read while the actor initializes itself`);\n    }\n    return this._snapshot;\n  }\n}\n/**\n * Creates a new actor instance for the given actor logic with the provided\n * options, if any.\n *\n * @remarks\n * When you create an actor from actor logic via `createActor(logic)`, you\n * implicitly create an actor system where the created actor is the root actor.\n * Any actors spawned from this root actor and its descendants are part of that\n * actor system.\n * @example\n *\n * ```ts\n * import { createActor } from 'xstate';\n * import { someActorLogic } from './someActorLogic.ts';\n *\n * // Creating the actor, which implicitly creates an actor system with itself as the root actor\n * const actor = createActor(someActorLogic);\n *\n * actor.subscribe((snapshot) => {\n *   console.log(snapshot);\n * });\n *\n * // Actors must be started by calling `actor.start()`, which will also start the actor system.\n * actor.start();\n *\n * // Actors can receive events\n * actor.send({ type: 'someEvent' });\n *\n * // You can stop root actors by calling `actor.stop()`, which will also stop the actor system and all actors in that system.\n * actor.stop();\n * ```\n *\n * @param logic - The actor logic to create an actor from. For a state machine\n *   actor logic creator, see {@link createMachine}. Other actor logic creators\n *   include {@link fromCallback}, {@link fromEventObservable},\n *   {@link fromObservable}, {@link fromPromise}, and {@link fromTransition}.\n * @param options - Actor options\n */\nfunction createActor(logic, ...[options]) {\n  return new Actor(logic, options);\n}\n\n/**\n * Creates a new Interpreter instance for the given machine with the provided\n * options, if any.\n *\n * @deprecated Use `createActor` instead\n * @alias\n */\nconst interpret = createActor;\n\n/**\n * @deprecated Use `Actor` instead.\n * @alias\n */\n\nfunction resolveCancel(_, snapshot, actionArgs, actionParams, {\n  sendId\n}) {\n  const resolvedSendId = typeof sendId === 'function' ? sendId(actionArgs, actionParams) : sendId;\n  return [snapshot, {\n    sendId: resolvedSendId\n  }, undefined];\n}\nfunction executeCancel(actorScope, params) {\n  actorScope.defer(() => {\n    actorScope.system.scheduler.cancel(actorScope.self, params.sendId);\n  });\n}\n/**\n * Cancels a delayed `sendTo(...)` action that is waiting to be executed. The\n * canceled `sendTo(...)` action will not send its event or execute, unless the\n * `delay` has already elapsed before `cancel(...)` is called.\n *\n * @example\n *\n * ```ts\n * import { createMachine, sendTo, cancel } from 'xstate';\n *\n * const machine = createMachine({\n *   // ...\n *   on: {\n *     sendEvent: {\n *       actions: sendTo(\n *         'some-actor',\n *         { type: 'someEvent' },\n *         {\n *           id: 'some-id',\n *           delay: 1000\n *         }\n *       )\n *     },\n *     cancelEvent: {\n *       actions: cancel('some-id')\n *     }\n *   }\n * });\n * ```\n *\n * @param sendId The `id` of the `sendTo(...)` action to cancel.\n */\nfunction cancel(sendId) {\n  function cancel(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  cancel.type = 'xstate.cancel';\n  cancel.sendId = sendId;\n  cancel.resolve = resolveCancel;\n  cancel.execute = executeCancel;\n  return cancel;\n}\n\nfunction resolveSpawn(actorScope, snapshot, actionArgs, _actionParams, {\n  id,\n  systemId,\n  src,\n  input,\n  syncSnapshot\n}) {\n  const logic = typeof src === 'string' ? resolveReferencedActor(snapshot.machine, src) : src;\n  const resolvedId = typeof id === 'function' ? id(actionArgs) : id;\n  let actorRef;\n  let resolvedInput = undefined;\n  if (logic) {\n    resolvedInput = typeof input === 'function' ? input({\n      context: snapshot.context,\n      event: actionArgs.event,\n      self: actorScope.self\n    }) : input;\n    actorRef = createActor(logic, {\n      id: resolvedId,\n      src,\n      parent: actorScope.self,\n      syncSnapshot,\n      systemId,\n      input: resolvedInput\n    });\n  }\n  if (!actorRef) {\n    console.warn(\n    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions,@typescript-eslint/no-base-to-string\n    `Actor type '${src}' not found in machine '${actorScope.id}'.`);\n  }\n  return [cloneMachineSnapshot(snapshot, {\n    children: {\n      ...snapshot.children,\n      [resolvedId]: actorRef\n    }\n  }), {\n    id,\n    systemId,\n    actorRef,\n    src,\n    input: resolvedInput\n  }, undefined];\n}\nfunction executeSpawn(actorScope, {\n  actorRef\n}) {\n  if (!actorRef) {\n    return;\n  }\n  actorScope.defer(() => {\n    if (actorRef._processingStatus === ProcessingStatus.Stopped) {\n      return;\n    }\n    actorRef.start();\n  });\n}\nfunction spawnChild(...[src, {\n  id,\n  systemId,\n  input,\n  syncSnapshot = false\n} = {}]) {\n  function spawnChild(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  spawnChild.type = 'xstate.spawnChild';\n  spawnChild.id = id;\n  spawnChild.systemId = systemId;\n  spawnChild.src = src;\n  spawnChild.input = input;\n  spawnChild.syncSnapshot = syncSnapshot;\n  spawnChild.resolve = resolveSpawn;\n  spawnChild.execute = executeSpawn;\n  return spawnChild;\n}\n\nfunction resolveStop(_, snapshot, args, actionParams, {\n  actorRef\n}) {\n  const actorRefOrString = typeof actorRef === 'function' ? actorRef(args, actionParams) : actorRef;\n  const resolvedActorRef = typeof actorRefOrString === 'string' ? snapshot.children[actorRefOrString] : actorRefOrString;\n  let children = snapshot.children;\n  if (resolvedActorRef) {\n    children = {\n      ...children\n    };\n    delete children[resolvedActorRef.id];\n  }\n  return [cloneMachineSnapshot(snapshot, {\n    children\n  }), resolvedActorRef, undefined];\n}\nfunction executeStop(actorScope, actorRef) {\n  if (!actorRef) {\n    return;\n  }\n\n  // we need to eagerly unregister it here so a new actor with the same systemId can be registered immediately\n  // since we defer actual stopping of the actor but we don't defer actor creations (and we can't do that)\n  // this could throw on `systemId` collision, for example, when dealing with reentering transitions\n  actorScope.system._unregister(actorRef);\n\n  // this allows us to prevent an actor from being started if it gets stopped within the same macrostep\n  // this can happen, for example, when the invoking state is being exited immediately by an always transition\n  if (actorRef._processingStatus !== ProcessingStatus.Running) {\n    actorScope.stopChild(actorRef);\n    return;\n  }\n  // stopping a child enqueues a stop event in the child actor's mailbox\n  // we need for all of the already enqueued events to be processed before we stop the child\n  // the parent itself might want to send some events to a child (for example from exit actions on the invoking state)\n  // and we don't want to ignore those events\n  actorScope.defer(() => {\n    actorScope.stopChild(actorRef);\n  });\n}\n/**\n * Stops a child actor.\n *\n * @param actorRef The actor to stop.\n */\nfunction stopChild(actorRef) {\n  function stop(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  stop.type = 'xstate.stopChild';\n  stop.actorRef = actorRef;\n  stop.resolve = resolveStop;\n  stop.execute = executeStop;\n  return stop;\n}\n\n/**\n * Stops a child actor.\n *\n * @deprecated Use `stopChild(...)` instead\n * @alias\n */\nconst stop = stopChild;\n\nfunction checkStateIn(snapshot, _, {\n  stateValue\n}) {\n  if (typeof stateValue === 'string' && isStateId(stateValue)) {\n    const target = snapshot.machine.getStateNodeById(stateValue);\n    return snapshot._nodes.some(sn => sn === target);\n  }\n  return snapshot.matches(stateValue);\n}\nfunction stateIn(stateValue) {\n  function stateIn() {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  stateIn.check = checkStateIn;\n  stateIn.stateValue = stateValue;\n  return stateIn;\n}\nfunction checkNot(snapshot, {\n  context,\n  event\n}, {\n  guards\n}) {\n  return !evaluateGuard(guards[0], context, event, snapshot);\n}\n\n/**\n * Higher-order guard that evaluates to `true` if the `guard` passed to it\n * evaluates to `false`.\n *\n * @category Guards\n * @example\n *\n * ```ts\n * import { setup, not } from 'xstate';\n *\n * const machine = setup({\n *   guards: {\n *     someNamedGuard: () => false\n *   }\n * }).createMachine({\n *   on: {\n *     someEvent: {\n *       guard: not('someNamedGuard'),\n *       actions: () => {\n *         // will be executed if guard in `not(...)`\n *         // evaluates to `false`\n *       }\n *     }\n *   }\n * });\n * ```\n *\n * @returns A guard\n */\nfunction not(guard) {\n  function not(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  not.check = checkNot;\n  not.guards = [guard];\n  return not;\n}\nfunction checkAnd(snapshot, {\n  context,\n  event\n}, {\n  guards\n}) {\n  return guards.every(guard => evaluateGuard(guard, context, event, snapshot));\n}\n\n/**\n * Higher-order guard that evaluates to `true` if all `guards` passed to it\n * evaluate to `true`.\n *\n * @category Guards\n * @example\n *\n * ```ts\n * import { setup, and } from 'xstate';\n *\n * const machine = setup({\n *   guards: {\n *     someNamedGuard: () => true\n *   }\n * }).createMachine({\n *   on: {\n *     someEvent: {\n *       guard: and([({ context }) => context.value > 0, 'someNamedGuard']),\n *       actions: () => {\n *         // will be executed if all guards in `and(...)`\n *         // evaluate to true\n *       }\n *     }\n *   }\n * });\n * ```\n *\n * @returns A guard action object\n */\nfunction and(guards) {\n  function and(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  and.check = checkAnd;\n  and.guards = guards;\n  return and;\n}\nfunction checkOr(snapshot, {\n  context,\n  event\n}, {\n  guards\n}) {\n  return guards.some(guard => evaluateGuard(guard, context, event, snapshot));\n}\n\n/**\n * Higher-order guard that evaluates to `true` if any of the `guards` passed to\n * it evaluate to `true`.\n *\n * @category Guards\n * @example\n *\n * ```ts\n * import { setup, or } from 'xstate';\n *\n * const machine = setup({\n *   guards: {\n *     someNamedGuard: () => true\n *   }\n * }).createMachine({\n *   on: {\n *     someEvent: {\n *       guard: or([({ context }) => context.value > 0, 'someNamedGuard']),\n *       actions: () => {\n *         // will be executed if any of the guards in `or(...)`\n *         // evaluate to true\n *       }\n *     }\n *   }\n * });\n * ```\n *\n * @returns A guard action object\n */\nfunction or(guards) {\n  function or(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  or.check = checkOr;\n  or.guards = guards;\n  return or;\n}\n\n// TODO: throw on cycles (depth check should be enough)\nfunction evaluateGuard(guard, context, event, snapshot) {\n  const {\n    machine\n  } = snapshot;\n  const isInline = typeof guard === 'function';\n  const resolved = isInline ? guard : machine.implementations.guards[typeof guard === 'string' ? guard : guard.type];\n  if (!isInline && !resolved) {\n    throw new Error(`Guard '${typeof guard === 'string' ? guard : guard.type}' is not implemented.'.`);\n  }\n  if (typeof resolved !== 'function') {\n    return evaluateGuard(resolved, context, event, snapshot);\n  }\n  const guardArgs = {\n    context,\n    event\n  };\n  const guardParams = isInline || typeof guard === 'string' ? undefined : 'params' in guard ? typeof guard.params === 'function' ? guard.params({\n    context,\n    event\n  }) : guard.params : undefined;\n  if (!('check' in resolved)) {\n    // the existing type of `.guards` assumes non-nullable `TExpressionGuard`\n    // inline guards expect `TExpressionGuard` to be set to `undefined`\n    // it's fine to cast this here, our logic makes sure that we call those 2 \"variants\" correctly\n    return resolved(guardArgs, guardParams);\n  }\n  const builtinGuard = resolved;\n  return builtinGuard.check(snapshot, guardArgs, resolved // this holds all params\n  );\n}\n\nconst isAtomicStateNode = stateNode => stateNode.type === 'atomic' || stateNode.type === 'final';\nfunction getChildren(stateNode) {\n  return Object.values(stateNode.states).filter(sn => sn.type !== 'history');\n}\nfunction getProperAncestors(stateNode, toStateNode) {\n  const ancestors = [];\n  if (toStateNode === stateNode) {\n    return ancestors;\n  }\n\n  // add all ancestors\n  let m = stateNode.parent;\n  while (m && m !== toStateNode) {\n    ancestors.push(m);\n    m = m.parent;\n  }\n  return ancestors;\n}\nfunction getAllStateNodes(stateNodes) {\n  const nodeSet = new Set(stateNodes);\n  const adjList = getAdjList(nodeSet);\n\n  // add descendants\n  for (const s of nodeSet) {\n    // if previously active, add existing child nodes\n    if (s.type === 'compound' && (!adjList.get(s) || !adjList.get(s).length)) {\n      getInitialStateNodesWithTheirAncestors(s).forEach(sn => nodeSet.add(sn));\n    } else {\n      if (s.type === 'parallel') {\n        for (const child of getChildren(s)) {\n          if (child.type === 'history') {\n            continue;\n          }\n          if (!nodeSet.has(child)) {\n            const initialStates = getInitialStateNodesWithTheirAncestors(child);\n            for (const initialStateNode of initialStates) {\n              nodeSet.add(initialStateNode);\n            }\n          }\n        }\n      }\n    }\n  }\n\n  // add all ancestors\n  for (const s of nodeSet) {\n    let m = s.parent;\n    while (m) {\n      nodeSet.add(m);\n      m = m.parent;\n    }\n  }\n  return nodeSet;\n}\nfunction getValueFromAdj(baseNode, adjList) {\n  const childStateNodes = adjList.get(baseNode);\n  if (!childStateNodes) {\n    return {}; // todo: fix?\n  }\n  if (baseNode.type === 'compound') {\n    const childStateNode = childStateNodes[0];\n    if (childStateNode) {\n      if (isAtomicStateNode(childStateNode)) {\n        return childStateNode.key;\n      }\n    } else {\n      return {};\n    }\n  }\n  const stateValue = {};\n  for (const childStateNode of childStateNodes) {\n    stateValue[childStateNode.key] = getValueFromAdj(childStateNode, adjList);\n  }\n  return stateValue;\n}\nfunction getAdjList(stateNodes) {\n  const adjList = new Map();\n  for (const s of stateNodes) {\n    if (!adjList.has(s)) {\n      adjList.set(s, []);\n    }\n    if (s.parent) {\n      if (!adjList.has(s.parent)) {\n        adjList.set(s.parent, []);\n      }\n      adjList.get(s.parent).push(s);\n    }\n  }\n  return adjList;\n}\nfunction getStateValue(rootNode, stateNodes) {\n  const config = getAllStateNodes(stateNodes);\n  return getValueFromAdj(rootNode, getAdjList(config));\n}\nfunction isInFinalState(stateNodeSet, stateNode) {\n  if (stateNode.type === 'compound') {\n    return getChildren(stateNode).some(s => s.type === 'final' && stateNodeSet.has(s));\n  }\n  if (stateNode.type === 'parallel') {\n    return getChildren(stateNode).every(sn => isInFinalState(stateNodeSet, sn));\n  }\n  return stateNode.type === 'final';\n}\nconst isStateId = str => str[0] === STATE_IDENTIFIER;\nfunction getCandidates(stateNode, receivedEventType) {\n  const candidates = stateNode.transitions.get(receivedEventType) || [...stateNode.transitions.keys()].filter(eventDescriptor => {\n    // check if transition is a wildcard transition,\n    // which matches any non-transient events\n    if (eventDescriptor === WILDCARD) {\n      return true;\n    }\n    if (!eventDescriptor.endsWith('.*')) {\n      return false;\n    }\n    if (/.*\\*.+/.test(eventDescriptor)) {\n      console.warn(`Wildcards can only be the last token of an event descriptor (e.g., \"event.*\") or the entire event descriptor (\"*\"). Check the \"${eventDescriptor}\" event.`);\n    }\n    const partialEventTokens = eventDescriptor.split('.');\n    const eventTokens = receivedEventType.split('.');\n    for (let tokenIndex = 0; tokenIndex < partialEventTokens.length; tokenIndex++) {\n      const partialEventToken = partialEventTokens[tokenIndex];\n      const eventToken = eventTokens[tokenIndex];\n      if (partialEventToken === '*') {\n        const isLastToken = tokenIndex === partialEventTokens.length - 1;\n        if (!isLastToken) {\n          console.warn(`Infix wildcards in transition events are not allowed. Check the \"${eventDescriptor}\" transition.`);\n        }\n        return isLastToken;\n      }\n      if (partialEventToken !== eventToken) {\n        return false;\n      }\n    }\n    return true;\n  }).sort((a, b) => b.length - a.length).flatMap(key => stateNode.transitions.get(key));\n  return candidates;\n}\n\n/** All delayed transitions from the config. */\nfunction getDelayedTransitions(stateNode) {\n  const afterConfig = stateNode.config.after;\n  if (!afterConfig) {\n    return [];\n  }\n  const mutateEntryExit = delay => {\n    const afterEvent = createAfterEvent(delay, stateNode.id);\n    const eventType = afterEvent.type;\n    stateNode.entry.push(raise(afterEvent, {\n      id: eventType,\n      delay\n    }));\n    stateNode.exit.push(cancel(eventType));\n    return eventType;\n  };\n  const delayedTransitions = Object.keys(afterConfig).flatMap(delay => {\n    const configTransition = afterConfig[delay];\n    const resolvedTransition = typeof configTransition === 'string' ? {\n      target: configTransition\n    } : configTransition;\n    const resolvedDelay = Number.isNaN(+delay) ? delay : +delay;\n    const eventType = mutateEntryExit(resolvedDelay);\n    return toArray(resolvedTransition).map(transition => ({\n      ...transition,\n      event: eventType,\n      delay: resolvedDelay\n    }));\n  });\n  return delayedTransitions.map(delayedTransition => {\n    const {\n      delay\n    } = delayedTransition;\n    return {\n      ...formatTransition(stateNode, delayedTransition.event, delayedTransition),\n      delay\n    };\n  });\n}\nfunction formatTransition(stateNode, descriptor, transitionConfig) {\n  const normalizedTarget = normalizeTarget(transitionConfig.target);\n  const reenter = transitionConfig.reenter ?? false;\n  const target = resolveTarget(stateNode, normalizedTarget);\n\n  // TODO: should this be part of a lint rule instead?\n  if (transitionConfig.cond) {\n    throw new Error(`State \"${stateNode.id}\" has declared \\`cond\\` for one of its transitions. This property has been renamed to \\`guard\\`. Please update your code.`);\n  }\n  const transition = {\n    ...transitionConfig,\n    actions: toArray(transitionConfig.actions),\n    guard: transitionConfig.guard,\n    target,\n    source: stateNode,\n    reenter,\n    eventType: descriptor,\n    toJSON: () => ({\n      ...transition,\n      source: `#${stateNode.id}`,\n      target: target ? target.map(t => `#${t.id}`) : undefined\n    })\n  };\n  return transition;\n}\nfunction formatTransitions(stateNode) {\n  const transitions = new Map();\n  if (stateNode.config.on) {\n    for (const descriptor of Object.keys(stateNode.config.on)) {\n      if (descriptor === NULL_EVENT) {\n        throw new Error('Null events (\"\") cannot be specified as a transition key. Use `always: { ... }` instead.');\n      }\n      const transitionsConfig = stateNode.config.on[descriptor];\n      transitions.set(descriptor, toTransitionConfigArray(transitionsConfig).map(t => formatTransition(stateNode, descriptor, t)));\n    }\n  }\n  if (stateNode.config.onDone) {\n    const descriptor = `xstate.done.state.${stateNode.id}`;\n    transitions.set(descriptor, toTransitionConfigArray(stateNode.config.onDone).map(t => formatTransition(stateNode, descriptor, t)));\n  }\n  for (const invokeDef of stateNode.invoke) {\n    if (invokeDef.onDone) {\n      const descriptor = `xstate.done.actor.${invokeDef.id}`;\n      transitions.set(descriptor, toTransitionConfigArray(invokeDef.onDone).map(t => formatTransition(stateNode, descriptor, t)));\n    }\n    if (invokeDef.onError) {\n      const descriptor = `xstate.error.actor.${invokeDef.id}`;\n      transitions.set(descriptor, toTransitionConfigArray(invokeDef.onError).map(t => formatTransition(stateNode, descriptor, t)));\n    }\n    if (invokeDef.onSnapshot) {\n      const descriptor = `xstate.snapshot.${invokeDef.id}`;\n      transitions.set(descriptor, toTransitionConfigArray(invokeDef.onSnapshot).map(t => formatTransition(stateNode, descriptor, t)));\n    }\n  }\n  for (const delayedTransition of stateNode.after) {\n    let existing = transitions.get(delayedTransition.eventType);\n    if (!existing) {\n      existing = [];\n      transitions.set(delayedTransition.eventType, existing);\n    }\n    existing.push(delayedTransition);\n  }\n  return transitions;\n}\nfunction formatInitialTransition(stateNode, _target) {\n  const resolvedTarget = typeof _target === 'string' ? stateNode.states[_target] : _target ? stateNode.states[_target.target] : undefined;\n  if (!resolvedTarget && _target) {\n    throw new Error(\n    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions, @typescript-eslint/no-base-to-string\n    `Initial state node \"${_target}\" not found on parent state node #${stateNode.id}`);\n  }\n  const transition = {\n    source: stateNode,\n    actions: !_target || typeof _target === 'string' ? [] : toArray(_target.actions),\n    eventType: null,\n    reenter: false,\n    target: resolvedTarget ? [resolvedTarget] : [],\n    toJSON: () => ({\n      ...transition,\n      source: `#${stateNode.id}`,\n      target: resolvedTarget ? [`#${resolvedTarget.id}`] : []\n    })\n  };\n  return transition;\n}\nfunction resolveTarget(stateNode, targets) {\n  if (targets === undefined) {\n    // an undefined target signals that the state node should not transition from that state when receiving that event\n    return undefined;\n  }\n  return targets.map(target => {\n    if (typeof target !== 'string') {\n      return target;\n    }\n    if (isStateId(target)) {\n      return stateNode.machine.getStateNodeById(target);\n    }\n    const isInternalTarget = target[0] === STATE_DELIMITER;\n    // If internal target is defined on machine,\n    // do not include machine key on target\n    if (isInternalTarget && !stateNode.parent) {\n      return getStateNodeByPath(stateNode, target.slice(1));\n    }\n    const resolvedTarget = isInternalTarget ? stateNode.key + target : target;\n    if (stateNode.parent) {\n      try {\n        const targetStateNode = getStateNodeByPath(stateNode.parent, resolvedTarget);\n        return targetStateNode;\n      } catch (err) {\n        throw new Error(`Invalid transition definition for state node '${stateNode.id}':\\n${err.message}`);\n      }\n    } else {\n      throw new Error(`Invalid target: \"${target}\" is not a valid target from the root node. Did you mean \".${target}\"?`);\n    }\n  });\n}\nfunction resolveHistoryDefaultTransition(stateNode) {\n  const normalizedTarget = normalizeTarget(stateNode.config.target);\n  if (!normalizedTarget) {\n    return stateNode.parent.initial;\n  }\n  return {\n    target: normalizedTarget.map(t => typeof t === 'string' ? getStateNodeByPath(stateNode.parent, t) : t)\n  };\n}\nfunction isHistoryNode(stateNode) {\n  return stateNode.type === 'history';\n}\nfunction getInitialStateNodesWithTheirAncestors(stateNode) {\n  const states = getInitialStateNodes(stateNode);\n  for (const initialState of states) {\n    for (const ancestor of getProperAncestors(initialState, stateNode)) {\n      states.add(ancestor);\n    }\n  }\n  return states;\n}\nfunction getInitialStateNodes(stateNode) {\n  const set = new Set();\n  function iter(descStateNode) {\n    if (set.has(descStateNode)) {\n      return;\n    }\n    set.add(descStateNode);\n    if (descStateNode.type === 'compound') {\n      iter(descStateNode.initial.target[0]);\n    } else if (descStateNode.type === 'parallel') {\n      for (const child of getChildren(descStateNode)) {\n        iter(child);\n      }\n    }\n  }\n  iter(stateNode);\n  return set;\n}\n/** Returns the child state node from its relative `stateKey`, or throws. */\nfunction getStateNode(stateNode, stateKey) {\n  if (isStateId(stateKey)) {\n    return stateNode.machine.getStateNodeById(stateKey);\n  }\n  if (!stateNode.states) {\n    throw new Error(`Unable to retrieve child state '${stateKey}' from '${stateNode.id}'; no child states exist.`);\n  }\n  const result = stateNode.states[stateKey];\n  if (!result) {\n    throw new Error(`Child state '${stateKey}' does not exist on '${stateNode.id}'`);\n  }\n  return result;\n}\n\n/**\n * Returns the relative state node from the given `statePath`, or throws.\n *\n * @param statePath The string or string array relative path to the state node.\n */\nfunction getStateNodeByPath(stateNode, statePath) {\n  if (typeof statePath === 'string' && isStateId(statePath)) {\n    try {\n      return stateNode.machine.getStateNodeById(statePath);\n    } catch {\n      // try individual paths\n      // throw e;\n    }\n  }\n  const arrayStatePath = toStatePath(statePath).slice();\n  let currentStateNode = stateNode;\n  while (arrayStatePath.length) {\n    const key = arrayStatePath.shift();\n    if (!key.length) {\n      break;\n    }\n    currentStateNode = getStateNode(currentStateNode, key);\n  }\n  return currentStateNode;\n}\n\n/**\n * Returns the state nodes represented by the current state value.\n *\n * @param stateValue The state value or State instance\n */\nfunction getStateNodes(stateNode, stateValue) {\n  if (typeof stateValue === 'string') {\n    const childStateNode = stateNode.states[stateValue];\n    if (!childStateNode) {\n      throw new Error(`State '${stateValue}' does not exist on '${stateNode.id}'`);\n    }\n    return [stateNode, childStateNode];\n  }\n  const childStateKeys = Object.keys(stateValue);\n  const childStateNodes = childStateKeys.map(subStateKey => getStateNode(stateNode, subStateKey)).filter(Boolean);\n  return [stateNode.machine.root, stateNode].concat(childStateNodes, childStateKeys.reduce((allSubStateNodes, subStateKey) => {\n    const subStateNode = getStateNode(stateNode, subStateKey);\n    if (!subStateNode) {\n      return allSubStateNodes;\n    }\n    const subStateNodes = getStateNodes(subStateNode, stateValue[subStateKey]);\n    return allSubStateNodes.concat(subStateNodes);\n  }, []));\n}\nfunction transitionAtomicNode(stateNode, stateValue, snapshot, event) {\n  const childStateNode = getStateNode(stateNode, stateValue);\n  const next = childStateNode.next(snapshot, event);\n  if (!next || !next.length) {\n    return stateNode.next(snapshot, event);\n  }\n  return next;\n}\nfunction transitionCompoundNode(stateNode, stateValue, snapshot, event) {\n  const subStateKeys = Object.keys(stateValue);\n  const childStateNode = getStateNode(stateNode, subStateKeys[0]);\n  const next = transitionNode(childStateNode, stateValue[subStateKeys[0]], snapshot, event);\n  if (!next || !next.length) {\n    return stateNode.next(snapshot, event);\n  }\n  return next;\n}\nfunction transitionParallelNode(stateNode, stateValue, snapshot, event) {\n  const allInnerTransitions = [];\n  for (const subStateKey of Object.keys(stateValue)) {\n    const subStateValue = stateValue[subStateKey];\n    if (!subStateValue) {\n      continue;\n    }\n    const subStateNode = getStateNode(stateNode, subStateKey);\n    const innerTransitions = transitionNode(subStateNode, subStateValue, snapshot, event);\n    if (innerTransitions) {\n      allInnerTransitions.push(...innerTransitions);\n    }\n  }\n  if (!allInnerTransitions.length) {\n    return stateNode.next(snapshot, event);\n  }\n  return allInnerTransitions;\n}\nfunction transitionNode(stateNode, stateValue, snapshot, event) {\n  // leaf node\n  if (typeof stateValue === 'string') {\n    return transitionAtomicNode(stateNode, stateValue, snapshot, event);\n  }\n\n  // compound node\n  if (Object.keys(stateValue).length === 1) {\n    return transitionCompoundNode(stateNode, stateValue, snapshot, event);\n  }\n\n  // parallel node\n  return transitionParallelNode(stateNode, stateValue, snapshot, event);\n}\nfunction getHistoryNodes(stateNode) {\n  return Object.keys(stateNode.states).map(key => stateNode.states[key]).filter(sn => sn.type === 'history');\n}\nfunction isDescendant(childStateNode, parentStateNode) {\n  let marker = childStateNode;\n  while (marker.parent && marker.parent !== parentStateNode) {\n    marker = marker.parent;\n  }\n  return marker.parent === parentStateNode;\n}\nfunction hasIntersection(s1, s2) {\n  const set1 = new Set(s1);\n  const set2 = new Set(s2);\n  for (const item of set1) {\n    if (set2.has(item)) {\n      return true;\n    }\n  }\n  for (const item of set2) {\n    if (set1.has(item)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction removeConflictingTransitions(enabledTransitions, stateNodeSet, historyValue) {\n  const filteredTransitions = new Set();\n  for (const t1 of enabledTransitions) {\n    let t1Preempted = false;\n    const transitionsToRemove = new Set();\n    for (const t2 of filteredTransitions) {\n      if (hasIntersection(computeExitSet([t1], stateNodeSet, historyValue), computeExitSet([t2], stateNodeSet, historyValue))) {\n        if (isDescendant(t1.source, t2.source)) {\n          transitionsToRemove.add(t2);\n        } else {\n          t1Preempted = true;\n          break;\n        }\n      }\n    }\n    if (!t1Preempted) {\n      for (const t3 of transitionsToRemove) {\n        filteredTransitions.delete(t3);\n      }\n      filteredTransitions.add(t1);\n    }\n  }\n  return Array.from(filteredTransitions);\n}\nfunction findLeastCommonAncestor(stateNodes) {\n  const [head, ...tail] = stateNodes;\n  for (const ancestor of getProperAncestors(head, undefined)) {\n    if (tail.every(sn => isDescendant(sn, ancestor))) {\n      return ancestor;\n    }\n  }\n}\nfunction getEffectiveTargetStates(transition, historyValue) {\n  if (!transition.target) {\n    return [];\n  }\n  const targets = new Set();\n  for (const targetNode of transition.target) {\n    if (isHistoryNode(targetNode)) {\n      if (historyValue[targetNode.id]) {\n        for (const node of historyValue[targetNode.id]) {\n          targets.add(node);\n        }\n      } else {\n        for (const node of getEffectiveTargetStates(resolveHistoryDefaultTransition(targetNode), historyValue)) {\n          targets.add(node);\n        }\n      }\n    } else {\n      targets.add(targetNode);\n    }\n  }\n  return [...targets];\n}\nfunction getTransitionDomain(transition, historyValue) {\n  const targetStates = getEffectiveTargetStates(transition, historyValue);\n  if (!targetStates) {\n    return;\n  }\n  if (!transition.reenter && targetStates.every(target => target === transition.source || isDescendant(target, transition.source))) {\n    return transition.source;\n  }\n  const lca = findLeastCommonAncestor(targetStates.concat(transition.source));\n  if (lca) {\n    return lca;\n  }\n\n  // at this point we know that it's a root transition since LCA couldn't be found\n  if (transition.reenter) {\n    return;\n  }\n  return transition.source.machine.root;\n}\nfunction computeExitSet(transitions, stateNodeSet, historyValue) {\n  const statesToExit = new Set();\n  for (const t of transitions) {\n    if (t.target?.length) {\n      const domain = getTransitionDomain(t, historyValue);\n      if (t.reenter && t.source === domain) {\n        statesToExit.add(domain);\n      }\n      for (const stateNode of stateNodeSet) {\n        if (isDescendant(stateNode, domain)) {\n          statesToExit.add(stateNode);\n        }\n      }\n    }\n  }\n  return [...statesToExit];\n}\nfunction areStateNodeCollectionsEqual(prevStateNodes, nextStateNodeSet) {\n  if (prevStateNodes.length !== nextStateNodeSet.size) {\n    return false;\n  }\n  for (const node of prevStateNodes) {\n    if (!nextStateNodeSet.has(node)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/** https://www.w3.org/TR/scxml/#microstepProcedure */\nfunction microstep(transitions, currentSnapshot, actorScope, event, isInitial, internalQueue) {\n  if (!transitions.length) {\n    return currentSnapshot;\n  }\n  const mutStateNodeSet = new Set(currentSnapshot._nodes);\n  let historyValue = currentSnapshot.historyValue;\n  const filteredTransitions = removeConflictingTransitions(transitions, mutStateNodeSet, historyValue);\n  let nextState = currentSnapshot;\n\n  // Exit states\n  if (!isInitial) {\n    [nextState, historyValue] = exitStates(nextState, event, actorScope, filteredTransitions, mutStateNodeSet, historyValue, internalQueue, actorScope.actionExecutor);\n  }\n\n  // Execute transition content\n  nextState = resolveActionsAndContext(nextState, event, actorScope, filteredTransitions.flatMap(t => t.actions), internalQueue, undefined);\n\n  // Enter states\n  nextState = enterStates(nextState, event, actorScope, filteredTransitions, mutStateNodeSet, internalQueue, historyValue, isInitial);\n  const nextStateNodes = [...mutStateNodeSet];\n  if (nextState.status === 'done') {\n    nextState = resolveActionsAndContext(nextState, event, actorScope, nextStateNodes.sort((a, b) => b.order - a.order).flatMap(state => state.exit), internalQueue, undefined);\n  }\n\n  // eslint-disable-next-line no-useless-catch\n  try {\n    if (historyValue === currentSnapshot.historyValue && areStateNodeCollectionsEqual(currentSnapshot._nodes, mutStateNodeSet)) {\n      return nextState;\n    }\n    return cloneMachineSnapshot(nextState, {\n      _nodes: nextStateNodes,\n      historyValue\n    });\n  } catch (e) {\n    // TODO: Refactor this once proper error handling is implemented.\n    // See https://github.com/statelyai/rfcs/pull/4\n    throw e;\n  }\n}\nfunction getMachineOutput(snapshot, event, actorScope, rootNode, rootCompletionNode) {\n  if (rootNode.output === undefined) {\n    return;\n  }\n  const doneStateEvent = createDoneStateEvent(rootCompletionNode.id, rootCompletionNode.output !== undefined && rootCompletionNode.parent ? resolveOutput(rootCompletionNode.output, snapshot.context, event, actorScope.self) : undefined);\n  return resolveOutput(rootNode.output, snapshot.context, doneStateEvent, actorScope.self);\n}\nfunction enterStates(currentSnapshot, event, actorScope, filteredTransitions, mutStateNodeSet, internalQueue, historyValue, isInitial) {\n  let nextSnapshot = currentSnapshot;\n  const statesToEnter = new Set();\n  // those are states that were directly targeted or indirectly targeted by the explicit target\n  // in other words, those are states for which initial actions should be executed\n  // when we target `#deep_child` initial actions of its ancestors shouldn't be executed\n  const statesForDefaultEntry = new Set();\n  computeEntrySet(filteredTransitions, historyValue, statesForDefaultEntry, statesToEnter);\n\n  // In the initial state, the root state node is \"entered\".\n  if (isInitial) {\n    statesForDefaultEntry.add(currentSnapshot.machine.root);\n  }\n  const completedNodes = new Set();\n  for (const stateNodeToEnter of [...statesToEnter].sort((a, b) => a.order - b.order)) {\n    mutStateNodeSet.add(stateNodeToEnter);\n    const actions = [];\n\n    // Add entry actions\n    actions.push(...stateNodeToEnter.entry);\n    for (const invokeDef of stateNodeToEnter.invoke) {\n      actions.push(spawnChild(invokeDef.src, {\n        ...invokeDef,\n        syncSnapshot: !!invokeDef.onSnapshot\n      }));\n    }\n    if (statesForDefaultEntry.has(stateNodeToEnter)) {\n      const initialActions = stateNodeToEnter.initial.actions;\n      actions.push(...initialActions);\n    }\n    nextSnapshot = resolveActionsAndContext(nextSnapshot, event, actorScope, actions, internalQueue, stateNodeToEnter.invoke.map(invokeDef => invokeDef.id));\n    if (stateNodeToEnter.type === 'final') {\n      const parent = stateNodeToEnter.parent;\n      let ancestorMarker = parent?.type === 'parallel' ? parent : parent?.parent;\n      let rootCompletionNode = ancestorMarker || stateNodeToEnter;\n      if (parent?.type === 'compound') {\n        internalQueue.push(createDoneStateEvent(parent.id, stateNodeToEnter.output !== undefined ? resolveOutput(stateNodeToEnter.output, nextSnapshot.context, event, actorScope.self) : undefined));\n      }\n      while (ancestorMarker?.type === 'parallel' && !completedNodes.has(ancestorMarker) && isInFinalState(mutStateNodeSet, ancestorMarker)) {\n        completedNodes.add(ancestorMarker);\n        internalQueue.push(createDoneStateEvent(ancestorMarker.id));\n        rootCompletionNode = ancestorMarker;\n        ancestorMarker = ancestorMarker.parent;\n      }\n      if (ancestorMarker) {\n        continue;\n      }\n      nextSnapshot = cloneMachineSnapshot(nextSnapshot, {\n        status: 'done',\n        output: getMachineOutput(nextSnapshot, event, actorScope, nextSnapshot.machine.root, rootCompletionNode)\n      });\n    }\n  }\n  return nextSnapshot;\n}\nfunction computeEntrySet(transitions, historyValue, statesForDefaultEntry, statesToEnter) {\n  for (const t of transitions) {\n    const domain = getTransitionDomain(t, historyValue);\n    for (const s of t.target || []) {\n      if (!isHistoryNode(s) && (\n      // if the target is different than the source then it will *definitely* be entered\n      t.source !== s ||\n      // we know that the domain can't lie within the source\n      // if it's different than the source then it's outside of it and it means that the target has to be entered as well\n      t.source !== domain ||\n      // reentering transitions always enter the target, even if it's the source itself\n      t.reenter)) {\n        statesToEnter.add(s);\n        statesForDefaultEntry.add(s);\n      }\n      addDescendantStatesToEnter(s, historyValue, statesForDefaultEntry, statesToEnter);\n    }\n    const targetStates = getEffectiveTargetStates(t, historyValue);\n    for (const s of targetStates) {\n      const ancestors = getProperAncestors(s, domain);\n      if (domain?.type === 'parallel') {\n        ancestors.push(domain);\n      }\n      addAncestorStatesToEnter(statesToEnter, historyValue, statesForDefaultEntry, ancestors, !t.source.parent && t.reenter ? undefined : domain);\n    }\n  }\n}\nfunction addDescendantStatesToEnter(stateNode, historyValue, statesForDefaultEntry, statesToEnter) {\n  if (isHistoryNode(stateNode)) {\n    if (historyValue[stateNode.id]) {\n      const historyStateNodes = historyValue[stateNode.id];\n      for (const s of historyStateNodes) {\n        statesToEnter.add(s);\n        addDescendantStatesToEnter(s, historyValue, statesForDefaultEntry, statesToEnter);\n      }\n      for (const s of historyStateNodes) {\n        addProperAncestorStatesToEnter(s, stateNode.parent, statesToEnter, historyValue, statesForDefaultEntry);\n      }\n    } else {\n      const historyDefaultTransition = resolveHistoryDefaultTransition(stateNode);\n      for (const s of historyDefaultTransition.target) {\n        statesToEnter.add(s);\n        if (historyDefaultTransition === stateNode.parent?.initial) {\n          statesForDefaultEntry.add(stateNode.parent);\n        }\n        addDescendantStatesToEnter(s, historyValue, statesForDefaultEntry, statesToEnter);\n      }\n      for (const s of historyDefaultTransition.target) {\n        addProperAncestorStatesToEnter(s, stateNode.parent, statesToEnter, historyValue, statesForDefaultEntry);\n      }\n    }\n  } else {\n    if (stateNode.type === 'compound') {\n      const [initialState] = stateNode.initial.target;\n      if (!isHistoryNode(initialState)) {\n        statesToEnter.add(initialState);\n        statesForDefaultEntry.add(initialState);\n      }\n      addDescendantStatesToEnter(initialState, historyValue, statesForDefaultEntry, statesToEnter);\n      addProperAncestorStatesToEnter(initialState, stateNode, statesToEnter, historyValue, statesForDefaultEntry);\n    } else {\n      if (stateNode.type === 'parallel') {\n        for (const child of getChildren(stateNode).filter(sn => !isHistoryNode(sn))) {\n          if (![...statesToEnter].some(s => isDescendant(s, child))) {\n            if (!isHistoryNode(child)) {\n              statesToEnter.add(child);\n              statesForDefaultEntry.add(child);\n            }\n            addDescendantStatesToEnter(child, historyValue, statesForDefaultEntry, statesToEnter);\n          }\n        }\n      }\n    }\n  }\n}\nfunction addAncestorStatesToEnter(statesToEnter, historyValue, statesForDefaultEntry, ancestors, reentrancyDomain) {\n  for (const anc of ancestors) {\n    if (!reentrancyDomain || isDescendant(anc, reentrancyDomain)) {\n      statesToEnter.add(anc);\n    }\n    if (anc.type === 'parallel') {\n      for (const child of getChildren(anc).filter(sn => !isHistoryNode(sn))) {\n        if (![...statesToEnter].some(s => isDescendant(s, child))) {\n          statesToEnter.add(child);\n          addDescendantStatesToEnter(child, historyValue, statesForDefaultEntry, statesToEnter);\n        }\n      }\n    }\n  }\n}\nfunction addProperAncestorStatesToEnter(stateNode, toStateNode, statesToEnter, historyValue, statesForDefaultEntry) {\n  addAncestorStatesToEnter(statesToEnter, historyValue, statesForDefaultEntry, getProperAncestors(stateNode, toStateNode));\n}\nfunction exitStates(currentSnapshot, event, actorScope, transitions, mutStateNodeSet, historyValue, internalQueue, _actionExecutor) {\n  let nextSnapshot = currentSnapshot;\n  const statesToExit = computeExitSet(transitions, mutStateNodeSet, historyValue);\n  statesToExit.sort((a, b) => b.order - a.order);\n  let changedHistory;\n\n  // From SCXML algorithm: https://www.w3.org/TR/scxml/#exitStates\n  for (const exitStateNode of statesToExit) {\n    for (const historyNode of getHistoryNodes(exitStateNode)) {\n      let predicate;\n      if (historyNode.history === 'deep') {\n        predicate = sn => isAtomicStateNode(sn) && isDescendant(sn, exitStateNode);\n      } else {\n        predicate = sn => {\n          return sn.parent === exitStateNode;\n        };\n      }\n      changedHistory ??= {\n        ...historyValue\n      };\n      changedHistory[historyNode.id] = Array.from(mutStateNodeSet).filter(predicate);\n    }\n  }\n  for (const s of statesToExit) {\n    nextSnapshot = resolveActionsAndContext(nextSnapshot, event, actorScope, [...s.exit, ...s.invoke.map(def => stopChild(def.id))], internalQueue, undefined);\n    mutStateNodeSet.delete(s);\n  }\n  return [nextSnapshot, changedHistory || historyValue];\n}\nfunction getAction(machine, actionType) {\n  return machine.implementations.actions[actionType];\n}\nfunction resolveAndExecuteActionsWithContext(currentSnapshot, event, actorScope, actions, extra, retries) {\n  const {\n    machine\n  } = currentSnapshot;\n  let intermediateSnapshot = currentSnapshot;\n  for (const action of actions) {\n    const isInline = typeof action === 'function';\n    const resolvedAction = isInline ? action :\n    // the existing type of `.actions` assumes non-nullable `TExpressionAction`\n    // it's fine to cast this here to get a common type and lack of errors in the rest of the code\n    // our logic below makes sure that we call those 2 \"variants\" correctly\n\n    getAction(machine, typeof action === 'string' ? action : action.type);\n    const actionArgs = {\n      context: intermediateSnapshot.context,\n      event,\n      self: actorScope.self,\n      system: actorScope.system\n    };\n    const actionParams = isInline || typeof action === 'string' ? undefined : 'params' in action ? typeof action.params === 'function' ? action.params({\n      context: intermediateSnapshot.context,\n      event\n    }) : action.params : undefined;\n    if (!resolvedAction || !('resolve' in resolvedAction)) {\n      actorScope.actionExecutor({\n        type: typeof action === 'string' ? action : typeof action === 'object' ? action.type : action.name || '(anonymous)',\n        info: actionArgs,\n        params: actionParams,\n        exec: resolvedAction\n      });\n      continue;\n    }\n    const builtinAction = resolvedAction;\n    const [nextState, params, actions] = builtinAction.resolve(actorScope, intermediateSnapshot, actionArgs, actionParams, resolvedAction,\n    // this holds all params\n    extra);\n    intermediateSnapshot = nextState;\n    if ('retryResolve' in builtinAction) {\n      retries?.push([builtinAction, params]);\n    }\n    if ('execute' in builtinAction) {\n      actorScope.actionExecutor({\n        type: builtinAction.type,\n        info: actionArgs,\n        params,\n        exec: builtinAction.execute.bind(null, actorScope, params)\n      });\n    }\n    if (actions) {\n      intermediateSnapshot = resolveAndExecuteActionsWithContext(intermediateSnapshot, event, actorScope, actions, extra, retries);\n    }\n  }\n  return intermediateSnapshot;\n}\nfunction resolveActionsAndContext(currentSnapshot, event, actorScope, actions, internalQueue, deferredActorIds) {\n  const retries = deferredActorIds ? [] : undefined;\n  const nextState = resolveAndExecuteActionsWithContext(currentSnapshot, event, actorScope, actions, {\n    internalQueue,\n    deferredActorIds\n  }, retries);\n  retries?.forEach(([builtinAction, params]) => {\n    builtinAction.retryResolve(actorScope, nextState, params);\n  });\n  return nextState;\n}\nfunction macrostep(snapshot, event, actorScope, internalQueue) {\n  if (event.type === WILDCARD) {\n    throw new Error(`An event cannot have the wildcard type ('${WILDCARD}')`);\n  }\n  let nextSnapshot = snapshot;\n  const microstates = [];\n  function addMicrostate(microstate, event, transitions) {\n    actorScope.system._sendInspectionEvent({\n      type: '@xstate.microstep',\n      actorRef: actorScope.self,\n      event,\n      snapshot: microstate,\n      _transitions: transitions\n    });\n    microstates.push(microstate);\n  }\n\n  // Handle stop event\n  if (event.type === XSTATE_STOP) {\n    nextSnapshot = cloneMachineSnapshot(stopChildren(nextSnapshot, event, actorScope), {\n      status: 'stopped'\n    });\n    addMicrostate(nextSnapshot, event, []);\n    return {\n      snapshot: nextSnapshot,\n      microstates\n    };\n  }\n  let nextEvent = event;\n\n  // Assume the state is at rest (no raised events)\n  // Determine the next state based on the next microstep\n  if (nextEvent.type !== XSTATE_INIT) {\n    const currentEvent = nextEvent;\n    const isErr = isErrorActorEvent(currentEvent);\n    const transitions = selectTransitions(currentEvent, nextSnapshot);\n    if (isErr && !transitions.length) {\n      // TODO: we should likely only allow transitions selected by very explicit descriptors\n      // `*` shouldn't be matched, likely `xstate.error.*` shouldnt be either\n      // similarly `xstate.error.actor.*` and `xstate.error.actor.todo.*` have to be considered too\n      nextSnapshot = cloneMachineSnapshot(snapshot, {\n        status: 'error',\n        error: currentEvent.error\n      });\n      addMicrostate(nextSnapshot, currentEvent, []);\n      return {\n        snapshot: nextSnapshot,\n        microstates\n      };\n    }\n    nextSnapshot = microstep(transitions, snapshot, actorScope, nextEvent, false,\n    // isInitial\n    internalQueue);\n    addMicrostate(nextSnapshot, currentEvent, transitions);\n  }\n  let shouldSelectEventlessTransitions = true;\n  while (nextSnapshot.status === 'active') {\n    let enabledTransitions = shouldSelectEventlessTransitions ? selectEventlessTransitions(nextSnapshot, nextEvent) : [];\n\n    // eventless transitions should always be selected after selecting *regular* transitions\n    // by assigning `undefined` to `previousState` we ensure that `shouldSelectEventlessTransitions` gets always computed to true in such a case\n    const previousState = enabledTransitions.length ? nextSnapshot : undefined;\n    if (!enabledTransitions.length) {\n      if (!internalQueue.length) {\n        break;\n      }\n      nextEvent = internalQueue.shift();\n      enabledTransitions = selectTransitions(nextEvent, nextSnapshot);\n    }\n    nextSnapshot = microstep(enabledTransitions, nextSnapshot, actorScope, nextEvent, false, internalQueue);\n    shouldSelectEventlessTransitions = nextSnapshot !== previousState;\n    addMicrostate(nextSnapshot, nextEvent, enabledTransitions);\n  }\n  if (nextSnapshot.status !== 'active') {\n    stopChildren(nextSnapshot, nextEvent, actorScope);\n  }\n  return {\n    snapshot: nextSnapshot,\n    microstates\n  };\n}\nfunction stopChildren(nextState, event, actorScope) {\n  return resolveActionsAndContext(nextState, event, actorScope, Object.values(nextState.children).map(child => stopChild(child)), [], undefined);\n}\nfunction selectTransitions(event, nextState) {\n  return nextState.machine.getTransitionData(nextState, event);\n}\nfunction selectEventlessTransitions(nextState, event) {\n  const enabledTransitionSet = new Set();\n  const atomicStates = nextState._nodes.filter(isAtomicStateNode);\n  for (const stateNode of atomicStates) {\n    loop: for (const s of [stateNode].concat(getProperAncestors(stateNode, undefined))) {\n      if (!s.always) {\n        continue;\n      }\n      for (const transition of s.always) {\n        if (transition.guard === undefined || evaluateGuard(transition.guard, nextState.context, event, nextState)) {\n          enabledTransitionSet.add(transition);\n          break loop;\n        }\n      }\n    }\n  }\n  return removeConflictingTransitions(Array.from(enabledTransitionSet), new Set(nextState._nodes), nextState.historyValue);\n}\n\n/**\n * Resolves a partial state value with its full representation in the state\n * node's machine.\n *\n * @param stateValue The partial state value to resolve.\n */\nfunction resolveStateValue(rootNode, stateValue) {\n  const allStateNodes = getAllStateNodes(getStateNodes(rootNode, stateValue));\n  return getStateValue(rootNode, [...allStateNodes]);\n}\n\nfunction isMachineSnapshot(value) {\n  return !!value && typeof value === 'object' && 'machine' in value && 'value' in value;\n}\nconst machineSnapshotMatches = function matches(testValue) {\n  return matchesState(testValue, this.value);\n};\nconst machineSnapshotHasTag = function hasTag(tag) {\n  return this.tags.has(tag);\n};\nconst machineSnapshotCan = function can(event) {\n  if (!this.machine) {\n    console.warn(`state.can(...) used outside of a machine-created State object; this will always return false.`);\n  }\n  const transitionData = this.machine.getTransitionData(this, event);\n  return !!transitionData?.length &&\n  // Check that at least one transition is not forbidden\n  transitionData.some(t => t.target !== undefined || t.actions.length);\n};\nconst machineSnapshotToJSON = function toJSON() {\n  const {\n    _nodes: nodes,\n    tags,\n    machine,\n    getMeta,\n    toJSON,\n    can,\n    hasTag,\n    matches,\n    ...jsonValues\n  } = this;\n  return {\n    ...jsonValues,\n    tags: Array.from(tags)\n  };\n};\nconst machineSnapshotGetMeta = function getMeta() {\n  return this._nodes.reduce((acc, stateNode) => {\n    if (stateNode.meta !== undefined) {\n      acc[stateNode.id] = stateNode.meta;\n    }\n    return acc;\n  }, {});\n};\nfunction createMachineSnapshot(config, machine) {\n  return {\n    status: config.status,\n    output: config.output,\n    error: config.error,\n    machine,\n    context: config.context,\n    _nodes: config._nodes,\n    value: getStateValue(machine.root, config._nodes),\n    tags: new Set(config._nodes.flatMap(sn => sn.tags)),\n    children: config.children,\n    historyValue: config.historyValue || {},\n    matches: machineSnapshotMatches,\n    hasTag: machineSnapshotHasTag,\n    can: machineSnapshotCan,\n    getMeta: machineSnapshotGetMeta,\n    toJSON: machineSnapshotToJSON\n  };\n}\nfunction cloneMachineSnapshot(snapshot, config = {}) {\n  return createMachineSnapshot({\n    ...snapshot,\n    ...config\n  }, snapshot.machine);\n}\nfunction getPersistedSnapshot(snapshot, options) {\n  const {\n    _nodes: nodes,\n    tags,\n    machine,\n    children,\n    context,\n    can,\n    hasTag,\n    matches,\n    getMeta,\n    toJSON,\n    ...jsonValues\n  } = snapshot;\n  const childrenJson = {};\n  for (const id in children) {\n    const child = children[id];\n    if (typeof child.src !== 'string' && (!options || !('__unsafeAllowInlineActors' in options))) {\n      throw new Error('An inline child actor cannot be persisted.');\n    }\n    childrenJson[id] = {\n      snapshot: child.getPersistedSnapshot(options),\n      src: child.src,\n      systemId: child._systemId,\n      syncSnapshot: child._syncSnapshot\n    };\n  }\n  const persisted = {\n    ...jsonValues,\n    context: persistContext(context),\n    children: childrenJson\n  };\n  return persisted;\n}\nfunction persistContext(contextPart) {\n  let copy;\n  for (const key in contextPart) {\n    const value = contextPart[key];\n    if (value && typeof value === 'object') {\n      if ('sessionId' in value && 'send' in value && 'ref' in value) {\n        copy ??= Array.isArray(contextPart) ? contextPart.slice() : {\n          ...contextPart\n        };\n        copy[key] = {\n          xstate$$type: $$ACTOR_TYPE,\n          id: value.id\n        };\n      } else {\n        const result = persistContext(value);\n        if (result !== value) {\n          copy ??= Array.isArray(contextPart) ? contextPart.slice() : {\n            ...contextPart\n          };\n          copy[key] = result;\n        }\n      }\n    }\n  }\n  return copy ?? contextPart;\n}\n\nfunction resolveRaise(_, snapshot, args, actionParams, {\n  event: eventOrExpr,\n  id,\n  delay\n}, {\n  internalQueue\n}) {\n  const delaysMap = snapshot.machine.implementations.delays;\n  if (typeof eventOrExpr === 'string') {\n    throw new Error(\n    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions\n    `Only event objects may be used with raise; use raise({ type: \"${eventOrExpr}\" }) instead`);\n  }\n  const resolvedEvent = typeof eventOrExpr === 'function' ? eventOrExpr(args, actionParams) : eventOrExpr;\n  let resolvedDelay;\n  if (typeof delay === 'string') {\n    const configDelay = delaysMap && delaysMap[delay];\n    resolvedDelay = typeof configDelay === 'function' ? configDelay(args, actionParams) : configDelay;\n  } else {\n    resolvedDelay = typeof delay === 'function' ? delay(args, actionParams) : delay;\n  }\n  if (typeof resolvedDelay !== 'number') {\n    internalQueue.push(resolvedEvent);\n  }\n  return [snapshot, {\n    event: resolvedEvent,\n    id,\n    delay: resolvedDelay\n  }, undefined];\n}\nfunction executeRaise(actorScope, params) {\n  const {\n    event,\n    delay,\n    id\n  } = params;\n  if (typeof delay === 'number') {\n    actorScope.defer(() => {\n      const self = actorScope.self;\n      actorScope.system.scheduler.schedule(self, self, event, delay, id);\n    });\n    return;\n  }\n}\n/**\n * Raises an event. This places the event in the internal event queue, so that\n * the event is immediately consumed by the machine in the current step.\n *\n * @param eventType The event to raise.\n */\nfunction raise(eventOrExpr, options) {\n  if (executingCustomAction) {\n    console.warn('Custom actions should not call `raise()` directly, as it is not imperative. See https://stately.ai/docs/actions#built-in-actions for more details.');\n  }\n  function raise(_args, _params) {\n    {\n      throw new Error(`This isn't supposed to be called`);\n    }\n  }\n  raise.type = 'xstate.raise';\n  raise.event = eventOrExpr;\n  raise.id = options?.id;\n  raise.delay = options?.delay;\n  raise.resolve = resolveRaise;\n  raise.execute = executeRaise;\n  return raise;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMveHN0YXRlL2Rpc3QvcmFpc2UtMWRiMjdhODIuZGV2ZWxvcG1lbnQuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTRFOztBQUU1RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixTQUFTLEdBQUcsR0FBRztBQUN6QztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixHQUFHO0FBQ2xDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLFNBQVM7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLEdBQUc7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLG9CQUFvQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsMEJBQTBCO0FBQzVDO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsMkJBQTJCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLHNPQUFzTyx3R0FBd0csSUFBSSxJQUFJLHVDQUF1QyxZQUFZO0FBQ3pZO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksTUFBTSxHQUFHLFlBQVk7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVksbUJBQW1CLEdBQUcsR0FBRztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsd0JBQXdCLFlBQVk7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsaURBQWlELFNBQVM7QUFDMUQ7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxHQUFHO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCxVQUFVLEtBQUssU0FBUztBQUM3RTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSw4Q0FBOEMsa0JBQWtCO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsUUFBUTtBQUMvQztBQUNBLGFBQWE7QUFDYjtBQUNBLFNBQVM7QUFDVDtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDO0FBQ2hDO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixXQUFXLCtCQUErQixTQUFTLEdBQUcsZUFBZSx1RkFBdUYsWUFBWTtBQUN2TTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLFlBQVksU0FBUyxNQUFNLEdBQUc7QUFDL0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0Esa0ZBQWtGLG9GQUFlO0FBQ2pHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sd0JBQXdCO0FBQzlCO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsdUJBQXVCO0FBQ2xDLFdBQVcsa0NBQWtDO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGNBQWM7QUFDMUIsWUFBWSxpQkFBaUI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQW1CO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixvQkFBb0I7QUFDbkQsY0FBYyxtQkFBbUIsR0FBRywwQkFBMEI7QUFDOUQsTUFBTSxxQkFBcUIsR0FBRyxrQkFBa0IsT0FBTyxxQkFBcUI7QUFDNUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksZ0NBQWdDO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxtQkFBbUI7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLElBQUksMEJBQTBCLGNBQWM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSxJQUFJO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLENBQUM7QUFDRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGFBQWE7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLENBQUM7QUFDRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGFBQWE7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0Esd0JBQXdCLFNBQVM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxZQUFZO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLHVCQUF1QixTQUFTO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QiwrQ0FBK0M7QUFDN0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFKQUFxSixnQkFBZ0I7QUFDcks7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLHdDQUF3QztBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkZBQTJGLGdCQUFnQjtBQUMzRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLDhCQUE4QixhQUFhO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsYUFBYTtBQUMvQiwyQ0FBMkMsS0FBSztBQUNoRCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtHQUFrRyxLQUFLO0FBQ3ZHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxhQUFhO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDLGFBQWE7QUFDM0Q7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLGFBQWE7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLGFBQWE7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLFFBQVEsb0NBQW9DLGFBQWE7QUFDcEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGFBQWE7QUFDL0Isb0NBQW9DLGtCQUFrQjtBQUN0RCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUix5RUFBeUUsYUFBYSxNQUFNLFlBQVk7QUFDeEc7QUFDQSxNQUFNO0FBQ04sMENBQTBDLE9BQU8sNkRBQTZELE9BQU87QUFDckg7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RCxTQUFTLFVBQVUsYUFBYSxHQUFHO0FBQzFGO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxTQUFTLHVCQUF1QixhQUFhO0FBQ2pGO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxXQUFXLHVCQUF1QixhQUFhO0FBQy9FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0VBQWdFLFNBQVM7QUFDekU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRkFBaUY7QUFDakY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxJQUFJO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbURBQW1EO0FBQ25EO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsWUFBWSxTQUFTLFlBQVksR0FBRztBQUNwRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVrakMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy94c3RhdGUvZGlzdC9yYWlzZS0xZGIyN2E4Mi5kZXZlbG9wbWVudC5lc20uanM/NDRiMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZXZUb29sc0FkYXB0ZXIgfSBmcm9tICcuLi9kZXYvZGlzdC94c3RhdGUtZGV2LmRldmVsb3BtZW50LmVzbS5qcyc7XG5cbmNsYXNzIE1haWxib3gge1xuICBjb25zdHJ1Y3RvcihfcHJvY2Vzcykge1xuICAgIHRoaXMuX3Byb2Nlc3MgPSBfcHJvY2VzcztcbiAgICB0aGlzLl9hY3RpdmUgPSBmYWxzZTtcbiAgICB0aGlzLl9jdXJyZW50ID0gbnVsbDtcbiAgICB0aGlzLl9sYXN0ID0gbnVsbDtcbiAgfVxuICBzdGFydCgpIHtcbiAgICB0aGlzLl9hY3RpdmUgPSB0cnVlO1xuICAgIHRoaXMuZmx1c2goKTtcbiAgfVxuICBjbGVhcigpIHtcbiAgICAvLyB3ZSBjYW4ndCBzZXQgX2N1cnJlbnQgdG8gbnVsbCBiZWNhdXNlIHdlIG1pZ2h0IGJlIGN1cnJlbnRseSBwcm9jZXNzaW5nXG4gICAgLy8gYW5kIGVucXVldWUgZm9sbG93aW5nIGNsZWFyIHNob3VsZG50IHN0YXJ0IHByb2Nlc3NpbmcgdGhlIGVucXVldWVkIGl0ZW0gaW1tZWRpYXRlbHlcbiAgICBpZiAodGhpcy5fY3VycmVudCkge1xuICAgICAgdGhpcy5fY3VycmVudC5uZXh0ID0gbnVsbDtcbiAgICAgIHRoaXMuX2xhc3QgPSB0aGlzLl9jdXJyZW50O1xuICAgIH1cbiAgfVxuICBlbnF1ZXVlKGV2ZW50KSB7XG4gICAgY29uc3QgZW5xdWV1ZWQgPSB7XG4gICAgICB2YWx1ZTogZXZlbnQsXG4gICAgICBuZXh0OiBudWxsXG4gICAgfTtcbiAgICBpZiAodGhpcy5fY3VycmVudCkge1xuICAgICAgdGhpcy5fbGFzdC5uZXh0ID0gZW5xdWV1ZWQ7XG4gICAgICB0aGlzLl9sYXN0ID0gZW5xdWV1ZWQ7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHRoaXMuX2N1cnJlbnQgPSBlbnF1ZXVlZDtcbiAgICB0aGlzLl9sYXN0ID0gZW5xdWV1ZWQ7XG4gICAgaWYgKHRoaXMuX2FjdGl2ZSkge1xuICAgICAgdGhpcy5mbHVzaCgpO1xuICAgIH1cbiAgfVxuICBmbHVzaCgpIHtcbiAgICB3aGlsZSAodGhpcy5fY3VycmVudCkge1xuICAgICAgLy8gYXRtIHRoZSBnaXZlbiBfcHJvY2VzcyBpcyByZXNwb25zaWJsZSBmb3IgaW1wbGVtZW50aW5nIHByb3BlciB0cnkvY2F0Y2ggaGFuZGxpbmdcbiAgICAgIC8vIHdlIGFzc3VtZSBoZXJlIHRoYXQgdGhpcyB3b24ndCB0aHJvdyBpbiBhIHdheSB0aGF0IGNhbiBhZmZlY3QgdGhpcyBtYWlsYm94XG4gICAgICBjb25zdCBjb25zdW1lZCA9IHRoaXMuX2N1cnJlbnQ7XG4gICAgICB0aGlzLl9wcm9jZXNzKGNvbnN1bWVkLnZhbHVlKTtcbiAgICAgIHRoaXMuX2N1cnJlbnQgPSBjb25zdW1lZC5uZXh0O1xuICAgIH1cbiAgICB0aGlzLl9sYXN0ID0gbnVsbDtcbiAgfVxufVxuXG5jb25zdCBTVEFURV9ERUxJTUlURVIgPSAnLic7XG5jb25zdCBUQVJHRVRMRVNTX0tFWSA9ICcnO1xuY29uc3QgTlVMTF9FVkVOVCA9ICcnO1xuY29uc3QgU1RBVEVfSURFTlRJRklFUiA9ICcjJztcbmNvbnN0IFdJTERDQVJEID0gJyonO1xuY29uc3QgWFNUQVRFX0lOSVQgPSAneHN0YXRlLmluaXQnO1xuY29uc3QgWFNUQVRFX0VSUk9SID0gJ3hzdGF0ZS5lcnJvcic7XG5jb25zdCBYU1RBVEVfU1RPUCA9ICd4c3RhdGUuc3RvcCc7XG5cbi8qKlxuICogUmV0dXJucyBhbiBldmVudCB0aGF0IHJlcHJlc2VudHMgYW4gaW1wbGljaXQgZXZlbnQgdGhhdCBpcyBzZW50IGFmdGVyIHRoZVxuICogc3BlY2lmaWVkIGBkZWxheWAuXG4gKlxuICogQHBhcmFtIGRlbGF5UmVmIFRoZSBkZWxheSBpbiBtaWxsaXNlY29uZHNcbiAqIEBwYXJhbSBpZCBUaGUgc3RhdGUgbm9kZSBJRCB3aGVyZSB0aGlzIGV2ZW50IGlzIGhhbmRsZWRcbiAqL1xuZnVuY3Rpb24gY3JlYXRlQWZ0ZXJFdmVudChkZWxheVJlZiwgaWQpIHtcbiAgcmV0dXJuIHtcbiAgICB0eXBlOiBgeHN0YXRlLmFmdGVyLiR7ZGVsYXlSZWZ9LiR7aWR9YFxuICB9O1xufVxuXG4vKipcbiAqIFJldHVybnMgYW4gZXZlbnQgdGhhdCByZXByZXNlbnRzIHRoYXQgYSBmaW5hbCBzdGF0ZSBub2RlIGhhcyBiZWVuIHJlYWNoZWQgaW5cbiAqIHRoZSBwYXJlbnQgc3RhdGUgbm9kZS5cbiAqXG4gKiBAcGFyYW0gaWQgVGhlIGZpbmFsIHN0YXRlIG5vZGUncyBwYXJlbnQgc3RhdGUgbm9kZSBgaWRgXG4gKiBAcGFyYW0gb3V0cHV0IFRoZSBkYXRhIHRvIHBhc3MgaW50byB0aGUgZXZlbnRcbiAqL1xuZnVuY3Rpb24gY3JlYXRlRG9uZVN0YXRlRXZlbnQoaWQsIG91dHB1dCkge1xuICByZXR1cm4ge1xuICAgIHR5cGU6IGB4c3RhdGUuZG9uZS5zdGF0ZS4ke2lkfWAsXG4gICAgb3V0cHV0XG4gIH07XG59XG5cbi8qKlxuICogUmV0dXJucyBhbiBldmVudCB0aGF0IHJlcHJlc2VudHMgdGhhdCBhbiBpbnZva2VkIHNlcnZpY2UgaGFzIHRlcm1pbmF0ZWQuXG4gKlxuICogQW4gaW52b2tlZCBzZXJ2aWNlIGlzIHRlcm1pbmF0ZWQgd2hlbiBpdCBoYXMgcmVhY2hlZCBhIHRvcC1sZXZlbCBmaW5hbCBzdGF0ZVxuICogbm9kZSwgYnV0IG5vdCB3aGVuIGl0IGlzIGNhbmNlbGVkLlxuICpcbiAqIEBwYXJhbSBpbnZva2VJZCBUaGUgaW52b2tlZCBzZXJ2aWNlIElEXG4gKiBAcGFyYW0gb3V0cHV0IFRoZSBkYXRhIHRvIHBhc3MgaW50byB0aGUgZXZlbnRcbiAqL1xuZnVuY3Rpb24gY3JlYXRlRG9uZUFjdG9yRXZlbnQoaW52b2tlSWQsIG91dHB1dCkge1xuICByZXR1cm4ge1xuICAgIHR5cGU6IGB4c3RhdGUuZG9uZS5hY3Rvci4ke2ludm9rZUlkfWAsXG4gICAgb3V0cHV0LFxuICAgIGFjdG9ySWQ6IGludm9rZUlkXG4gIH07XG59XG5mdW5jdGlvbiBjcmVhdGVFcnJvckFjdG9yRXZlbnQoaWQsIGVycm9yKSB7XG4gIHJldHVybiB7XG4gICAgdHlwZTogYHhzdGF0ZS5lcnJvci5hY3Rvci4ke2lkfWAsXG4gICAgZXJyb3IsXG4gICAgYWN0b3JJZDogaWRcbiAgfTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZUluaXRFdmVudChpbnB1dCkge1xuICByZXR1cm4ge1xuICAgIHR5cGU6IFhTVEFURV9JTklULFxuICAgIGlucHV0XG4gIH07XG59XG5cbi8qKlxuICogVGhpcyBmdW5jdGlvbiBtYWtlcyBzdXJlIHRoYXQgdW5oYW5kbGVkIGVycm9ycyBhcmUgdGhyb3duIGluIGEgc2VwYXJhdGVcbiAqIG1hY3JvdGFzay4gSXQgYWxsb3dzIHRob3NlIGVycm9ycyB0byBiZSBkZXRlY3RlZCBieSBnbG9iYWwgZXJyb3IgaGFuZGxlcnMgYW5kXG4gKiByZXBvcnRlZCB0byBidWcgdHJhY2tpbmcgc2VydmljZXMgd2l0aG91dCBpbnRlcnJ1cHRpbmcgb3VyIG93biBzdGFjayBvZlxuICogZXhlY3V0aW9uLlxuICpcbiAqIEBwYXJhbSBlcnIgRXJyb3IgdG8gYmUgdGhyb3duXG4gKi9cbmZ1bmN0aW9uIHJlcG9ydFVuaGFuZGxlZEVycm9yKGVycikge1xuICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICB0aHJvdyBlcnI7XG4gIH0pO1xufVxuXG5jb25zdCBzeW1ib2xPYnNlcnZhYmxlID0gKCgpID0+IHR5cGVvZiBTeW1ib2wgPT09ICdmdW5jdGlvbicgJiYgU3ltYm9sLm9ic2VydmFibGUgfHwgJ0BAb2JzZXJ2YWJsZScpKCk7XG5cbmZ1bmN0aW9uIG1hdGNoZXNTdGF0ZShwYXJlbnRTdGF0ZUlkLCBjaGlsZFN0YXRlSWQpIHtcbiAgY29uc3QgcGFyZW50U3RhdGVWYWx1ZSA9IHRvU3RhdGVWYWx1ZShwYXJlbnRTdGF0ZUlkKTtcbiAgY29uc3QgY2hpbGRTdGF0ZVZhbHVlID0gdG9TdGF0ZVZhbHVlKGNoaWxkU3RhdGVJZCk7XG4gIGlmICh0eXBlb2YgY2hpbGRTdGF0ZVZhbHVlID09PSAnc3RyaW5nJykge1xuICAgIGlmICh0eXBlb2YgcGFyZW50U3RhdGVWYWx1ZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgIHJldHVybiBjaGlsZFN0YXRlVmFsdWUgPT09IHBhcmVudFN0YXRlVmFsdWU7XG4gICAgfVxuXG4gICAgLy8gUGFyZW50IG1vcmUgc3BlY2lmaWMgdGhhbiBjaGlsZFxuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBpZiAodHlwZW9mIHBhcmVudFN0YXRlVmFsdWUgPT09ICdzdHJpbmcnKSB7XG4gICAgcmV0dXJuIHBhcmVudFN0YXRlVmFsdWUgaW4gY2hpbGRTdGF0ZVZhbHVlO1xuICB9XG4gIHJldHVybiBPYmplY3Qua2V5cyhwYXJlbnRTdGF0ZVZhbHVlKS5ldmVyeShrZXkgPT4ge1xuICAgIGlmICghKGtleSBpbiBjaGlsZFN0YXRlVmFsdWUpKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHJldHVybiBtYXRjaGVzU3RhdGUocGFyZW50U3RhdGVWYWx1ZVtrZXldLCBjaGlsZFN0YXRlVmFsdWVba2V5XSk7XG4gIH0pO1xufVxuZnVuY3Rpb24gdG9TdGF0ZVBhdGgoc3RhdGVJZCkge1xuICBpZiAoaXNBcnJheShzdGF0ZUlkKSkge1xuICAgIHJldHVybiBzdGF0ZUlkO1xuICB9XG4gIGNvbnN0IHJlc3VsdCA9IFtdO1xuICBsZXQgc2VnbWVudCA9ICcnO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IHN0YXRlSWQubGVuZ3RoOyBpKyspIHtcbiAgICBjb25zdCBjaGFyID0gc3RhdGVJZC5jaGFyQ29kZUF0KGkpO1xuICAgIHN3aXRjaCAoY2hhcikge1xuICAgICAgLy8gXFxcbiAgICAgIGNhc2UgOTI6XG4gICAgICAgIC8vIGNvbnN1bWUgdGhlIG5leHQgY2hhcmFjdGVyXG4gICAgICAgIHNlZ21lbnQgKz0gc3RhdGVJZFtpICsgMV07XG4gICAgICAgIC8vIGFuZCBza2lwIG92ZXIgaXRcbiAgICAgICAgaSsrO1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIC8vIC5cbiAgICAgIGNhc2UgNDY6XG4gICAgICAgIHJlc3VsdC5wdXNoKHNlZ21lbnQpO1xuICAgICAgICBzZWdtZW50ID0gJyc7XG4gICAgICAgIGNvbnRpbnVlO1xuICAgIH1cbiAgICBzZWdtZW50ICs9IHN0YXRlSWRbaV07XG4gIH1cbiAgcmVzdWx0LnB1c2goc2VnbWVudCk7XG4gIHJldHVybiByZXN1bHQ7XG59XG5mdW5jdGlvbiB0b1N0YXRlVmFsdWUoc3RhdGVWYWx1ZSkge1xuICBpZiAoaXNNYWNoaW5lU25hcHNob3Qoc3RhdGVWYWx1ZSkpIHtcbiAgICByZXR1cm4gc3RhdGVWYWx1ZS52YWx1ZTtcbiAgfVxuICBpZiAodHlwZW9mIHN0YXRlVmFsdWUgIT09ICdzdHJpbmcnKSB7XG4gICAgcmV0dXJuIHN0YXRlVmFsdWU7XG4gIH1cbiAgY29uc3Qgc3RhdGVQYXRoID0gdG9TdGF0ZVBhdGgoc3RhdGVWYWx1ZSk7XG4gIHJldHVybiBwYXRoVG9TdGF0ZVZhbHVlKHN0YXRlUGF0aCk7XG59XG5mdW5jdGlvbiBwYXRoVG9TdGF0ZVZhbHVlKHN0YXRlUGF0aCkge1xuICBpZiAoc3RhdGVQYXRoLmxlbmd0aCA9PT0gMSkge1xuICAgIHJldHVybiBzdGF0ZVBhdGhbMF07XG4gIH1cbiAgY29uc3QgdmFsdWUgPSB7fTtcbiAgbGV0IG1hcmtlciA9IHZhbHVlO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IHN0YXRlUGF0aC5sZW5ndGggLSAxOyBpKyspIHtcbiAgICBpZiAoaSA9PT0gc3RhdGVQYXRoLmxlbmd0aCAtIDIpIHtcbiAgICAgIG1hcmtlcltzdGF0ZVBhdGhbaV1dID0gc3RhdGVQYXRoW2kgKyAxXTtcbiAgICB9IGVsc2Uge1xuICAgICAgY29uc3QgcHJldmlvdXMgPSBtYXJrZXI7XG4gICAgICBtYXJrZXIgPSB7fTtcbiAgICAgIHByZXZpb3VzW3N0YXRlUGF0aFtpXV0gPSBtYXJrZXI7XG4gICAgfVxuICB9XG4gIHJldHVybiB2YWx1ZTtcbn1cbmZ1bmN0aW9uIG1hcFZhbHVlcyhjb2xsZWN0aW9uLCBpdGVyYXRlZSkge1xuICBjb25zdCByZXN1bHQgPSB7fTtcbiAgY29uc3QgY29sbGVjdGlvbktleXMgPSBPYmplY3Qua2V5cyhjb2xsZWN0aW9uKTtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb2xsZWN0aW9uS2V5cy5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IGtleSA9IGNvbGxlY3Rpb25LZXlzW2ldO1xuICAgIHJlc3VsdFtrZXldID0gaXRlcmF0ZWUoY29sbGVjdGlvbltrZXldLCBrZXksIGNvbGxlY3Rpb24sIGkpO1xuICB9XG4gIHJldHVybiByZXN1bHQ7XG59XG5mdW5jdGlvbiB0b0FycmF5U3RyaWN0KHZhbHVlKSB7XG4gIGlmIChpc0FycmF5KHZhbHVlKSkge1xuICAgIHJldHVybiB2YWx1ZTtcbiAgfVxuICByZXR1cm4gW3ZhbHVlXTtcbn1cbmZ1bmN0aW9uIHRvQXJyYXkodmFsdWUpIHtcbiAgaWYgKHZhbHVlID09PSB1bmRlZmluZWQpIHtcbiAgICByZXR1cm4gW107XG4gIH1cbiAgcmV0dXJuIHRvQXJyYXlTdHJpY3QodmFsdWUpO1xufVxuZnVuY3Rpb24gcmVzb2x2ZU91dHB1dChtYXBwZXIsIGNvbnRleHQsIGV2ZW50LCBzZWxmKSB7XG4gIGlmICh0eXBlb2YgbWFwcGVyID09PSAnZnVuY3Rpb24nKSB7XG4gICAgcmV0dXJuIG1hcHBlcih7XG4gICAgICBjb250ZXh0LFxuICAgICAgZXZlbnQsXG4gICAgICBzZWxmXG4gICAgfSk7XG4gIH1cbiAgaWYgKCEhbWFwcGVyICYmIHR5cGVvZiBtYXBwZXIgPT09ICdvYmplY3QnICYmIE9iamVjdC52YWx1ZXMobWFwcGVyKS5zb21lKHZhbCA9PiB0eXBlb2YgdmFsID09PSAnZnVuY3Rpb24nKSkge1xuICAgIGNvbnNvbGUud2FybihgRHluYW1pY2FsbHkgbWFwcGluZyB2YWx1ZXMgdG8gaW5kaXZpZHVhbCBwcm9wZXJ0aWVzIGlzIGRlcHJlY2F0ZWQuIFVzZSBhIHNpbmdsZSBmdW5jdGlvbiB0aGF0IHJldHVybnMgdGhlIG1hcHBlZCBvYmplY3QgaW5zdGVhZC5cXG5Gb3VuZCBvYmplY3QgY29udGFpbmluZyBwcm9wZXJ0aWVzIHdob3NlIHZhbHVlcyBhcmUgcG9zc2libHkgbWFwcGluZyBmdW5jdGlvbnM6ICR7T2JqZWN0LmVudHJpZXMobWFwcGVyKS5maWx0ZXIoKFssIHZhbHVlXSkgPT4gdHlwZW9mIHZhbHVlID09PSAnZnVuY3Rpb24nKS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gYFxcbiAtICR7a2V5fTogJHt2YWx1ZS50b1N0cmluZygpLnJlcGxhY2UoL1xcblxccyovZywgJycpfWApLmpvaW4oJycpfWApO1xuICB9XG4gIHJldHVybiBtYXBwZXI7XG59XG5mdW5jdGlvbiBpc0FycmF5KHZhbHVlKSB7XG4gIHJldHVybiBBcnJheS5pc0FycmF5KHZhbHVlKTtcbn1cbmZ1bmN0aW9uIGlzRXJyb3JBY3RvckV2ZW50KGV2ZW50KSB7XG4gIHJldHVybiBldmVudC50eXBlLnN0YXJ0c1dpdGgoJ3hzdGF0ZS5lcnJvci5hY3RvcicpO1xufVxuZnVuY3Rpb24gdG9UcmFuc2l0aW9uQ29uZmlnQXJyYXkoY29uZmlnTGlrZSkge1xuICByZXR1cm4gdG9BcnJheVN0cmljdChjb25maWdMaWtlKS5tYXAodHJhbnNpdGlvbkxpa2UgPT4ge1xuICAgIGlmICh0eXBlb2YgdHJhbnNpdGlvbkxpa2UgPT09ICd1bmRlZmluZWQnIHx8IHR5cGVvZiB0cmFuc2l0aW9uTGlrZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHRhcmdldDogdHJhbnNpdGlvbkxpa2VcbiAgICAgIH07XG4gICAgfVxuICAgIHJldHVybiB0cmFuc2l0aW9uTGlrZTtcbiAgfSk7XG59XG5mdW5jdGlvbiBub3JtYWxpemVUYXJnZXQodGFyZ2V0KSB7XG4gIGlmICh0YXJnZXQgPT09IHVuZGVmaW5lZCB8fCB0YXJnZXQgPT09IFRBUkdFVExFU1NfS0VZKSB7XG4gICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgfVxuICByZXR1cm4gdG9BcnJheSh0YXJnZXQpO1xufVxuZnVuY3Rpb24gdG9PYnNlcnZlcihuZXh0SGFuZGxlciwgZXJyb3JIYW5kbGVyLCBjb21wbGV0aW9uSGFuZGxlcikge1xuICBjb25zdCBpc09ic2VydmVyID0gdHlwZW9mIG5leHRIYW5kbGVyID09PSAnb2JqZWN0JztcbiAgY29uc3Qgc2VsZiA9IGlzT2JzZXJ2ZXIgPyBuZXh0SGFuZGxlciA6IHVuZGVmaW5lZDtcbiAgcmV0dXJuIHtcbiAgICBuZXh0OiAoaXNPYnNlcnZlciA/IG5leHRIYW5kbGVyLm5leHQgOiBuZXh0SGFuZGxlcik/LmJpbmQoc2VsZiksXG4gICAgZXJyb3I6IChpc09ic2VydmVyID8gbmV4dEhhbmRsZXIuZXJyb3IgOiBlcnJvckhhbmRsZXIpPy5iaW5kKHNlbGYpLFxuICAgIGNvbXBsZXRlOiAoaXNPYnNlcnZlciA/IG5leHRIYW5kbGVyLmNvbXBsZXRlIDogY29tcGxldGlvbkhhbmRsZXIpPy5iaW5kKHNlbGYpXG4gIH07XG59XG5mdW5jdGlvbiBjcmVhdGVJbnZva2VJZChzdGF0ZU5vZGVJZCwgaW5kZXgpIHtcbiAgcmV0dXJuIGAke2luZGV4fS4ke3N0YXRlTm9kZUlkfWA7XG59XG5mdW5jdGlvbiByZXNvbHZlUmVmZXJlbmNlZEFjdG9yKG1hY2hpbmUsIHNyYykge1xuICBjb25zdCBtYXRjaCA9IHNyYy5tYXRjaCgvXnhzdGF0ZVxcLmludm9rZVxcLihcXGQrKVxcLiguKikvKTtcbiAgaWYgKCFtYXRjaCkge1xuICAgIHJldHVybiBtYWNoaW5lLmltcGxlbWVudGF0aW9ucy5hY3RvcnNbc3JjXTtcbiAgfVxuICBjb25zdCBbLCBpbmRleFN0ciwgbm9kZUlkXSA9IG1hdGNoO1xuICBjb25zdCBub2RlID0gbWFjaGluZS5nZXRTdGF0ZU5vZGVCeUlkKG5vZGVJZCk7XG4gIGNvbnN0IGludm9rZUNvbmZpZyA9IG5vZGUuY29uZmlnLmludm9rZTtcbiAgcmV0dXJuIChBcnJheS5pc0FycmF5KGludm9rZUNvbmZpZykgPyBpbnZva2VDb25maWdbaW5kZXhTdHJdIDogaW52b2tlQ29uZmlnKS5zcmM7XG59XG5mdW5jdGlvbiBnZXRBbGxPd25FdmVudERlc2NyaXB0b3JzKHNuYXBzaG90KSB7XG4gIHJldHVybiBbLi4ubmV3IFNldChbLi4uc25hcHNob3QuX25vZGVzLmZsYXRNYXAoc24gPT4gc24ub3duRXZlbnRzKV0pXTtcbn1cblxuZnVuY3Rpb24gY3JlYXRlU2NoZWR1bGVkRXZlbnRJZChhY3RvclJlZiwgaWQpIHtcbiAgcmV0dXJuIGAke2FjdG9yUmVmLnNlc3Npb25JZH0uJHtpZH1gO1xufVxubGV0IGlkQ291bnRlciA9IDA7XG5mdW5jdGlvbiBjcmVhdGVTeXN0ZW0ocm9vdEFjdG9yLCBvcHRpb25zKSB7XG4gIGNvbnN0IGNoaWxkcmVuID0gbmV3IE1hcCgpO1xuICBjb25zdCBrZXllZEFjdG9ycyA9IG5ldyBNYXAoKTtcbiAgY29uc3QgcmV2ZXJzZUtleWVkQWN0b3JzID0gbmV3IFdlYWtNYXAoKTtcbiAgY29uc3QgaW5zcGVjdGlvbk9ic2VydmVycyA9IG5ldyBTZXQoKTtcbiAgY29uc3QgdGltZXJNYXAgPSB7fTtcbiAgY29uc3Qge1xuICAgIGNsb2NrLFxuICAgIGxvZ2dlclxuICB9ID0gb3B0aW9ucztcbiAgY29uc3Qgc2NoZWR1bGVyID0ge1xuICAgIHNjaGVkdWxlOiAoc291cmNlLCB0YXJnZXQsIGV2ZW50LCBkZWxheSwgaWQgPSBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zbGljZSgyKSkgPT4ge1xuICAgICAgY29uc3Qgc2NoZWR1bGVkRXZlbnQgPSB7XG4gICAgICAgIHNvdXJjZSxcbiAgICAgICAgdGFyZ2V0LFxuICAgICAgICBldmVudCxcbiAgICAgICAgZGVsYXksXG4gICAgICAgIGlkLFxuICAgICAgICBzdGFydGVkQXQ6IERhdGUubm93KClcbiAgICAgIH07XG4gICAgICBjb25zdCBzY2hlZHVsZWRFdmVudElkID0gY3JlYXRlU2NoZWR1bGVkRXZlbnRJZChzb3VyY2UsIGlkKTtcbiAgICAgIHN5c3RlbS5fc25hcHNob3QuX3NjaGVkdWxlZEV2ZW50c1tzY2hlZHVsZWRFdmVudElkXSA9IHNjaGVkdWxlZEV2ZW50O1xuICAgICAgY29uc3QgdGltZW91dCA9IGNsb2NrLnNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBkZWxldGUgdGltZXJNYXBbc2NoZWR1bGVkRXZlbnRJZF07XG4gICAgICAgIGRlbGV0ZSBzeXN0ZW0uX3NuYXBzaG90Ll9zY2hlZHVsZWRFdmVudHNbc2NoZWR1bGVkRXZlbnRJZF07XG4gICAgICAgIHN5c3RlbS5fcmVsYXkoc291cmNlLCB0YXJnZXQsIGV2ZW50KTtcbiAgICAgIH0sIGRlbGF5KTtcbiAgICAgIHRpbWVyTWFwW3NjaGVkdWxlZEV2ZW50SWRdID0gdGltZW91dDtcbiAgICB9LFxuICAgIGNhbmNlbDogKHNvdXJjZSwgaWQpID0+IHtcbiAgICAgIGNvbnN0IHNjaGVkdWxlZEV2ZW50SWQgPSBjcmVhdGVTY2hlZHVsZWRFdmVudElkKHNvdXJjZSwgaWQpO1xuICAgICAgY29uc3QgdGltZW91dCA9IHRpbWVyTWFwW3NjaGVkdWxlZEV2ZW50SWRdO1xuICAgICAgZGVsZXRlIHRpbWVyTWFwW3NjaGVkdWxlZEV2ZW50SWRdO1xuICAgICAgZGVsZXRlIHN5c3RlbS5fc25hcHNob3QuX3NjaGVkdWxlZEV2ZW50c1tzY2hlZHVsZWRFdmVudElkXTtcbiAgICAgIGlmICh0aW1lb3V0ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgY2xvY2suY2xlYXJUaW1lb3V0KHRpbWVvdXQpO1xuICAgICAgfVxuICAgIH0sXG4gICAgY2FuY2VsQWxsOiBhY3RvclJlZiA9PiB7XG4gICAgICBmb3IgKGNvbnN0IHNjaGVkdWxlZEV2ZW50SWQgaW4gc3lzdGVtLl9zbmFwc2hvdC5fc2NoZWR1bGVkRXZlbnRzKSB7XG4gICAgICAgIGNvbnN0IHNjaGVkdWxlZEV2ZW50ID0gc3lzdGVtLl9zbmFwc2hvdC5fc2NoZWR1bGVkRXZlbnRzW3NjaGVkdWxlZEV2ZW50SWRdO1xuICAgICAgICBpZiAoc2NoZWR1bGVkRXZlbnQuc291cmNlID09PSBhY3RvclJlZikge1xuICAgICAgICAgIHNjaGVkdWxlci5jYW5jZWwoYWN0b3JSZWYsIHNjaGVkdWxlZEV2ZW50LmlkKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfTtcbiAgY29uc3Qgc2VuZEluc3BlY3Rpb25FdmVudCA9IGV2ZW50ID0+IHtcbiAgICBpZiAoIWluc3BlY3Rpb25PYnNlcnZlcnMuc2l6ZSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjb25zdCByZXNvbHZlZEluc3BlY3Rpb25FdmVudCA9IHtcbiAgICAgIC4uLmV2ZW50LFxuICAgICAgcm9vdElkOiByb290QWN0b3Iuc2Vzc2lvbklkXG4gICAgfTtcbiAgICBpbnNwZWN0aW9uT2JzZXJ2ZXJzLmZvckVhY2gob2JzZXJ2ZXIgPT4gb2JzZXJ2ZXIubmV4dD8uKHJlc29sdmVkSW5zcGVjdGlvbkV2ZW50KSk7XG4gIH07XG4gIGNvbnN0IHN5c3RlbSA9IHtcbiAgICBfc25hcHNob3Q6IHtcbiAgICAgIF9zY2hlZHVsZWRFdmVudHM6IChvcHRpb25zPy5zbmFwc2hvdCAmJiBvcHRpb25zLnNuYXBzaG90LnNjaGVkdWxlcikgPz8ge31cbiAgICB9LFxuICAgIF9ib29rSWQ6ICgpID0+IGB4OiR7aWRDb3VudGVyKyt9YCxcbiAgICBfcmVnaXN0ZXI6IChzZXNzaW9uSWQsIGFjdG9yUmVmKSA9PiB7XG4gICAgICBjaGlsZHJlbi5zZXQoc2Vzc2lvbklkLCBhY3RvclJlZik7XG4gICAgICByZXR1cm4gc2Vzc2lvbklkO1xuICAgIH0sXG4gICAgX3VucmVnaXN0ZXI6IGFjdG9yUmVmID0+IHtcbiAgICAgIGNoaWxkcmVuLmRlbGV0ZShhY3RvclJlZi5zZXNzaW9uSWQpO1xuICAgICAgY29uc3Qgc3lzdGVtSWQgPSByZXZlcnNlS2V5ZWRBY3RvcnMuZ2V0KGFjdG9yUmVmKTtcbiAgICAgIGlmIChzeXN0ZW1JZCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGtleWVkQWN0b3JzLmRlbGV0ZShzeXN0ZW1JZCk7XG4gICAgICAgIHJldmVyc2VLZXllZEFjdG9ycy5kZWxldGUoYWN0b3JSZWYpO1xuICAgICAgfVxuICAgIH0sXG4gICAgZ2V0OiBzeXN0ZW1JZCA9PiB7XG4gICAgICByZXR1cm4ga2V5ZWRBY3RvcnMuZ2V0KHN5c3RlbUlkKTtcbiAgICB9LFxuICAgIF9zZXQ6IChzeXN0ZW1JZCwgYWN0b3JSZWYpID0+IHtcbiAgICAgIGNvbnN0IGV4aXN0aW5nID0ga2V5ZWRBY3RvcnMuZ2V0KHN5c3RlbUlkKTtcbiAgICAgIGlmIChleGlzdGluZyAmJiBleGlzdGluZyAhPT0gYWN0b3JSZWYpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBBY3RvciB3aXRoIHN5c3RlbSBJRCAnJHtzeXN0ZW1JZH0nIGFscmVhZHkgZXhpc3RzLmApO1xuICAgICAgfVxuICAgICAga2V5ZWRBY3RvcnMuc2V0KHN5c3RlbUlkLCBhY3RvclJlZik7XG4gICAgICByZXZlcnNlS2V5ZWRBY3RvcnMuc2V0KGFjdG9yUmVmLCBzeXN0ZW1JZCk7XG4gICAgfSxcbiAgICBpbnNwZWN0OiBvYnNlcnZlck9yRm4gPT4ge1xuICAgICAgY29uc3Qgb2JzZXJ2ZXIgPSB0b09ic2VydmVyKG9ic2VydmVyT3JGbik7XG4gICAgICBpbnNwZWN0aW9uT2JzZXJ2ZXJzLmFkZChvYnNlcnZlcik7XG4gICAgICByZXR1cm4ge1xuICAgICAgICB1bnN1YnNjcmliZSgpIHtcbiAgICAgICAgICBpbnNwZWN0aW9uT2JzZXJ2ZXJzLmRlbGV0ZShvYnNlcnZlcik7XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfSxcbiAgICBfc2VuZEluc3BlY3Rpb25FdmVudDogc2VuZEluc3BlY3Rpb25FdmVudCxcbiAgICBfcmVsYXk6IChzb3VyY2UsIHRhcmdldCwgZXZlbnQpID0+IHtcbiAgICAgIHN5c3RlbS5fc2VuZEluc3BlY3Rpb25FdmVudCh7XG4gICAgICAgIHR5cGU6ICdAeHN0YXRlLmV2ZW50JyxcbiAgICAgICAgc291cmNlUmVmOiBzb3VyY2UsXG4gICAgICAgIGFjdG9yUmVmOiB0YXJnZXQsXG4gICAgICAgIGV2ZW50XG4gICAgICB9KTtcbiAgICAgIHRhcmdldC5fc2VuZChldmVudCk7XG4gICAgfSxcbiAgICBzY2hlZHVsZXIsXG4gICAgZ2V0U25hcHNob3Q6ICgpID0+IHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIF9zY2hlZHVsZWRFdmVudHM6IHtcbiAgICAgICAgICAuLi5zeXN0ZW0uX3NuYXBzaG90Ll9zY2hlZHVsZWRFdmVudHNcbiAgICAgICAgfVxuICAgICAgfTtcbiAgICB9LFxuICAgIHN0YXJ0OiAoKSA9PiB7XG4gICAgICBjb25zdCBzY2hlZHVsZWRFdmVudHMgPSBzeXN0ZW0uX3NuYXBzaG90Ll9zY2hlZHVsZWRFdmVudHM7XG4gICAgICBzeXN0ZW0uX3NuYXBzaG90Ll9zY2hlZHVsZWRFdmVudHMgPSB7fTtcbiAgICAgIGZvciAoY29uc3Qgc2NoZWR1bGVkSWQgaW4gc2NoZWR1bGVkRXZlbnRzKSB7XG4gICAgICAgIGNvbnN0IHtcbiAgICAgICAgICBzb3VyY2UsXG4gICAgICAgICAgdGFyZ2V0LFxuICAgICAgICAgIGV2ZW50LFxuICAgICAgICAgIGRlbGF5LFxuICAgICAgICAgIGlkXG4gICAgICAgIH0gPSBzY2hlZHVsZWRFdmVudHNbc2NoZWR1bGVkSWRdO1xuICAgICAgICBzY2hlZHVsZXIuc2NoZWR1bGUoc291cmNlLCB0YXJnZXQsIGV2ZW50LCBkZWxheSwgaWQpO1xuICAgICAgfVxuICAgIH0sXG4gICAgX2Nsb2NrOiBjbG9jayxcbiAgICBfbG9nZ2VyOiBsb2dnZXJcbiAgfTtcbiAgcmV0dXJuIHN5c3RlbTtcbn1cblxubGV0IGV4ZWN1dGluZ0N1c3RvbUFjdGlvbiA9IGZhbHNlO1xuY29uc3QgJCRBQ1RPUl9UWVBFID0gMTtcblxuLy8gdGhvc2UgdmFsdWVzIGFyZSBjdXJyZW50bHkgdXNlZCBieSBAeHN0YXRlL3JlYWN0IGRpcmVjdGx5IHNvIGl0J3MgaW1wb3J0YW50IHRvIGtlZXAgdGhlIGFzc2lnbmVkIHZhbHVlcyBpbiBzeW5jXG5sZXQgUHJvY2Vzc2luZ1N0YXR1cyA9IC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoUHJvY2Vzc2luZ1N0YXR1cykge1xuICBQcm9jZXNzaW5nU3RhdHVzW1Byb2Nlc3NpbmdTdGF0dXNbXCJOb3RTdGFydGVkXCJdID0gMF0gPSBcIk5vdFN0YXJ0ZWRcIjtcbiAgUHJvY2Vzc2luZ1N0YXR1c1tQcm9jZXNzaW5nU3RhdHVzW1wiUnVubmluZ1wiXSA9IDFdID0gXCJSdW5uaW5nXCI7XG4gIFByb2Nlc3NpbmdTdGF0dXNbUHJvY2Vzc2luZ1N0YXR1c1tcIlN0b3BwZWRcIl0gPSAyXSA9IFwiU3RvcHBlZFwiO1xuICByZXR1cm4gUHJvY2Vzc2luZ1N0YXR1cztcbn0oe30pO1xuY29uc3QgZGVmYXVsdE9wdGlvbnMgPSB7XG4gIGNsb2NrOiB7XG4gICAgc2V0VGltZW91dDogKGZuLCBtcykgPT4ge1xuICAgICAgcmV0dXJuIHNldFRpbWVvdXQoZm4sIG1zKTtcbiAgICB9LFxuICAgIGNsZWFyVGltZW91dDogaWQgPT4ge1xuICAgICAgcmV0dXJuIGNsZWFyVGltZW91dChpZCk7XG4gICAgfVxuICB9LFxuICBsb2dnZXI6IGNvbnNvbGUubG9nLmJpbmQoY29uc29sZSksXG4gIGRldlRvb2xzOiBmYWxzZVxufTtcblxuLyoqXG4gKiBBbiBBY3RvciBpcyBhIHJ1bm5pbmcgcHJvY2VzcyB0aGF0IGNhbiByZWNlaXZlIGV2ZW50cywgc2VuZCBldmVudHMgYW5kIGNoYW5nZVxuICogaXRzIGJlaGF2aW9yIGJhc2VkIG9uIHRoZSBldmVudHMgaXQgcmVjZWl2ZXMsIHdoaWNoIGNhbiBjYXVzZSBlZmZlY3RzIG91dHNpZGVcbiAqIG9mIHRoZSBhY3Rvci4gV2hlbiB5b3UgcnVuIGEgc3RhdGUgbWFjaGluZSwgaXQgYmVjb21lcyBhbiBhY3Rvci5cbiAqL1xuY2xhc3MgQWN0b3Ige1xuICAvKipcbiAgICogQ3JlYXRlcyBhIG5ldyBhY3RvciBpbnN0YW5jZSBmb3IgdGhlIGdpdmVuIGxvZ2ljIHdpdGggdGhlIHByb3ZpZGVkIG9wdGlvbnMsXG4gICAqIGlmIGFueS5cbiAgICpcbiAgICogQHBhcmFtIGxvZ2ljIFRoZSBsb2dpYyB0byBjcmVhdGUgYW4gYWN0b3IgZnJvbVxuICAgKiBAcGFyYW0gb3B0aW9ucyBBY3RvciBvcHRpb25zXG4gICAqL1xuICBjb25zdHJ1Y3Rvcihsb2dpYywgb3B0aW9ucykge1xuICAgIHRoaXMubG9naWMgPSBsb2dpYztcbiAgICAvKiogVGhlIGN1cnJlbnQgaW50ZXJuYWwgc3RhdGUgb2YgdGhlIGFjdG9yLiAqL1xuICAgIHRoaXMuX3NuYXBzaG90ID0gdm9pZCAwO1xuICAgIC8qKlxuICAgICAqIFRoZSBjbG9jayB0aGF0IGlzIHJlc3BvbnNpYmxlIGZvciBzZXR0aW5nIGFuZCBjbGVhcmluZyB0aW1lb3V0cywgc3VjaCBhc1xuICAgICAqIGRlbGF5ZWQgZXZlbnRzIGFuZCB0cmFuc2l0aW9ucy5cbiAgICAgKi9cbiAgICB0aGlzLmNsb2NrID0gdm9pZCAwO1xuICAgIHRoaXMub3B0aW9ucyA9IHZvaWQgMDtcbiAgICAvKiogVGhlIHVuaXF1ZSBpZGVudGlmaWVyIGZvciB0aGlzIGFjdG9yIHJlbGF0aXZlIHRvIGl0cyBwYXJlbnQuICovXG4gICAgdGhpcy5pZCA9IHZvaWQgMDtcbiAgICB0aGlzLm1haWxib3ggPSBuZXcgTWFpbGJveCh0aGlzLl9wcm9jZXNzLmJpbmQodGhpcykpO1xuICAgIHRoaXMub2JzZXJ2ZXJzID0gbmV3IFNldCgpO1xuICAgIHRoaXMuZXZlbnRMaXN0ZW5lcnMgPSBuZXcgTWFwKCk7XG4gICAgdGhpcy5sb2dnZXIgPSB2b2lkIDA7XG4gICAgLyoqIEBpbnRlcm5hbCAqL1xuICAgIHRoaXMuX3Byb2Nlc3NpbmdTdGF0dXMgPSBQcm9jZXNzaW5nU3RhdHVzLk5vdFN0YXJ0ZWQ7XG4gICAgLy8gQWN0b3IgUmVmXG4gICAgdGhpcy5fcGFyZW50ID0gdm9pZCAwO1xuICAgIC8qKiBAaW50ZXJuYWwgKi9cbiAgICB0aGlzLl9zeW5jU25hcHNob3QgPSB2b2lkIDA7XG4gICAgdGhpcy5yZWYgPSB2b2lkIDA7XG4gICAgLy8gVE9ETzogYWRkIHR5cGluZ3MgZm9yIHN5c3RlbVxuICAgIHRoaXMuX2FjdG9yU2NvcGUgPSB2b2lkIDA7XG4gICAgdGhpcy5fc3lzdGVtSWQgPSB2b2lkIDA7XG4gICAgLyoqIFRoZSBnbG9iYWxseSB1bmlxdWUgcHJvY2VzcyBJRCBmb3IgdGhpcyBpbnZvY2F0aW9uLiAqL1xuICAgIHRoaXMuc2Vzc2lvbklkID0gdm9pZCAwO1xuICAgIC8qKiBUaGUgc3lzdGVtIHRvIHdoaWNoIHRoaXMgYWN0b3IgYmVsb25ncy4gKi9cbiAgICB0aGlzLnN5c3RlbSA9IHZvaWQgMDtcbiAgICB0aGlzLl9kb25lRXZlbnQgPSB2b2lkIDA7XG4gICAgdGhpcy5zcmMgPSB2b2lkIDA7XG4gICAgLy8gYXJyYXkgb2YgZnVuY3Rpb25zIHRvIGRlZmVyXG4gICAgdGhpcy5fZGVmZXJyZWQgPSBbXTtcbiAgICBjb25zdCByZXNvbHZlZE9wdGlvbnMgPSB7XG4gICAgICAuLi5kZWZhdWx0T3B0aW9ucyxcbiAgICAgIC4uLm9wdGlvbnNcbiAgICB9O1xuICAgIGNvbnN0IHtcbiAgICAgIGNsb2NrLFxuICAgICAgbG9nZ2VyLFxuICAgICAgcGFyZW50LFxuICAgICAgc3luY1NuYXBzaG90LFxuICAgICAgaWQsXG4gICAgICBzeXN0ZW1JZCxcbiAgICAgIGluc3BlY3RcbiAgICB9ID0gcmVzb2x2ZWRPcHRpb25zO1xuICAgIHRoaXMuc3lzdGVtID0gcGFyZW50ID8gcGFyZW50LnN5c3RlbSA6IGNyZWF0ZVN5c3RlbSh0aGlzLCB7XG4gICAgICBjbG9jayxcbiAgICAgIGxvZ2dlclxuICAgIH0pO1xuICAgIGlmIChpbnNwZWN0ICYmICFwYXJlbnQpIHtcbiAgICAgIC8vIEFsd2F5cyBpbnNwZWN0IGF0IHRoZSBzeXN0ZW0tbGV2ZWxcbiAgICAgIHRoaXMuc3lzdGVtLmluc3BlY3QodG9PYnNlcnZlcihpbnNwZWN0KSk7XG4gICAgfVxuICAgIHRoaXMuc2Vzc2lvbklkID0gdGhpcy5zeXN0ZW0uX2Jvb2tJZCgpO1xuICAgIHRoaXMuaWQgPSBpZCA/PyB0aGlzLnNlc3Npb25JZDtcbiAgICB0aGlzLmxvZ2dlciA9IG9wdGlvbnM/LmxvZ2dlciA/PyB0aGlzLnN5c3RlbS5fbG9nZ2VyO1xuICAgIHRoaXMuY2xvY2sgPSBvcHRpb25zPy5jbG9jayA/PyB0aGlzLnN5c3RlbS5fY2xvY2s7XG4gICAgdGhpcy5fcGFyZW50ID0gcGFyZW50O1xuICAgIHRoaXMuX3N5bmNTbmFwc2hvdCA9IHN5bmNTbmFwc2hvdDtcbiAgICB0aGlzLm9wdGlvbnMgPSByZXNvbHZlZE9wdGlvbnM7XG4gICAgdGhpcy5zcmMgPSByZXNvbHZlZE9wdGlvbnMuc3JjID8/IGxvZ2ljO1xuICAgIHRoaXMucmVmID0gdGhpcztcbiAgICB0aGlzLl9hY3RvclNjb3BlID0ge1xuICAgICAgc2VsZjogdGhpcyxcbiAgICAgIGlkOiB0aGlzLmlkLFxuICAgICAgc2Vzc2lvbklkOiB0aGlzLnNlc3Npb25JZCxcbiAgICAgIGxvZ2dlcjogdGhpcy5sb2dnZXIsXG4gICAgICBkZWZlcjogZm4gPT4ge1xuICAgICAgICB0aGlzLl9kZWZlcnJlZC5wdXNoKGZuKTtcbiAgICAgIH0sXG4gICAgICBzeXN0ZW06IHRoaXMuc3lzdGVtLFxuICAgICAgc3RvcENoaWxkOiBjaGlsZCA9PiB7XG4gICAgICAgIGlmIChjaGlsZC5fcGFyZW50ICE9PSB0aGlzKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBDYW5ub3Qgc3RvcCBjaGlsZCBhY3RvciAke2NoaWxkLmlkfSBvZiAke3RoaXMuaWR9IGJlY2F1c2UgaXQgaXMgbm90IGEgY2hpbGRgKTtcbiAgICAgICAgfVxuICAgICAgICBjaGlsZC5fc3RvcCgpO1xuICAgICAgfSxcbiAgICAgIGVtaXQ6IGVtaXR0ZWRFdmVudCA9PiB7XG4gICAgICAgIGNvbnN0IGxpc3RlbmVycyA9IHRoaXMuZXZlbnRMaXN0ZW5lcnMuZ2V0KGVtaXR0ZWRFdmVudC50eXBlKTtcbiAgICAgICAgY29uc3Qgd2lsZGNhcmRMaXN0ZW5lciA9IHRoaXMuZXZlbnRMaXN0ZW5lcnMuZ2V0KCcqJyk7XG4gICAgICAgIGlmICghbGlzdGVuZXJzICYmICF3aWxkY2FyZExpc3RlbmVyKSB7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGFsbExpc3RlbmVycyA9IFsuLi4obGlzdGVuZXJzID8gbGlzdGVuZXJzLnZhbHVlcygpIDogW10pLCAuLi4od2lsZGNhcmRMaXN0ZW5lciA/IHdpbGRjYXJkTGlzdGVuZXIudmFsdWVzKCkgOiBbXSldO1xuICAgICAgICBmb3IgKGNvbnN0IGhhbmRsZXIgb2YgYWxsTGlzdGVuZXJzKSB7XG4gICAgICAgICAgaGFuZGxlcihlbWl0dGVkRXZlbnQpO1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgYWN0aW9uRXhlY3V0b3I6IGFjdGlvbiA9PiB7XG4gICAgICAgIGNvbnN0IGV4ZWMgPSAoKSA9PiB7XG4gICAgICAgICAgdGhpcy5fYWN0b3JTY29wZS5zeXN0ZW0uX3NlbmRJbnNwZWN0aW9uRXZlbnQoe1xuICAgICAgICAgICAgdHlwZTogJ0B4c3RhdGUuYWN0aW9uJyxcbiAgICAgICAgICAgIGFjdG9yUmVmOiB0aGlzLFxuICAgICAgICAgICAgYWN0aW9uOiB7XG4gICAgICAgICAgICAgIHR5cGU6IGFjdGlvbi50eXBlLFxuICAgICAgICAgICAgICBwYXJhbXM6IGFjdGlvbi5wYXJhbXNcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgICBpZiAoIWFjdGlvbi5leGVjKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuICAgICAgICAgIGNvbnN0IHNhdmVFeGVjdXRpbmdDdXN0b21BY3Rpb24gPSBleGVjdXRpbmdDdXN0b21BY3Rpb247XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGV4ZWN1dGluZ0N1c3RvbUFjdGlvbiA9IHRydWU7XG4gICAgICAgICAgICBhY3Rpb24uZXhlYyhhY3Rpb24uaW5mbywgYWN0aW9uLnBhcmFtcyk7XG4gICAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICAgIGV4ZWN1dGluZ0N1c3RvbUFjdGlvbiA9IHNhdmVFeGVjdXRpbmdDdXN0b21BY3Rpb247XG4gICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICBpZiAodGhpcy5fcHJvY2Vzc2luZ1N0YXR1cyA9PT0gUHJvY2Vzc2luZ1N0YXR1cy5SdW5uaW5nKSB7XG4gICAgICAgICAgZXhlYygpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHRoaXMuX2RlZmVycmVkLnB1c2goZXhlYyk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9O1xuXG4gICAgLy8gRW5zdXJlIHRoYXQgdGhlIHNlbmQgbWV0aG9kIGlzIGJvdW5kIHRvIHRoaXMgQWN0b3IgaW5zdGFuY2VcbiAgICAvLyBpZiBkZXN0cnVjdHVyZWRcbiAgICB0aGlzLnNlbmQgPSB0aGlzLnNlbmQuYmluZCh0aGlzKTtcbiAgICB0aGlzLnN5c3RlbS5fc2VuZEluc3BlY3Rpb25FdmVudCh7XG4gICAgICB0eXBlOiAnQHhzdGF0ZS5hY3RvcicsXG4gICAgICBhY3RvclJlZjogdGhpc1xuICAgIH0pO1xuICAgIGlmIChzeXN0ZW1JZCkge1xuICAgICAgdGhpcy5fc3lzdGVtSWQgPSBzeXN0ZW1JZDtcbiAgICAgIHRoaXMuc3lzdGVtLl9zZXQoc3lzdGVtSWQsIHRoaXMpO1xuICAgIH1cbiAgICB0aGlzLl9pbml0U3RhdGUob3B0aW9ucz8uc25hcHNob3QgPz8gb3B0aW9ucz8uc3RhdGUpO1xuICAgIGlmIChzeXN0ZW1JZCAmJiB0aGlzLl9zbmFwc2hvdC5zdGF0dXMgIT09ICdhY3RpdmUnKSB7XG4gICAgICB0aGlzLnN5c3RlbS5fdW5yZWdpc3Rlcih0aGlzKTtcbiAgICB9XG4gIH1cbiAgX2luaXRTdGF0ZShwZXJzaXN0ZWRTdGF0ZSkge1xuICAgIHRyeSB7XG4gICAgICB0aGlzLl9zbmFwc2hvdCA9IHBlcnNpc3RlZFN0YXRlID8gdGhpcy5sb2dpYy5yZXN0b3JlU25hcHNob3QgPyB0aGlzLmxvZ2ljLnJlc3RvcmVTbmFwc2hvdChwZXJzaXN0ZWRTdGF0ZSwgdGhpcy5fYWN0b3JTY29wZSkgOiBwZXJzaXN0ZWRTdGF0ZSA6IHRoaXMubG9naWMuZ2V0SW5pdGlhbFNuYXBzaG90KHRoaXMuX2FjdG9yU2NvcGUsIHRoaXMub3B0aW9ucz8uaW5wdXQpO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgLy8gaWYgd2UgZ2V0IGhlcmUgdGhlbiBpdCBtZWFucyB0aGF0IHdlIGFzc2lnbiBhIHZhbHVlIHRvIHRoaXMuX3NuYXBzaG90IHRoYXQgaXMgbm90IG9mIHRoZSBjb3JyZWN0IHR5cGVcbiAgICAgIC8vIHdlIGNhbid0IGdldCB0aGUgdHJ1ZSBgVFNuYXBzaG90ICYgeyBzdGF0dXM6ICdlcnJvcic7IH1gLCBpdCdzIGltcG9zc2libGVcbiAgICAgIC8vIHNvIHJpZ2h0IG5vdyB0aGlzIGlzIGEgbGllIG9mIHNvcnRzXG4gICAgICB0aGlzLl9zbmFwc2hvdCA9IHtcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICBvdXRwdXQ6IHVuZGVmaW5lZCxcbiAgICAgICAgZXJyb3I6IGVyclxuICAgICAgfTtcbiAgICB9XG4gIH1cbiAgdXBkYXRlKHNuYXBzaG90LCBldmVudCkge1xuICAgIC8vIFVwZGF0ZSBzdGF0ZVxuICAgIHRoaXMuX3NuYXBzaG90ID0gc25hcHNob3Q7XG5cbiAgICAvLyBFeGVjdXRlIGRlZmVycmVkIGVmZmVjdHNcbiAgICBsZXQgZGVmZXJyZWRGbjtcbiAgICB3aGlsZSAoZGVmZXJyZWRGbiA9IHRoaXMuX2RlZmVycmVkLnNoaWZ0KCkpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGRlZmVycmVkRm4oKTtcbiAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAvLyB0aGlzIGVycm9yIGNhbiBvbmx5IGJlIGNhdWdodCB3aGVuIGV4ZWN1dGluZyAqaW5pdGlhbCogYWN0aW9uc1xuICAgICAgICAvLyBpdCdzIHRoZSBvbmx5IHRpbWUgd2hlbiB3ZSBjYWxsIGFjdGlvbnMgcHJvdmlkZWQgYnkgdGhlIHVzZXIgdGhyb3VnaCB0aG9zZSBkZWZlcnJlZHNcbiAgICAgICAgLy8gd2hlbiB0aGUgYWN0b3IgaXMgYWxyZWFkeSBydW5uaW5nIHdlIGFsd2F5cyBleGVjdXRlIHRoZW0gc3luY2hyb25vdXNseSB3aGlsZSB0cmFuc2l0aW9uaW5nXG4gICAgICAgIC8vIG5vIFwiYnVpbHRpbiBkZWZlcnJlZFwiIHNob3VsZCBhY3R1YWxseSB0aHJvdyBhbiBlcnJvciBzaW5jZSB0aGV5IGFyZSBlaXRoZXIgc2FmZVxuICAgICAgICAvLyBvciB0aGUgY29udHJvbCBmbG93IGlzIHBhc3NlZCB0aHJvdWdoIHRoZSBtYWlsYm94IGFuZCBlcnJvcnMgc2hvdWxkIGJlIGNhdWdodCBieSB0aGUgYF9wcm9jZXNzYCB1c2VkIGJ5IHRoZSBtYWlsYm94XG4gICAgICAgIHRoaXMuX2RlZmVycmVkLmxlbmd0aCA9IDA7XG4gICAgICAgIHRoaXMuX3NuYXBzaG90ID0ge1xuICAgICAgICAgIC4uLnNuYXBzaG90LFxuICAgICAgICAgIHN0YXR1czogJ2Vycm9yJyxcbiAgICAgICAgICBlcnJvcjogZXJyXG4gICAgICAgIH07XG4gICAgICB9XG4gICAgfVxuICAgIHN3aXRjaCAodGhpcy5fc25hcHNob3Quc3RhdHVzKSB7XG4gICAgICBjYXNlICdhY3RpdmUnOlxuICAgICAgICBmb3IgKGNvbnN0IG9ic2VydmVyIG9mIHRoaXMub2JzZXJ2ZXJzKSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIG9ic2VydmVyLm5leHQ/LihzbmFwc2hvdCk7XG4gICAgICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICByZXBvcnRVbmhhbmRsZWRFcnJvcihlcnIpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ2RvbmUnOlxuICAgICAgICAvLyBuZXh0IG9ic2VydmVycyBhcmUgbWVhbnQgdG8gYmUgbm90aWZpZWQgYWJvdXQgZG9uZSBzbmFwc2hvdHNcbiAgICAgICAgLy8gdGhpcyBjYW4gYmUgc2VlbiBhcyBzb21ldGhpbmcgdGhhdCBpcyBkaWZmZXJlbnQgZnJvbSBob3cgb2JzZXJ2YWJsZSB3b3JrXG4gICAgICAgIC8vIGJ1dCB3aXRoIG9ic2VydmFibGVzIGBjb21wbGV0ZWAgY2FsbGJhY2sgaXMgY2FsbGVkIHdpdGhvdXQgYW55IGFyZ3VtZW50c1xuICAgICAgICAvLyBpdCdzIG1vcmUgZXJnb25vbWljIGZvciBYU3RhdGUgdG8gdHJlYXQgYSBkb25lIHNuYXBzaG90IGFzIGEgXCJuZXh0XCIgdmFsdWVcbiAgICAgICAgLy8gYW5kIHRoZSBjb21wbGV0aW9uIGV2ZW50IGFzIHNvbWV0aGluZyB0aGF0IGlzIHNlcGFyYXRlLFxuICAgICAgICAvLyBzb21ldGhpbmcgdGhhdCBtZXJlbHkgZm9sbG93cyBlbWl0dGluZyB0aGF0IGRvbmUgc25hcHNob3RcbiAgICAgICAgZm9yIChjb25zdCBvYnNlcnZlciBvZiB0aGlzLm9ic2VydmVycykge1xuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICBvYnNlcnZlci5uZXh0Py4oc25hcHNob3QpO1xuICAgICAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgcmVwb3J0VW5oYW5kbGVkRXJyb3IoZXJyKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5fc3RvcFByb2NlZHVyZSgpO1xuICAgICAgICB0aGlzLl9jb21wbGV0ZSgpO1xuICAgICAgICB0aGlzLl9kb25lRXZlbnQgPSBjcmVhdGVEb25lQWN0b3JFdmVudCh0aGlzLmlkLCB0aGlzLl9zbmFwc2hvdC5vdXRwdXQpO1xuICAgICAgICBpZiAodGhpcy5fcGFyZW50KSB7XG4gICAgICAgICAgdGhpcy5zeXN0ZW0uX3JlbGF5KHRoaXMsIHRoaXMuX3BhcmVudCwgdGhpcy5fZG9uZUV2ZW50KTtcbiAgICAgICAgfVxuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ2Vycm9yJzpcbiAgICAgICAgdGhpcy5fZXJyb3IodGhpcy5fc25hcHNob3QuZXJyb3IpO1xuICAgICAgICBicmVhaztcbiAgICB9XG4gICAgdGhpcy5zeXN0ZW0uX3NlbmRJbnNwZWN0aW9uRXZlbnQoe1xuICAgICAgdHlwZTogJ0B4c3RhdGUuc25hcHNob3QnLFxuICAgICAgYWN0b3JSZWY6IHRoaXMsXG4gICAgICBldmVudCxcbiAgICAgIHNuYXBzaG90XG4gICAgfSk7XG4gIH1cblxuICAvKipcbiAgICogU3Vic2NyaWJlIGFuIG9ic2VydmVyIHRvIGFuIGFjdG9y4oCZcyBzbmFwc2hvdCB2YWx1ZXMuXG4gICAqXG4gICAqIEByZW1hcmtzXG4gICAqIFRoZSBvYnNlcnZlciB3aWxsIHJlY2VpdmUgdGhlIGFjdG9y4oCZcyBzbmFwc2hvdCB2YWx1ZSB3aGVuIGl0IGlzIGVtaXR0ZWQuXG4gICAqIFRoZSBvYnNlcnZlciBjYW4gYmU6XG4gICAqXG4gICAqIC0gQSBwbGFpbiBmdW5jdGlvbiB0aGF0IHJlY2VpdmVzIHRoZSBsYXRlc3Qgc25hcHNob3QsIG9yXG4gICAqIC0gQW4gb2JzZXJ2ZXIgb2JqZWN0IHdob3NlIGAubmV4dChzbmFwc2hvdClgIG1ldGhvZCByZWNlaXZlcyB0aGUgbGF0ZXN0XG4gICAqICAgc25hcHNob3RcbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICpcbiAgICogYGBgdHNcbiAgICogLy8gT2JzZXJ2ZXIgYXMgYSBwbGFpbiBmdW5jdGlvblxuICAgKiBjb25zdCBzdWJzY3JpcHRpb24gPSBhY3Rvci5zdWJzY3JpYmUoKHNuYXBzaG90KSA9PiB7XG4gICAqICAgY29uc29sZS5sb2coc25hcHNob3QpO1xuICAgKiB9KTtcbiAgICogYGBgXG4gICAqXG4gICAqIEBleGFtcGxlXG4gICAqXG4gICAqIGBgYHRzXG4gICAqIC8vIE9ic2VydmVyIGFzIGFuIG9iamVjdFxuICAgKiBjb25zdCBzdWJzY3JpcHRpb24gPSBhY3Rvci5zdWJzY3JpYmUoe1xuICAgKiAgIG5leHQoc25hcHNob3QpIHtcbiAgICogICAgIGNvbnNvbGUubG9nKHNuYXBzaG90KTtcbiAgICogICB9LFxuICAgKiAgIGVycm9yKGVycikge1xuICAgKiAgICAgLy8gLi4uXG4gICAqICAgfSxcbiAgICogICBjb21wbGV0ZSgpIHtcbiAgICogICAgIC8vIC4uLlxuICAgKiAgIH1cbiAgICogfSk7XG4gICAqIGBgYFxuICAgKlxuICAgKiBUaGUgcmV0dXJuIHZhbHVlIG9mIGBhY3Rvci5zdWJzY3JpYmUob2JzZXJ2ZXIpYCBpcyBhIHN1YnNjcmlwdGlvbiBvYmplY3RcbiAgICogdGhhdCBoYXMgYW4gYC51bnN1YnNjcmliZSgpYCBtZXRob2QuIFlvdSBjYW4gY2FsbFxuICAgKiBgc3Vic2NyaXB0aW9uLnVuc3Vic2NyaWJlKClgIHRvIHVuc3Vic2NyaWJlIHRoZSBvYnNlcnZlcjpcbiAgICpcbiAgICogQGV4YW1wbGVcbiAgICpcbiAgICogYGBgdHNcbiAgICogY29uc3Qgc3Vic2NyaXB0aW9uID0gYWN0b3Iuc3Vic2NyaWJlKChzbmFwc2hvdCkgPT4ge1xuICAgKiAgIC8vIC4uLlxuICAgKiB9KTtcbiAgICpcbiAgICogLy8gVW5zdWJzY3JpYmUgdGhlIG9ic2VydmVyXG4gICAqIHN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpO1xuICAgKiBgYGBcbiAgICpcbiAgICogV2hlbiB0aGUgYWN0b3IgaXMgc3RvcHBlZCwgYWxsIG9mIGl0cyBvYnNlcnZlcnMgd2lsbCBhdXRvbWF0aWNhbGx5IGJlXG4gICAqIHVuc3Vic2NyaWJlZC5cbiAgICpcbiAgICogQHBhcmFtIG9ic2VydmVyIC0gRWl0aGVyIGEgcGxhaW4gZnVuY3Rpb24gdGhhdCByZWNlaXZlcyB0aGUgbGF0ZXN0XG4gICAqICAgc25hcHNob3QsIG9yIGFuIG9ic2VydmVyIG9iamVjdCB3aG9zZSBgLm5leHQoc25hcHNob3QpYCBtZXRob2QgcmVjZWl2ZXNcbiAgICogICB0aGUgbGF0ZXN0IHNuYXBzaG90XG4gICAqL1xuXG4gIHN1YnNjcmliZShuZXh0TGlzdGVuZXJPck9ic2VydmVyLCBlcnJvckxpc3RlbmVyLCBjb21wbGV0ZUxpc3RlbmVyKSB7XG4gICAgY29uc3Qgb2JzZXJ2ZXIgPSB0b09ic2VydmVyKG5leHRMaXN0ZW5lck9yT2JzZXJ2ZXIsIGVycm9yTGlzdGVuZXIsIGNvbXBsZXRlTGlzdGVuZXIpO1xuICAgIGlmICh0aGlzLl9wcm9jZXNzaW5nU3RhdHVzICE9PSBQcm9jZXNzaW5nU3RhdHVzLlN0b3BwZWQpIHtcbiAgICAgIHRoaXMub2JzZXJ2ZXJzLmFkZChvYnNlcnZlcik7XG4gICAgfSBlbHNlIHtcbiAgICAgIHN3aXRjaCAodGhpcy5fc25hcHNob3Quc3RhdHVzKSB7XG4gICAgICAgIGNhc2UgJ2RvbmUnOlxuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICBvYnNlcnZlci5jb21wbGV0ZT8uKCk7XG4gICAgICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICByZXBvcnRVbmhhbmRsZWRFcnJvcihlcnIpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnZXJyb3InOlxuICAgICAgICAgIHtcbiAgICAgICAgICAgIGNvbnN0IGVyciA9IHRoaXMuX3NuYXBzaG90LmVycm9yO1xuICAgICAgICAgICAgaWYgKCFvYnNlcnZlci5lcnJvcikge1xuICAgICAgICAgICAgICByZXBvcnRVbmhhbmRsZWRFcnJvcihlcnIpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBvYnNlcnZlci5lcnJvcihlcnIpO1xuICAgICAgICAgICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICByZXBvcnRVbmhhbmRsZWRFcnJvcihlcnIpO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICB1bnN1YnNjcmliZTogKCkgPT4ge1xuICAgICAgICB0aGlzLm9ic2VydmVycy5kZWxldGUob2JzZXJ2ZXIpO1xuICAgICAgfVxuICAgIH07XG4gIH1cbiAgb24odHlwZSwgaGFuZGxlcikge1xuICAgIGxldCBsaXN0ZW5lcnMgPSB0aGlzLmV2ZW50TGlzdGVuZXJzLmdldCh0eXBlKTtcbiAgICBpZiAoIWxpc3RlbmVycykge1xuICAgICAgbGlzdGVuZXJzID0gbmV3IFNldCgpO1xuICAgICAgdGhpcy5ldmVudExpc3RlbmVycy5zZXQodHlwZSwgbGlzdGVuZXJzKTtcbiAgICB9XG4gICAgY29uc3Qgd3JhcHBlZEhhbmRsZXIgPSBoYW5kbGVyLmJpbmQodW5kZWZpbmVkKTtcbiAgICBsaXN0ZW5lcnMuYWRkKHdyYXBwZWRIYW5kbGVyKTtcbiAgICByZXR1cm4ge1xuICAgICAgdW5zdWJzY3JpYmU6ICgpID0+IHtcbiAgICAgICAgbGlzdGVuZXJzLmRlbGV0ZSh3cmFwcGVkSGFuZGxlcik7XG4gICAgICB9XG4gICAgfTtcbiAgfVxuXG4gIC8qKiBTdGFydHMgdGhlIEFjdG9yIGZyb20gdGhlIGluaXRpYWwgc3RhdGUgKi9cbiAgc3RhcnQoKSB7XG4gICAgaWYgKHRoaXMuX3Byb2Nlc3NpbmdTdGF0dXMgPT09IFByb2Nlc3NpbmdTdGF0dXMuUnVubmluZykge1xuICAgICAgLy8gRG8gbm90IHJlc3RhcnQgdGhlIHNlcnZpY2UgaWYgaXQgaXMgYWxyZWFkeSBzdGFydGVkXG4gICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgaWYgKHRoaXMuX3N5bmNTbmFwc2hvdCkge1xuICAgICAgdGhpcy5zdWJzY3JpYmUoe1xuICAgICAgICBuZXh0OiBzbmFwc2hvdCA9PiB7XG4gICAgICAgICAgaWYgKHNuYXBzaG90LnN0YXR1cyA9PT0gJ2FjdGl2ZScpIHtcbiAgICAgICAgICAgIHRoaXMuc3lzdGVtLl9yZWxheSh0aGlzLCB0aGlzLl9wYXJlbnQsIHtcbiAgICAgICAgICAgICAgdHlwZTogYHhzdGF0ZS5zbmFwc2hvdC4ke3RoaXMuaWR9YCxcbiAgICAgICAgICAgICAgc25hcHNob3RcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgZXJyb3I6ICgpID0+IHt9XG4gICAgICB9KTtcbiAgICB9XG4gICAgdGhpcy5zeXN0ZW0uX3JlZ2lzdGVyKHRoaXMuc2Vzc2lvbklkLCB0aGlzKTtcbiAgICBpZiAodGhpcy5fc3lzdGVtSWQpIHtcbiAgICAgIHRoaXMuc3lzdGVtLl9zZXQodGhpcy5fc3lzdGVtSWQsIHRoaXMpO1xuICAgIH1cbiAgICB0aGlzLl9wcm9jZXNzaW5nU3RhdHVzID0gUHJvY2Vzc2luZ1N0YXR1cy5SdW5uaW5nO1xuXG4gICAgLy8gVE9ETzogdGhpcyBpc24ndCBjb3JyZWN0IHdoZW4gcmVoeWRyYXRpbmdcbiAgICBjb25zdCBpbml0RXZlbnQgPSBjcmVhdGVJbml0RXZlbnQodGhpcy5vcHRpb25zLmlucHV0KTtcbiAgICB0aGlzLnN5c3RlbS5fc2VuZEluc3BlY3Rpb25FdmVudCh7XG4gICAgICB0eXBlOiAnQHhzdGF0ZS5ldmVudCcsXG4gICAgICBzb3VyY2VSZWY6IHRoaXMuX3BhcmVudCxcbiAgICAgIGFjdG9yUmVmOiB0aGlzLFxuICAgICAgZXZlbnQ6IGluaXRFdmVudFxuICAgIH0pO1xuICAgIGNvbnN0IHN0YXR1cyA9IHRoaXMuX3NuYXBzaG90LnN0YXR1cztcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnZG9uZSc6XG4gICAgICAgIC8vIGEgc3RhdGUgbWFjaGluZSBjYW4gYmUgXCJkb25lXCIgdXBvbiBpbml0aWFsaXphdGlvbiAoaXQgY291bGQgcmVhY2ggYSBmaW5hbCBzdGF0ZSB1c2luZyBpbml0aWFsIG1pY3Jvc3RlcHMpXG4gICAgICAgIC8vIHdlIHN0aWxsIG5lZWQgdG8gY29tcGxldGUgb2JzZXJ2ZXJzLCBmbHVzaCBkZWZlcnJlZHMgZXRjXG4gICAgICAgIHRoaXMudXBkYXRlKHRoaXMuX3NuYXBzaG90LCBpbml0RXZlbnQpO1xuICAgICAgICAvLyBUT0RPOiByZXRoaW5rIGNsZWFudXAgb2Ygb2JzZXJ2ZXJzLCBtYWlsYm94LCBldGNcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgICBjYXNlICdlcnJvcic6XG4gICAgICAgIHRoaXMuX2Vycm9yKHRoaXMuX3NuYXBzaG90LmVycm9yKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIGlmICghdGhpcy5fcGFyZW50KSB7XG4gICAgICB0aGlzLnN5c3RlbS5zdGFydCgpO1xuICAgIH1cbiAgICBpZiAodGhpcy5sb2dpYy5zdGFydCkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgdGhpcy5sb2dpYy5zdGFydCh0aGlzLl9zbmFwc2hvdCwgdGhpcy5fYWN0b3JTY29wZSk7XG4gICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgdGhpcy5fc25hcHNob3QgPSB7XG4gICAgICAgICAgLi4udGhpcy5fc25hcHNob3QsXG4gICAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICAgIGVycm9yOiBlcnJcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5fZXJyb3IoZXJyKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gVE9ETzogdGhpcyBub3RpZmllcyBhbGwgc3Vic2NyaWJlcnMgYnV0IHVzdWFsbHkgdGhpcyBpcyByZWR1bmRhbnRcbiAgICAvLyB0aGVyZSBpcyBubyByZWFsIGNoYW5nZSBoYXBwZW5pbmcgaGVyZVxuICAgIC8vIHdlIG5lZWQgdG8gcmV0aGluayBpZiB0aGlzIG5lZWRzIHRvIGJlIHJlZmFjdG9yZWRcbiAgICB0aGlzLnVwZGF0ZSh0aGlzLl9zbmFwc2hvdCwgaW5pdEV2ZW50KTtcbiAgICBpZiAodGhpcy5vcHRpb25zLmRldlRvb2xzKSB7XG4gICAgICB0aGlzLmF0dGFjaERldlRvb2xzKCk7XG4gICAgfVxuICAgIHRoaXMubWFpbGJveC5zdGFydCgpO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG4gIF9wcm9jZXNzKGV2ZW50KSB7XG4gICAgbGV0IG5leHRTdGF0ZTtcbiAgICBsZXQgY2F1Z2h0RXJyb3I7XG4gICAgdHJ5IHtcbiAgICAgIG5leHRTdGF0ZSA9IHRoaXMubG9naWMudHJhbnNpdGlvbih0aGlzLl9zbmFwc2hvdCwgZXZlbnQsIHRoaXMuX2FjdG9yU2NvcGUpO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgLy8gd2Ugd3JhcCBpdCBpbiBhIGJveCBzbyB3ZSBjYW4gcmV0aHJvdyBpdCBsYXRlciBldmVuIGlmIGZhbHN5IHZhbHVlIGdldHMgY2F1Z2h0IGhlcmVcbiAgICAgIGNhdWdodEVycm9yID0ge1xuICAgICAgICBlcnJcbiAgICAgIH07XG4gICAgfVxuICAgIGlmIChjYXVnaHRFcnJvcikge1xuICAgICAgY29uc3Qge1xuICAgICAgICBlcnJcbiAgICAgIH0gPSBjYXVnaHRFcnJvcjtcbiAgICAgIHRoaXMuX3NuYXBzaG90ID0ge1xuICAgICAgICAuLi50aGlzLl9zbmFwc2hvdCxcbiAgICAgICAgc3RhdHVzOiAnZXJyb3InLFxuICAgICAgICBlcnJvcjogZXJyXG4gICAgICB9O1xuICAgICAgdGhpcy5fZXJyb3IoZXJyKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgdGhpcy51cGRhdGUobmV4dFN0YXRlLCBldmVudCk7XG4gICAgaWYgKGV2ZW50LnR5cGUgPT09IFhTVEFURV9TVE9QKSB7XG4gICAgICB0aGlzLl9zdG9wUHJvY2VkdXJlKCk7XG4gICAgICB0aGlzLl9jb21wbGV0ZSgpO1xuICAgIH1cbiAgfVxuICBfc3RvcCgpIHtcbiAgICBpZiAodGhpcy5fcHJvY2Vzc2luZ1N0YXR1cyA9PT0gUHJvY2Vzc2luZ1N0YXR1cy5TdG9wcGVkKSB7XG4gICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgdGhpcy5tYWlsYm94LmNsZWFyKCk7XG4gICAgaWYgKHRoaXMuX3Byb2Nlc3NpbmdTdGF0dXMgPT09IFByb2Nlc3NpbmdTdGF0dXMuTm90U3RhcnRlZCkge1xuICAgICAgdGhpcy5fcHJvY2Vzc2luZ1N0YXR1cyA9IFByb2Nlc3NpbmdTdGF0dXMuU3RvcHBlZDtcbiAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICB0aGlzLm1haWxib3guZW5xdWV1ZSh7XG4gICAgICB0eXBlOiBYU1RBVEVfU1RPUFxuICAgIH0pO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG5cbiAgLyoqIFN0b3BzIHRoZSBBY3RvciBhbmQgdW5zdWJzY3JpYmUgYWxsIGxpc3RlbmVycy4gKi9cbiAgc3RvcCgpIHtcbiAgICBpZiAodGhpcy5fcGFyZW50KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0Egbm9uLXJvb3QgYWN0b3IgY2Fubm90IGJlIHN0b3BwZWQgZGlyZWN0bHkuJyk7XG4gICAgfVxuICAgIHJldHVybiB0aGlzLl9zdG9wKCk7XG4gIH1cbiAgX2NvbXBsZXRlKCkge1xuICAgIGZvciAoY29uc3Qgb2JzZXJ2ZXIgb2YgdGhpcy5vYnNlcnZlcnMpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIG9ic2VydmVyLmNvbXBsZXRlPy4oKTtcbiAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICByZXBvcnRVbmhhbmRsZWRFcnJvcihlcnIpO1xuICAgICAgfVxuICAgIH1cbiAgICB0aGlzLm9ic2VydmVycy5jbGVhcigpO1xuICB9XG4gIF9yZXBvcnRFcnJvcihlcnIpIHtcbiAgICBpZiAoIXRoaXMub2JzZXJ2ZXJzLnNpemUpIHtcbiAgICAgIGlmICghdGhpcy5fcGFyZW50KSB7XG4gICAgICAgIHJlcG9ydFVuaGFuZGxlZEVycm9yKGVycik7XG4gICAgICB9XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGxldCByZXBvcnRFcnJvciA9IGZhbHNlO1xuICAgIGZvciAoY29uc3Qgb2JzZXJ2ZXIgb2YgdGhpcy5vYnNlcnZlcnMpIHtcbiAgICAgIGNvbnN0IGVycm9yTGlzdGVuZXIgPSBvYnNlcnZlci5lcnJvcjtcbiAgICAgIHJlcG9ydEVycm9yIHx8PSAhZXJyb3JMaXN0ZW5lcjtcbiAgICAgIHRyeSB7XG4gICAgICAgIGVycm9yTGlzdGVuZXI/LihlcnIpO1xuICAgICAgfSBjYXRjaCAoZXJyMikge1xuICAgICAgICByZXBvcnRVbmhhbmRsZWRFcnJvcihlcnIyKTtcbiAgICAgIH1cbiAgICB9XG4gICAgdGhpcy5vYnNlcnZlcnMuY2xlYXIoKTtcbiAgICBpZiAocmVwb3J0RXJyb3IpIHtcbiAgICAgIHJlcG9ydFVuaGFuZGxlZEVycm9yKGVycik7XG4gICAgfVxuICB9XG4gIF9lcnJvcihlcnIpIHtcbiAgICB0aGlzLl9zdG9wUHJvY2VkdXJlKCk7XG4gICAgdGhpcy5fcmVwb3J0RXJyb3IoZXJyKTtcbiAgICBpZiAodGhpcy5fcGFyZW50KSB7XG4gICAgICB0aGlzLnN5c3RlbS5fcmVsYXkodGhpcywgdGhpcy5fcGFyZW50LCBjcmVhdGVFcnJvckFjdG9yRXZlbnQodGhpcy5pZCwgZXJyKSk7XG4gICAgfVxuICB9XG4gIC8vIFRPRE86IGF0bSBjaGlsZHJlbiBkb24ndCBiZWxvbmcgZW50aXJlbHkgdG8gdGhlIGFjdG9yIHNvXG4gIC8vIGluIGEgd2F5IC0gaXQncyBub3QgZXZlbiBzdXBlciBhd2FyZSBvZiB0aGVtXG4gIC8vIHNvIHdlIGNhbid0IHN0b3AgdGhlbSBmcm9tIGhlcmUgYnV0IHdlIHJlYWxseSBzaG91bGQhXG4gIC8vIHJpZ2h0IG5vdywgdGhleSBhcmUgYmVpbmcgc3RvcHBlZCB3aXRoaW4gdGhlIG1hY2hpbmUncyB0cmFuc2l0aW9uXG4gIC8vIGJ1dCB0aGF0IGNvdWxkIHRocm93IGFuZCBsZWF2ZSB1cyB3aXRoIFwib3JwaGFuZWRcIiBhY3RpdmUgYWN0b3JzXG4gIF9zdG9wUHJvY2VkdXJlKCkge1xuICAgIGlmICh0aGlzLl9wcm9jZXNzaW5nU3RhdHVzICE9PSBQcm9jZXNzaW5nU3RhdHVzLlJ1bm5pbmcpIHtcbiAgICAgIC8vIEFjdG9yIGFscmVhZHkgc3RvcHBlZDsgZG8gbm90aGluZ1xuICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuXG4gICAgLy8gQ2FuY2VsIGFsbCBkZWxheWVkIGV2ZW50c1xuICAgIHRoaXMuc3lzdGVtLnNjaGVkdWxlci5jYW5jZWxBbGwodGhpcyk7XG5cbiAgICAvLyBUT0RPOiBtYWlsYm94LnJlc2V0XG4gICAgdGhpcy5tYWlsYm94LmNsZWFyKCk7XG4gICAgLy8gVE9ETzogYWZ0ZXIgYHN0b3BgIHdlIG11c3QgcHJlcGFyZSBvdXJzZWx2ZXMgZm9yIHJlY2VpdmluZyBldmVudHMgYWdhaW5cbiAgICAvLyBldmVudHMgc2VudCAqYWZ0ZXIqIHN0b3Agc2lnbmFsIG11c3QgYmUgcXVldWVkXG4gICAgLy8gaXQgc2VlbXMgbGlrZSB0aGlzIHNob3VsZCBiZSB0aGUgY29tbW9uIGJlaGF2aW9yIGZvciBhbGwgb2Ygb3VyIGNvbnN1bWVyc1xuICAgIC8vIHNvIHBlcmhhcHMgdGhpcyBzaG91bGQgYmUgdW5pZmllZCBzb21laG93IGZvciBhbGwgb2YgdGhlbVxuICAgIHRoaXMubWFpbGJveCA9IG5ldyBNYWlsYm94KHRoaXMuX3Byb2Nlc3MuYmluZCh0aGlzKSk7XG4gICAgdGhpcy5fcHJvY2Vzc2luZ1N0YXR1cyA9IFByb2Nlc3NpbmdTdGF0dXMuU3RvcHBlZDtcbiAgICB0aGlzLnN5c3RlbS5fdW5yZWdpc3Rlcih0aGlzKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIC8qKiBAaW50ZXJuYWwgKi9cbiAgX3NlbmQoZXZlbnQpIHtcbiAgICBpZiAodGhpcy5fcHJvY2Vzc2luZ1N0YXR1cyA9PT0gUHJvY2Vzc2luZ1N0YXR1cy5TdG9wcGVkKSB7XG4gICAgICAvLyBkbyBub3RoaW5nXG4gICAgICB7XG4gICAgICAgIGNvbnN0IGV2ZW50U3RyaW5nID0gSlNPTi5zdHJpbmdpZnkoZXZlbnQpO1xuICAgICAgICBjb25zb2xlLndhcm4oYEV2ZW50IFwiJHtldmVudC50eXBlfVwiIHdhcyBzZW50IHRvIHN0b3BwZWQgYWN0b3IgXCIke3RoaXMuaWR9ICgke3RoaXMuc2Vzc2lvbklkfSlcIi4gVGhpcyBhY3RvciBoYXMgYWxyZWFkeSByZWFjaGVkIGl0cyBmaW5hbCBzdGF0ZSwgYW5kIHdpbGwgbm90IHRyYW5zaXRpb24uXFxuRXZlbnQ6ICR7ZXZlbnRTdHJpbmd9YCk7XG4gICAgICB9XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHRoaXMubWFpbGJveC5lbnF1ZXVlKGV2ZW50KTtcbiAgfVxuXG4gIC8qKlxuICAgKiBTZW5kcyBhbiBldmVudCB0byB0aGUgcnVubmluZyBBY3RvciB0byB0cmlnZ2VyIGEgdHJhbnNpdGlvbi5cbiAgICpcbiAgICogQHBhcmFtIGV2ZW50IFRoZSBldmVudCB0byBzZW5kXG4gICAqL1xuICBzZW5kKGV2ZW50KSB7XG4gICAgaWYgKHR5cGVvZiBldmVudCA9PT0gJ3N0cmluZycpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgT25seSBldmVudCBvYmplY3RzIG1heSBiZSBzZW50IHRvIGFjdG9yczsgdXNlIC5zZW5kKHsgdHlwZTogXCIke2V2ZW50fVwiIH0pIGluc3RlYWRgKTtcbiAgICB9XG4gICAgdGhpcy5zeXN0ZW0uX3JlbGF5KHVuZGVmaW5lZCwgdGhpcywgZXZlbnQpO1xuICB9XG4gIGF0dGFjaERldlRvb2xzKCkge1xuICAgIGNvbnN0IHtcbiAgICAgIGRldlRvb2xzXG4gICAgfSA9IHRoaXMub3B0aW9ucztcbiAgICBpZiAoZGV2VG9vbHMpIHtcbiAgICAgIGNvbnN0IHJlc29sdmVkRGV2VG9vbHNBZGFwdGVyID0gdHlwZW9mIGRldlRvb2xzID09PSAnZnVuY3Rpb24nID8gZGV2VG9vbHMgOiBkZXZUb29sc0FkYXB0ZXI7XG4gICAgICByZXNvbHZlZERldlRvb2xzQWRhcHRlcih0aGlzKTtcbiAgICB9XG4gIH1cbiAgdG9KU09OKCkge1xuICAgIHJldHVybiB7XG4gICAgICB4c3RhdGUkJHR5cGU6ICQkQUNUT1JfVFlQRSxcbiAgICAgIGlkOiB0aGlzLmlkXG4gICAgfTtcbiAgfVxuXG4gIC8qKlxuICAgKiBPYnRhaW4gdGhlIGludGVybmFsIHN0YXRlIG9mIHRoZSBhY3Rvciwgd2hpY2ggY2FuIGJlIHBlcnNpc3RlZC5cbiAgICpcbiAgICogQHJlbWFya3NcbiAgICogVGhlIGludGVybmFsIHN0YXRlIGNhbiBiZSBwZXJzaXN0ZWQgZnJvbSBhbnkgYWN0b3IsIG5vdCBvbmx5IG1hY2hpbmVzLlxuICAgKlxuICAgKiBOb3RlIHRoYXQgdGhlIHBlcnNpc3RlZCBzdGF0ZSBpcyBub3QgdGhlIHNhbWUgYXMgdGhlIHNuYXBzaG90IGZyb21cbiAgICoge0BsaW5rIEFjdG9yLmdldFNuYXBzaG90fS4gUGVyc2lzdGVkIHN0YXRlIHJlcHJlc2VudHMgdGhlIGludGVybmFsIHN0YXRlIG9mXG4gICAqIHRoZSBhY3Rvciwgd2hpbGUgc25hcHNob3RzIHJlcHJlc2VudCB0aGUgYWN0b3IncyBsYXN0IGVtaXR0ZWQgdmFsdWUuXG4gICAqXG4gICAqIENhbiBiZSByZXN0b3JlZCB3aXRoIHtAbGluayBBY3Rvck9wdGlvbnMuc3RhdGV9XG4gICAqIEBzZWUgaHR0cHM6Ly9zdGF0ZWx5LmFpL2RvY3MvcGVyc2lzdGVuY2VcbiAgICovXG5cbiAgZ2V0UGVyc2lzdGVkU25hcHNob3Qob3B0aW9ucykge1xuICAgIHJldHVybiB0aGlzLmxvZ2ljLmdldFBlcnNpc3RlZFNuYXBzaG90KHRoaXMuX3NuYXBzaG90LCBvcHRpb25zKTtcbiAgfVxuICBbc3ltYm9sT2JzZXJ2YWJsZV0oKSB7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cblxuICAvKipcbiAgICogUmVhZCBhbiBhY3RvcuKAmXMgc25hcHNob3Qgc3luY2hyb25vdXNseS5cbiAgICpcbiAgICogQHJlbWFya3NcbiAgICogVGhlIHNuYXBzaG90IHJlcHJlc2VudCBhbiBhY3RvcidzIGxhc3QgZW1pdHRlZCB2YWx1ZS5cbiAgICpcbiAgICogV2hlbiBhbiBhY3RvciByZWNlaXZlcyBhbiBldmVudCwgaXRzIGludGVybmFsIHN0YXRlIG1heSBjaGFuZ2UuIEFuIGFjdG9yXG4gICAqIG1heSBlbWl0IGEgc25hcHNob3Qgd2hlbiBhIHN0YXRlIHRyYW5zaXRpb24gb2NjdXJzLlxuICAgKlxuICAgKiBOb3RlIHRoYXQgc29tZSBhY3RvcnMsIHN1Y2ggYXMgY2FsbGJhY2sgYWN0b3JzIGdlbmVyYXRlZCB3aXRoXG4gICAqIGBmcm9tQ2FsbGJhY2tgLCB3aWxsIG5vdCBlbWl0IHNuYXBzaG90cy5cbiAgICogQHNlZSB7QGxpbmsgQWN0b3Iuc3Vic2NyaWJlfSB0byBzdWJzY3JpYmUgdG8gYW4gYWN0b3LigJlzIHNuYXBzaG90IHZhbHVlcy5cbiAgICogQHNlZSB7QGxpbmsgQWN0b3IuZ2V0UGVyc2lzdGVkU25hcHNob3R9IHRvIHBlcnNpc3QgdGhlIGludGVybmFsIHN0YXRlIG9mIGFuIGFjdG9yICh3aGljaCBpcyBtb3JlIHRoYW4ganVzdCBhIHNuYXBzaG90KS5cbiAgICovXG4gIGdldFNuYXBzaG90KCkge1xuICAgIGlmICghdGhpcy5fc25hcHNob3QpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgU25hcHNob3QgY2FuJ3QgYmUgcmVhZCB3aGlsZSB0aGUgYWN0b3IgaW5pdGlhbGl6ZXMgaXRzZWxmYCk7XG4gICAgfVxuICAgIHJldHVybiB0aGlzLl9zbmFwc2hvdDtcbiAgfVxufVxuLyoqXG4gKiBDcmVhdGVzIGEgbmV3IGFjdG9yIGluc3RhbmNlIGZvciB0aGUgZ2l2ZW4gYWN0b3IgbG9naWMgd2l0aCB0aGUgcHJvdmlkZWRcbiAqIG9wdGlvbnMsIGlmIGFueS5cbiAqXG4gKiBAcmVtYXJrc1xuICogV2hlbiB5b3UgY3JlYXRlIGFuIGFjdG9yIGZyb20gYWN0b3IgbG9naWMgdmlhIGBjcmVhdGVBY3Rvcihsb2dpYylgLCB5b3VcbiAqIGltcGxpY2l0bHkgY3JlYXRlIGFuIGFjdG9yIHN5c3RlbSB3aGVyZSB0aGUgY3JlYXRlZCBhY3RvciBpcyB0aGUgcm9vdCBhY3Rvci5cbiAqIEFueSBhY3RvcnMgc3Bhd25lZCBmcm9tIHRoaXMgcm9vdCBhY3RvciBhbmQgaXRzIGRlc2NlbmRhbnRzIGFyZSBwYXJ0IG9mIHRoYXRcbiAqIGFjdG9yIHN5c3RlbS5cbiAqIEBleGFtcGxlXG4gKlxuICogYGBgdHNcbiAqIGltcG9ydCB7IGNyZWF0ZUFjdG9yIH0gZnJvbSAneHN0YXRlJztcbiAqIGltcG9ydCB7IHNvbWVBY3RvckxvZ2ljIH0gZnJvbSAnLi9zb21lQWN0b3JMb2dpYy50cyc7XG4gKlxuICogLy8gQ3JlYXRpbmcgdGhlIGFjdG9yLCB3aGljaCBpbXBsaWNpdGx5IGNyZWF0ZXMgYW4gYWN0b3Igc3lzdGVtIHdpdGggaXRzZWxmIGFzIHRoZSByb290IGFjdG9yXG4gKiBjb25zdCBhY3RvciA9IGNyZWF0ZUFjdG9yKHNvbWVBY3RvckxvZ2ljKTtcbiAqXG4gKiBhY3Rvci5zdWJzY3JpYmUoKHNuYXBzaG90KSA9PiB7XG4gKiAgIGNvbnNvbGUubG9nKHNuYXBzaG90KTtcbiAqIH0pO1xuICpcbiAqIC8vIEFjdG9ycyBtdXN0IGJlIHN0YXJ0ZWQgYnkgY2FsbGluZyBgYWN0b3Iuc3RhcnQoKWAsIHdoaWNoIHdpbGwgYWxzbyBzdGFydCB0aGUgYWN0b3Igc3lzdGVtLlxuICogYWN0b3Iuc3RhcnQoKTtcbiAqXG4gKiAvLyBBY3RvcnMgY2FuIHJlY2VpdmUgZXZlbnRzXG4gKiBhY3Rvci5zZW5kKHsgdHlwZTogJ3NvbWVFdmVudCcgfSk7XG4gKlxuICogLy8gWW91IGNhbiBzdG9wIHJvb3QgYWN0b3JzIGJ5IGNhbGxpbmcgYGFjdG9yLnN0b3AoKWAsIHdoaWNoIHdpbGwgYWxzbyBzdG9wIHRoZSBhY3RvciBzeXN0ZW0gYW5kIGFsbCBhY3RvcnMgaW4gdGhhdCBzeXN0ZW0uXG4gKiBhY3Rvci5zdG9wKCk7XG4gKiBgYGBcbiAqXG4gKiBAcGFyYW0gbG9naWMgLSBUaGUgYWN0b3IgbG9naWMgdG8gY3JlYXRlIGFuIGFjdG9yIGZyb20uIEZvciBhIHN0YXRlIG1hY2hpbmVcbiAqICAgYWN0b3IgbG9naWMgY3JlYXRvciwgc2VlIHtAbGluayBjcmVhdGVNYWNoaW5lfS4gT3RoZXIgYWN0b3IgbG9naWMgY3JlYXRvcnNcbiAqICAgaW5jbHVkZSB7QGxpbmsgZnJvbUNhbGxiYWNrfSwge0BsaW5rIGZyb21FdmVudE9ic2VydmFibGV9LFxuICogICB7QGxpbmsgZnJvbU9ic2VydmFibGV9LCB7QGxpbmsgZnJvbVByb21pc2V9LCBhbmQge0BsaW5rIGZyb21UcmFuc2l0aW9ufS5cbiAqIEBwYXJhbSBvcHRpb25zIC0gQWN0b3Igb3B0aW9uc1xuICovXG5mdW5jdGlvbiBjcmVhdGVBY3Rvcihsb2dpYywgLi4uW29wdGlvbnNdKSB7XG4gIHJldHVybiBuZXcgQWN0b3IobG9naWMsIG9wdGlvbnMpO1xufVxuXG4vKipcbiAqIENyZWF0ZXMgYSBuZXcgSW50ZXJwcmV0ZXIgaW5zdGFuY2UgZm9yIHRoZSBnaXZlbiBtYWNoaW5lIHdpdGggdGhlIHByb3ZpZGVkXG4gKiBvcHRpb25zLCBpZiBhbnkuXG4gKlxuICogQGRlcHJlY2F0ZWQgVXNlIGBjcmVhdGVBY3RvcmAgaW5zdGVhZFxuICogQGFsaWFzXG4gKi9cbmNvbnN0IGludGVycHJldCA9IGNyZWF0ZUFjdG9yO1xuXG4vKipcbiAqIEBkZXByZWNhdGVkIFVzZSBgQWN0b3JgIGluc3RlYWQuXG4gKiBAYWxpYXNcbiAqL1xuXG5mdW5jdGlvbiByZXNvbHZlQ2FuY2VsKF8sIHNuYXBzaG90LCBhY3Rpb25BcmdzLCBhY3Rpb25QYXJhbXMsIHtcbiAgc2VuZElkXG59KSB7XG4gIGNvbnN0IHJlc29sdmVkU2VuZElkID0gdHlwZW9mIHNlbmRJZCA9PT0gJ2Z1bmN0aW9uJyA/IHNlbmRJZChhY3Rpb25BcmdzLCBhY3Rpb25QYXJhbXMpIDogc2VuZElkO1xuICByZXR1cm4gW3NuYXBzaG90LCB7XG4gICAgc2VuZElkOiByZXNvbHZlZFNlbmRJZFxuICB9LCB1bmRlZmluZWRdO1xufVxuZnVuY3Rpb24gZXhlY3V0ZUNhbmNlbChhY3RvclNjb3BlLCBwYXJhbXMpIHtcbiAgYWN0b3JTY29wZS5kZWZlcigoKSA9PiB7XG4gICAgYWN0b3JTY29wZS5zeXN0ZW0uc2NoZWR1bGVyLmNhbmNlbChhY3RvclNjb3BlLnNlbGYsIHBhcmFtcy5zZW5kSWQpO1xuICB9KTtcbn1cbi8qKlxuICogQ2FuY2VscyBhIGRlbGF5ZWQgYHNlbmRUbyguLi4pYCBhY3Rpb24gdGhhdCBpcyB3YWl0aW5nIHRvIGJlIGV4ZWN1dGVkLiBUaGVcbiAqIGNhbmNlbGVkIGBzZW5kVG8oLi4uKWAgYWN0aW9uIHdpbGwgbm90IHNlbmQgaXRzIGV2ZW50IG9yIGV4ZWN1dGUsIHVubGVzcyB0aGVcbiAqIGBkZWxheWAgaGFzIGFscmVhZHkgZWxhcHNlZCBiZWZvcmUgYGNhbmNlbCguLi4pYCBpcyBjYWxsZWQuXG4gKlxuICogQGV4YW1wbGVcbiAqXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgY3JlYXRlTWFjaGluZSwgc2VuZFRvLCBjYW5jZWwgfSBmcm9tICd4c3RhdGUnO1xuICpcbiAqIGNvbnN0IG1hY2hpbmUgPSBjcmVhdGVNYWNoaW5lKHtcbiAqICAgLy8gLi4uXG4gKiAgIG9uOiB7XG4gKiAgICAgc2VuZEV2ZW50OiB7XG4gKiAgICAgICBhY3Rpb25zOiBzZW5kVG8oXG4gKiAgICAgICAgICdzb21lLWFjdG9yJyxcbiAqICAgICAgICAgeyB0eXBlOiAnc29tZUV2ZW50JyB9LFxuICogICAgICAgICB7XG4gKiAgICAgICAgICAgaWQ6ICdzb21lLWlkJyxcbiAqICAgICAgICAgICBkZWxheTogMTAwMFxuICogICAgICAgICB9XG4gKiAgICAgICApXG4gKiAgICAgfSxcbiAqICAgICBjYW5jZWxFdmVudDoge1xuICogICAgICAgYWN0aW9uczogY2FuY2VsKCdzb21lLWlkJylcbiAqICAgICB9XG4gKiAgIH1cbiAqIH0pO1xuICogYGBgXG4gKlxuICogQHBhcmFtIHNlbmRJZCBUaGUgYGlkYCBvZiB0aGUgYHNlbmRUbyguLi4pYCBhY3Rpb24gdG8gY2FuY2VsLlxuICovXG5mdW5jdGlvbiBjYW5jZWwoc2VuZElkKSB7XG4gIGZ1bmN0aW9uIGNhbmNlbChfYXJncywgX3BhcmFtcykge1xuICAgIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVGhpcyBpc24ndCBzdXBwb3NlZCB0byBiZSBjYWxsZWRgKTtcbiAgICB9XG4gIH1cbiAgY2FuY2VsLnR5cGUgPSAneHN0YXRlLmNhbmNlbCc7XG4gIGNhbmNlbC5zZW5kSWQgPSBzZW5kSWQ7XG4gIGNhbmNlbC5yZXNvbHZlID0gcmVzb2x2ZUNhbmNlbDtcbiAgY2FuY2VsLmV4ZWN1dGUgPSBleGVjdXRlQ2FuY2VsO1xuICByZXR1cm4gY2FuY2VsO1xufVxuXG5mdW5jdGlvbiByZXNvbHZlU3Bhd24oYWN0b3JTY29wZSwgc25hcHNob3QsIGFjdGlvbkFyZ3MsIF9hY3Rpb25QYXJhbXMsIHtcbiAgaWQsXG4gIHN5c3RlbUlkLFxuICBzcmMsXG4gIGlucHV0LFxuICBzeW5jU25hcHNob3Rcbn0pIHtcbiAgY29uc3QgbG9naWMgPSB0eXBlb2Ygc3JjID09PSAnc3RyaW5nJyA/IHJlc29sdmVSZWZlcmVuY2VkQWN0b3Ioc25hcHNob3QubWFjaGluZSwgc3JjKSA6IHNyYztcbiAgY29uc3QgcmVzb2x2ZWRJZCA9IHR5cGVvZiBpZCA9PT0gJ2Z1bmN0aW9uJyA/IGlkKGFjdGlvbkFyZ3MpIDogaWQ7XG4gIGxldCBhY3RvclJlZjtcbiAgbGV0IHJlc29sdmVkSW5wdXQgPSB1bmRlZmluZWQ7XG4gIGlmIChsb2dpYykge1xuICAgIHJlc29sdmVkSW5wdXQgPSB0eXBlb2YgaW5wdXQgPT09ICdmdW5jdGlvbicgPyBpbnB1dCh7XG4gICAgICBjb250ZXh0OiBzbmFwc2hvdC5jb250ZXh0LFxuICAgICAgZXZlbnQ6IGFjdGlvbkFyZ3MuZXZlbnQsXG4gICAgICBzZWxmOiBhY3RvclNjb3BlLnNlbGZcbiAgICB9KSA6IGlucHV0O1xuICAgIGFjdG9yUmVmID0gY3JlYXRlQWN0b3IobG9naWMsIHtcbiAgICAgIGlkOiByZXNvbHZlZElkLFxuICAgICAgc3JjLFxuICAgICAgcGFyZW50OiBhY3RvclNjb3BlLnNlbGYsXG4gICAgICBzeW5jU25hcHNob3QsXG4gICAgICBzeXN0ZW1JZCxcbiAgICAgIGlucHV0OiByZXNvbHZlZElucHV0XG4gICAgfSk7XG4gIH1cbiAgaWYgKCFhY3RvclJlZikge1xuICAgIGNvbnNvbGUud2FybihcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L3Jlc3RyaWN0LXRlbXBsYXRlLWV4cHJlc3Npb25zLEB0eXBlc2NyaXB0LWVzbGludC9uby1iYXNlLXRvLXN0cmluZ1xuICAgIGBBY3RvciB0eXBlICcke3NyY30nIG5vdCBmb3VuZCBpbiBtYWNoaW5lICcke2FjdG9yU2NvcGUuaWR9Jy5gKTtcbiAgfVxuICByZXR1cm4gW2Nsb25lTWFjaGluZVNuYXBzaG90KHNuYXBzaG90LCB7XG4gICAgY2hpbGRyZW46IHtcbiAgICAgIC4uLnNuYXBzaG90LmNoaWxkcmVuLFxuICAgICAgW3Jlc29sdmVkSWRdOiBhY3RvclJlZlxuICAgIH1cbiAgfSksIHtcbiAgICBpZCxcbiAgICBzeXN0ZW1JZCxcbiAgICBhY3RvclJlZixcbiAgICBzcmMsXG4gICAgaW5wdXQ6IHJlc29sdmVkSW5wdXRcbiAgfSwgdW5kZWZpbmVkXTtcbn1cbmZ1bmN0aW9uIGV4ZWN1dGVTcGF3bihhY3RvclNjb3BlLCB7XG4gIGFjdG9yUmVmXG59KSB7XG4gIGlmICghYWN0b3JSZWYpIHtcbiAgICByZXR1cm47XG4gIH1cbiAgYWN0b3JTY29wZS5kZWZlcigoKSA9PiB7XG4gICAgaWYgKGFjdG9yUmVmLl9wcm9jZXNzaW5nU3RhdHVzID09PSBQcm9jZXNzaW5nU3RhdHVzLlN0b3BwZWQpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgYWN0b3JSZWYuc3RhcnQoKTtcbiAgfSk7XG59XG5mdW5jdGlvbiBzcGF3bkNoaWxkKC4uLltzcmMsIHtcbiAgaWQsXG4gIHN5c3RlbUlkLFxuICBpbnB1dCxcbiAgc3luY1NuYXBzaG90ID0gZmFsc2Vcbn0gPSB7fV0pIHtcbiAgZnVuY3Rpb24gc3Bhd25DaGlsZChfYXJncywgX3BhcmFtcykge1xuICAgIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVGhpcyBpc24ndCBzdXBwb3NlZCB0byBiZSBjYWxsZWRgKTtcbiAgICB9XG4gIH1cbiAgc3Bhd25DaGlsZC50eXBlID0gJ3hzdGF0ZS5zcGF3bkNoaWxkJztcbiAgc3Bhd25DaGlsZC5pZCA9IGlkO1xuICBzcGF3bkNoaWxkLnN5c3RlbUlkID0gc3lzdGVtSWQ7XG4gIHNwYXduQ2hpbGQuc3JjID0gc3JjO1xuICBzcGF3bkNoaWxkLmlucHV0ID0gaW5wdXQ7XG4gIHNwYXduQ2hpbGQuc3luY1NuYXBzaG90ID0gc3luY1NuYXBzaG90O1xuICBzcGF3bkNoaWxkLnJlc29sdmUgPSByZXNvbHZlU3Bhd247XG4gIHNwYXduQ2hpbGQuZXhlY3V0ZSA9IGV4ZWN1dGVTcGF3bjtcbiAgcmV0dXJuIHNwYXduQ2hpbGQ7XG59XG5cbmZ1bmN0aW9uIHJlc29sdmVTdG9wKF8sIHNuYXBzaG90LCBhcmdzLCBhY3Rpb25QYXJhbXMsIHtcbiAgYWN0b3JSZWZcbn0pIHtcbiAgY29uc3QgYWN0b3JSZWZPclN0cmluZyA9IHR5cGVvZiBhY3RvclJlZiA9PT0gJ2Z1bmN0aW9uJyA/IGFjdG9yUmVmKGFyZ3MsIGFjdGlvblBhcmFtcykgOiBhY3RvclJlZjtcbiAgY29uc3QgcmVzb2x2ZWRBY3RvclJlZiA9IHR5cGVvZiBhY3RvclJlZk9yU3RyaW5nID09PSAnc3RyaW5nJyA/IHNuYXBzaG90LmNoaWxkcmVuW2FjdG9yUmVmT3JTdHJpbmddIDogYWN0b3JSZWZPclN0cmluZztcbiAgbGV0IGNoaWxkcmVuID0gc25hcHNob3QuY2hpbGRyZW47XG4gIGlmIChyZXNvbHZlZEFjdG9yUmVmKSB7XG4gICAgY2hpbGRyZW4gPSB7XG4gICAgICAuLi5jaGlsZHJlblxuICAgIH07XG4gICAgZGVsZXRlIGNoaWxkcmVuW3Jlc29sdmVkQWN0b3JSZWYuaWRdO1xuICB9XG4gIHJldHVybiBbY2xvbmVNYWNoaW5lU25hcHNob3Qoc25hcHNob3QsIHtcbiAgICBjaGlsZHJlblxuICB9KSwgcmVzb2x2ZWRBY3RvclJlZiwgdW5kZWZpbmVkXTtcbn1cbmZ1bmN0aW9uIGV4ZWN1dGVTdG9wKGFjdG9yU2NvcGUsIGFjdG9yUmVmKSB7XG4gIGlmICghYWN0b3JSZWYpIHtcbiAgICByZXR1cm47XG4gIH1cblxuICAvLyB3ZSBuZWVkIHRvIGVhZ2VybHkgdW5yZWdpc3RlciBpdCBoZXJlIHNvIGEgbmV3IGFjdG9yIHdpdGggdGhlIHNhbWUgc3lzdGVtSWQgY2FuIGJlIHJlZ2lzdGVyZWQgaW1tZWRpYXRlbHlcbiAgLy8gc2luY2Ugd2UgZGVmZXIgYWN0dWFsIHN0b3BwaW5nIG9mIHRoZSBhY3RvciBidXQgd2UgZG9uJ3QgZGVmZXIgYWN0b3IgY3JlYXRpb25zIChhbmQgd2UgY2FuJ3QgZG8gdGhhdClcbiAgLy8gdGhpcyBjb3VsZCB0aHJvdyBvbiBgc3lzdGVtSWRgIGNvbGxpc2lvbiwgZm9yIGV4YW1wbGUsIHdoZW4gZGVhbGluZyB3aXRoIHJlZW50ZXJpbmcgdHJhbnNpdGlvbnNcbiAgYWN0b3JTY29wZS5zeXN0ZW0uX3VucmVnaXN0ZXIoYWN0b3JSZWYpO1xuXG4gIC8vIHRoaXMgYWxsb3dzIHVzIHRvIHByZXZlbnQgYW4gYWN0b3IgZnJvbSBiZWluZyBzdGFydGVkIGlmIGl0IGdldHMgc3RvcHBlZCB3aXRoaW4gdGhlIHNhbWUgbWFjcm9zdGVwXG4gIC8vIHRoaXMgY2FuIGhhcHBlbiwgZm9yIGV4YW1wbGUsIHdoZW4gdGhlIGludm9raW5nIHN0YXRlIGlzIGJlaW5nIGV4aXRlZCBpbW1lZGlhdGVseSBieSBhbiBhbHdheXMgdHJhbnNpdGlvblxuICBpZiAoYWN0b3JSZWYuX3Byb2Nlc3NpbmdTdGF0dXMgIT09IFByb2Nlc3NpbmdTdGF0dXMuUnVubmluZykge1xuICAgIGFjdG9yU2NvcGUuc3RvcENoaWxkKGFjdG9yUmVmKTtcbiAgICByZXR1cm47XG4gIH1cbiAgLy8gc3RvcHBpbmcgYSBjaGlsZCBlbnF1ZXVlcyBhIHN0b3AgZXZlbnQgaW4gdGhlIGNoaWxkIGFjdG9yJ3MgbWFpbGJveFxuICAvLyB3ZSBuZWVkIGZvciBhbGwgb2YgdGhlIGFscmVhZHkgZW5xdWV1ZWQgZXZlbnRzIHRvIGJlIHByb2Nlc3NlZCBiZWZvcmUgd2Ugc3RvcCB0aGUgY2hpbGRcbiAgLy8gdGhlIHBhcmVudCBpdHNlbGYgbWlnaHQgd2FudCB0byBzZW5kIHNvbWUgZXZlbnRzIHRvIGEgY2hpbGQgKGZvciBleGFtcGxlIGZyb20gZXhpdCBhY3Rpb25zIG9uIHRoZSBpbnZva2luZyBzdGF0ZSlcbiAgLy8gYW5kIHdlIGRvbid0IHdhbnQgdG8gaWdub3JlIHRob3NlIGV2ZW50c1xuICBhY3RvclNjb3BlLmRlZmVyKCgpID0+IHtcbiAgICBhY3RvclNjb3BlLnN0b3BDaGlsZChhY3RvclJlZik7XG4gIH0pO1xufVxuLyoqXG4gKiBTdG9wcyBhIGNoaWxkIGFjdG9yLlxuICpcbiAqIEBwYXJhbSBhY3RvclJlZiBUaGUgYWN0b3IgdG8gc3RvcC5cbiAqL1xuZnVuY3Rpb24gc3RvcENoaWxkKGFjdG9yUmVmKSB7XG4gIGZ1bmN0aW9uIHN0b3AoX2FyZ3MsIF9wYXJhbXMpIHtcbiAgICB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRoaXMgaXNuJ3Qgc3VwcG9zZWQgdG8gYmUgY2FsbGVkYCk7XG4gICAgfVxuICB9XG4gIHN0b3AudHlwZSA9ICd4c3RhdGUuc3RvcENoaWxkJztcbiAgc3RvcC5hY3RvclJlZiA9IGFjdG9yUmVmO1xuICBzdG9wLnJlc29sdmUgPSByZXNvbHZlU3RvcDtcbiAgc3RvcC5leGVjdXRlID0gZXhlY3V0ZVN0b3A7XG4gIHJldHVybiBzdG9wO1xufVxuXG4vKipcbiAqIFN0b3BzIGEgY2hpbGQgYWN0b3IuXG4gKlxuICogQGRlcHJlY2F0ZWQgVXNlIGBzdG9wQ2hpbGQoLi4uKWAgaW5zdGVhZFxuICogQGFsaWFzXG4gKi9cbmNvbnN0IHN0b3AgPSBzdG9wQ2hpbGQ7XG5cbmZ1bmN0aW9uIGNoZWNrU3RhdGVJbihzbmFwc2hvdCwgXywge1xuICBzdGF0ZVZhbHVlXG59KSB7XG4gIGlmICh0eXBlb2Ygc3RhdGVWYWx1ZSA9PT0gJ3N0cmluZycgJiYgaXNTdGF0ZUlkKHN0YXRlVmFsdWUpKSB7XG4gICAgY29uc3QgdGFyZ2V0ID0gc25hcHNob3QubWFjaGluZS5nZXRTdGF0ZU5vZGVCeUlkKHN0YXRlVmFsdWUpO1xuICAgIHJldHVybiBzbmFwc2hvdC5fbm9kZXMuc29tZShzbiA9PiBzbiA9PT0gdGFyZ2V0KTtcbiAgfVxuICByZXR1cm4gc25hcHNob3QubWF0Y2hlcyhzdGF0ZVZhbHVlKTtcbn1cbmZ1bmN0aW9uIHN0YXRlSW4oc3RhdGVWYWx1ZSkge1xuICBmdW5jdGlvbiBzdGF0ZUluKCkge1xuICAgIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVGhpcyBpc24ndCBzdXBwb3NlZCB0byBiZSBjYWxsZWRgKTtcbiAgICB9XG4gIH1cbiAgc3RhdGVJbi5jaGVjayA9IGNoZWNrU3RhdGVJbjtcbiAgc3RhdGVJbi5zdGF0ZVZhbHVlID0gc3RhdGVWYWx1ZTtcbiAgcmV0dXJuIHN0YXRlSW47XG59XG5mdW5jdGlvbiBjaGVja05vdChzbmFwc2hvdCwge1xuICBjb250ZXh0LFxuICBldmVudFxufSwge1xuICBndWFyZHNcbn0pIHtcbiAgcmV0dXJuICFldmFsdWF0ZUd1YXJkKGd1YXJkc1swXSwgY29udGV4dCwgZXZlbnQsIHNuYXBzaG90KTtcbn1cblxuLyoqXG4gKiBIaWdoZXItb3JkZXIgZ3VhcmQgdGhhdCBldmFsdWF0ZXMgdG8gYHRydWVgIGlmIHRoZSBgZ3VhcmRgIHBhc3NlZCB0byBpdFxuICogZXZhbHVhdGVzIHRvIGBmYWxzZWAuXG4gKlxuICogQGNhdGVnb3J5IEd1YXJkc1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgc2V0dXAsIG5vdCB9IGZyb20gJ3hzdGF0ZSc7XG4gKlxuICogY29uc3QgbWFjaGluZSA9IHNldHVwKHtcbiAqICAgZ3VhcmRzOiB7XG4gKiAgICAgc29tZU5hbWVkR3VhcmQ6ICgpID0+IGZhbHNlXG4gKiAgIH1cbiAqIH0pLmNyZWF0ZU1hY2hpbmUoe1xuICogICBvbjoge1xuICogICAgIHNvbWVFdmVudDoge1xuICogICAgICAgZ3VhcmQ6IG5vdCgnc29tZU5hbWVkR3VhcmQnKSxcbiAqICAgICAgIGFjdGlvbnM6ICgpID0+IHtcbiAqICAgICAgICAgLy8gd2lsbCBiZSBleGVjdXRlZCBpZiBndWFyZCBpbiBgbm90KC4uLilgXG4gKiAgICAgICAgIC8vIGV2YWx1YXRlcyB0byBgZmFsc2VgXG4gKiAgICAgICB9XG4gKiAgICAgfVxuICogICB9XG4gKiB9KTtcbiAqIGBgYFxuICpcbiAqIEByZXR1cm5zIEEgZ3VhcmRcbiAqL1xuZnVuY3Rpb24gbm90KGd1YXJkKSB7XG4gIGZ1bmN0aW9uIG5vdChfYXJncywgX3BhcmFtcykge1xuICAgIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVGhpcyBpc24ndCBzdXBwb3NlZCB0byBiZSBjYWxsZWRgKTtcbiAgICB9XG4gIH1cbiAgbm90LmNoZWNrID0gY2hlY2tOb3Q7XG4gIG5vdC5ndWFyZHMgPSBbZ3VhcmRdO1xuICByZXR1cm4gbm90O1xufVxuZnVuY3Rpb24gY2hlY2tBbmQoc25hcHNob3QsIHtcbiAgY29udGV4dCxcbiAgZXZlbnRcbn0sIHtcbiAgZ3VhcmRzXG59KSB7XG4gIHJldHVybiBndWFyZHMuZXZlcnkoZ3VhcmQgPT4gZXZhbHVhdGVHdWFyZChndWFyZCwgY29udGV4dCwgZXZlbnQsIHNuYXBzaG90KSk7XG59XG5cbi8qKlxuICogSGlnaGVyLW9yZGVyIGd1YXJkIHRoYXQgZXZhbHVhdGVzIHRvIGB0cnVlYCBpZiBhbGwgYGd1YXJkc2AgcGFzc2VkIHRvIGl0XG4gKiBldmFsdWF0ZSB0byBgdHJ1ZWAuXG4gKlxuICogQGNhdGVnb3J5IEd1YXJkc1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgc2V0dXAsIGFuZCB9IGZyb20gJ3hzdGF0ZSc7XG4gKlxuICogY29uc3QgbWFjaGluZSA9IHNldHVwKHtcbiAqICAgZ3VhcmRzOiB7XG4gKiAgICAgc29tZU5hbWVkR3VhcmQ6ICgpID0+IHRydWVcbiAqICAgfVxuICogfSkuY3JlYXRlTWFjaGluZSh7XG4gKiAgIG9uOiB7XG4gKiAgICAgc29tZUV2ZW50OiB7XG4gKiAgICAgICBndWFyZDogYW5kKFsoeyBjb250ZXh0IH0pID0+IGNvbnRleHQudmFsdWUgPiAwLCAnc29tZU5hbWVkR3VhcmQnXSksXG4gKiAgICAgICBhY3Rpb25zOiAoKSA9PiB7XG4gKiAgICAgICAgIC8vIHdpbGwgYmUgZXhlY3V0ZWQgaWYgYWxsIGd1YXJkcyBpbiBgYW5kKC4uLilgXG4gKiAgICAgICAgIC8vIGV2YWx1YXRlIHRvIHRydWVcbiAqICAgICAgIH1cbiAqICAgICB9XG4gKiAgIH1cbiAqIH0pO1xuICogYGBgXG4gKlxuICogQHJldHVybnMgQSBndWFyZCBhY3Rpb24gb2JqZWN0XG4gKi9cbmZ1bmN0aW9uIGFuZChndWFyZHMpIHtcbiAgZnVuY3Rpb24gYW5kKF9hcmdzLCBfcGFyYW1zKSB7XG4gICAge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBUaGlzIGlzbid0IHN1cHBvc2VkIHRvIGJlIGNhbGxlZGApO1xuICAgIH1cbiAgfVxuICBhbmQuY2hlY2sgPSBjaGVja0FuZDtcbiAgYW5kLmd1YXJkcyA9IGd1YXJkcztcbiAgcmV0dXJuIGFuZDtcbn1cbmZ1bmN0aW9uIGNoZWNrT3Ioc25hcHNob3QsIHtcbiAgY29udGV4dCxcbiAgZXZlbnRcbn0sIHtcbiAgZ3VhcmRzXG59KSB7XG4gIHJldHVybiBndWFyZHMuc29tZShndWFyZCA9PiBldmFsdWF0ZUd1YXJkKGd1YXJkLCBjb250ZXh0LCBldmVudCwgc25hcHNob3QpKTtcbn1cblxuLyoqXG4gKiBIaWdoZXItb3JkZXIgZ3VhcmQgdGhhdCBldmFsdWF0ZXMgdG8gYHRydWVgIGlmIGFueSBvZiB0aGUgYGd1YXJkc2AgcGFzc2VkIHRvXG4gKiBpdCBldmFsdWF0ZSB0byBgdHJ1ZWAuXG4gKlxuICogQGNhdGVnb3J5IEd1YXJkc1xuICogQGV4YW1wbGVcbiAqXG4gKiBgYGB0c1xuICogaW1wb3J0IHsgc2V0dXAsIG9yIH0gZnJvbSAneHN0YXRlJztcbiAqXG4gKiBjb25zdCBtYWNoaW5lID0gc2V0dXAoe1xuICogICBndWFyZHM6IHtcbiAqICAgICBzb21lTmFtZWRHdWFyZDogKCkgPT4gdHJ1ZVxuICogICB9XG4gKiB9KS5jcmVhdGVNYWNoaW5lKHtcbiAqICAgb246IHtcbiAqICAgICBzb21lRXZlbnQ6IHtcbiAqICAgICAgIGd1YXJkOiBvcihbKHsgY29udGV4dCB9KSA9PiBjb250ZXh0LnZhbHVlID4gMCwgJ3NvbWVOYW1lZEd1YXJkJ10pLFxuICogICAgICAgYWN0aW9uczogKCkgPT4ge1xuICogICAgICAgICAvLyB3aWxsIGJlIGV4ZWN1dGVkIGlmIGFueSBvZiB0aGUgZ3VhcmRzIGluIGBvciguLi4pYFxuICogICAgICAgICAvLyBldmFsdWF0ZSB0byB0cnVlXG4gKiAgICAgICB9XG4gKiAgICAgfVxuICogICB9XG4gKiB9KTtcbiAqIGBgYFxuICpcbiAqIEByZXR1cm5zIEEgZ3VhcmQgYWN0aW9uIG9iamVjdFxuICovXG5mdW5jdGlvbiBvcihndWFyZHMpIHtcbiAgZnVuY3Rpb24gb3IoX2FyZ3MsIF9wYXJhbXMpIHtcbiAgICB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRoaXMgaXNuJ3Qgc3VwcG9zZWQgdG8gYmUgY2FsbGVkYCk7XG4gICAgfVxuICB9XG4gIG9yLmNoZWNrID0gY2hlY2tPcjtcbiAgb3IuZ3VhcmRzID0gZ3VhcmRzO1xuICByZXR1cm4gb3I7XG59XG5cbi8vIFRPRE86IHRocm93IG9uIGN5Y2xlcyAoZGVwdGggY2hlY2sgc2hvdWxkIGJlIGVub3VnaClcbmZ1bmN0aW9uIGV2YWx1YXRlR3VhcmQoZ3VhcmQsIGNvbnRleHQsIGV2ZW50LCBzbmFwc2hvdCkge1xuICBjb25zdCB7XG4gICAgbWFjaGluZVxuICB9ID0gc25hcHNob3Q7XG4gIGNvbnN0IGlzSW5saW5lID0gdHlwZW9mIGd1YXJkID09PSAnZnVuY3Rpb24nO1xuICBjb25zdCByZXNvbHZlZCA9IGlzSW5saW5lID8gZ3VhcmQgOiBtYWNoaW5lLmltcGxlbWVudGF0aW9ucy5ndWFyZHNbdHlwZW9mIGd1YXJkID09PSAnc3RyaW5nJyA/IGd1YXJkIDogZ3VhcmQudHlwZV07XG4gIGlmICghaXNJbmxpbmUgJiYgIXJlc29sdmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBHdWFyZCAnJHt0eXBlb2YgZ3VhcmQgPT09ICdzdHJpbmcnID8gZ3VhcmQgOiBndWFyZC50eXBlfScgaXMgbm90IGltcGxlbWVudGVkLicuYCk7XG4gIH1cbiAgaWYgKHR5cGVvZiByZXNvbHZlZCAhPT0gJ2Z1bmN0aW9uJykge1xuICAgIHJldHVybiBldmFsdWF0ZUd1YXJkKHJlc29sdmVkLCBjb250ZXh0LCBldmVudCwgc25hcHNob3QpO1xuICB9XG4gIGNvbnN0IGd1YXJkQXJncyA9IHtcbiAgICBjb250ZXh0LFxuICAgIGV2ZW50XG4gIH07XG4gIGNvbnN0IGd1YXJkUGFyYW1zID0gaXNJbmxpbmUgfHwgdHlwZW9mIGd1YXJkID09PSAnc3RyaW5nJyA/IHVuZGVmaW5lZCA6ICdwYXJhbXMnIGluIGd1YXJkID8gdHlwZW9mIGd1YXJkLnBhcmFtcyA9PT0gJ2Z1bmN0aW9uJyA/IGd1YXJkLnBhcmFtcyh7XG4gICAgY29udGV4dCxcbiAgICBldmVudFxuICB9KSA6IGd1YXJkLnBhcmFtcyA6IHVuZGVmaW5lZDtcbiAgaWYgKCEoJ2NoZWNrJyBpbiByZXNvbHZlZCkpIHtcbiAgICAvLyB0aGUgZXhpc3RpbmcgdHlwZSBvZiBgLmd1YXJkc2AgYXNzdW1lcyBub24tbnVsbGFibGUgYFRFeHByZXNzaW9uR3VhcmRgXG4gICAgLy8gaW5saW5lIGd1YXJkcyBleHBlY3QgYFRFeHByZXNzaW9uR3VhcmRgIHRvIGJlIHNldCB0byBgdW5kZWZpbmVkYFxuICAgIC8vIGl0J3MgZmluZSB0byBjYXN0IHRoaXMgaGVyZSwgb3VyIGxvZ2ljIG1ha2VzIHN1cmUgdGhhdCB3ZSBjYWxsIHRob3NlIDIgXCJ2YXJpYW50c1wiIGNvcnJlY3RseVxuICAgIHJldHVybiByZXNvbHZlZChndWFyZEFyZ3MsIGd1YXJkUGFyYW1zKTtcbiAgfVxuICBjb25zdCBidWlsdGluR3VhcmQgPSByZXNvbHZlZDtcbiAgcmV0dXJuIGJ1aWx0aW5HdWFyZC5jaGVjayhzbmFwc2hvdCwgZ3VhcmRBcmdzLCByZXNvbHZlZCAvLyB0aGlzIGhvbGRzIGFsbCBwYXJhbXNcbiAgKTtcbn1cblxuY29uc3QgaXNBdG9taWNTdGF0ZU5vZGUgPSBzdGF0ZU5vZGUgPT4gc3RhdGVOb2RlLnR5cGUgPT09ICdhdG9taWMnIHx8IHN0YXRlTm9kZS50eXBlID09PSAnZmluYWwnO1xuZnVuY3Rpb24gZ2V0Q2hpbGRyZW4oc3RhdGVOb2RlKSB7XG4gIHJldHVybiBPYmplY3QudmFsdWVzKHN0YXRlTm9kZS5zdGF0ZXMpLmZpbHRlcihzbiA9PiBzbi50eXBlICE9PSAnaGlzdG9yeScpO1xufVxuZnVuY3Rpb24gZ2V0UHJvcGVyQW5jZXN0b3JzKHN0YXRlTm9kZSwgdG9TdGF0ZU5vZGUpIHtcbiAgY29uc3QgYW5jZXN0b3JzID0gW107XG4gIGlmICh0b1N0YXRlTm9kZSA9PT0gc3RhdGVOb2RlKSB7XG4gICAgcmV0dXJuIGFuY2VzdG9ycztcbiAgfVxuXG4gIC8vIGFkZCBhbGwgYW5jZXN0b3JzXG4gIGxldCBtID0gc3RhdGVOb2RlLnBhcmVudDtcbiAgd2hpbGUgKG0gJiYgbSAhPT0gdG9TdGF0ZU5vZGUpIHtcbiAgICBhbmNlc3RvcnMucHVzaChtKTtcbiAgICBtID0gbS5wYXJlbnQ7XG4gIH1cbiAgcmV0dXJuIGFuY2VzdG9ycztcbn1cbmZ1bmN0aW9uIGdldEFsbFN0YXRlTm9kZXMoc3RhdGVOb2Rlcykge1xuICBjb25zdCBub2RlU2V0ID0gbmV3IFNldChzdGF0ZU5vZGVzKTtcbiAgY29uc3QgYWRqTGlzdCA9IGdldEFkakxpc3Qobm9kZVNldCk7XG5cbiAgLy8gYWRkIGRlc2NlbmRhbnRzXG4gIGZvciAoY29uc3QgcyBvZiBub2RlU2V0KSB7XG4gICAgLy8gaWYgcHJldmlvdXNseSBhY3RpdmUsIGFkZCBleGlzdGluZyBjaGlsZCBub2Rlc1xuICAgIGlmIChzLnR5cGUgPT09ICdjb21wb3VuZCcgJiYgKCFhZGpMaXN0LmdldChzKSB8fCAhYWRqTGlzdC5nZXQocykubGVuZ3RoKSkge1xuICAgICAgZ2V0SW5pdGlhbFN0YXRlTm9kZXNXaXRoVGhlaXJBbmNlc3RvcnMocykuZm9yRWFjaChzbiA9PiBub2RlU2V0LmFkZChzbikpO1xuICAgIH0gZWxzZSB7XG4gICAgICBpZiAocy50eXBlID09PSAncGFyYWxsZWwnKSB7XG4gICAgICAgIGZvciAoY29uc3QgY2hpbGQgb2YgZ2V0Q2hpbGRyZW4ocykpIHtcbiAgICAgICAgICBpZiAoY2hpbGQudHlwZSA9PT0gJ2hpc3RvcnknKSB7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKCFub2RlU2V0LmhhcyhjaGlsZCkpIHtcbiAgICAgICAgICAgIGNvbnN0IGluaXRpYWxTdGF0ZXMgPSBnZXRJbml0aWFsU3RhdGVOb2Rlc1dpdGhUaGVpckFuY2VzdG9ycyhjaGlsZCk7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGluaXRpYWxTdGF0ZU5vZGUgb2YgaW5pdGlhbFN0YXRlcykge1xuICAgICAgICAgICAgICBub2RlU2V0LmFkZChpbml0aWFsU3RhdGVOb2RlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBhZGQgYWxsIGFuY2VzdG9yc1xuICBmb3IgKGNvbnN0IHMgb2Ygbm9kZVNldCkge1xuICAgIGxldCBtID0gcy5wYXJlbnQ7XG4gICAgd2hpbGUgKG0pIHtcbiAgICAgIG5vZGVTZXQuYWRkKG0pO1xuICAgICAgbSA9IG0ucGFyZW50O1xuICAgIH1cbiAgfVxuICByZXR1cm4gbm9kZVNldDtcbn1cbmZ1bmN0aW9uIGdldFZhbHVlRnJvbUFkaihiYXNlTm9kZSwgYWRqTGlzdCkge1xuICBjb25zdCBjaGlsZFN0YXRlTm9kZXMgPSBhZGpMaXN0LmdldChiYXNlTm9kZSk7XG4gIGlmICghY2hpbGRTdGF0ZU5vZGVzKSB7XG4gICAgcmV0dXJuIHt9OyAvLyB0b2RvOiBmaXg/XG4gIH1cbiAgaWYgKGJhc2VOb2RlLnR5cGUgPT09ICdjb21wb3VuZCcpIHtcbiAgICBjb25zdCBjaGlsZFN0YXRlTm9kZSA9IGNoaWxkU3RhdGVOb2Rlc1swXTtcbiAgICBpZiAoY2hpbGRTdGF0ZU5vZGUpIHtcbiAgICAgIGlmIChpc0F0b21pY1N0YXRlTm9kZShjaGlsZFN0YXRlTm9kZSkpIHtcbiAgICAgICAgcmV0dXJuIGNoaWxkU3RhdGVOb2RlLmtleTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIHt9O1xuICAgIH1cbiAgfVxuICBjb25zdCBzdGF0ZVZhbHVlID0ge307XG4gIGZvciAoY29uc3QgY2hpbGRTdGF0ZU5vZGUgb2YgY2hpbGRTdGF0ZU5vZGVzKSB7XG4gICAgc3RhdGVWYWx1ZVtjaGlsZFN0YXRlTm9kZS5rZXldID0gZ2V0VmFsdWVGcm9tQWRqKGNoaWxkU3RhdGVOb2RlLCBhZGpMaXN0KTtcbiAgfVxuICByZXR1cm4gc3RhdGVWYWx1ZTtcbn1cbmZ1bmN0aW9uIGdldEFkakxpc3Qoc3RhdGVOb2Rlcykge1xuICBjb25zdCBhZGpMaXN0ID0gbmV3IE1hcCgpO1xuICBmb3IgKGNvbnN0IHMgb2Ygc3RhdGVOb2Rlcykge1xuICAgIGlmICghYWRqTGlzdC5oYXMocykpIHtcbiAgICAgIGFkakxpc3Quc2V0KHMsIFtdKTtcbiAgICB9XG4gICAgaWYgKHMucGFyZW50KSB7XG4gICAgICBpZiAoIWFkakxpc3QuaGFzKHMucGFyZW50KSkge1xuICAgICAgICBhZGpMaXN0LnNldChzLnBhcmVudCwgW10pO1xuICAgICAgfVxuICAgICAgYWRqTGlzdC5nZXQocy5wYXJlbnQpLnB1c2gocyk7XG4gICAgfVxuICB9XG4gIHJldHVybiBhZGpMaXN0O1xufVxuZnVuY3Rpb24gZ2V0U3RhdGVWYWx1ZShyb290Tm9kZSwgc3RhdGVOb2Rlcykge1xuICBjb25zdCBjb25maWcgPSBnZXRBbGxTdGF0ZU5vZGVzKHN0YXRlTm9kZXMpO1xuICByZXR1cm4gZ2V0VmFsdWVGcm9tQWRqKHJvb3ROb2RlLCBnZXRBZGpMaXN0KGNvbmZpZykpO1xufVxuZnVuY3Rpb24gaXNJbkZpbmFsU3RhdGUoc3RhdGVOb2RlU2V0LCBzdGF0ZU5vZGUpIHtcbiAgaWYgKHN0YXRlTm9kZS50eXBlID09PSAnY29tcG91bmQnKSB7XG4gICAgcmV0dXJuIGdldENoaWxkcmVuKHN0YXRlTm9kZSkuc29tZShzID0+IHMudHlwZSA9PT0gJ2ZpbmFsJyAmJiBzdGF0ZU5vZGVTZXQuaGFzKHMpKTtcbiAgfVxuICBpZiAoc3RhdGVOb2RlLnR5cGUgPT09ICdwYXJhbGxlbCcpIHtcbiAgICByZXR1cm4gZ2V0Q2hpbGRyZW4oc3RhdGVOb2RlKS5ldmVyeShzbiA9PiBpc0luRmluYWxTdGF0ZShzdGF0ZU5vZGVTZXQsIHNuKSk7XG4gIH1cbiAgcmV0dXJuIHN0YXRlTm9kZS50eXBlID09PSAnZmluYWwnO1xufVxuY29uc3QgaXNTdGF0ZUlkID0gc3RyID0+IHN0clswXSA9PT0gU1RBVEVfSURFTlRJRklFUjtcbmZ1bmN0aW9uIGdldENhbmRpZGF0ZXMoc3RhdGVOb2RlLCByZWNlaXZlZEV2ZW50VHlwZSkge1xuICBjb25zdCBjYW5kaWRhdGVzID0gc3RhdGVOb2RlLnRyYW5zaXRpb25zLmdldChyZWNlaXZlZEV2ZW50VHlwZSkgfHwgWy4uLnN0YXRlTm9kZS50cmFuc2l0aW9ucy5rZXlzKCldLmZpbHRlcihldmVudERlc2NyaXB0b3IgPT4ge1xuICAgIC8vIGNoZWNrIGlmIHRyYW5zaXRpb24gaXMgYSB3aWxkY2FyZCB0cmFuc2l0aW9uLFxuICAgIC8vIHdoaWNoIG1hdGNoZXMgYW55IG5vbi10cmFuc2llbnQgZXZlbnRzXG4gICAgaWYgKGV2ZW50RGVzY3JpcHRvciA9PT0gV0lMRENBUkQpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBpZiAoIWV2ZW50RGVzY3JpcHRvci5lbmRzV2l0aCgnLionKSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAoLy4qXFwqLisvLnRlc3QoZXZlbnREZXNjcmlwdG9yKSkge1xuICAgICAgY29uc29sZS53YXJuKGBXaWxkY2FyZHMgY2FuIG9ubHkgYmUgdGhlIGxhc3QgdG9rZW4gb2YgYW4gZXZlbnQgZGVzY3JpcHRvciAoZS5nLiwgXCJldmVudC4qXCIpIG9yIHRoZSBlbnRpcmUgZXZlbnQgZGVzY3JpcHRvciAoXCIqXCIpLiBDaGVjayB0aGUgXCIke2V2ZW50RGVzY3JpcHRvcn1cIiBldmVudC5gKTtcbiAgICB9XG4gICAgY29uc3QgcGFydGlhbEV2ZW50VG9rZW5zID0gZXZlbnREZXNjcmlwdG9yLnNwbGl0KCcuJyk7XG4gICAgY29uc3QgZXZlbnRUb2tlbnMgPSByZWNlaXZlZEV2ZW50VHlwZS5zcGxpdCgnLicpO1xuICAgIGZvciAobGV0IHRva2VuSW5kZXggPSAwOyB0b2tlbkluZGV4IDwgcGFydGlhbEV2ZW50VG9rZW5zLmxlbmd0aDsgdG9rZW5JbmRleCsrKSB7XG4gICAgICBjb25zdCBwYXJ0aWFsRXZlbnRUb2tlbiA9IHBhcnRpYWxFdmVudFRva2Vuc1t0b2tlbkluZGV4XTtcbiAgICAgIGNvbnN0IGV2ZW50VG9rZW4gPSBldmVudFRva2Vuc1t0b2tlbkluZGV4XTtcbiAgICAgIGlmIChwYXJ0aWFsRXZlbnRUb2tlbiA9PT0gJyonKSB7XG4gICAgICAgIGNvbnN0IGlzTGFzdFRva2VuID0gdG9rZW5JbmRleCA9PT0gcGFydGlhbEV2ZW50VG9rZW5zLmxlbmd0aCAtIDE7XG4gICAgICAgIGlmICghaXNMYXN0VG9rZW4pIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oYEluZml4IHdpbGRjYXJkcyBpbiB0cmFuc2l0aW9uIGV2ZW50cyBhcmUgbm90IGFsbG93ZWQuIENoZWNrIHRoZSBcIiR7ZXZlbnREZXNjcmlwdG9yfVwiIHRyYW5zaXRpb24uYCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGlzTGFzdFRva2VuO1xuICAgICAgfVxuICAgICAgaWYgKHBhcnRpYWxFdmVudFRva2VuICE9PSBldmVudFRva2VuKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG4gIH0pLnNvcnQoKGEsIGIpID0+IGIubGVuZ3RoIC0gYS5sZW5ndGgpLmZsYXRNYXAoa2V5ID0+IHN0YXRlTm9kZS50cmFuc2l0aW9ucy5nZXQoa2V5KSk7XG4gIHJldHVybiBjYW5kaWRhdGVzO1xufVxuXG4vKiogQWxsIGRlbGF5ZWQgdHJhbnNpdGlvbnMgZnJvbSB0aGUgY29uZmlnLiAqL1xuZnVuY3Rpb24gZ2V0RGVsYXllZFRyYW5zaXRpb25zKHN0YXRlTm9kZSkge1xuICBjb25zdCBhZnRlckNvbmZpZyA9IHN0YXRlTm9kZS5jb25maWcuYWZ0ZXI7XG4gIGlmICghYWZ0ZXJDb25maWcpIHtcbiAgICByZXR1cm4gW107XG4gIH1cbiAgY29uc3QgbXV0YXRlRW50cnlFeGl0ID0gZGVsYXkgPT4ge1xuICAgIGNvbnN0IGFmdGVyRXZlbnQgPSBjcmVhdGVBZnRlckV2ZW50KGRlbGF5LCBzdGF0ZU5vZGUuaWQpO1xuICAgIGNvbnN0IGV2ZW50VHlwZSA9IGFmdGVyRXZlbnQudHlwZTtcbiAgICBzdGF0ZU5vZGUuZW50cnkucHVzaChyYWlzZShhZnRlckV2ZW50LCB7XG4gICAgICBpZDogZXZlbnRUeXBlLFxuICAgICAgZGVsYXlcbiAgICB9KSk7XG4gICAgc3RhdGVOb2RlLmV4aXQucHVzaChjYW5jZWwoZXZlbnRUeXBlKSk7XG4gICAgcmV0dXJuIGV2ZW50VHlwZTtcbiAgfTtcbiAgY29uc3QgZGVsYXllZFRyYW5zaXRpb25zID0gT2JqZWN0LmtleXMoYWZ0ZXJDb25maWcpLmZsYXRNYXAoZGVsYXkgPT4ge1xuICAgIGNvbnN0IGNvbmZpZ1RyYW5zaXRpb24gPSBhZnRlckNvbmZpZ1tkZWxheV07XG4gICAgY29uc3QgcmVzb2x2ZWRUcmFuc2l0aW9uID0gdHlwZW9mIGNvbmZpZ1RyYW5zaXRpb24gPT09ICdzdHJpbmcnID8ge1xuICAgICAgdGFyZ2V0OiBjb25maWdUcmFuc2l0aW9uXG4gICAgfSA6IGNvbmZpZ1RyYW5zaXRpb247XG4gICAgY29uc3QgcmVzb2x2ZWREZWxheSA9IE51bWJlci5pc05hTigrZGVsYXkpID8gZGVsYXkgOiArZGVsYXk7XG4gICAgY29uc3QgZXZlbnRUeXBlID0gbXV0YXRlRW50cnlFeGl0KHJlc29sdmVkRGVsYXkpO1xuICAgIHJldHVybiB0b0FycmF5KHJlc29sdmVkVHJhbnNpdGlvbikubWFwKHRyYW5zaXRpb24gPT4gKHtcbiAgICAgIC4uLnRyYW5zaXRpb24sXG4gICAgICBldmVudDogZXZlbnRUeXBlLFxuICAgICAgZGVsYXk6IHJlc29sdmVkRGVsYXlcbiAgICB9KSk7XG4gIH0pO1xuICByZXR1cm4gZGVsYXllZFRyYW5zaXRpb25zLm1hcChkZWxheWVkVHJhbnNpdGlvbiA9PiB7XG4gICAgY29uc3Qge1xuICAgICAgZGVsYXlcbiAgICB9ID0gZGVsYXllZFRyYW5zaXRpb247XG4gICAgcmV0dXJuIHtcbiAgICAgIC4uLmZvcm1hdFRyYW5zaXRpb24oc3RhdGVOb2RlLCBkZWxheWVkVHJhbnNpdGlvbi5ldmVudCwgZGVsYXllZFRyYW5zaXRpb24pLFxuICAgICAgZGVsYXlcbiAgICB9O1xuICB9KTtcbn1cbmZ1bmN0aW9uIGZvcm1hdFRyYW5zaXRpb24oc3RhdGVOb2RlLCBkZXNjcmlwdG9yLCB0cmFuc2l0aW9uQ29uZmlnKSB7XG4gIGNvbnN0IG5vcm1hbGl6ZWRUYXJnZXQgPSBub3JtYWxpemVUYXJnZXQodHJhbnNpdGlvbkNvbmZpZy50YXJnZXQpO1xuICBjb25zdCByZWVudGVyID0gdHJhbnNpdGlvbkNvbmZpZy5yZWVudGVyID8/IGZhbHNlO1xuICBjb25zdCB0YXJnZXQgPSByZXNvbHZlVGFyZ2V0KHN0YXRlTm9kZSwgbm9ybWFsaXplZFRhcmdldCk7XG5cbiAgLy8gVE9ETzogc2hvdWxkIHRoaXMgYmUgcGFydCBvZiBhIGxpbnQgcnVsZSBpbnN0ZWFkP1xuICBpZiAodHJhbnNpdGlvbkNvbmZpZy5jb25kKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBTdGF0ZSBcIiR7c3RhdGVOb2RlLmlkfVwiIGhhcyBkZWNsYXJlZCBcXGBjb25kXFxgIGZvciBvbmUgb2YgaXRzIHRyYW5zaXRpb25zLiBUaGlzIHByb3BlcnR5IGhhcyBiZWVuIHJlbmFtZWQgdG8gXFxgZ3VhcmRcXGAuIFBsZWFzZSB1cGRhdGUgeW91ciBjb2RlLmApO1xuICB9XG4gIGNvbnN0IHRyYW5zaXRpb24gPSB7XG4gICAgLi4udHJhbnNpdGlvbkNvbmZpZyxcbiAgICBhY3Rpb25zOiB0b0FycmF5KHRyYW5zaXRpb25Db25maWcuYWN0aW9ucyksXG4gICAgZ3VhcmQ6IHRyYW5zaXRpb25Db25maWcuZ3VhcmQsXG4gICAgdGFyZ2V0LFxuICAgIHNvdXJjZTogc3RhdGVOb2RlLFxuICAgIHJlZW50ZXIsXG4gICAgZXZlbnRUeXBlOiBkZXNjcmlwdG9yLFxuICAgIHRvSlNPTjogKCkgPT4gKHtcbiAgICAgIC4uLnRyYW5zaXRpb24sXG4gICAgICBzb3VyY2U6IGAjJHtzdGF0ZU5vZGUuaWR9YCxcbiAgICAgIHRhcmdldDogdGFyZ2V0ID8gdGFyZ2V0Lm1hcCh0ID0+IGAjJHt0LmlkfWApIDogdW5kZWZpbmVkXG4gICAgfSlcbiAgfTtcbiAgcmV0dXJuIHRyYW5zaXRpb247XG59XG5mdW5jdGlvbiBmb3JtYXRUcmFuc2l0aW9ucyhzdGF0ZU5vZGUpIHtcbiAgY29uc3QgdHJhbnNpdGlvbnMgPSBuZXcgTWFwKCk7XG4gIGlmIChzdGF0ZU5vZGUuY29uZmlnLm9uKSB7XG4gICAgZm9yIChjb25zdCBkZXNjcmlwdG9yIG9mIE9iamVjdC5rZXlzKHN0YXRlTm9kZS5jb25maWcub24pKSB7XG4gICAgICBpZiAoZGVzY3JpcHRvciA9PT0gTlVMTF9FVkVOVCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ051bGwgZXZlbnRzIChcIlwiKSBjYW5ub3QgYmUgc3BlY2lmaWVkIGFzIGEgdHJhbnNpdGlvbiBrZXkuIFVzZSBgYWx3YXlzOiB7IC4uLiB9YCBpbnN0ZWFkLicpO1xuICAgICAgfVxuICAgICAgY29uc3QgdHJhbnNpdGlvbnNDb25maWcgPSBzdGF0ZU5vZGUuY29uZmlnLm9uW2Rlc2NyaXB0b3JdO1xuICAgICAgdHJhbnNpdGlvbnMuc2V0KGRlc2NyaXB0b3IsIHRvVHJhbnNpdGlvbkNvbmZpZ0FycmF5KHRyYW5zaXRpb25zQ29uZmlnKS5tYXAodCA9PiBmb3JtYXRUcmFuc2l0aW9uKHN0YXRlTm9kZSwgZGVzY3JpcHRvciwgdCkpKTtcbiAgICB9XG4gIH1cbiAgaWYgKHN0YXRlTm9kZS5jb25maWcub25Eb25lKSB7XG4gICAgY29uc3QgZGVzY3JpcHRvciA9IGB4c3RhdGUuZG9uZS5zdGF0ZS4ke3N0YXRlTm9kZS5pZH1gO1xuICAgIHRyYW5zaXRpb25zLnNldChkZXNjcmlwdG9yLCB0b1RyYW5zaXRpb25Db25maWdBcnJheShzdGF0ZU5vZGUuY29uZmlnLm9uRG9uZSkubWFwKHQgPT4gZm9ybWF0VHJhbnNpdGlvbihzdGF0ZU5vZGUsIGRlc2NyaXB0b3IsIHQpKSk7XG4gIH1cbiAgZm9yIChjb25zdCBpbnZva2VEZWYgb2Ygc3RhdGVOb2RlLmludm9rZSkge1xuICAgIGlmIChpbnZva2VEZWYub25Eb25lKSB7XG4gICAgICBjb25zdCBkZXNjcmlwdG9yID0gYHhzdGF0ZS5kb25lLmFjdG9yLiR7aW52b2tlRGVmLmlkfWA7XG4gICAgICB0cmFuc2l0aW9ucy5zZXQoZGVzY3JpcHRvciwgdG9UcmFuc2l0aW9uQ29uZmlnQXJyYXkoaW52b2tlRGVmLm9uRG9uZSkubWFwKHQgPT4gZm9ybWF0VHJhbnNpdGlvbihzdGF0ZU5vZGUsIGRlc2NyaXB0b3IsIHQpKSk7XG4gICAgfVxuICAgIGlmIChpbnZva2VEZWYub25FcnJvcikge1xuICAgICAgY29uc3QgZGVzY3JpcHRvciA9IGB4c3RhdGUuZXJyb3IuYWN0b3IuJHtpbnZva2VEZWYuaWR9YDtcbiAgICAgIHRyYW5zaXRpb25zLnNldChkZXNjcmlwdG9yLCB0b1RyYW5zaXRpb25Db25maWdBcnJheShpbnZva2VEZWYub25FcnJvcikubWFwKHQgPT4gZm9ybWF0VHJhbnNpdGlvbihzdGF0ZU5vZGUsIGRlc2NyaXB0b3IsIHQpKSk7XG4gICAgfVxuICAgIGlmIChpbnZva2VEZWYub25TbmFwc2hvdCkge1xuICAgICAgY29uc3QgZGVzY3JpcHRvciA9IGB4c3RhdGUuc25hcHNob3QuJHtpbnZva2VEZWYuaWR9YDtcbiAgICAgIHRyYW5zaXRpb25zLnNldChkZXNjcmlwdG9yLCB0b1RyYW5zaXRpb25Db25maWdBcnJheShpbnZva2VEZWYub25TbmFwc2hvdCkubWFwKHQgPT4gZm9ybWF0VHJhbnNpdGlvbihzdGF0ZU5vZGUsIGRlc2NyaXB0b3IsIHQpKSk7XG4gICAgfVxuICB9XG4gIGZvciAoY29uc3QgZGVsYXllZFRyYW5zaXRpb24gb2Ygc3RhdGVOb2RlLmFmdGVyKSB7XG4gICAgbGV0IGV4aXN0aW5nID0gdHJhbnNpdGlvbnMuZ2V0KGRlbGF5ZWRUcmFuc2l0aW9uLmV2ZW50VHlwZSk7XG4gICAgaWYgKCFleGlzdGluZykge1xuICAgICAgZXhpc3RpbmcgPSBbXTtcbiAgICAgIHRyYW5zaXRpb25zLnNldChkZWxheWVkVHJhbnNpdGlvbi5ldmVudFR5cGUsIGV4aXN0aW5nKTtcbiAgICB9XG4gICAgZXhpc3RpbmcucHVzaChkZWxheWVkVHJhbnNpdGlvbik7XG4gIH1cbiAgcmV0dXJuIHRyYW5zaXRpb25zO1xufVxuZnVuY3Rpb24gZm9ybWF0SW5pdGlhbFRyYW5zaXRpb24oc3RhdGVOb2RlLCBfdGFyZ2V0KSB7XG4gIGNvbnN0IHJlc29sdmVkVGFyZ2V0ID0gdHlwZW9mIF90YXJnZXQgPT09ICdzdHJpbmcnID8gc3RhdGVOb2RlLnN0YXRlc1tfdGFyZ2V0XSA6IF90YXJnZXQgPyBzdGF0ZU5vZGUuc3RhdGVzW190YXJnZXQudGFyZ2V0XSA6IHVuZGVmaW5lZDtcbiAgaWYgKCFyZXNvbHZlZFRhcmdldCAmJiBfdGFyZ2V0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvcmVzdHJpY3QtdGVtcGxhdGUtZXhwcmVzc2lvbnMsIEB0eXBlc2NyaXB0LWVzbGludC9uby1iYXNlLXRvLXN0cmluZ1xuICAgIGBJbml0aWFsIHN0YXRlIG5vZGUgXCIke190YXJnZXR9XCIgbm90IGZvdW5kIG9uIHBhcmVudCBzdGF0ZSBub2RlICMke3N0YXRlTm9kZS5pZH1gKTtcbiAgfVxuICBjb25zdCB0cmFuc2l0aW9uID0ge1xuICAgIHNvdXJjZTogc3RhdGVOb2RlLFxuICAgIGFjdGlvbnM6ICFfdGFyZ2V0IHx8IHR5cGVvZiBfdGFyZ2V0ID09PSAnc3RyaW5nJyA/IFtdIDogdG9BcnJheShfdGFyZ2V0LmFjdGlvbnMpLFxuICAgIGV2ZW50VHlwZTogbnVsbCxcbiAgICByZWVudGVyOiBmYWxzZSxcbiAgICB0YXJnZXQ6IHJlc29sdmVkVGFyZ2V0ID8gW3Jlc29sdmVkVGFyZ2V0XSA6IFtdLFxuICAgIHRvSlNPTjogKCkgPT4gKHtcbiAgICAgIC4uLnRyYW5zaXRpb24sXG4gICAgICBzb3VyY2U6IGAjJHtzdGF0ZU5vZGUuaWR9YCxcbiAgICAgIHRhcmdldDogcmVzb2x2ZWRUYXJnZXQgPyBbYCMke3Jlc29sdmVkVGFyZ2V0LmlkfWBdIDogW11cbiAgICB9KVxuICB9O1xuICByZXR1cm4gdHJhbnNpdGlvbjtcbn1cbmZ1bmN0aW9uIHJlc29sdmVUYXJnZXQoc3RhdGVOb2RlLCB0YXJnZXRzKSB7XG4gIGlmICh0YXJnZXRzID09PSB1bmRlZmluZWQpIHtcbiAgICAvLyBhbiB1bmRlZmluZWQgdGFyZ2V0IHNpZ25hbHMgdGhhdCB0aGUgc3RhdGUgbm9kZSBzaG91bGQgbm90IHRyYW5zaXRpb24gZnJvbSB0aGF0IHN0YXRlIHdoZW4gcmVjZWl2aW5nIHRoYXQgZXZlbnRcbiAgICByZXR1cm4gdW5kZWZpbmVkO1xuICB9XG4gIHJldHVybiB0YXJnZXRzLm1hcCh0YXJnZXQgPT4ge1xuICAgIGlmICh0eXBlb2YgdGFyZ2V0ICE9PSAnc3RyaW5nJykge1xuICAgICAgcmV0dXJuIHRhcmdldDtcbiAgICB9XG4gICAgaWYgKGlzU3RhdGVJZCh0YXJnZXQpKSB7XG4gICAgICByZXR1cm4gc3RhdGVOb2RlLm1hY2hpbmUuZ2V0U3RhdGVOb2RlQnlJZCh0YXJnZXQpO1xuICAgIH1cbiAgICBjb25zdCBpc0ludGVybmFsVGFyZ2V0ID0gdGFyZ2V0WzBdID09PSBTVEFURV9ERUxJTUlURVI7XG4gICAgLy8gSWYgaW50ZXJuYWwgdGFyZ2V0IGlzIGRlZmluZWQgb24gbWFjaGluZSxcbiAgICAvLyBkbyBub3QgaW5jbHVkZSBtYWNoaW5lIGtleSBvbiB0YXJnZXRcbiAgICBpZiAoaXNJbnRlcm5hbFRhcmdldCAmJiAhc3RhdGVOb2RlLnBhcmVudCkge1xuICAgICAgcmV0dXJuIGdldFN0YXRlTm9kZUJ5UGF0aChzdGF0ZU5vZGUsIHRhcmdldC5zbGljZSgxKSk7XG4gICAgfVxuICAgIGNvbnN0IHJlc29sdmVkVGFyZ2V0ID0gaXNJbnRlcm5hbFRhcmdldCA/IHN0YXRlTm9kZS5rZXkgKyB0YXJnZXQgOiB0YXJnZXQ7XG4gICAgaWYgKHN0YXRlTm9kZS5wYXJlbnQpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHRhcmdldFN0YXRlTm9kZSA9IGdldFN0YXRlTm9kZUJ5UGF0aChzdGF0ZU5vZGUucGFyZW50LCByZXNvbHZlZFRhcmdldCk7XG4gICAgICAgIHJldHVybiB0YXJnZXRTdGF0ZU5vZGU7XG4gICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbnZhbGlkIHRyYW5zaXRpb24gZGVmaW5pdGlvbiBmb3Igc3RhdGUgbm9kZSAnJHtzdGF0ZU5vZGUuaWR9JzpcXG4ke2Vyci5tZXNzYWdlfWApO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEludmFsaWQgdGFyZ2V0OiBcIiR7dGFyZ2V0fVwiIGlzIG5vdCBhIHZhbGlkIHRhcmdldCBmcm9tIHRoZSByb290IG5vZGUuIERpZCB5b3UgbWVhbiBcIi4ke3RhcmdldH1cIj9gKTtcbiAgICB9XG4gIH0pO1xufVxuZnVuY3Rpb24gcmVzb2x2ZUhpc3RvcnlEZWZhdWx0VHJhbnNpdGlvbihzdGF0ZU5vZGUpIHtcbiAgY29uc3Qgbm9ybWFsaXplZFRhcmdldCA9IG5vcm1hbGl6ZVRhcmdldChzdGF0ZU5vZGUuY29uZmlnLnRhcmdldCk7XG4gIGlmICghbm9ybWFsaXplZFRhcmdldCkge1xuICAgIHJldHVybiBzdGF0ZU5vZGUucGFyZW50LmluaXRpYWw7XG4gIH1cbiAgcmV0dXJuIHtcbiAgICB0YXJnZXQ6IG5vcm1hbGl6ZWRUYXJnZXQubWFwKHQgPT4gdHlwZW9mIHQgPT09ICdzdHJpbmcnID8gZ2V0U3RhdGVOb2RlQnlQYXRoKHN0YXRlTm9kZS5wYXJlbnQsIHQpIDogdClcbiAgfTtcbn1cbmZ1bmN0aW9uIGlzSGlzdG9yeU5vZGUoc3RhdGVOb2RlKSB7XG4gIHJldHVybiBzdGF0ZU5vZGUudHlwZSA9PT0gJ2hpc3RvcnknO1xufVxuZnVuY3Rpb24gZ2V0SW5pdGlhbFN0YXRlTm9kZXNXaXRoVGhlaXJBbmNlc3RvcnMoc3RhdGVOb2RlKSB7XG4gIGNvbnN0IHN0YXRlcyA9IGdldEluaXRpYWxTdGF0ZU5vZGVzKHN0YXRlTm9kZSk7XG4gIGZvciAoY29uc3QgaW5pdGlhbFN0YXRlIG9mIHN0YXRlcykge1xuICAgIGZvciAoY29uc3QgYW5jZXN0b3Igb2YgZ2V0UHJvcGVyQW5jZXN0b3JzKGluaXRpYWxTdGF0ZSwgc3RhdGVOb2RlKSkge1xuICAgICAgc3RhdGVzLmFkZChhbmNlc3Rvcik7XG4gICAgfVxuICB9XG4gIHJldHVybiBzdGF0ZXM7XG59XG5mdW5jdGlvbiBnZXRJbml0aWFsU3RhdGVOb2RlcyhzdGF0ZU5vZGUpIHtcbiAgY29uc3Qgc2V0ID0gbmV3IFNldCgpO1xuICBmdW5jdGlvbiBpdGVyKGRlc2NTdGF0ZU5vZGUpIHtcbiAgICBpZiAoc2V0LmhhcyhkZXNjU3RhdGVOb2RlKSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBzZXQuYWRkKGRlc2NTdGF0ZU5vZGUpO1xuICAgIGlmIChkZXNjU3RhdGVOb2RlLnR5cGUgPT09ICdjb21wb3VuZCcpIHtcbiAgICAgIGl0ZXIoZGVzY1N0YXRlTm9kZS5pbml0aWFsLnRhcmdldFswXSk7XG4gICAgfSBlbHNlIGlmIChkZXNjU3RhdGVOb2RlLnR5cGUgPT09ICdwYXJhbGxlbCcpIHtcbiAgICAgIGZvciAoY29uc3QgY2hpbGQgb2YgZ2V0Q2hpbGRyZW4oZGVzY1N0YXRlTm9kZSkpIHtcbiAgICAgICAgaXRlcihjaGlsZCk7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIGl0ZXIoc3RhdGVOb2RlKTtcbiAgcmV0dXJuIHNldDtcbn1cbi8qKiBSZXR1cm5zIHRoZSBjaGlsZCBzdGF0ZSBub2RlIGZyb20gaXRzIHJlbGF0aXZlIGBzdGF0ZUtleWAsIG9yIHRocm93cy4gKi9cbmZ1bmN0aW9uIGdldFN0YXRlTm9kZShzdGF0ZU5vZGUsIHN0YXRlS2V5KSB7XG4gIGlmIChpc1N0YXRlSWQoc3RhdGVLZXkpKSB7XG4gICAgcmV0dXJuIHN0YXRlTm9kZS5tYWNoaW5lLmdldFN0YXRlTm9kZUJ5SWQoc3RhdGVLZXkpO1xuICB9XG4gIGlmICghc3RhdGVOb2RlLnN0YXRlcykge1xuICAgIHRocm93IG5ldyBFcnJvcihgVW5hYmxlIHRvIHJldHJpZXZlIGNoaWxkIHN0YXRlICcke3N0YXRlS2V5fScgZnJvbSAnJHtzdGF0ZU5vZGUuaWR9Jzsgbm8gY2hpbGQgc3RhdGVzIGV4aXN0LmApO1xuICB9XG4gIGNvbnN0IHJlc3VsdCA9IHN0YXRlTm9kZS5zdGF0ZXNbc3RhdGVLZXldO1xuICBpZiAoIXJlc3VsdCkge1xuICAgIHRocm93IG5ldyBFcnJvcihgQ2hpbGQgc3RhdGUgJyR7c3RhdGVLZXl9JyBkb2VzIG5vdCBleGlzdCBvbiAnJHtzdGF0ZU5vZGUuaWR9J2ApO1xuICB9XG4gIHJldHVybiByZXN1bHQ7XG59XG5cbi8qKlxuICogUmV0dXJucyB0aGUgcmVsYXRpdmUgc3RhdGUgbm9kZSBmcm9tIHRoZSBnaXZlbiBgc3RhdGVQYXRoYCwgb3IgdGhyb3dzLlxuICpcbiAqIEBwYXJhbSBzdGF0ZVBhdGggVGhlIHN0cmluZyBvciBzdHJpbmcgYXJyYXkgcmVsYXRpdmUgcGF0aCB0byB0aGUgc3RhdGUgbm9kZS5cbiAqL1xuZnVuY3Rpb24gZ2V0U3RhdGVOb2RlQnlQYXRoKHN0YXRlTm9kZSwgc3RhdGVQYXRoKSB7XG4gIGlmICh0eXBlb2Ygc3RhdGVQYXRoID09PSAnc3RyaW5nJyAmJiBpc1N0YXRlSWQoc3RhdGVQYXRoKSkge1xuICAgIHRyeSB7XG4gICAgICByZXR1cm4gc3RhdGVOb2RlLm1hY2hpbmUuZ2V0U3RhdGVOb2RlQnlJZChzdGF0ZVBhdGgpO1xuICAgIH0gY2F0Y2gge1xuICAgICAgLy8gdHJ5IGluZGl2aWR1YWwgcGF0aHNcbiAgICAgIC8vIHRocm93IGU7XG4gICAgfVxuICB9XG4gIGNvbnN0IGFycmF5U3RhdGVQYXRoID0gdG9TdGF0ZVBhdGgoc3RhdGVQYXRoKS5zbGljZSgpO1xuICBsZXQgY3VycmVudFN0YXRlTm9kZSA9IHN0YXRlTm9kZTtcbiAgd2hpbGUgKGFycmF5U3RhdGVQYXRoLmxlbmd0aCkge1xuICAgIGNvbnN0IGtleSA9IGFycmF5U3RhdGVQYXRoLnNoaWZ0KCk7XG4gICAgaWYgKCFrZXkubGVuZ3RoKSB7XG4gICAgICBicmVhaztcbiAgICB9XG4gICAgY3VycmVudFN0YXRlTm9kZSA9IGdldFN0YXRlTm9kZShjdXJyZW50U3RhdGVOb2RlLCBrZXkpO1xuICB9XG4gIHJldHVybiBjdXJyZW50U3RhdGVOb2RlO1xufVxuXG4vKipcbiAqIFJldHVybnMgdGhlIHN0YXRlIG5vZGVzIHJlcHJlc2VudGVkIGJ5IHRoZSBjdXJyZW50IHN0YXRlIHZhbHVlLlxuICpcbiAqIEBwYXJhbSBzdGF0ZVZhbHVlIFRoZSBzdGF0ZSB2YWx1ZSBvciBTdGF0ZSBpbnN0YW5jZVxuICovXG5mdW5jdGlvbiBnZXRTdGF0ZU5vZGVzKHN0YXRlTm9kZSwgc3RhdGVWYWx1ZSkge1xuICBpZiAodHlwZW9mIHN0YXRlVmFsdWUgPT09ICdzdHJpbmcnKSB7XG4gICAgY29uc3QgY2hpbGRTdGF0ZU5vZGUgPSBzdGF0ZU5vZGUuc3RhdGVzW3N0YXRlVmFsdWVdO1xuICAgIGlmICghY2hpbGRTdGF0ZU5vZGUpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgU3RhdGUgJyR7c3RhdGVWYWx1ZX0nIGRvZXMgbm90IGV4aXN0IG9uICcke3N0YXRlTm9kZS5pZH0nYCk7XG4gICAgfVxuICAgIHJldHVybiBbc3RhdGVOb2RlLCBjaGlsZFN0YXRlTm9kZV07XG4gIH1cbiAgY29uc3QgY2hpbGRTdGF0ZUtleXMgPSBPYmplY3Qua2V5cyhzdGF0ZVZhbHVlKTtcbiAgY29uc3QgY2hpbGRTdGF0ZU5vZGVzID0gY2hpbGRTdGF0ZUtleXMubWFwKHN1YlN0YXRlS2V5ID0+IGdldFN0YXRlTm9kZShzdGF0ZU5vZGUsIHN1YlN0YXRlS2V5KSkuZmlsdGVyKEJvb2xlYW4pO1xuICByZXR1cm4gW3N0YXRlTm9kZS5tYWNoaW5lLnJvb3QsIHN0YXRlTm9kZV0uY29uY2F0KGNoaWxkU3RhdGVOb2RlcywgY2hpbGRTdGF0ZUtleXMucmVkdWNlKChhbGxTdWJTdGF0ZU5vZGVzLCBzdWJTdGF0ZUtleSkgPT4ge1xuICAgIGNvbnN0IHN1YlN0YXRlTm9kZSA9IGdldFN0YXRlTm9kZShzdGF0ZU5vZGUsIHN1YlN0YXRlS2V5KTtcbiAgICBpZiAoIXN1YlN0YXRlTm9kZSkge1xuICAgICAgcmV0dXJuIGFsbFN1YlN0YXRlTm9kZXM7XG4gICAgfVxuICAgIGNvbnN0IHN1YlN0YXRlTm9kZXMgPSBnZXRTdGF0ZU5vZGVzKHN1YlN0YXRlTm9kZSwgc3RhdGVWYWx1ZVtzdWJTdGF0ZUtleV0pO1xuICAgIHJldHVybiBhbGxTdWJTdGF0ZU5vZGVzLmNvbmNhdChzdWJTdGF0ZU5vZGVzKTtcbiAgfSwgW10pKTtcbn1cbmZ1bmN0aW9uIHRyYW5zaXRpb25BdG9taWNOb2RlKHN0YXRlTm9kZSwgc3RhdGVWYWx1ZSwgc25hcHNob3QsIGV2ZW50KSB7XG4gIGNvbnN0IGNoaWxkU3RhdGVOb2RlID0gZ2V0U3RhdGVOb2RlKHN0YXRlTm9kZSwgc3RhdGVWYWx1ZSk7XG4gIGNvbnN0IG5leHQgPSBjaGlsZFN0YXRlTm9kZS5uZXh0KHNuYXBzaG90LCBldmVudCk7XG4gIGlmICghbmV4dCB8fCAhbmV4dC5sZW5ndGgpIHtcbiAgICByZXR1cm4gc3RhdGVOb2RlLm5leHQoc25hcHNob3QsIGV2ZW50KTtcbiAgfVxuICByZXR1cm4gbmV4dDtcbn1cbmZ1bmN0aW9uIHRyYW5zaXRpb25Db21wb3VuZE5vZGUoc3RhdGVOb2RlLCBzdGF0ZVZhbHVlLCBzbmFwc2hvdCwgZXZlbnQpIHtcbiAgY29uc3Qgc3ViU3RhdGVLZXlzID0gT2JqZWN0LmtleXMoc3RhdGVWYWx1ZSk7XG4gIGNvbnN0IGNoaWxkU3RhdGVOb2RlID0gZ2V0U3RhdGVOb2RlKHN0YXRlTm9kZSwgc3ViU3RhdGVLZXlzWzBdKTtcbiAgY29uc3QgbmV4dCA9IHRyYW5zaXRpb25Ob2RlKGNoaWxkU3RhdGVOb2RlLCBzdGF0ZVZhbHVlW3N1YlN0YXRlS2V5c1swXV0sIHNuYXBzaG90LCBldmVudCk7XG4gIGlmICghbmV4dCB8fCAhbmV4dC5sZW5ndGgpIHtcbiAgICByZXR1cm4gc3RhdGVOb2RlLm5leHQoc25hcHNob3QsIGV2ZW50KTtcbiAgfVxuICByZXR1cm4gbmV4dDtcbn1cbmZ1bmN0aW9uIHRyYW5zaXRpb25QYXJhbGxlbE5vZGUoc3RhdGVOb2RlLCBzdGF0ZVZhbHVlLCBzbmFwc2hvdCwgZXZlbnQpIHtcbiAgY29uc3QgYWxsSW5uZXJUcmFuc2l0aW9ucyA9IFtdO1xuICBmb3IgKGNvbnN0IHN1YlN0YXRlS2V5IG9mIE9iamVjdC5rZXlzKHN0YXRlVmFsdWUpKSB7XG4gICAgY29uc3Qgc3ViU3RhdGVWYWx1ZSA9IHN0YXRlVmFsdWVbc3ViU3RhdGVLZXldO1xuICAgIGlmICghc3ViU3RhdGVWYWx1ZSkge1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGNvbnN0IHN1YlN0YXRlTm9kZSA9IGdldFN0YXRlTm9kZShzdGF0ZU5vZGUsIHN1YlN0YXRlS2V5KTtcbiAgICBjb25zdCBpbm5lclRyYW5zaXRpb25zID0gdHJhbnNpdGlvbk5vZGUoc3ViU3RhdGVOb2RlLCBzdWJTdGF0ZVZhbHVlLCBzbmFwc2hvdCwgZXZlbnQpO1xuICAgIGlmIChpbm5lclRyYW5zaXRpb25zKSB7XG4gICAgICBhbGxJbm5lclRyYW5zaXRpb25zLnB1c2goLi4uaW5uZXJUcmFuc2l0aW9ucyk7XG4gICAgfVxuICB9XG4gIGlmICghYWxsSW5uZXJUcmFuc2l0aW9ucy5sZW5ndGgpIHtcbiAgICByZXR1cm4gc3RhdGVOb2RlLm5leHQoc25hcHNob3QsIGV2ZW50KTtcbiAgfVxuICByZXR1cm4gYWxsSW5uZXJUcmFuc2l0aW9ucztcbn1cbmZ1bmN0aW9uIHRyYW5zaXRpb25Ob2RlKHN0YXRlTm9kZSwgc3RhdGVWYWx1ZSwgc25hcHNob3QsIGV2ZW50KSB7XG4gIC8vIGxlYWYgbm9kZVxuICBpZiAodHlwZW9mIHN0YXRlVmFsdWUgPT09ICdzdHJpbmcnKSB7XG4gICAgcmV0dXJuIHRyYW5zaXRpb25BdG9taWNOb2RlKHN0YXRlTm9kZSwgc3RhdGVWYWx1ZSwgc25hcHNob3QsIGV2ZW50KTtcbiAgfVxuXG4gIC8vIGNvbXBvdW5kIG5vZGVcbiAgaWYgKE9iamVjdC5rZXlzKHN0YXRlVmFsdWUpLmxlbmd0aCA9PT0gMSkge1xuICAgIHJldHVybiB0cmFuc2l0aW9uQ29tcG91bmROb2RlKHN0YXRlTm9kZSwgc3RhdGVWYWx1ZSwgc25hcHNob3QsIGV2ZW50KTtcbiAgfVxuXG4gIC8vIHBhcmFsbGVsIG5vZGVcbiAgcmV0dXJuIHRyYW5zaXRpb25QYXJhbGxlbE5vZGUoc3RhdGVOb2RlLCBzdGF0ZVZhbHVlLCBzbmFwc2hvdCwgZXZlbnQpO1xufVxuZnVuY3Rpb24gZ2V0SGlzdG9yeU5vZGVzKHN0YXRlTm9kZSkge1xuICByZXR1cm4gT2JqZWN0LmtleXMoc3RhdGVOb2RlLnN0YXRlcykubWFwKGtleSA9PiBzdGF0ZU5vZGUuc3RhdGVzW2tleV0pLmZpbHRlcihzbiA9PiBzbi50eXBlID09PSAnaGlzdG9yeScpO1xufVxuZnVuY3Rpb24gaXNEZXNjZW5kYW50KGNoaWxkU3RhdGVOb2RlLCBwYXJlbnRTdGF0ZU5vZGUpIHtcbiAgbGV0IG1hcmtlciA9IGNoaWxkU3RhdGVOb2RlO1xuICB3aGlsZSAobWFya2VyLnBhcmVudCAmJiBtYXJrZXIucGFyZW50ICE9PSBwYXJlbnRTdGF0ZU5vZGUpIHtcbiAgICBtYXJrZXIgPSBtYXJrZXIucGFyZW50O1xuICB9XG4gIHJldHVybiBtYXJrZXIucGFyZW50ID09PSBwYXJlbnRTdGF0ZU5vZGU7XG59XG5mdW5jdGlvbiBoYXNJbnRlcnNlY3Rpb24oczEsIHMyKSB7XG4gIGNvbnN0IHNldDEgPSBuZXcgU2V0KHMxKTtcbiAgY29uc3Qgc2V0MiA9IG5ldyBTZXQoczIpO1xuICBmb3IgKGNvbnN0IGl0ZW0gb2Ygc2V0MSkge1xuICAgIGlmIChzZXQyLmhhcyhpdGVtKSkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICB9XG4gIGZvciAoY29uc3QgaXRlbSBvZiBzZXQyKSB7XG4gICAgaWYgKHNldDEuaGFzKGl0ZW0pKSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIGZhbHNlO1xufVxuZnVuY3Rpb24gcmVtb3ZlQ29uZmxpY3RpbmdUcmFuc2l0aW9ucyhlbmFibGVkVHJhbnNpdGlvbnMsIHN0YXRlTm9kZVNldCwgaGlzdG9yeVZhbHVlKSB7XG4gIGNvbnN0IGZpbHRlcmVkVHJhbnNpdGlvbnMgPSBuZXcgU2V0KCk7XG4gIGZvciAoY29uc3QgdDEgb2YgZW5hYmxlZFRyYW5zaXRpb25zKSB7XG4gICAgbGV0IHQxUHJlZW1wdGVkID0gZmFsc2U7XG4gICAgY29uc3QgdHJhbnNpdGlvbnNUb1JlbW92ZSA9IG5ldyBTZXQoKTtcbiAgICBmb3IgKGNvbnN0IHQyIG9mIGZpbHRlcmVkVHJhbnNpdGlvbnMpIHtcbiAgICAgIGlmIChoYXNJbnRlcnNlY3Rpb24oY29tcHV0ZUV4aXRTZXQoW3QxXSwgc3RhdGVOb2RlU2V0LCBoaXN0b3J5VmFsdWUpLCBjb21wdXRlRXhpdFNldChbdDJdLCBzdGF0ZU5vZGVTZXQsIGhpc3RvcnlWYWx1ZSkpKSB7XG4gICAgICAgIGlmIChpc0Rlc2NlbmRhbnQodDEuc291cmNlLCB0Mi5zb3VyY2UpKSB7XG4gICAgICAgICAgdHJhbnNpdGlvbnNUb1JlbW92ZS5hZGQodDIpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHQxUHJlZW1wdGVkID0gdHJ1ZTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgICBpZiAoIXQxUHJlZW1wdGVkKSB7XG4gICAgICBmb3IgKGNvbnN0IHQzIG9mIHRyYW5zaXRpb25zVG9SZW1vdmUpIHtcbiAgICAgICAgZmlsdGVyZWRUcmFuc2l0aW9ucy5kZWxldGUodDMpO1xuICAgICAgfVxuICAgICAgZmlsdGVyZWRUcmFuc2l0aW9ucy5hZGQodDEpO1xuICAgIH1cbiAgfVxuICByZXR1cm4gQXJyYXkuZnJvbShmaWx0ZXJlZFRyYW5zaXRpb25zKTtcbn1cbmZ1bmN0aW9uIGZpbmRMZWFzdENvbW1vbkFuY2VzdG9yKHN0YXRlTm9kZXMpIHtcbiAgY29uc3QgW2hlYWQsIC4uLnRhaWxdID0gc3RhdGVOb2RlcztcbiAgZm9yIChjb25zdCBhbmNlc3RvciBvZiBnZXRQcm9wZXJBbmNlc3RvcnMoaGVhZCwgdW5kZWZpbmVkKSkge1xuICAgIGlmICh0YWlsLmV2ZXJ5KHNuID0+IGlzRGVzY2VuZGFudChzbiwgYW5jZXN0b3IpKSkge1xuICAgICAgcmV0dXJuIGFuY2VzdG9yO1xuICAgIH1cbiAgfVxufVxuZnVuY3Rpb24gZ2V0RWZmZWN0aXZlVGFyZ2V0U3RhdGVzKHRyYW5zaXRpb24sIGhpc3RvcnlWYWx1ZSkge1xuICBpZiAoIXRyYW5zaXRpb24udGFyZ2V0KSB7XG4gICAgcmV0dXJuIFtdO1xuICB9XG4gIGNvbnN0IHRhcmdldHMgPSBuZXcgU2V0KCk7XG4gIGZvciAoY29uc3QgdGFyZ2V0Tm9kZSBvZiB0cmFuc2l0aW9uLnRhcmdldCkge1xuICAgIGlmIChpc0hpc3RvcnlOb2RlKHRhcmdldE5vZGUpKSB7XG4gICAgICBpZiAoaGlzdG9yeVZhbHVlW3RhcmdldE5vZGUuaWRdKSB7XG4gICAgICAgIGZvciAoY29uc3Qgbm9kZSBvZiBoaXN0b3J5VmFsdWVbdGFyZ2V0Tm9kZS5pZF0pIHtcbiAgICAgICAgICB0YXJnZXRzLmFkZChub2RlKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZm9yIChjb25zdCBub2RlIG9mIGdldEVmZmVjdGl2ZVRhcmdldFN0YXRlcyhyZXNvbHZlSGlzdG9yeURlZmF1bHRUcmFuc2l0aW9uKHRhcmdldE5vZGUpLCBoaXN0b3J5VmFsdWUpKSB7XG4gICAgICAgICAgdGFyZ2V0cy5hZGQobm9kZSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgdGFyZ2V0cy5hZGQodGFyZ2V0Tm9kZSk7XG4gICAgfVxuICB9XG4gIHJldHVybiBbLi4udGFyZ2V0c107XG59XG5mdW5jdGlvbiBnZXRUcmFuc2l0aW9uRG9tYWluKHRyYW5zaXRpb24sIGhpc3RvcnlWYWx1ZSkge1xuICBjb25zdCB0YXJnZXRTdGF0ZXMgPSBnZXRFZmZlY3RpdmVUYXJnZXRTdGF0ZXModHJhbnNpdGlvbiwgaGlzdG9yeVZhbHVlKTtcbiAgaWYgKCF0YXJnZXRTdGF0ZXMpIHtcbiAgICByZXR1cm47XG4gIH1cbiAgaWYgKCF0cmFuc2l0aW9uLnJlZW50ZXIgJiYgdGFyZ2V0U3RhdGVzLmV2ZXJ5KHRhcmdldCA9PiB0YXJnZXQgPT09IHRyYW5zaXRpb24uc291cmNlIHx8IGlzRGVzY2VuZGFudCh0YXJnZXQsIHRyYW5zaXRpb24uc291cmNlKSkpIHtcbiAgICByZXR1cm4gdHJhbnNpdGlvbi5zb3VyY2U7XG4gIH1cbiAgY29uc3QgbGNhID0gZmluZExlYXN0Q29tbW9uQW5jZXN0b3IodGFyZ2V0U3RhdGVzLmNvbmNhdCh0cmFuc2l0aW9uLnNvdXJjZSkpO1xuICBpZiAobGNhKSB7XG4gICAgcmV0dXJuIGxjYTtcbiAgfVxuXG4gIC8vIGF0IHRoaXMgcG9pbnQgd2Uga25vdyB0aGF0IGl0J3MgYSByb290IHRyYW5zaXRpb24gc2luY2UgTENBIGNvdWxkbid0IGJlIGZvdW5kXG4gIGlmICh0cmFuc2l0aW9uLnJlZW50ZXIpIHtcbiAgICByZXR1cm47XG4gIH1cbiAgcmV0dXJuIHRyYW5zaXRpb24uc291cmNlLm1hY2hpbmUucm9vdDtcbn1cbmZ1bmN0aW9uIGNvbXB1dGVFeGl0U2V0KHRyYW5zaXRpb25zLCBzdGF0ZU5vZGVTZXQsIGhpc3RvcnlWYWx1ZSkge1xuICBjb25zdCBzdGF0ZXNUb0V4aXQgPSBuZXcgU2V0KCk7XG4gIGZvciAoY29uc3QgdCBvZiB0cmFuc2l0aW9ucykge1xuICAgIGlmICh0LnRhcmdldD8ubGVuZ3RoKSB7XG4gICAgICBjb25zdCBkb21haW4gPSBnZXRUcmFuc2l0aW9uRG9tYWluKHQsIGhpc3RvcnlWYWx1ZSk7XG4gICAgICBpZiAodC5yZWVudGVyICYmIHQuc291cmNlID09PSBkb21haW4pIHtcbiAgICAgICAgc3RhdGVzVG9FeGl0LmFkZChkb21haW4pO1xuICAgICAgfVxuICAgICAgZm9yIChjb25zdCBzdGF0ZU5vZGUgb2Ygc3RhdGVOb2RlU2V0KSB7XG4gICAgICAgIGlmIChpc0Rlc2NlbmRhbnQoc3RhdGVOb2RlLCBkb21haW4pKSB7XG4gICAgICAgICAgc3RhdGVzVG9FeGl0LmFkZChzdGF0ZU5vZGUpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBbLi4uc3RhdGVzVG9FeGl0XTtcbn1cbmZ1bmN0aW9uIGFyZVN0YXRlTm9kZUNvbGxlY3Rpb25zRXF1YWwocHJldlN0YXRlTm9kZXMsIG5leHRTdGF0ZU5vZGVTZXQpIHtcbiAgaWYgKHByZXZTdGF0ZU5vZGVzLmxlbmd0aCAhPT0gbmV4dFN0YXRlTm9kZVNldC5zaXplKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGZvciAoY29uc3Qgbm9kZSBvZiBwcmV2U3RhdGVOb2Rlcykge1xuICAgIGlmICghbmV4dFN0YXRlTm9kZVNldC5oYXMobm9kZSkpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHRydWU7XG59XG5cbi8qKiBodHRwczovL3d3dy53My5vcmcvVFIvc2N4bWwvI21pY3Jvc3RlcFByb2NlZHVyZSAqL1xuZnVuY3Rpb24gbWljcm9zdGVwKHRyYW5zaXRpb25zLCBjdXJyZW50U25hcHNob3QsIGFjdG9yU2NvcGUsIGV2ZW50LCBpc0luaXRpYWwsIGludGVybmFsUXVldWUpIHtcbiAgaWYgKCF0cmFuc2l0aW9ucy5sZW5ndGgpIHtcbiAgICByZXR1cm4gY3VycmVudFNuYXBzaG90O1xuICB9XG4gIGNvbnN0IG11dFN0YXRlTm9kZVNldCA9IG5ldyBTZXQoY3VycmVudFNuYXBzaG90Ll9ub2Rlcyk7XG4gIGxldCBoaXN0b3J5VmFsdWUgPSBjdXJyZW50U25hcHNob3QuaGlzdG9yeVZhbHVlO1xuICBjb25zdCBmaWx0ZXJlZFRyYW5zaXRpb25zID0gcmVtb3ZlQ29uZmxpY3RpbmdUcmFuc2l0aW9ucyh0cmFuc2l0aW9ucywgbXV0U3RhdGVOb2RlU2V0LCBoaXN0b3J5VmFsdWUpO1xuICBsZXQgbmV4dFN0YXRlID0gY3VycmVudFNuYXBzaG90O1xuXG4gIC8vIEV4aXQgc3RhdGVzXG4gIGlmICghaXNJbml0aWFsKSB7XG4gICAgW25leHRTdGF0ZSwgaGlzdG9yeVZhbHVlXSA9IGV4aXRTdGF0ZXMobmV4dFN0YXRlLCBldmVudCwgYWN0b3JTY29wZSwgZmlsdGVyZWRUcmFuc2l0aW9ucywgbXV0U3RhdGVOb2RlU2V0LCBoaXN0b3J5VmFsdWUsIGludGVybmFsUXVldWUsIGFjdG9yU2NvcGUuYWN0aW9uRXhlY3V0b3IpO1xuICB9XG5cbiAgLy8gRXhlY3V0ZSB0cmFuc2l0aW9uIGNvbnRlbnRcbiAgbmV4dFN0YXRlID0gcmVzb2x2ZUFjdGlvbnNBbmRDb250ZXh0KG5leHRTdGF0ZSwgZXZlbnQsIGFjdG9yU2NvcGUsIGZpbHRlcmVkVHJhbnNpdGlvbnMuZmxhdE1hcCh0ID0+IHQuYWN0aW9ucyksIGludGVybmFsUXVldWUsIHVuZGVmaW5lZCk7XG5cbiAgLy8gRW50ZXIgc3RhdGVzXG4gIG5leHRTdGF0ZSA9IGVudGVyU3RhdGVzKG5leHRTdGF0ZSwgZXZlbnQsIGFjdG9yU2NvcGUsIGZpbHRlcmVkVHJhbnNpdGlvbnMsIG11dFN0YXRlTm9kZVNldCwgaW50ZXJuYWxRdWV1ZSwgaGlzdG9yeVZhbHVlLCBpc0luaXRpYWwpO1xuICBjb25zdCBuZXh0U3RhdGVOb2RlcyA9IFsuLi5tdXRTdGF0ZU5vZGVTZXRdO1xuICBpZiAobmV4dFN0YXRlLnN0YXR1cyA9PT0gJ2RvbmUnKSB7XG4gICAgbmV4dFN0YXRlID0gcmVzb2x2ZUFjdGlvbnNBbmRDb250ZXh0KG5leHRTdGF0ZSwgZXZlbnQsIGFjdG9yU2NvcGUsIG5leHRTdGF0ZU5vZGVzLnNvcnQoKGEsIGIpID0+IGIub3JkZXIgLSBhLm9yZGVyKS5mbGF0TWFwKHN0YXRlID0+IHN0YXRlLmV4aXQpLCBpbnRlcm5hbFF1ZXVlLCB1bmRlZmluZWQpO1xuICB9XG5cbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVzZWxlc3MtY2F0Y2hcbiAgdHJ5IHtcbiAgICBpZiAoaGlzdG9yeVZhbHVlID09PSBjdXJyZW50U25hcHNob3QuaGlzdG9yeVZhbHVlICYmIGFyZVN0YXRlTm9kZUNvbGxlY3Rpb25zRXF1YWwoY3VycmVudFNuYXBzaG90Ll9ub2RlcywgbXV0U3RhdGVOb2RlU2V0KSkge1xuICAgICAgcmV0dXJuIG5leHRTdGF0ZTtcbiAgICB9XG4gICAgcmV0dXJuIGNsb25lTWFjaGluZVNuYXBzaG90KG5leHRTdGF0ZSwge1xuICAgICAgX25vZGVzOiBuZXh0U3RhdGVOb2RlcyxcbiAgICAgIGhpc3RvcnlWYWx1ZVxuICAgIH0pO1xuICB9IGNhdGNoIChlKSB7XG4gICAgLy8gVE9ETzogUmVmYWN0b3IgdGhpcyBvbmNlIHByb3BlciBlcnJvciBoYW5kbGluZyBpcyBpbXBsZW1lbnRlZC5cbiAgICAvLyBTZWUgaHR0cHM6Ly9naXRodWIuY29tL3N0YXRlbHlhaS9yZmNzL3B1bGwvNFxuICAgIHRocm93IGU7XG4gIH1cbn1cbmZ1bmN0aW9uIGdldE1hY2hpbmVPdXRwdXQoc25hcHNob3QsIGV2ZW50LCBhY3RvclNjb3BlLCByb290Tm9kZSwgcm9vdENvbXBsZXRpb25Ob2RlKSB7XG4gIGlmIChyb290Tm9kZS5vdXRwdXQgPT09IHVuZGVmaW5lZCkge1xuICAgIHJldHVybjtcbiAgfVxuICBjb25zdCBkb25lU3RhdGVFdmVudCA9IGNyZWF0ZURvbmVTdGF0ZUV2ZW50KHJvb3RDb21wbGV0aW9uTm9kZS5pZCwgcm9vdENvbXBsZXRpb25Ob2RlLm91dHB1dCAhPT0gdW5kZWZpbmVkICYmIHJvb3RDb21wbGV0aW9uTm9kZS5wYXJlbnQgPyByZXNvbHZlT3V0cHV0KHJvb3RDb21wbGV0aW9uTm9kZS5vdXRwdXQsIHNuYXBzaG90LmNvbnRleHQsIGV2ZW50LCBhY3RvclNjb3BlLnNlbGYpIDogdW5kZWZpbmVkKTtcbiAgcmV0dXJuIHJlc29sdmVPdXRwdXQocm9vdE5vZGUub3V0cHV0LCBzbmFwc2hvdC5jb250ZXh0LCBkb25lU3RhdGVFdmVudCwgYWN0b3JTY29wZS5zZWxmKTtcbn1cbmZ1bmN0aW9uIGVudGVyU3RhdGVzKGN1cnJlbnRTbmFwc2hvdCwgZXZlbnQsIGFjdG9yU2NvcGUsIGZpbHRlcmVkVHJhbnNpdGlvbnMsIG11dFN0YXRlTm9kZVNldCwgaW50ZXJuYWxRdWV1ZSwgaGlzdG9yeVZhbHVlLCBpc0luaXRpYWwpIHtcbiAgbGV0IG5leHRTbmFwc2hvdCA9IGN1cnJlbnRTbmFwc2hvdDtcbiAgY29uc3Qgc3RhdGVzVG9FbnRlciA9IG5ldyBTZXQoKTtcbiAgLy8gdGhvc2UgYXJlIHN0YXRlcyB0aGF0IHdlcmUgZGlyZWN0bHkgdGFyZ2V0ZWQgb3IgaW5kaXJlY3RseSB0YXJnZXRlZCBieSB0aGUgZXhwbGljaXQgdGFyZ2V0XG4gIC8vIGluIG90aGVyIHdvcmRzLCB0aG9zZSBhcmUgc3RhdGVzIGZvciB3aGljaCBpbml0aWFsIGFjdGlvbnMgc2hvdWxkIGJlIGV4ZWN1dGVkXG4gIC8vIHdoZW4gd2UgdGFyZ2V0IGAjZGVlcF9jaGlsZGAgaW5pdGlhbCBhY3Rpb25zIG9mIGl0cyBhbmNlc3RvcnMgc2hvdWxkbid0IGJlIGV4ZWN1dGVkXG4gIGNvbnN0IHN0YXRlc0ZvckRlZmF1bHRFbnRyeSA9IG5ldyBTZXQoKTtcbiAgY29tcHV0ZUVudHJ5U2V0KGZpbHRlcmVkVHJhbnNpdGlvbnMsIGhpc3RvcnlWYWx1ZSwgc3RhdGVzRm9yRGVmYXVsdEVudHJ5LCBzdGF0ZXNUb0VudGVyKTtcblxuICAvLyBJbiB0aGUgaW5pdGlhbCBzdGF0ZSwgdGhlIHJvb3Qgc3RhdGUgbm9kZSBpcyBcImVudGVyZWRcIi5cbiAgaWYgKGlzSW5pdGlhbCkge1xuICAgIHN0YXRlc0ZvckRlZmF1bHRFbnRyeS5hZGQoY3VycmVudFNuYXBzaG90Lm1hY2hpbmUucm9vdCk7XG4gIH1cbiAgY29uc3QgY29tcGxldGVkTm9kZXMgPSBuZXcgU2V0KCk7XG4gIGZvciAoY29uc3Qgc3RhdGVOb2RlVG9FbnRlciBvZiBbLi4uc3RhdGVzVG9FbnRlcl0uc29ydCgoYSwgYikgPT4gYS5vcmRlciAtIGIub3JkZXIpKSB7XG4gICAgbXV0U3RhdGVOb2RlU2V0LmFkZChzdGF0ZU5vZGVUb0VudGVyKTtcbiAgICBjb25zdCBhY3Rpb25zID0gW107XG5cbiAgICAvLyBBZGQgZW50cnkgYWN0aW9uc1xuICAgIGFjdGlvbnMucHVzaCguLi5zdGF0ZU5vZGVUb0VudGVyLmVudHJ5KTtcbiAgICBmb3IgKGNvbnN0IGludm9rZURlZiBvZiBzdGF0ZU5vZGVUb0VudGVyLmludm9rZSkge1xuICAgICAgYWN0aW9ucy5wdXNoKHNwYXduQ2hpbGQoaW52b2tlRGVmLnNyYywge1xuICAgICAgICAuLi5pbnZva2VEZWYsXG4gICAgICAgIHN5bmNTbmFwc2hvdDogISFpbnZva2VEZWYub25TbmFwc2hvdFxuICAgICAgfSkpO1xuICAgIH1cbiAgICBpZiAoc3RhdGVzRm9yRGVmYXVsdEVudHJ5LmhhcyhzdGF0ZU5vZGVUb0VudGVyKSkge1xuICAgICAgY29uc3QgaW5pdGlhbEFjdGlvbnMgPSBzdGF0ZU5vZGVUb0VudGVyLmluaXRpYWwuYWN0aW9ucztcbiAgICAgIGFjdGlvbnMucHVzaCguLi5pbml0aWFsQWN0aW9ucyk7XG4gICAgfVxuICAgIG5leHRTbmFwc2hvdCA9IHJlc29sdmVBY3Rpb25zQW5kQ29udGV4dChuZXh0U25hcHNob3QsIGV2ZW50LCBhY3RvclNjb3BlLCBhY3Rpb25zLCBpbnRlcm5hbFF1ZXVlLCBzdGF0ZU5vZGVUb0VudGVyLmludm9rZS5tYXAoaW52b2tlRGVmID0+IGludm9rZURlZi5pZCkpO1xuICAgIGlmIChzdGF0ZU5vZGVUb0VudGVyLnR5cGUgPT09ICdmaW5hbCcpIHtcbiAgICAgIGNvbnN0IHBhcmVudCA9IHN0YXRlTm9kZVRvRW50ZXIucGFyZW50O1xuICAgICAgbGV0IGFuY2VzdG9yTWFya2VyID0gcGFyZW50Py50eXBlID09PSAncGFyYWxsZWwnID8gcGFyZW50IDogcGFyZW50Py5wYXJlbnQ7XG4gICAgICBsZXQgcm9vdENvbXBsZXRpb25Ob2RlID0gYW5jZXN0b3JNYXJrZXIgfHwgc3RhdGVOb2RlVG9FbnRlcjtcbiAgICAgIGlmIChwYXJlbnQ/LnR5cGUgPT09ICdjb21wb3VuZCcpIHtcbiAgICAgICAgaW50ZXJuYWxRdWV1ZS5wdXNoKGNyZWF0ZURvbmVTdGF0ZUV2ZW50KHBhcmVudC5pZCwgc3RhdGVOb2RlVG9FbnRlci5vdXRwdXQgIT09IHVuZGVmaW5lZCA/IHJlc29sdmVPdXRwdXQoc3RhdGVOb2RlVG9FbnRlci5vdXRwdXQsIG5leHRTbmFwc2hvdC5jb250ZXh0LCBldmVudCwgYWN0b3JTY29wZS5zZWxmKSA6IHVuZGVmaW5lZCkpO1xuICAgICAgfVxuICAgICAgd2hpbGUgKGFuY2VzdG9yTWFya2VyPy50eXBlID09PSAncGFyYWxsZWwnICYmICFjb21wbGV0ZWROb2Rlcy5oYXMoYW5jZXN0b3JNYXJrZXIpICYmIGlzSW5GaW5hbFN0YXRlKG11dFN0YXRlTm9kZVNldCwgYW5jZXN0b3JNYXJrZXIpKSB7XG4gICAgICAgIGNvbXBsZXRlZE5vZGVzLmFkZChhbmNlc3Rvck1hcmtlcik7XG4gICAgICAgIGludGVybmFsUXVldWUucHVzaChjcmVhdGVEb25lU3RhdGVFdmVudChhbmNlc3Rvck1hcmtlci5pZCkpO1xuICAgICAgICByb290Q29tcGxldGlvbk5vZGUgPSBhbmNlc3Rvck1hcmtlcjtcbiAgICAgICAgYW5jZXN0b3JNYXJrZXIgPSBhbmNlc3Rvck1hcmtlci5wYXJlbnQ7XG4gICAgICB9XG4gICAgICBpZiAoYW5jZXN0b3JNYXJrZXIpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG4gICAgICBuZXh0U25hcHNob3QgPSBjbG9uZU1hY2hpbmVTbmFwc2hvdChuZXh0U25hcHNob3QsIHtcbiAgICAgICAgc3RhdHVzOiAnZG9uZScsXG4gICAgICAgIG91dHB1dDogZ2V0TWFjaGluZU91dHB1dChuZXh0U25hcHNob3QsIGV2ZW50LCBhY3RvclNjb3BlLCBuZXh0U25hcHNob3QubWFjaGluZS5yb290LCByb290Q29tcGxldGlvbk5vZGUpXG4gICAgICB9KTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG5leHRTbmFwc2hvdDtcbn1cbmZ1bmN0aW9uIGNvbXB1dGVFbnRyeVNldCh0cmFuc2l0aW9ucywgaGlzdG9yeVZhbHVlLCBzdGF0ZXNGb3JEZWZhdWx0RW50cnksIHN0YXRlc1RvRW50ZXIpIHtcbiAgZm9yIChjb25zdCB0IG9mIHRyYW5zaXRpb25zKSB7XG4gICAgY29uc3QgZG9tYWluID0gZ2V0VHJhbnNpdGlvbkRvbWFpbih0LCBoaXN0b3J5VmFsdWUpO1xuICAgIGZvciAoY29uc3QgcyBvZiB0LnRhcmdldCB8fCBbXSkge1xuICAgICAgaWYgKCFpc0hpc3RvcnlOb2RlKHMpICYmIChcbiAgICAgIC8vIGlmIHRoZSB0YXJnZXQgaXMgZGlmZmVyZW50IHRoYW4gdGhlIHNvdXJjZSB0aGVuIGl0IHdpbGwgKmRlZmluaXRlbHkqIGJlIGVudGVyZWRcbiAgICAgIHQuc291cmNlICE9PSBzIHx8XG4gICAgICAvLyB3ZSBrbm93IHRoYXQgdGhlIGRvbWFpbiBjYW4ndCBsaWUgd2l0aGluIHRoZSBzb3VyY2VcbiAgICAgIC8vIGlmIGl0J3MgZGlmZmVyZW50IHRoYW4gdGhlIHNvdXJjZSB0aGVuIGl0J3Mgb3V0c2lkZSBvZiBpdCBhbmQgaXQgbWVhbnMgdGhhdCB0aGUgdGFyZ2V0IGhhcyB0byBiZSBlbnRlcmVkIGFzIHdlbGxcbiAgICAgIHQuc291cmNlICE9PSBkb21haW4gfHxcbiAgICAgIC8vIHJlZW50ZXJpbmcgdHJhbnNpdGlvbnMgYWx3YXlzIGVudGVyIHRoZSB0YXJnZXQsIGV2ZW4gaWYgaXQncyB0aGUgc291cmNlIGl0c2VsZlxuICAgICAgdC5yZWVudGVyKSkge1xuICAgICAgICBzdGF0ZXNUb0VudGVyLmFkZChzKTtcbiAgICAgICAgc3RhdGVzRm9yRGVmYXVsdEVudHJ5LmFkZChzKTtcbiAgICAgIH1cbiAgICAgIGFkZERlc2NlbmRhbnRTdGF0ZXNUb0VudGVyKHMsIGhpc3RvcnlWYWx1ZSwgc3RhdGVzRm9yRGVmYXVsdEVudHJ5LCBzdGF0ZXNUb0VudGVyKTtcbiAgICB9XG4gICAgY29uc3QgdGFyZ2V0U3RhdGVzID0gZ2V0RWZmZWN0aXZlVGFyZ2V0U3RhdGVzKHQsIGhpc3RvcnlWYWx1ZSk7XG4gICAgZm9yIChjb25zdCBzIG9mIHRhcmdldFN0YXRlcykge1xuICAgICAgY29uc3QgYW5jZXN0b3JzID0gZ2V0UHJvcGVyQW5jZXN0b3JzKHMsIGRvbWFpbik7XG4gICAgICBpZiAoZG9tYWluPy50eXBlID09PSAncGFyYWxsZWwnKSB7XG4gICAgICAgIGFuY2VzdG9ycy5wdXNoKGRvbWFpbik7XG4gICAgICB9XG4gICAgICBhZGRBbmNlc3RvclN0YXRlc1RvRW50ZXIoc3RhdGVzVG9FbnRlciwgaGlzdG9yeVZhbHVlLCBzdGF0ZXNGb3JEZWZhdWx0RW50cnksIGFuY2VzdG9ycywgIXQuc291cmNlLnBhcmVudCAmJiB0LnJlZW50ZXIgPyB1bmRlZmluZWQgOiBkb21haW4pO1xuICAgIH1cbiAgfVxufVxuZnVuY3Rpb24gYWRkRGVzY2VuZGFudFN0YXRlc1RvRW50ZXIoc3RhdGVOb2RlLCBoaXN0b3J5VmFsdWUsIHN0YXRlc0ZvckRlZmF1bHRFbnRyeSwgc3RhdGVzVG9FbnRlcikge1xuICBpZiAoaXNIaXN0b3J5Tm9kZShzdGF0ZU5vZGUpKSB7XG4gICAgaWYgKGhpc3RvcnlWYWx1ZVtzdGF0ZU5vZGUuaWRdKSB7XG4gICAgICBjb25zdCBoaXN0b3J5U3RhdGVOb2RlcyA9IGhpc3RvcnlWYWx1ZVtzdGF0ZU5vZGUuaWRdO1xuICAgICAgZm9yIChjb25zdCBzIG9mIGhpc3RvcnlTdGF0ZU5vZGVzKSB7XG4gICAgICAgIHN0YXRlc1RvRW50ZXIuYWRkKHMpO1xuICAgICAgICBhZGREZXNjZW5kYW50U3RhdGVzVG9FbnRlcihzLCBoaXN0b3J5VmFsdWUsIHN0YXRlc0ZvckRlZmF1bHRFbnRyeSwgc3RhdGVzVG9FbnRlcik7XG4gICAgICB9XG4gICAgICBmb3IgKGNvbnN0IHMgb2YgaGlzdG9yeVN0YXRlTm9kZXMpIHtcbiAgICAgICAgYWRkUHJvcGVyQW5jZXN0b3JTdGF0ZXNUb0VudGVyKHMsIHN0YXRlTm9kZS5wYXJlbnQsIHN0YXRlc1RvRW50ZXIsIGhpc3RvcnlWYWx1ZSwgc3RhdGVzRm9yRGVmYXVsdEVudHJ5KTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgY29uc3QgaGlzdG9yeURlZmF1bHRUcmFuc2l0aW9uID0gcmVzb2x2ZUhpc3RvcnlEZWZhdWx0VHJhbnNpdGlvbihzdGF0ZU5vZGUpO1xuICAgICAgZm9yIChjb25zdCBzIG9mIGhpc3RvcnlEZWZhdWx0VHJhbnNpdGlvbi50YXJnZXQpIHtcbiAgICAgICAgc3RhdGVzVG9FbnRlci5hZGQocyk7XG4gICAgICAgIGlmIChoaXN0b3J5RGVmYXVsdFRyYW5zaXRpb24gPT09IHN0YXRlTm9kZS5wYXJlbnQ/LmluaXRpYWwpIHtcbiAgICAgICAgICBzdGF0ZXNGb3JEZWZhdWx0RW50cnkuYWRkKHN0YXRlTm9kZS5wYXJlbnQpO1xuICAgICAgICB9XG4gICAgICAgIGFkZERlc2NlbmRhbnRTdGF0ZXNUb0VudGVyKHMsIGhpc3RvcnlWYWx1ZSwgc3RhdGVzRm9yRGVmYXVsdEVudHJ5LCBzdGF0ZXNUb0VudGVyKTtcbiAgICAgIH1cbiAgICAgIGZvciAoY29uc3QgcyBvZiBoaXN0b3J5RGVmYXVsdFRyYW5zaXRpb24udGFyZ2V0KSB7XG4gICAgICAgIGFkZFByb3BlckFuY2VzdG9yU3RhdGVzVG9FbnRlcihzLCBzdGF0ZU5vZGUucGFyZW50LCBzdGF0ZXNUb0VudGVyLCBoaXN0b3J5VmFsdWUsIHN0YXRlc0ZvckRlZmF1bHRFbnRyeSk7XG4gICAgICB9XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIGlmIChzdGF0ZU5vZGUudHlwZSA9PT0gJ2NvbXBvdW5kJykge1xuICAgICAgY29uc3QgW2luaXRpYWxTdGF0ZV0gPSBzdGF0ZU5vZGUuaW5pdGlhbC50YXJnZXQ7XG4gICAgICBpZiAoIWlzSGlzdG9yeU5vZGUoaW5pdGlhbFN0YXRlKSkge1xuICAgICAgICBzdGF0ZXNUb0VudGVyLmFkZChpbml0aWFsU3RhdGUpO1xuICAgICAgICBzdGF0ZXNGb3JEZWZhdWx0RW50cnkuYWRkKGluaXRpYWxTdGF0ZSk7XG4gICAgICB9XG4gICAgICBhZGREZXNjZW5kYW50U3RhdGVzVG9FbnRlcihpbml0aWFsU3RhdGUsIGhpc3RvcnlWYWx1ZSwgc3RhdGVzRm9yRGVmYXVsdEVudHJ5LCBzdGF0ZXNUb0VudGVyKTtcbiAgICAgIGFkZFByb3BlckFuY2VzdG9yU3RhdGVzVG9FbnRlcihpbml0aWFsU3RhdGUsIHN0YXRlTm9kZSwgc3RhdGVzVG9FbnRlciwgaGlzdG9yeVZhbHVlLCBzdGF0ZXNGb3JEZWZhdWx0RW50cnkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBpZiAoc3RhdGVOb2RlLnR5cGUgPT09ICdwYXJhbGxlbCcpIHtcbiAgICAgICAgZm9yIChjb25zdCBjaGlsZCBvZiBnZXRDaGlsZHJlbihzdGF0ZU5vZGUpLmZpbHRlcihzbiA9PiAhaXNIaXN0b3J5Tm9kZShzbikpKSB7XG4gICAgICAgICAgaWYgKCFbLi4uc3RhdGVzVG9FbnRlcl0uc29tZShzID0+IGlzRGVzY2VuZGFudChzLCBjaGlsZCkpKSB7XG4gICAgICAgICAgICBpZiAoIWlzSGlzdG9yeU5vZGUoY2hpbGQpKSB7XG4gICAgICAgICAgICAgIHN0YXRlc1RvRW50ZXIuYWRkKGNoaWxkKTtcbiAgICAgICAgICAgICAgc3RhdGVzRm9yRGVmYXVsdEVudHJ5LmFkZChjaGlsZCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBhZGREZXNjZW5kYW50U3RhdGVzVG9FbnRlcihjaGlsZCwgaGlzdG9yeVZhbHVlLCBzdGF0ZXNGb3JEZWZhdWx0RW50cnksIHN0YXRlc1RvRW50ZXIpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuZnVuY3Rpb24gYWRkQW5jZXN0b3JTdGF0ZXNUb0VudGVyKHN0YXRlc1RvRW50ZXIsIGhpc3RvcnlWYWx1ZSwgc3RhdGVzRm9yRGVmYXVsdEVudHJ5LCBhbmNlc3RvcnMsIHJlZW50cmFuY3lEb21haW4pIHtcbiAgZm9yIChjb25zdCBhbmMgb2YgYW5jZXN0b3JzKSB7XG4gICAgaWYgKCFyZWVudHJhbmN5RG9tYWluIHx8IGlzRGVzY2VuZGFudChhbmMsIHJlZW50cmFuY3lEb21haW4pKSB7XG4gICAgICBzdGF0ZXNUb0VudGVyLmFkZChhbmMpO1xuICAgIH1cbiAgICBpZiAoYW5jLnR5cGUgPT09ICdwYXJhbGxlbCcpIHtcbiAgICAgIGZvciAoY29uc3QgY2hpbGQgb2YgZ2V0Q2hpbGRyZW4oYW5jKS5maWx0ZXIoc24gPT4gIWlzSGlzdG9yeU5vZGUoc24pKSkge1xuICAgICAgICBpZiAoIVsuLi5zdGF0ZXNUb0VudGVyXS5zb21lKHMgPT4gaXNEZXNjZW5kYW50KHMsIGNoaWxkKSkpIHtcbiAgICAgICAgICBzdGF0ZXNUb0VudGVyLmFkZChjaGlsZCk7XG4gICAgICAgICAgYWRkRGVzY2VuZGFudFN0YXRlc1RvRW50ZXIoY2hpbGQsIGhpc3RvcnlWYWx1ZSwgc3RhdGVzRm9yRGVmYXVsdEVudHJ5LCBzdGF0ZXNUb0VudGVyKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuZnVuY3Rpb24gYWRkUHJvcGVyQW5jZXN0b3JTdGF0ZXNUb0VudGVyKHN0YXRlTm9kZSwgdG9TdGF0ZU5vZGUsIHN0YXRlc1RvRW50ZXIsIGhpc3RvcnlWYWx1ZSwgc3RhdGVzRm9yRGVmYXVsdEVudHJ5KSB7XG4gIGFkZEFuY2VzdG9yU3RhdGVzVG9FbnRlcihzdGF0ZXNUb0VudGVyLCBoaXN0b3J5VmFsdWUsIHN0YXRlc0ZvckRlZmF1bHRFbnRyeSwgZ2V0UHJvcGVyQW5jZXN0b3JzKHN0YXRlTm9kZSwgdG9TdGF0ZU5vZGUpKTtcbn1cbmZ1bmN0aW9uIGV4aXRTdGF0ZXMoY3VycmVudFNuYXBzaG90LCBldmVudCwgYWN0b3JTY29wZSwgdHJhbnNpdGlvbnMsIG11dFN0YXRlTm9kZVNldCwgaGlzdG9yeVZhbHVlLCBpbnRlcm5hbFF1ZXVlLCBfYWN0aW9uRXhlY3V0b3IpIHtcbiAgbGV0IG5leHRTbmFwc2hvdCA9IGN1cnJlbnRTbmFwc2hvdDtcbiAgY29uc3Qgc3RhdGVzVG9FeGl0ID0gY29tcHV0ZUV4aXRTZXQodHJhbnNpdGlvbnMsIG11dFN0YXRlTm9kZVNldCwgaGlzdG9yeVZhbHVlKTtcbiAgc3RhdGVzVG9FeGl0LnNvcnQoKGEsIGIpID0+IGIub3JkZXIgLSBhLm9yZGVyKTtcbiAgbGV0IGNoYW5nZWRIaXN0b3J5O1xuXG4gIC8vIEZyb20gU0NYTUwgYWxnb3JpdGhtOiBodHRwczovL3d3dy53My5vcmcvVFIvc2N4bWwvI2V4aXRTdGF0ZXNcbiAgZm9yIChjb25zdCBleGl0U3RhdGVOb2RlIG9mIHN0YXRlc1RvRXhpdCkge1xuICAgIGZvciAoY29uc3QgaGlzdG9yeU5vZGUgb2YgZ2V0SGlzdG9yeU5vZGVzKGV4aXRTdGF0ZU5vZGUpKSB7XG4gICAgICBsZXQgcHJlZGljYXRlO1xuICAgICAgaWYgKGhpc3RvcnlOb2RlLmhpc3RvcnkgPT09ICdkZWVwJykge1xuICAgICAgICBwcmVkaWNhdGUgPSBzbiA9PiBpc0F0b21pY1N0YXRlTm9kZShzbikgJiYgaXNEZXNjZW5kYW50KHNuLCBleGl0U3RhdGVOb2RlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHByZWRpY2F0ZSA9IHNuID0+IHtcbiAgICAgICAgICByZXR1cm4gc24ucGFyZW50ID09PSBleGl0U3RhdGVOb2RlO1xuICAgICAgICB9O1xuICAgICAgfVxuICAgICAgY2hhbmdlZEhpc3RvcnkgPz89IHtcbiAgICAgICAgLi4uaGlzdG9yeVZhbHVlXG4gICAgICB9O1xuICAgICAgY2hhbmdlZEhpc3RvcnlbaGlzdG9yeU5vZGUuaWRdID0gQXJyYXkuZnJvbShtdXRTdGF0ZU5vZGVTZXQpLmZpbHRlcihwcmVkaWNhdGUpO1xuICAgIH1cbiAgfVxuICBmb3IgKGNvbnN0IHMgb2Ygc3RhdGVzVG9FeGl0KSB7XG4gICAgbmV4dFNuYXBzaG90ID0gcmVzb2x2ZUFjdGlvbnNBbmRDb250ZXh0KG5leHRTbmFwc2hvdCwgZXZlbnQsIGFjdG9yU2NvcGUsIFsuLi5zLmV4aXQsIC4uLnMuaW52b2tlLm1hcChkZWYgPT4gc3RvcENoaWxkKGRlZi5pZCkpXSwgaW50ZXJuYWxRdWV1ZSwgdW5kZWZpbmVkKTtcbiAgICBtdXRTdGF0ZU5vZGVTZXQuZGVsZXRlKHMpO1xuICB9XG4gIHJldHVybiBbbmV4dFNuYXBzaG90LCBjaGFuZ2VkSGlzdG9yeSB8fCBoaXN0b3J5VmFsdWVdO1xufVxuZnVuY3Rpb24gZ2V0QWN0aW9uKG1hY2hpbmUsIGFjdGlvblR5cGUpIHtcbiAgcmV0dXJuIG1hY2hpbmUuaW1wbGVtZW50YXRpb25zLmFjdGlvbnNbYWN0aW9uVHlwZV07XG59XG5mdW5jdGlvbiByZXNvbHZlQW5kRXhlY3V0ZUFjdGlvbnNXaXRoQ29udGV4dChjdXJyZW50U25hcHNob3QsIGV2ZW50LCBhY3RvclNjb3BlLCBhY3Rpb25zLCBleHRyYSwgcmV0cmllcykge1xuICBjb25zdCB7XG4gICAgbWFjaGluZVxuICB9ID0gY3VycmVudFNuYXBzaG90O1xuICBsZXQgaW50ZXJtZWRpYXRlU25hcHNob3QgPSBjdXJyZW50U25hcHNob3Q7XG4gIGZvciAoY29uc3QgYWN0aW9uIG9mIGFjdGlvbnMpIHtcbiAgICBjb25zdCBpc0lubGluZSA9IHR5cGVvZiBhY3Rpb24gPT09ICdmdW5jdGlvbic7XG4gICAgY29uc3QgcmVzb2x2ZWRBY3Rpb24gPSBpc0lubGluZSA/IGFjdGlvbiA6XG4gICAgLy8gdGhlIGV4aXN0aW5nIHR5cGUgb2YgYC5hY3Rpb25zYCBhc3N1bWVzIG5vbi1udWxsYWJsZSBgVEV4cHJlc3Npb25BY3Rpb25gXG4gICAgLy8gaXQncyBmaW5lIHRvIGNhc3QgdGhpcyBoZXJlIHRvIGdldCBhIGNvbW1vbiB0eXBlIGFuZCBsYWNrIG9mIGVycm9ycyBpbiB0aGUgcmVzdCBvZiB0aGUgY29kZVxuICAgIC8vIG91ciBsb2dpYyBiZWxvdyBtYWtlcyBzdXJlIHRoYXQgd2UgY2FsbCB0aG9zZSAyIFwidmFyaWFudHNcIiBjb3JyZWN0bHlcblxuICAgIGdldEFjdGlvbihtYWNoaW5lLCB0eXBlb2YgYWN0aW9uID09PSAnc3RyaW5nJyA/IGFjdGlvbiA6IGFjdGlvbi50eXBlKTtcbiAgICBjb25zdCBhY3Rpb25BcmdzID0ge1xuICAgICAgY29udGV4dDogaW50ZXJtZWRpYXRlU25hcHNob3QuY29udGV4dCxcbiAgICAgIGV2ZW50LFxuICAgICAgc2VsZjogYWN0b3JTY29wZS5zZWxmLFxuICAgICAgc3lzdGVtOiBhY3RvclNjb3BlLnN5c3RlbVxuICAgIH07XG4gICAgY29uc3QgYWN0aW9uUGFyYW1zID0gaXNJbmxpbmUgfHwgdHlwZW9mIGFjdGlvbiA9PT0gJ3N0cmluZycgPyB1bmRlZmluZWQgOiAncGFyYW1zJyBpbiBhY3Rpb24gPyB0eXBlb2YgYWN0aW9uLnBhcmFtcyA9PT0gJ2Z1bmN0aW9uJyA/IGFjdGlvbi5wYXJhbXMoe1xuICAgICAgY29udGV4dDogaW50ZXJtZWRpYXRlU25hcHNob3QuY29udGV4dCxcbiAgICAgIGV2ZW50XG4gICAgfSkgOiBhY3Rpb24ucGFyYW1zIDogdW5kZWZpbmVkO1xuICAgIGlmICghcmVzb2x2ZWRBY3Rpb24gfHwgISgncmVzb2x2ZScgaW4gcmVzb2x2ZWRBY3Rpb24pKSB7XG4gICAgICBhY3RvclNjb3BlLmFjdGlvbkV4ZWN1dG9yKHtcbiAgICAgICAgdHlwZTogdHlwZW9mIGFjdGlvbiA9PT0gJ3N0cmluZycgPyBhY3Rpb24gOiB0eXBlb2YgYWN0aW9uID09PSAnb2JqZWN0JyA/IGFjdGlvbi50eXBlIDogYWN0aW9uLm5hbWUgfHwgJyhhbm9ueW1vdXMpJyxcbiAgICAgICAgaW5mbzogYWN0aW9uQXJncyxcbiAgICAgICAgcGFyYW1zOiBhY3Rpb25QYXJhbXMsXG4gICAgICAgIGV4ZWM6IHJlc29sdmVkQWN0aW9uXG4gICAgICB9KTtcbiAgICAgIGNvbnRpbnVlO1xuICAgIH1cbiAgICBjb25zdCBidWlsdGluQWN0aW9uID0gcmVzb2x2ZWRBY3Rpb247XG4gICAgY29uc3QgW25leHRTdGF0ZSwgcGFyYW1zLCBhY3Rpb25zXSA9IGJ1aWx0aW5BY3Rpb24ucmVzb2x2ZShhY3RvclNjb3BlLCBpbnRlcm1lZGlhdGVTbmFwc2hvdCwgYWN0aW9uQXJncywgYWN0aW9uUGFyYW1zLCByZXNvbHZlZEFjdGlvbixcbiAgICAvLyB0aGlzIGhvbGRzIGFsbCBwYXJhbXNcbiAgICBleHRyYSk7XG4gICAgaW50ZXJtZWRpYXRlU25hcHNob3QgPSBuZXh0U3RhdGU7XG4gICAgaWYgKCdyZXRyeVJlc29sdmUnIGluIGJ1aWx0aW5BY3Rpb24pIHtcbiAgICAgIHJldHJpZXM/LnB1c2goW2J1aWx0aW5BY3Rpb24sIHBhcmFtc10pO1xuICAgIH1cbiAgICBpZiAoJ2V4ZWN1dGUnIGluIGJ1aWx0aW5BY3Rpb24pIHtcbiAgICAgIGFjdG9yU2NvcGUuYWN0aW9uRXhlY3V0b3Ioe1xuICAgICAgICB0eXBlOiBidWlsdGluQWN0aW9uLnR5cGUsXG4gICAgICAgIGluZm86IGFjdGlvbkFyZ3MsXG4gICAgICAgIHBhcmFtcyxcbiAgICAgICAgZXhlYzogYnVpbHRpbkFjdGlvbi5leGVjdXRlLmJpbmQobnVsbCwgYWN0b3JTY29wZSwgcGFyYW1zKVxuICAgICAgfSk7XG4gICAgfVxuICAgIGlmIChhY3Rpb25zKSB7XG4gICAgICBpbnRlcm1lZGlhdGVTbmFwc2hvdCA9IHJlc29sdmVBbmRFeGVjdXRlQWN0aW9uc1dpdGhDb250ZXh0KGludGVybWVkaWF0ZVNuYXBzaG90LCBldmVudCwgYWN0b3JTY29wZSwgYWN0aW9ucywgZXh0cmEsIHJldHJpZXMpO1xuICAgIH1cbiAgfVxuICByZXR1cm4gaW50ZXJtZWRpYXRlU25hcHNob3Q7XG59XG5mdW5jdGlvbiByZXNvbHZlQWN0aW9uc0FuZENvbnRleHQoY3VycmVudFNuYXBzaG90LCBldmVudCwgYWN0b3JTY29wZSwgYWN0aW9ucywgaW50ZXJuYWxRdWV1ZSwgZGVmZXJyZWRBY3Rvcklkcykge1xuICBjb25zdCByZXRyaWVzID0gZGVmZXJyZWRBY3RvcklkcyA/IFtdIDogdW5kZWZpbmVkO1xuICBjb25zdCBuZXh0U3RhdGUgPSByZXNvbHZlQW5kRXhlY3V0ZUFjdGlvbnNXaXRoQ29udGV4dChjdXJyZW50U25hcHNob3QsIGV2ZW50LCBhY3RvclNjb3BlLCBhY3Rpb25zLCB7XG4gICAgaW50ZXJuYWxRdWV1ZSxcbiAgICBkZWZlcnJlZEFjdG9ySWRzXG4gIH0sIHJldHJpZXMpO1xuICByZXRyaWVzPy5mb3JFYWNoKChbYnVpbHRpbkFjdGlvbiwgcGFyYW1zXSkgPT4ge1xuICAgIGJ1aWx0aW5BY3Rpb24ucmV0cnlSZXNvbHZlKGFjdG9yU2NvcGUsIG5leHRTdGF0ZSwgcGFyYW1zKTtcbiAgfSk7XG4gIHJldHVybiBuZXh0U3RhdGU7XG59XG5mdW5jdGlvbiBtYWNyb3N0ZXAoc25hcHNob3QsIGV2ZW50LCBhY3RvclNjb3BlLCBpbnRlcm5hbFF1ZXVlKSB7XG4gIGlmIChldmVudC50eXBlID09PSBXSUxEQ0FSRCkge1xuICAgIHRocm93IG5ldyBFcnJvcihgQW4gZXZlbnQgY2Fubm90IGhhdmUgdGhlIHdpbGRjYXJkIHR5cGUgKCcke1dJTERDQVJEfScpYCk7XG4gIH1cbiAgbGV0IG5leHRTbmFwc2hvdCA9IHNuYXBzaG90O1xuICBjb25zdCBtaWNyb3N0YXRlcyA9IFtdO1xuICBmdW5jdGlvbiBhZGRNaWNyb3N0YXRlKG1pY3Jvc3RhdGUsIGV2ZW50LCB0cmFuc2l0aW9ucykge1xuICAgIGFjdG9yU2NvcGUuc3lzdGVtLl9zZW5kSW5zcGVjdGlvbkV2ZW50KHtcbiAgICAgIHR5cGU6ICdAeHN0YXRlLm1pY3Jvc3RlcCcsXG4gICAgICBhY3RvclJlZjogYWN0b3JTY29wZS5zZWxmLFxuICAgICAgZXZlbnQsXG4gICAgICBzbmFwc2hvdDogbWljcm9zdGF0ZSxcbiAgICAgIF90cmFuc2l0aW9uczogdHJhbnNpdGlvbnNcbiAgICB9KTtcbiAgICBtaWNyb3N0YXRlcy5wdXNoKG1pY3Jvc3RhdGUpO1xuICB9XG5cbiAgLy8gSGFuZGxlIHN0b3AgZXZlbnRcbiAgaWYgKGV2ZW50LnR5cGUgPT09IFhTVEFURV9TVE9QKSB7XG4gICAgbmV4dFNuYXBzaG90ID0gY2xvbmVNYWNoaW5lU25hcHNob3Qoc3RvcENoaWxkcmVuKG5leHRTbmFwc2hvdCwgZXZlbnQsIGFjdG9yU2NvcGUpLCB7XG4gICAgICBzdGF0dXM6ICdzdG9wcGVkJ1xuICAgIH0pO1xuICAgIGFkZE1pY3Jvc3RhdGUobmV4dFNuYXBzaG90LCBldmVudCwgW10pO1xuICAgIHJldHVybiB7XG4gICAgICBzbmFwc2hvdDogbmV4dFNuYXBzaG90LFxuICAgICAgbWljcm9zdGF0ZXNcbiAgICB9O1xuICB9XG4gIGxldCBuZXh0RXZlbnQgPSBldmVudDtcblxuICAvLyBBc3N1bWUgdGhlIHN0YXRlIGlzIGF0IHJlc3QgKG5vIHJhaXNlZCBldmVudHMpXG4gIC8vIERldGVybWluZSB0aGUgbmV4dCBzdGF0ZSBiYXNlZCBvbiB0aGUgbmV4dCBtaWNyb3N0ZXBcbiAgaWYgKG5leHRFdmVudC50eXBlICE9PSBYU1RBVEVfSU5JVCkge1xuICAgIGNvbnN0IGN1cnJlbnRFdmVudCA9IG5leHRFdmVudDtcbiAgICBjb25zdCBpc0VyciA9IGlzRXJyb3JBY3RvckV2ZW50KGN1cnJlbnRFdmVudCk7XG4gICAgY29uc3QgdHJhbnNpdGlvbnMgPSBzZWxlY3RUcmFuc2l0aW9ucyhjdXJyZW50RXZlbnQsIG5leHRTbmFwc2hvdCk7XG4gICAgaWYgKGlzRXJyICYmICF0cmFuc2l0aW9ucy5sZW5ndGgpIHtcbiAgICAgIC8vIFRPRE86IHdlIHNob3VsZCBsaWtlbHkgb25seSBhbGxvdyB0cmFuc2l0aW9ucyBzZWxlY3RlZCBieSB2ZXJ5IGV4cGxpY2l0IGRlc2NyaXB0b3JzXG4gICAgICAvLyBgKmAgc2hvdWxkbid0IGJlIG1hdGNoZWQsIGxpa2VseSBgeHN0YXRlLmVycm9yLipgIHNob3VsZG50IGJlIGVpdGhlclxuICAgICAgLy8gc2ltaWxhcmx5IGB4c3RhdGUuZXJyb3IuYWN0b3IuKmAgYW5kIGB4c3RhdGUuZXJyb3IuYWN0b3IudG9kby4qYCBoYXZlIHRvIGJlIGNvbnNpZGVyZWQgdG9vXG4gICAgICBuZXh0U25hcHNob3QgPSBjbG9uZU1hY2hpbmVTbmFwc2hvdChzbmFwc2hvdCwge1xuICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgIGVycm9yOiBjdXJyZW50RXZlbnQuZXJyb3JcbiAgICAgIH0pO1xuICAgICAgYWRkTWljcm9zdGF0ZShuZXh0U25hcHNob3QsIGN1cnJlbnRFdmVudCwgW10pO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc25hcHNob3Q6IG5leHRTbmFwc2hvdCxcbiAgICAgICAgbWljcm9zdGF0ZXNcbiAgICAgIH07XG4gICAgfVxuICAgIG5leHRTbmFwc2hvdCA9IG1pY3Jvc3RlcCh0cmFuc2l0aW9ucywgc25hcHNob3QsIGFjdG9yU2NvcGUsIG5leHRFdmVudCwgZmFsc2UsXG4gICAgLy8gaXNJbml0aWFsXG4gICAgaW50ZXJuYWxRdWV1ZSk7XG4gICAgYWRkTWljcm9zdGF0ZShuZXh0U25hcHNob3QsIGN1cnJlbnRFdmVudCwgdHJhbnNpdGlvbnMpO1xuICB9XG4gIGxldCBzaG91bGRTZWxlY3RFdmVudGxlc3NUcmFuc2l0aW9ucyA9IHRydWU7XG4gIHdoaWxlIChuZXh0U25hcHNob3Quc3RhdHVzID09PSAnYWN0aXZlJykge1xuICAgIGxldCBlbmFibGVkVHJhbnNpdGlvbnMgPSBzaG91bGRTZWxlY3RFdmVudGxlc3NUcmFuc2l0aW9ucyA/IHNlbGVjdEV2ZW50bGVzc1RyYW5zaXRpb25zKG5leHRTbmFwc2hvdCwgbmV4dEV2ZW50KSA6IFtdO1xuXG4gICAgLy8gZXZlbnRsZXNzIHRyYW5zaXRpb25zIHNob3VsZCBhbHdheXMgYmUgc2VsZWN0ZWQgYWZ0ZXIgc2VsZWN0aW5nICpyZWd1bGFyKiB0cmFuc2l0aW9uc1xuICAgIC8vIGJ5IGFzc2lnbmluZyBgdW5kZWZpbmVkYCB0byBgcHJldmlvdXNTdGF0ZWAgd2UgZW5zdXJlIHRoYXQgYHNob3VsZFNlbGVjdEV2ZW50bGVzc1RyYW5zaXRpb25zYCBnZXRzIGFsd2F5cyBjb21wdXRlZCB0byB0cnVlIGluIHN1Y2ggYSBjYXNlXG4gICAgY29uc3QgcHJldmlvdXNTdGF0ZSA9IGVuYWJsZWRUcmFuc2l0aW9ucy5sZW5ndGggPyBuZXh0U25hcHNob3QgOiB1bmRlZmluZWQ7XG4gICAgaWYgKCFlbmFibGVkVHJhbnNpdGlvbnMubGVuZ3RoKSB7XG4gICAgICBpZiAoIWludGVybmFsUXVldWUubGVuZ3RoKSB7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgbmV4dEV2ZW50ID0gaW50ZXJuYWxRdWV1ZS5zaGlmdCgpO1xuICAgICAgZW5hYmxlZFRyYW5zaXRpb25zID0gc2VsZWN0VHJhbnNpdGlvbnMobmV4dEV2ZW50LCBuZXh0U25hcHNob3QpO1xuICAgIH1cbiAgICBuZXh0U25hcHNob3QgPSBtaWNyb3N0ZXAoZW5hYmxlZFRyYW5zaXRpb25zLCBuZXh0U25hcHNob3QsIGFjdG9yU2NvcGUsIG5leHRFdmVudCwgZmFsc2UsIGludGVybmFsUXVldWUpO1xuICAgIHNob3VsZFNlbGVjdEV2ZW50bGVzc1RyYW5zaXRpb25zID0gbmV4dFNuYXBzaG90ICE9PSBwcmV2aW91c1N0YXRlO1xuICAgIGFkZE1pY3Jvc3RhdGUobmV4dFNuYXBzaG90LCBuZXh0RXZlbnQsIGVuYWJsZWRUcmFuc2l0aW9ucyk7XG4gIH1cbiAgaWYgKG5leHRTbmFwc2hvdC5zdGF0dXMgIT09ICdhY3RpdmUnKSB7XG4gICAgc3RvcENoaWxkcmVuKG5leHRTbmFwc2hvdCwgbmV4dEV2ZW50LCBhY3RvclNjb3BlKTtcbiAgfVxuICByZXR1cm4ge1xuICAgIHNuYXBzaG90OiBuZXh0U25hcHNob3QsXG4gICAgbWljcm9zdGF0ZXNcbiAgfTtcbn1cbmZ1bmN0aW9uIHN0b3BDaGlsZHJlbihuZXh0U3RhdGUsIGV2ZW50LCBhY3RvclNjb3BlKSB7XG4gIHJldHVybiByZXNvbHZlQWN0aW9uc0FuZENvbnRleHQobmV4dFN0YXRlLCBldmVudCwgYWN0b3JTY29wZSwgT2JqZWN0LnZhbHVlcyhuZXh0U3RhdGUuY2hpbGRyZW4pLm1hcChjaGlsZCA9PiBzdG9wQ2hpbGQoY2hpbGQpKSwgW10sIHVuZGVmaW5lZCk7XG59XG5mdW5jdGlvbiBzZWxlY3RUcmFuc2l0aW9ucyhldmVudCwgbmV4dFN0YXRlKSB7XG4gIHJldHVybiBuZXh0U3RhdGUubWFjaGluZS5nZXRUcmFuc2l0aW9uRGF0YShuZXh0U3RhdGUsIGV2ZW50KTtcbn1cbmZ1bmN0aW9uIHNlbGVjdEV2ZW50bGVzc1RyYW5zaXRpb25zKG5leHRTdGF0ZSwgZXZlbnQpIHtcbiAgY29uc3QgZW5hYmxlZFRyYW5zaXRpb25TZXQgPSBuZXcgU2V0KCk7XG4gIGNvbnN0IGF0b21pY1N0YXRlcyA9IG5leHRTdGF0ZS5fbm9kZXMuZmlsdGVyKGlzQXRvbWljU3RhdGVOb2RlKTtcbiAgZm9yIChjb25zdCBzdGF0ZU5vZGUgb2YgYXRvbWljU3RhdGVzKSB7XG4gICAgbG9vcDogZm9yIChjb25zdCBzIG9mIFtzdGF0ZU5vZGVdLmNvbmNhdChnZXRQcm9wZXJBbmNlc3RvcnMoc3RhdGVOb2RlLCB1bmRlZmluZWQpKSkge1xuICAgICAgaWYgKCFzLmFsd2F5cykge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIGZvciAoY29uc3QgdHJhbnNpdGlvbiBvZiBzLmFsd2F5cykge1xuICAgICAgICBpZiAodHJhbnNpdGlvbi5ndWFyZCA9PT0gdW5kZWZpbmVkIHx8IGV2YWx1YXRlR3VhcmQodHJhbnNpdGlvbi5ndWFyZCwgbmV4dFN0YXRlLmNvbnRleHQsIGV2ZW50LCBuZXh0U3RhdGUpKSB7XG4gICAgICAgICAgZW5hYmxlZFRyYW5zaXRpb25TZXQuYWRkKHRyYW5zaXRpb24pO1xuICAgICAgICAgIGJyZWFrIGxvb3A7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIHJlbW92ZUNvbmZsaWN0aW5nVHJhbnNpdGlvbnMoQXJyYXkuZnJvbShlbmFibGVkVHJhbnNpdGlvblNldCksIG5ldyBTZXQobmV4dFN0YXRlLl9ub2RlcyksIG5leHRTdGF0ZS5oaXN0b3J5VmFsdWUpO1xufVxuXG4vKipcbiAqIFJlc29sdmVzIGEgcGFydGlhbCBzdGF0ZSB2YWx1ZSB3aXRoIGl0cyBmdWxsIHJlcHJlc2VudGF0aW9uIGluIHRoZSBzdGF0ZVxuICogbm9kZSdzIG1hY2hpbmUuXG4gKlxuICogQHBhcmFtIHN0YXRlVmFsdWUgVGhlIHBhcnRpYWwgc3RhdGUgdmFsdWUgdG8gcmVzb2x2ZS5cbiAqL1xuZnVuY3Rpb24gcmVzb2x2ZVN0YXRlVmFsdWUocm9vdE5vZGUsIHN0YXRlVmFsdWUpIHtcbiAgY29uc3QgYWxsU3RhdGVOb2RlcyA9IGdldEFsbFN0YXRlTm9kZXMoZ2V0U3RhdGVOb2Rlcyhyb290Tm9kZSwgc3RhdGVWYWx1ZSkpO1xuICByZXR1cm4gZ2V0U3RhdGVWYWx1ZShyb290Tm9kZSwgWy4uLmFsbFN0YXRlTm9kZXNdKTtcbn1cblxuZnVuY3Rpb24gaXNNYWNoaW5lU25hcHNob3QodmFsdWUpIHtcbiAgcmV0dXJuICEhdmFsdWUgJiYgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJiAnbWFjaGluZScgaW4gdmFsdWUgJiYgJ3ZhbHVlJyBpbiB2YWx1ZTtcbn1cbmNvbnN0IG1hY2hpbmVTbmFwc2hvdE1hdGNoZXMgPSBmdW5jdGlvbiBtYXRjaGVzKHRlc3RWYWx1ZSkge1xuICByZXR1cm4gbWF0Y2hlc1N0YXRlKHRlc3RWYWx1ZSwgdGhpcy52YWx1ZSk7XG59O1xuY29uc3QgbWFjaGluZVNuYXBzaG90SGFzVGFnID0gZnVuY3Rpb24gaGFzVGFnKHRhZykge1xuICByZXR1cm4gdGhpcy50YWdzLmhhcyh0YWcpO1xufTtcbmNvbnN0IG1hY2hpbmVTbmFwc2hvdENhbiA9IGZ1bmN0aW9uIGNhbihldmVudCkge1xuICBpZiAoIXRoaXMubWFjaGluZSkge1xuICAgIGNvbnNvbGUud2Fybihgc3RhdGUuY2FuKC4uLikgdXNlZCBvdXRzaWRlIG9mIGEgbWFjaGluZS1jcmVhdGVkIFN0YXRlIG9iamVjdDsgdGhpcyB3aWxsIGFsd2F5cyByZXR1cm4gZmFsc2UuYCk7XG4gIH1cbiAgY29uc3QgdHJhbnNpdGlvbkRhdGEgPSB0aGlzLm1hY2hpbmUuZ2V0VHJhbnNpdGlvbkRhdGEodGhpcywgZXZlbnQpO1xuICByZXR1cm4gISF0cmFuc2l0aW9uRGF0YT8ubGVuZ3RoICYmXG4gIC8vIENoZWNrIHRoYXQgYXQgbGVhc3Qgb25lIHRyYW5zaXRpb24gaXMgbm90IGZvcmJpZGRlblxuICB0cmFuc2l0aW9uRGF0YS5zb21lKHQgPT4gdC50YXJnZXQgIT09IHVuZGVmaW5lZCB8fCB0LmFjdGlvbnMubGVuZ3RoKTtcbn07XG5jb25zdCBtYWNoaW5lU25hcHNob3RUb0pTT04gPSBmdW5jdGlvbiB0b0pTT04oKSB7XG4gIGNvbnN0IHtcbiAgICBfbm9kZXM6IG5vZGVzLFxuICAgIHRhZ3MsXG4gICAgbWFjaGluZSxcbiAgICBnZXRNZXRhLFxuICAgIHRvSlNPTixcbiAgICBjYW4sXG4gICAgaGFzVGFnLFxuICAgIG1hdGNoZXMsXG4gICAgLi4uanNvblZhbHVlc1xuICB9ID0gdGhpcztcbiAgcmV0dXJuIHtcbiAgICAuLi5qc29uVmFsdWVzLFxuICAgIHRhZ3M6IEFycmF5LmZyb20odGFncylcbiAgfTtcbn07XG5jb25zdCBtYWNoaW5lU25hcHNob3RHZXRNZXRhID0gZnVuY3Rpb24gZ2V0TWV0YSgpIHtcbiAgcmV0dXJuIHRoaXMuX25vZGVzLnJlZHVjZSgoYWNjLCBzdGF0ZU5vZGUpID0+IHtcbiAgICBpZiAoc3RhdGVOb2RlLm1ldGEgIT09IHVuZGVmaW5lZCkge1xuICAgICAgYWNjW3N0YXRlTm9kZS5pZF0gPSBzdGF0ZU5vZGUubWV0YTtcbiAgICB9XG4gICAgcmV0dXJuIGFjYztcbiAgfSwge30pO1xufTtcbmZ1bmN0aW9uIGNyZWF0ZU1hY2hpbmVTbmFwc2hvdChjb25maWcsIG1hY2hpbmUpIHtcbiAgcmV0dXJuIHtcbiAgICBzdGF0dXM6IGNvbmZpZy5zdGF0dXMsXG4gICAgb3V0cHV0OiBjb25maWcub3V0cHV0LFxuICAgIGVycm9yOiBjb25maWcuZXJyb3IsXG4gICAgbWFjaGluZSxcbiAgICBjb250ZXh0OiBjb25maWcuY29udGV4dCxcbiAgICBfbm9kZXM6IGNvbmZpZy5fbm9kZXMsXG4gICAgdmFsdWU6IGdldFN0YXRlVmFsdWUobWFjaGluZS5yb290LCBjb25maWcuX25vZGVzKSxcbiAgICB0YWdzOiBuZXcgU2V0KGNvbmZpZy5fbm9kZXMuZmxhdE1hcChzbiA9PiBzbi50YWdzKSksXG4gICAgY2hpbGRyZW46IGNvbmZpZy5jaGlsZHJlbixcbiAgICBoaXN0b3J5VmFsdWU6IGNvbmZpZy5oaXN0b3J5VmFsdWUgfHwge30sXG4gICAgbWF0Y2hlczogbWFjaGluZVNuYXBzaG90TWF0Y2hlcyxcbiAgICBoYXNUYWc6IG1hY2hpbmVTbmFwc2hvdEhhc1RhZyxcbiAgICBjYW46IG1hY2hpbmVTbmFwc2hvdENhbixcbiAgICBnZXRNZXRhOiBtYWNoaW5lU25hcHNob3RHZXRNZXRhLFxuICAgIHRvSlNPTjogbWFjaGluZVNuYXBzaG90VG9KU09OXG4gIH07XG59XG5mdW5jdGlvbiBjbG9uZU1hY2hpbmVTbmFwc2hvdChzbmFwc2hvdCwgY29uZmlnID0ge30pIHtcbiAgcmV0dXJuIGNyZWF0ZU1hY2hpbmVTbmFwc2hvdCh7XG4gICAgLi4uc25hcHNob3QsXG4gICAgLi4uY29uZmlnXG4gIH0sIHNuYXBzaG90Lm1hY2hpbmUpO1xufVxuZnVuY3Rpb24gZ2V0UGVyc2lzdGVkU25hcHNob3Qoc25hcHNob3QsIG9wdGlvbnMpIHtcbiAgY29uc3Qge1xuICAgIF9ub2Rlczogbm9kZXMsXG4gICAgdGFncyxcbiAgICBtYWNoaW5lLFxuICAgIGNoaWxkcmVuLFxuICAgIGNvbnRleHQsXG4gICAgY2FuLFxuICAgIGhhc1RhZyxcbiAgICBtYXRjaGVzLFxuICAgIGdldE1ldGEsXG4gICAgdG9KU09OLFxuICAgIC4uLmpzb25WYWx1ZXNcbiAgfSA9IHNuYXBzaG90O1xuICBjb25zdCBjaGlsZHJlbkpzb24gPSB7fTtcbiAgZm9yIChjb25zdCBpZCBpbiBjaGlsZHJlbikge1xuICAgIGNvbnN0IGNoaWxkID0gY2hpbGRyZW5baWRdO1xuICAgIGlmICh0eXBlb2YgY2hpbGQuc3JjICE9PSAnc3RyaW5nJyAmJiAoIW9wdGlvbnMgfHwgISgnX191bnNhZmVBbGxvd0lubGluZUFjdG9ycycgaW4gb3B0aW9ucykpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0FuIGlubGluZSBjaGlsZCBhY3RvciBjYW5ub3QgYmUgcGVyc2lzdGVkLicpO1xuICAgIH1cbiAgICBjaGlsZHJlbkpzb25baWRdID0ge1xuICAgICAgc25hcHNob3Q6IGNoaWxkLmdldFBlcnNpc3RlZFNuYXBzaG90KG9wdGlvbnMpLFxuICAgICAgc3JjOiBjaGlsZC5zcmMsXG4gICAgICBzeXN0ZW1JZDogY2hpbGQuX3N5c3RlbUlkLFxuICAgICAgc3luY1NuYXBzaG90OiBjaGlsZC5fc3luY1NuYXBzaG90XG4gICAgfTtcbiAgfVxuICBjb25zdCBwZXJzaXN0ZWQgPSB7XG4gICAgLi4uanNvblZhbHVlcyxcbiAgICBjb250ZXh0OiBwZXJzaXN0Q29udGV4dChjb250ZXh0KSxcbiAgICBjaGlsZHJlbjogY2hpbGRyZW5Kc29uXG4gIH07XG4gIHJldHVybiBwZXJzaXN0ZWQ7XG59XG5mdW5jdGlvbiBwZXJzaXN0Q29udGV4dChjb250ZXh0UGFydCkge1xuICBsZXQgY29weTtcbiAgZm9yIChjb25zdCBrZXkgaW4gY29udGV4dFBhcnQpIHtcbiAgICBjb25zdCB2YWx1ZSA9IGNvbnRleHRQYXJ0W2tleV07XG4gICAgaWYgKHZhbHVlICYmIHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCcpIHtcbiAgICAgIGlmICgnc2Vzc2lvbklkJyBpbiB2YWx1ZSAmJiAnc2VuZCcgaW4gdmFsdWUgJiYgJ3JlZicgaW4gdmFsdWUpIHtcbiAgICAgICAgY29weSA/Pz0gQXJyYXkuaXNBcnJheShjb250ZXh0UGFydCkgPyBjb250ZXh0UGFydC5zbGljZSgpIDoge1xuICAgICAgICAgIC4uLmNvbnRleHRQYXJ0XG4gICAgICAgIH07XG4gICAgICAgIGNvcHlba2V5XSA9IHtcbiAgICAgICAgICB4c3RhdGUkJHR5cGU6ICQkQUNUT1JfVFlQRSxcbiAgICAgICAgICBpZDogdmFsdWUuaWRcbiAgICAgICAgfTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IHBlcnNpc3RDb250ZXh0KHZhbHVlKTtcbiAgICAgICAgaWYgKHJlc3VsdCAhPT0gdmFsdWUpIHtcbiAgICAgICAgICBjb3B5ID8/PSBBcnJheS5pc0FycmF5KGNvbnRleHRQYXJ0KSA/IGNvbnRleHRQYXJ0LnNsaWNlKCkgOiB7XG4gICAgICAgICAgICAuLi5jb250ZXh0UGFydFxuICAgICAgICAgIH07XG4gICAgICAgICAgY29weVtrZXldID0gcmVzdWx0O1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBjb3B5ID8/IGNvbnRleHRQYXJ0O1xufVxuXG5mdW5jdGlvbiByZXNvbHZlUmFpc2UoXywgc25hcHNob3QsIGFyZ3MsIGFjdGlvblBhcmFtcywge1xuICBldmVudDogZXZlbnRPckV4cHIsXG4gIGlkLFxuICBkZWxheVxufSwge1xuICBpbnRlcm5hbFF1ZXVlXG59KSB7XG4gIGNvbnN0IGRlbGF5c01hcCA9IHNuYXBzaG90Lm1hY2hpbmUuaW1wbGVtZW50YXRpb25zLmRlbGF5cztcbiAgaWYgKHR5cGVvZiBldmVudE9yRXhwciA9PT0gJ3N0cmluZycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9yZXN0cmljdC10ZW1wbGF0ZS1leHByZXNzaW9uc1xuICAgIGBPbmx5IGV2ZW50IG9iamVjdHMgbWF5IGJlIHVzZWQgd2l0aCByYWlzZTsgdXNlIHJhaXNlKHsgdHlwZTogXCIke2V2ZW50T3JFeHByfVwiIH0pIGluc3RlYWRgKTtcbiAgfVxuICBjb25zdCByZXNvbHZlZEV2ZW50ID0gdHlwZW9mIGV2ZW50T3JFeHByID09PSAnZnVuY3Rpb24nID8gZXZlbnRPckV4cHIoYXJncywgYWN0aW9uUGFyYW1zKSA6IGV2ZW50T3JFeHByO1xuICBsZXQgcmVzb2x2ZWREZWxheTtcbiAgaWYgKHR5cGVvZiBkZWxheSA9PT0gJ3N0cmluZycpIHtcbiAgICBjb25zdCBjb25maWdEZWxheSA9IGRlbGF5c01hcCAmJiBkZWxheXNNYXBbZGVsYXldO1xuICAgIHJlc29sdmVkRGVsYXkgPSB0eXBlb2YgY29uZmlnRGVsYXkgPT09ICdmdW5jdGlvbicgPyBjb25maWdEZWxheShhcmdzLCBhY3Rpb25QYXJhbXMpIDogY29uZmlnRGVsYXk7XG4gIH0gZWxzZSB7XG4gICAgcmVzb2x2ZWREZWxheSA9IHR5cGVvZiBkZWxheSA9PT0gJ2Z1bmN0aW9uJyA/IGRlbGF5KGFyZ3MsIGFjdGlvblBhcmFtcykgOiBkZWxheTtcbiAgfVxuICBpZiAodHlwZW9mIHJlc29sdmVkRGVsYXkgIT09ICdudW1iZXInKSB7XG4gICAgaW50ZXJuYWxRdWV1ZS5wdXNoKHJlc29sdmVkRXZlbnQpO1xuICB9XG4gIHJldHVybiBbc25hcHNob3QsIHtcbiAgICBldmVudDogcmVzb2x2ZWRFdmVudCxcbiAgICBpZCxcbiAgICBkZWxheTogcmVzb2x2ZWREZWxheVxuICB9LCB1bmRlZmluZWRdO1xufVxuZnVuY3Rpb24gZXhlY3V0ZVJhaXNlKGFjdG9yU2NvcGUsIHBhcmFtcykge1xuICBjb25zdCB7XG4gICAgZXZlbnQsXG4gICAgZGVsYXksXG4gICAgaWRcbiAgfSA9IHBhcmFtcztcbiAgaWYgKHR5cGVvZiBkZWxheSA9PT0gJ251bWJlcicpIHtcbiAgICBhY3RvclNjb3BlLmRlZmVyKCgpID0+IHtcbiAgICAgIGNvbnN0IHNlbGYgPSBhY3RvclNjb3BlLnNlbGY7XG4gICAgICBhY3RvclNjb3BlLnN5c3RlbS5zY2hlZHVsZXIuc2NoZWR1bGUoc2VsZiwgc2VsZiwgZXZlbnQsIGRlbGF5LCBpZCk7XG4gICAgfSk7XG4gICAgcmV0dXJuO1xuICB9XG59XG4vKipcbiAqIFJhaXNlcyBhbiBldmVudC4gVGhpcyBwbGFjZXMgdGhlIGV2ZW50IGluIHRoZSBpbnRlcm5hbCBldmVudCBxdWV1ZSwgc28gdGhhdFxuICogdGhlIGV2ZW50IGlzIGltbWVkaWF0ZWx5IGNvbnN1bWVkIGJ5IHRoZSBtYWNoaW5lIGluIHRoZSBjdXJyZW50IHN0ZXAuXG4gKlxuICogQHBhcmFtIGV2ZW50VHlwZSBUaGUgZXZlbnQgdG8gcmFpc2UuXG4gKi9cbmZ1bmN0aW9uIHJhaXNlKGV2ZW50T3JFeHByLCBvcHRpb25zKSB7XG4gIGlmIChleGVjdXRpbmdDdXN0b21BY3Rpb24pIHtcbiAgICBjb25zb2xlLndhcm4oJ0N1c3RvbSBhY3Rpb25zIHNob3VsZCBub3QgY2FsbCBgcmFpc2UoKWAgZGlyZWN0bHksIGFzIGl0IGlzIG5vdCBpbXBlcmF0aXZlLiBTZWUgaHR0cHM6Ly9zdGF0ZWx5LmFpL2RvY3MvYWN0aW9ucyNidWlsdC1pbi1hY3Rpb25zIGZvciBtb3JlIGRldGFpbHMuJyk7XG4gIH1cbiAgZnVuY3Rpb24gcmFpc2UoX2FyZ3MsIF9wYXJhbXMpIHtcbiAgICB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFRoaXMgaXNuJ3Qgc3VwcG9zZWQgdG8gYmUgY2FsbGVkYCk7XG4gICAgfVxuICB9XG4gIHJhaXNlLnR5cGUgPSAneHN0YXRlLnJhaXNlJztcbiAgcmFpc2UuZXZlbnQgPSBldmVudE9yRXhwcjtcbiAgcmFpc2UuaWQgPSBvcHRpb25zPy5pZDtcbiAgcmFpc2UuZGVsYXkgPSBvcHRpb25zPy5kZWxheTtcbiAgcmFpc2UucmVzb2x2ZSA9IHJlc29sdmVSYWlzZTtcbiAgcmFpc2UuZXhlY3V0ZSA9IGV4ZWN1dGVSYWlzZTtcbiAgcmV0dXJuIHJhaXNlO1xufVxuXG5leHBvcnQgeyAkJEFDVE9SX1RZUEUgYXMgJCwgY3JlYXRlQWN0b3IgYXMgQSwgQWN0b3IgYXMgQiwgaW50ZXJwcmV0IGFzIEMsIGFuZCBhcyBELCBub3QgYXMgRSwgb3IgYXMgRiwgc3RhdGVJbiBhcyBHLCBpc01hY2hpbmVTbmFwc2hvdCBhcyBILCBnZXRBbGxPd25FdmVudERlc2NyaXB0b3JzIGFzIEksIG1hdGNoZXNTdGF0ZSBhcyBKLCBwYXRoVG9TdGF0ZVZhbHVlIGFzIEssIHRvT2JzZXJ2ZXIgYXMgTCwgY2FuY2VsIGFzIE0sIE5VTExfRVZFTlQgYXMgTiwgcmFpc2UgYXMgTywgc3Bhd25DaGlsZCBhcyBQLCBzdG9wIGFzIFEsIHN0b3BDaGlsZCBhcyBSLCBTVEFURV9ERUxJTUlURVIgYXMgUywgUHJvY2Vzc2luZ1N0YXR1cyBhcyBULCBjbG9uZU1hY2hpbmVTbmFwc2hvdCBhcyBVLCBleGVjdXRpbmdDdXN0b21BY3Rpb24gYXMgViwgWFNUQVRFX0VSUk9SIGFzIFcsIFhTVEFURV9TVE9QIGFzIFgsIGNyZWF0ZUVycm9yQWN0b3JFdmVudCBhcyBZLCB0b1RyYW5zaXRpb25Db25maWdBcnJheSBhcyBhLCBmb3JtYXRUcmFuc2l0aW9uIGFzIGIsIGNyZWF0ZUludm9rZUlkIGFzIGMsIGZvcm1hdEluaXRpYWxUcmFuc2l0aW9uIGFzIGQsIGV2YWx1YXRlR3VhcmQgYXMgZSwgZm9ybWF0VHJhbnNpdGlvbnMgYXMgZiwgZ2V0RGVsYXllZFRyYW5zaXRpb25zIGFzIGcsIGdldENhbmRpZGF0ZXMgYXMgaCwgZ2V0QWxsU3RhdGVOb2RlcyBhcyBpLCBnZXRTdGF0ZU5vZGVzIGFzIGosIGNyZWF0ZU1hY2hpbmVTbmFwc2hvdCBhcyBrLCBpc0luRmluYWxTdGF0ZSBhcyBsLCBtYXBWYWx1ZXMgYXMgbSwgbWFjcm9zdGVwIGFzIG4sIHRyYW5zaXRpb25Ob2RlIGFzIG8sIHJlc29sdmVBY3Rpb25zQW5kQ29udGV4dCBhcyBwLCBjcmVhdGVJbml0RXZlbnQgYXMgcSwgcmVzb2x2ZVN0YXRlVmFsdWUgYXMgciwgbWljcm9zdGVwIGFzIHMsIHRvQXJyYXkgYXMgdCwgZ2V0SW5pdGlhbFN0YXRlTm9kZXMgYXMgdSwgdG9TdGF0ZVBhdGggYXMgdiwgaXNTdGF0ZUlkIGFzIHcsIGdldFN0YXRlTm9kZUJ5UGF0aCBhcyB4LCBnZXRQZXJzaXN0ZWRTbmFwc2hvdCBhcyB5LCByZXNvbHZlUmVmZXJlbmNlZEFjdG9yIGFzIHogfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xstate/dist/raise-1db27a82.development.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xstate/dist/xstate.development.esm.js":
/*!************************************************************!*\
  !*** ./node_modules/xstate/dist/xstate.development.esm.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Actor: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.B),\n/* harmony export */   SimulatedClock: () => (/* binding */ SimulatedClock),\n/* harmony export */   SpecialTargets: () => (/* reexport safe */ _log_0acd9069_development_esm_js__WEBPACK_IMPORTED_MODULE_2__.S),\n/* harmony export */   StateMachine: () => (/* binding */ StateMachine),\n/* harmony export */   StateNode: () => (/* binding */ StateNode),\n/* harmony export */   __unsafe_getAllOwnEventDescriptors: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.I),\n/* harmony export */   and: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.D),\n/* harmony export */   assertEvent: () => (/* binding */ assertEvent),\n/* harmony export */   assign: () => (/* reexport safe */ _log_0acd9069_development_esm_js__WEBPACK_IMPORTED_MODULE_2__.a),\n/* harmony export */   cancel: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.M),\n/* harmony export */   createActor: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.A),\n/* harmony export */   createEmptyActor: () => (/* reexport safe */ _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.createEmptyActor),\n/* harmony export */   createMachine: () => (/* binding */ createMachine),\n/* harmony export */   emit: () => (/* reexport safe */ _log_0acd9069_development_esm_js__WEBPACK_IMPORTED_MODULE_2__.e),\n/* harmony export */   enqueueActions: () => (/* reexport safe */ _log_0acd9069_development_esm_js__WEBPACK_IMPORTED_MODULE_2__.b),\n/* harmony export */   forwardTo: () => (/* reexport safe */ _log_0acd9069_development_esm_js__WEBPACK_IMPORTED_MODULE_2__.f),\n/* harmony export */   fromCallback: () => (/* reexport safe */ _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.fromCallback),\n/* harmony export */   fromEventObservable: () => (/* reexport safe */ _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.fromEventObservable),\n/* harmony export */   fromObservable: () => (/* reexport safe */ _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.fromObservable),\n/* harmony export */   fromPromise: () => (/* reexport safe */ _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.fromPromise),\n/* harmony export */   fromTransition: () => (/* reexport safe */ _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__.fromTransition),\n/* harmony export */   getInitialSnapshot: () => (/* binding */ getInitialSnapshot),\n/* harmony export */   getNextSnapshot: () => (/* binding */ getNextSnapshot),\n/* harmony export */   getStateNodes: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.j),\n/* harmony export */   initialTransition: () => (/* binding */ initialTransition),\n/* harmony export */   interpret: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.C),\n/* harmony export */   isMachineSnapshot: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.H),\n/* harmony export */   log: () => (/* reexport safe */ _log_0acd9069_development_esm_js__WEBPACK_IMPORTED_MODULE_2__.l),\n/* harmony export */   matchesState: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.J),\n/* harmony export */   not: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.E),\n/* harmony export */   or: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.F),\n/* harmony export */   pathToStateValue: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.K),\n/* harmony export */   raise: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.O),\n/* harmony export */   sendParent: () => (/* reexport safe */ _log_0acd9069_development_esm_js__WEBPACK_IMPORTED_MODULE_2__.s),\n/* harmony export */   sendTo: () => (/* reexport safe */ _log_0acd9069_development_esm_js__WEBPACK_IMPORTED_MODULE_2__.c),\n/* harmony export */   setup: () => (/* binding */ setup),\n/* harmony export */   spawnChild: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.P),\n/* harmony export */   stateIn: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.G),\n/* harmony export */   stop: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.Q),\n/* harmony export */   stopChild: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.R),\n/* harmony export */   toObserver: () => (/* reexport safe */ _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.L),\n/* harmony export */   toPromise: () => (/* binding */ toPromise),\n/* harmony export */   transition: () => (/* binding */ transition),\n/* harmony export */   waitFor: () => (/* binding */ waitFor)\n/* harmony export */ });\n/* harmony import */ var _actors_dist_xstate_actors_development_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../actors/dist/xstate-actors.development.esm.js */ \"(ssr)/./node_modules/xstate/actors/dist/xstate-actors.development.esm.js\");\n/* harmony import */ var _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./raise-1db27a82.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/raise-1db27a82.development.esm.js\");\n/* harmony import */ var _log_0acd9069_development_esm_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./log-0acd9069.development.esm.js */ \"(ssr)/./node_modules/xstate/dist/log-0acd9069.development.esm.js\");\n/* harmony import */ var _dev_dist_xstate_dev_development_esm_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../dev/dist/xstate-dev.development.esm.js */ \"(ssr)/./node_modules/xstate/dev/dist/xstate-dev.development.esm.js\");\n\n\n\n\n\n\n\n/**\n * Asserts that the given event object is of the specified type or types. Throws\n * an error if the event object is not of the specified types.\n *\n * @example\n *\n * ```ts\n * // ...\n * entry: ({ event }) => {\n *   assertEvent(event, 'doNothing');\n *   // event is { type: 'doNothing' }\n * },\n * // ...\n * exit: ({ event }) => {\n *   assertEvent(event, 'greet');\n *   // event is { type: 'greet'; message: string }\n *\n *   assertEvent(event, ['greet', 'notify']);\n *   // event is { type: 'greet'; message: string }\n *   // or { type: 'notify'; message: string; level: 'info' | 'error' }\n * },\n * ```\n */\nfunction assertEvent(event, type) {\n  const types = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.t)(type);\n  if (!types.includes(event.type)) {\n    const typesText = types.length === 1 ? `type \"${types[0]}\"` : `one of types \"${types.join('\", \"')}\"`;\n    throw new Error(`Expected event ${JSON.stringify(event)} to have ${typesText}`);\n  }\n}\n\nconst cache = new WeakMap();\nfunction memo(object, key, fn) {\n  let memoizedData = cache.get(object);\n  if (!memoizedData) {\n    memoizedData = {\n      [key]: fn()\n    };\n    cache.set(object, memoizedData);\n  } else if (!(key in memoizedData)) {\n    memoizedData[key] = fn();\n  }\n  return memoizedData[key];\n}\n\nconst EMPTY_OBJECT = {};\nconst toSerializableAction = action => {\n  if (typeof action === 'string') {\n    return {\n      type: action\n    };\n  }\n  if (typeof action === 'function') {\n    if ('resolve' in action) {\n      return {\n        type: action.type\n      };\n    }\n    return {\n      type: action.name\n    };\n  }\n  return action;\n};\nclass StateNode {\n  constructor(/** The raw config used to create the machine. */\n  config, options) {\n    this.config = config;\n    /**\n     * The relative key of the state node, which represents its location in the\n     * overall state value.\n     */\n    this.key = void 0;\n    /** The unique ID of the state node. */\n    this.id = void 0;\n    /**\n     * The type of this state node:\n     *\n     * - `'atomic'` - no child state nodes\n     * - `'compound'` - nested child state nodes (XOR)\n     * - `'parallel'` - orthogonal nested child state nodes (AND)\n     * - `'history'` - history state node\n     * - `'final'` - final state node\n     */\n    this.type = void 0;\n    /** The string path from the root machine node to this node. */\n    this.path = void 0;\n    /** The child state nodes. */\n    this.states = void 0;\n    /**\n     * The type of history on this state node. Can be:\n     *\n     * - `'shallow'` - recalls only top-level historical state value\n     * - `'deep'` - recalls historical state value at all levels\n     */\n    this.history = void 0;\n    /** The action(s) to be executed upon entering the state node. */\n    this.entry = void 0;\n    /** The action(s) to be executed upon exiting the state node. */\n    this.exit = void 0;\n    /** The parent state node. */\n    this.parent = void 0;\n    /** The root machine node. */\n    this.machine = void 0;\n    /**\n     * The meta data associated with this state node, which will be returned in\n     * State instances.\n     */\n    this.meta = void 0;\n    /**\n     * The output data sent with the \"xstate.done.state._id_\" event if this is a\n     * final state node.\n     */\n    this.output = void 0;\n    /**\n     * The order this state node appears. Corresponds to the implicit document\n     * order.\n     */\n    this.order = -1;\n    this.description = void 0;\n    this.tags = [];\n    this.transitions = void 0;\n    this.always = void 0;\n    this.parent = options._parent;\n    this.key = options._key;\n    this.machine = options._machine;\n    this.path = this.parent ? this.parent.path.concat(this.key) : [];\n    this.id = this.config.id || [this.machine.id, ...this.path].join(_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.S);\n    this.type = this.config.type || (this.config.states && Object.keys(this.config.states).length ? 'compound' : this.config.history ? 'history' : 'atomic');\n    this.description = this.config.description;\n    this.order = this.machine.idMap.size;\n    this.machine.idMap.set(this.id, this);\n    this.states = this.config.states ? (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.m)(this.config.states, (stateConfig, key) => {\n      const stateNode = new StateNode(stateConfig, {\n        _parent: this,\n        _key: key,\n        _machine: this.machine\n      });\n      return stateNode;\n    }) : EMPTY_OBJECT;\n    if (this.type === 'compound' && !this.config.initial) {\n      throw new Error(`No initial state specified for compound state node \"#${this.id}\". Try adding { initial: \"${Object.keys(this.states)[0]}\" } to the state config.`);\n    }\n\n    // History config\n    this.history = this.config.history === true ? 'shallow' : this.config.history || false;\n    this.entry = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.t)(this.config.entry).slice();\n    this.exit = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.t)(this.config.exit).slice();\n    this.meta = this.config.meta;\n    this.output = this.type === 'final' || !this.parent ? this.config.output : undefined;\n    this.tags = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.t)(config.tags).slice();\n  }\n\n  /** @internal */\n  _initialize() {\n    this.transitions = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.f)(this);\n    if (this.config.always) {\n      this.always = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.a)(this.config.always).map(t => (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.b)(this, _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.N, t));\n    }\n    Object.keys(this.states).forEach(key => {\n      this.states[key]._initialize();\n    });\n  }\n\n  /** The well-structured state node definition. */\n  get definition() {\n    return {\n      id: this.id,\n      key: this.key,\n      version: this.machine.version,\n      type: this.type,\n      initial: this.initial ? {\n        target: this.initial.target,\n        source: this,\n        actions: this.initial.actions.map(toSerializableAction),\n        eventType: null,\n        reenter: false,\n        toJSON: () => ({\n          target: this.initial.target.map(t => `#${t.id}`),\n          source: `#${this.id}`,\n          actions: this.initial.actions.map(toSerializableAction),\n          eventType: null\n        })\n      } : undefined,\n      history: this.history,\n      states: (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.m)(this.states, state => {\n        return state.definition;\n      }),\n      on: this.on,\n      transitions: [...this.transitions.values()].flat().map(t => ({\n        ...t,\n        actions: t.actions.map(toSerializableAction)\n      })),\n      entry: this.entry.map(toSerializableAction),\n      exit: this.exit.map(toSerializableAction),\n      meta: this.meta,\n      order: this.order || -1,\n      output: this.output,\n      invoke: this.invoke,\n      description: this.description,\n      tags: this.tags\n    };\n  }\n\n  /** @internal */\n  toJSON() {\n    return this.definition;\n  }\n\n  /** The logic invoked as actors by this state node. */\n  get invoke() {\n    return memo(this, 'invoke', () => (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.t)(this.config.invoke).map((invokeConfig, i) => {\n      const {\n        src,\n        systemId\n      } = invokeConfig;\n      const resolvedId = invokeConfig.id ?? (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.c)(this.id, i);\n      const sourceName = typeof src === 'string' ? src : `xstate.invoke.${(0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.c)(this.id, i)}`;\n      return {\n        ...invokeConfig,\n        src: sourceName,\n        id: resolvedId,\n        systemId: systemId,\n        toJSON() {\n          const {\n            onDone,\n            onError,\n            ...invokeDefValues\n          } = invokeConfig;\n          return {\n            ...invokeDefValues,\n            type: 'xstate.invoke',\n            src: sourceName,\n            id: resolvedId\n          };\n        }\n      };\n    }));\n  }\n\n  /** The mapping of events to transitions. */\n  get on() {\n    return memo(this, 'on', () => {\n      const transitions = this.transitions;\n      return [...transitions].flatMap(([descriptor, t]) => t.map(t => [descriptor, t])).reduce((map, [descriptor, transition]) => {\n        map[descriptor] = map[descriptor] || [];\n        map[descriptor].push(transition);\n        return map;\n      }, {});\n    });\n  }\n  get after() {\n    return memo(this, 'delayedTransitions', () => (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.g)(this));\n  }\n  get initial() {\n    return memo(this, 'initial', () => (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.d)(this, this.config.initial));\n  }\n\n  /** @internal */\n  next(snapshot, event) {\n    const eventType = event.type;\n    const actions = [];\n    let selectedTransition;\n    const candidates = memo(this, `candidates-${eventType}`, () => (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.h)(this, eventType));\n    for (const candidate of candidates) {\n      const {\n        guard\n      } = candidate;\n      const resolvedContext = snapshot.context;\n      let guardPassed = false;\n      try {\n        guardPassed = !guard || (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.e)(guard, resolvedContext, event, snapshot);\n      } catch (err) {\n        const guardType = typeof guard === 'string' ? guard : typeof guard === 'object' ? guard.type : undefined;\n        throw new Error(`Unable to evaluate guard ${guardType ? `'${guardType}' ` : ''}in transition for event '${eventType}' in state node '${this.id}':\\n${err.message}`);\n      }\n      if (guardPassed) {\n        actions.push(...candidate.actions);\n        selectedTransition = candidate;\n        break;\n      }\n    }\n    return selectedTransition ? [selectedTransition] : undefined;\n  }\n\n  /** All the event types accepted by this state node and its descendants. */\n  get events() {\n    return memo(this, 'events', () => {\n      const {\n        states\n      } = this;\n      const events = new Set(this.ownEvents);\n      if (states) {\n        for (const stateId of Object.keys(states)) {\n          const state = states[stateId];\n          if (state.states) {\n            for (const event of state.events) {\n              events.add(`${event}`);\n            }\n          }\n        }\n      }\n      return Array.from(events);\n    });\n  }\n\n  /**\n   * All the events that have transitions directly from this state node.\n   *\n   * Excludes any inert events.\n   */\n  get ownEvents() {\n    const events = new Set([...this.transitions.keys()].filter(descriptor => {\n      return this.transitions.get(descriptor).some(transition => !(!transition.target && !transition.actions.length && !transition.reenter));\n    }));\n    return Array.from(events);\n  }\n}\n\nconst STATE_IDENTIFIER = '#';\nclass StateMachine {\n  constructor(/** The raw config used to create the machine. */\n  config, implementations) {\n    this.config = config;\n    /** The machine's own version. */\n    this.version = void 0;\n    this.schemas = void 0;\n    this.implementations = void 0;\n    /** @internal */\n    this.__xstatenode = true;\n    /** @internal */\n    this.idMap = new Map();\n    this.root = void 0;\n    this.id = void 0;\n    this.states = void 0;\n    this.events = void 0;\n    this.id = config.id || '(machine)';\n    this.implementations = {\n      actors: implementations?.actors ?? {},\n      actions: implementations?.actions ?? {},\n      delays: implementations?.delays ?? {},\n      guards: implementations?.guards ?? {}\n    };\n    this.version = this.config.version;\n    this.schemas = this.config.schemas;\n    this.transition = this.transition.bind(this);\n    this.getInitialSnapshot = this.getInitialSnapshot.bind(this);\n    this.getPersistedSnapshot = this.getPersistedSnapshot.bind(this);\n    this.restoreSnapshot = this.restoreSnapshot.bind(this);\n    this.start = this.start.bind(this);\n    this.root = new StateNode(config, {\n      _key: this.id,\n      _machine: this\n    });\n    this.root._initialize();\n    this.states = this.root.states; // TODO: remove!\n    this.events = this.root.events;\n    if (!('output' in this.root) && Object.values(this.states).some(state => state.type === 'final' && 'output' in state)) {\n      console.warn('Missing `machine.output` declaration (top-level final state with output detected)');\n    }\n  }\n\n  /**\n   * Clones this state machine with the provided implementations and merges the\n   * `context` (if provided).\n   *\n   * @param implementations Options (`actions`, `guards`, `actors`, `delays`,\n   *   `context`) to recursively merge with the existing options.\n   * @returns A new `StateMachine` instance with the provided implementations.\n   */\n  provide(implementations) {\n    const {\n      actions,\n      guards,\n      actors,\n      delays\n    } = this.implementations;\n    return new StateMachine(this.config, {\n      actions: {\n        ...actions,\n        ...implementations.actions\n      },\n      guards: {\n        ...guards,\n        ...implementations.guards\n      },\n      actors: {\n        ...actors,\n        ...implementations.actors\n      },\n      delays: {\n        ...delays,\n        ...implementations.delays\n      }\n    });\n  }\n  resolveState(config) {\n    const resolvedStateValue = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.r)(this.root, config.value);\n    const nodeSet = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.i)((0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.j)(this.root, resolvedStateValue));\n    return (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.k)({\n      _nodes: [...nodeSet],\n      context: config.context || {},\n      children: {},\n      status: (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.l)(nodeSet, this.root) ? 'done' : config.status || 'active',\n      output: config.output,\n      error: config.error,\n      historyValue: config.historyValue\n    }, this);\n  }\n\n  /**\n   * Determines the next snapshot given the current `snapshot` and received\n   * `event`. Calculates a full macrostep from all microsteps.\n   *\n   * @param snapshot The current snapshot\n   * @param event The received event\n   */\n  transition(snapshot, event, actorScope) {\n    return (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.n)(snapshot, event, actorScope, []).snapshot;\n  }\n\n  /**\n   * Determines the next state given the current `state` and `event`. Calculates\n   * a microstep.\n   *\n   * @param state The current state\n   * @param event The received event\n   */\n  microstep(snapshot, event, actorScope) {\n    return (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.n)(snapshot, event, actorScope, []).microstates;\n  }\n  getTransitionData(snapshot, event) {\n    return (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.o)(this.root, snapshot.value, snapshot, event) || [];\n  }\n\n  /**\n   * The initial state _before_ evaluating any microsteps. This \"pre-initial\"\n   * state is provided to initial actions executed in the initial state.\n   */\n  getPreInitialState(actorScope, initEvent, internalQueue) {\n    const {\n      context\n    } = this.config;\n    const preInitial = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.k)({\n      context: typeof context !== 'function' && context ? context : {},\n      _nodes: [this.root],\n      children: {},\n      status: 'active'\n    }, this);\n    if (typeof context === 'function') {\n      const assignment = ({\n        spawn,\n        event,\n        self\n      }) => context({\n        spawn,\n        input: event.input,\n        self\n      });\n      return (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.p)(preInitial, initEvent, actorScope, [(0,_log_0acd9069_development_esm_js__WEBPACK_IMPORTED_MODULE_2__.a)(assignment)], internalQueue, undefined);\n    }\n    return preInitial;\n  }\n\n  /**\n   * Returns the initial `State` instance, with reference to `self` as an\n   * `ActorRef`.\n   */\n  getInitialSnapshot(actorScope, input) {\n    const initEvent = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.q)(input); // TODO: fix;\n    const internalQueue = [];\n    const preInitialState = this.getPreInitialState(actorScope, initEvent, internalQueue);\n    const nextState = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.s)([{\n      target: [...(0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.u)(this.root)],\n      source: this.root,\n      reenter: true,\n      actions: [],\n      eventType: null,\n      toJSON: null // TODO: fix\n    }], preInitialState, actorScope, initEvent, true, internalQueue);\n    const {\n      snapshot: macroState\n    } = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.n)(nextState, initEvent, actorScope, internalQueue);\n    return macroState;\n  }\n  start(snapshot) {\n    Object.values(snapshot.children).forEach(child => {\n      if (child.getSnapshot().status === 'active') {\n        child.start();\n      }\n    });\n  }\n  getStateNodeById(stateId) {\n    const fullPath = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.v)(stateId);\n    const relativePath = fullPath.slice(1);\n    const resolvedStateId = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.w)(fullPath[0]) ? fullPath[0].slice(STATE_IDENTIFIER.length) : fullPath[0];\n    const stateNode = this.idMap.get(resolvedStateId);\n    if (!stateNode) {\n      throw new Error(`Child state node '#${resolvedStateId}' does not exist on machine '${this.id}'`);\n    }\n    return (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.x)(stateNode, relativePath);\n  }\n  get definition() {\n    return this.root.definition;\n  }\n  toJSON() {\n    return this.definition;\n  }\n  getPersistedSnapshot(snapshot, options) {\n    return (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.y)(snapshot, options);\n  }\n  restoreSnapshot(snapshot, _actorScope) {\n    const children = {};\n    const snapshotChildren = snapshot.children;\n    Object.keys(snapshotChildren).forEach(actorId => {\n      const actorData = snapshotChildren[actorId];\n      const childState = actorData.snapshot;\n      const src = actorData.src;\n      const logic = typeof src === 'string' ? (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.z)(this, src) : src;\n      if (!logic) {\n        return;\n      }\n      const actorRef = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.A)(logic, {\n        id: actorId,\n        parent: _actorScope.self,\n        syncSnapshot: actorData.syncSnapshot,\n        snapshot: childState,\n        src,\n        systemId: actorData.systemId\n      });\n      children[actorId] = actorRef;\n    });\n    const restoredSnapshot = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.k)({\n      ...snapshot,\n      children,\n      _nodes: Array.from((0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.i)((0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.j)(this.root, snapshot.value)))\n    }, this);\n    const seen = new Set();\n    function reviveContext(contextPart, children) {\n      if (seen.has(contextPart)) {\n        return;\n      }\n      seen.add(contextPart);\n      for (const key in contextPart) {\n        const value = contextPart[key];\n        if (value && typeof value === 'object') {\n          if ('xstate$$type' in value && value.xstate$$type === _raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.$) {\n            contextPart[key] = children[value.id];\n            continue;\n          }\n          reviveContext(value, children);\n        }\n      }\n    }\n    reviveContext(restoredSnapshot.context, children);\n    return restoredSnapshot;\n  }\n}\n\n/**\n * Creates a state machine (statechart) with the given configuration.\n *\n * The state machine represents the pure logic of a state machine actor.\n *\n * @example\n *\n * ```ts\n * import { createMachine } from 'xstate';\n *\n * const lightMachine = createMachine({\n *   id: 'light',\n *   initial: 'green',\n *   states: {\n *     green: {\n *       on: {\n *         TIMER: { target: 'yellow' }\n *       }\n *     },\n *     yellow: {\n *       on: {\n *         TIMER: { target: 'red' }\n *       }\n *     },\n *     red: {\n *       on: {\n *         TIMER: { target: 'green' }\n *       }\n *     }\n *   }\n * });\n *\n * const lightActor = createActor(lightMachine);\n * lightActor.start();\n *\n * lightActor.send({ type: 'TIMER' });\n * ```\n *\n * @param config The state machine configuration.\n * @param options DEPRECATED: use `setup({ ... })` or `machine.provide({ ... })`\n *   to provide machine implementations instead.\n */\nfunction createMachine(config, implementations) {\n  return new StateMachine(config, implementations);\n}\n\n/** @internal */\nfunction createInertActorScope(actorLogic) {\n  const self = (0,_raise_1db27a82_development_esm_js__WEBPACK_IMPORTED_MODULE_1__.A)(actorLogic);\n  const inertActorScope = {\n    self,\n    defer: () => {},\n    id: '',\n    logger: () => {},\n    sessionId: '',\n    stopChild: () => {},\n    system: self.system,\n    emit: () => {},\n    actionExecutor: () => {}\n  };\n  return inertActorScope;\n}\n\n/** @deprecated Use `initialTransition(…)` instead. */\nfunction getInitialSnapshot(actorLogic, ...[input]) {\n  const actorScope = createInertActorScope(actorLogic);\n  return actorLogic.getInitialSnapshot(actorScope, input);\n}\n\n/**\n * Determines the next snapshot for the given `actorLogic` based on the given\n * `snapshot` and `event`.\n *\n * If the `snapshot` is `undefined`, the initial snapshot of the `actorLogic` is\n * used.\n *\n * @deprecated Use `transition(…)` instead.\n * @example\n *\n * ```ts\n * import { getNextSnapshot } from 'xstate';\n * import { trafficLightMachine } from './trafficLightMachine.ts';\n *\n * const nextSnapshot = getNextSnapshot(\n *   trafficLightMachine, // actor logic\n *   undefined, // snapshot (or initial state if undefined)\n *   { type: 'TIMER' }\n * ); // event object\n *\n * console.log(nextSnapshot.value);\n * // => 'yellow'\n *\n * const nextSnapshot2 = getNextSnapshot(\n *   trafficLightMachine, // actor logic\n *   nextSnapshot, // snapshot\n *   { type: 'TIMER' }\n * ); // event object\n *\n * console.log(nextSnapshot2.value);\n * // =>'red'\n * ```\n */\nfunction getNextSnapshot(actorLogic, snapshot, event) {\n  const inertActorScope = createInertActorScope(actorLogic);\n  inertActorScope.self._snapshot = snapshot;\n  return actorLogic.transition(snapshot, event, inertActorScope);\n}\n\n// at the moment we allow extra actors - ones that are not specified by `children`\n// this could be reconsidered in the future\n\nfunction setup({\n  schemas,\n  actors,\n  actions,\n  guards,\n  delays\n}) {\n  return {\n    createMachine: config => createMachine({\n      ...config,\n      schemas\n    }, {\n      actors,\n      actions,\n      guards,\n      delays\n    })\n  };\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging\nclass SimulatedClock {\n  constructor() {\n    this.timeouts = new Map();\n    this._now = 0;\n    this._id = 0;\n    this._flushing = false;\n    this._flushingInvalidated = false;\n  }\n  now() {\n    return this._now;\n  }\n  getId() {\n    return this._id++;\n  }\n  setTimeout(fn, timeout) {\n    this._flushingInvalidated = this._flushing;\n    const id = this.getId();\n    this.timeouts.set(id, {\n      start: this.now(),\n      timeout,\n      fn\n    });\n    return id;\n  }\n  clearTimeout(id) {\n    this._flushingInvalidated = this._flushing;\n    this.timeouts.delete(id);\n  }\n  set(time) {\n    if (this._now > time) {\n      throw new Error('Unable to travel back in time');\n    }\n    this._now = time;\n    this.flushTimeouts();\n  }\n  flushTimeouts() {\n    if (this._flushing) {\n      this._flushingInvalidated = true;\n      return;\n    }\n    this._flushing = true;\n    const sorted = [...this.timeouts].sort(([_idA, timeoutA], [_idB, timeoutB]) => {\n      const endA = timeoutA.start + timeoutA.timeout;\n      const endB = timeoutB.start + timeoutB.timeout;\n      return endB > endA ? -1 : 1;\n    });\n    for (const [id, timeout] of sorted) {\n      if (this._flushingInvalidated) {\n        this._flushingInvalidated = false;\n        this._flushing = false;\n        this.flushTimeouts();\n        return;\n      }\n      if (this.now() - timeout.start >= timeout.timeout) {\n        this.timeouts.delete(id);\n        timeout.fn.call(null);\n      }\n    }\n    this._flushing = false;\n  }\n  increment(ms) {\n    this._now += ms;\n    this.flushTimeouts();\n  }\n}\n\n/**\n * Returns a promise that resolves to the `output` of the actor when it is done.\n *\n * @example\n *\n * ```ts\n * const machine = createMachine({\n *   // ...\n *   output: {\n *     count: 42\n *   }\n * });\n *\n * const actor = createActor(machine);\n *\n * actor.start();\n *\n * const output = await toPromise(actor);\n *\n * console.log(output);\n * // logs { count: 42 }\n * ```\n */\nfunction toPromise(actor) {\n  return new Promise((resolve, reject) => {\n    actor.subscribe({\n      complete: () => {\n        resolve(actor.getSnapshot().output);\n      },\n      error: reject\n    });\n  });\n}\n\n/**\n * Given actor `logic`, a `snapshot`, and an `event`, returns a tuple of the\n * `nextSnapshot` and `actions` to execute.\n *\n * This is a pure function that does not execute `actions`.\n */\nfunction transition(logic, snapshot, event) {\n  const executableActions = [];\n  const actorScope = createInertActorScope(logic);\n  actorScope.actionExecutor = action => {\n    executableActions.push(action);\n  };\n  const nextSnapshot = logic.transition(snapshot, event, actorScope);\n  return [nextSnapshot, executableActions];\n}\n\n/**\n * Given actor `logic` and optional `input`, returns a tuple of the\n * `nextSnapshot` and `actions` to execute from the initial transition (no\n * previous state).\n *\n * This is a pure function that does not execute `actions`.\n */\nfunction initialTransition(logic, ...[input]) {\n  const executableActions = [];\n  const actorScope = createInertActorScope(logic);\n  actorScope.actionExecutor = action => {\n    executableActions.push(action);\n  };\n  const nextSnapshot = logic.getInitialSnapshot(actorScope, input);\n  return [nextSnapshot, executableActions];\n}\n\nconst defaultWaitForOptions = {\n  timeout: Infinity // much more than 10 seconds\n};\n\n/**\n * Subscribes to an actor ref and waits for its emitted value to satisfy a\n * predicate, and then resolves with that value. Will throw if the desired state\n * is not reached after an optional timeout. (defaults to Infinity).\n *\n * @example\n *\n * ```js\n * const state = await waitFor(someService, (state) => {\n *   return state.hasTag('loaded');\n * });\n *\n * state.hasTag('loaded'); // true\n * ```\n *\n * @param actorRef The actor ref to subscribe to\n * @param predicate Determines if a value matches the condition to wait for\n * @param options\n * @returns A promise that eventually resolves to the emitted value that matches\n *   the condition\n */\nfunction waitFor(actorRef, predicate, options) {\n  const resolvedOptions = {\n    ...defaultWaitForOptions,\n    ...options\n  };\n  return new Promise((res, rej) => {\n    const {\n      signal\n    } = resolvedOptions;\n    if (signal?.aborted) {\n      // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\n      rej(signal.reason);\n      return;\n    }\n    let done = false;\n    if (resolvedOptions.timeout < 0) {\n      console.error('`timeout` passed to `waitFor` is negative and it will reject its internal promise immediately.');\n    }\n    const handle = resolvedOptions.timeout === Infinity ? undefined : setTimeout(() => {\n      dispose();\n      rej(new Error(`Timeout of ${resolvedOptions.timeout} ms exceeded`));\n    }, resolvedOptions.timeout);\n    const dispose = () => {\n      clearTimeout(handle);\n      done = true;\n      sub?.unsubscribe();\n      if (abortListener) {\n        signal.removeEventListener('abort', abortListener);\n      }\n    };\n    function checkEmitted(emitted) {\n      if (predicate(emitted)) {\n        dispose();\n        res(emitted);\n      }\n    }\n\n    /**\n     * If the `signal` option is provided, this will be the listener for its\n     * `abort` event\n     */\n    let abortListener;\n    // eslint-disable-next-line prefer-const\n    let sub; // avoid TDZ when disposing synchronously\n\n    // See if the current snapshot already matches the predicate\n    checkEmitted(actorRef.getSnapshot());\n    if (done) {\n      return;\n    }\n\n    // only define the `abortListener` if the `signal` option is provided\n    if (signal) {\n      abortListener = () => {\n        dispose();\n        // XState does not \"own\" the signal, so we should reject with its reason (if any)\n        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\n        rej(signal.reason);\n      };\n      signal.addEventListener('abort', abortListener);\n    }\n    sub = actorRef.subscribe({\n      next: checkEmitted,\n      error: err => {\n        dispose();\n        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\n        rej(err);\n      },\n      complete: () => {\n        dispose();\n        rej(new Error(`Actor terminated without satisfying predicate`));\n      }\n    });\n    if (done) {\n      sub.unsubscribe();\n    }\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xstate/dist/xstate.development.esm.js\n");

/***/ })

};
;