"use client"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { Form } from '@/components/ui/form'
import { z } from "zod"
import { useForm } from 'react-hook-form'
import DefaultInput from "@/components/input-form/default-input";
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useLoginSeekerFormSchema } from "../../(auth)/form/use-login-form.schema"
import { useTranslations } from "next-intl"
import { useLogin } from "@/core/applications/mutations/auth/use-login"

import { LoginDto } from "@/core/infrastructures/auth/dto"

import { checkIfEmail } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"
import PasswordInput from "@/components/input-form/password-input"
import SeekersSocialAuthentication from "./seekers-social-authentication"
import { Separator } from "@/components/ui/separator"
import RecaptchaWrapper from "@/components/recaptcha/recaptcha"


export function SeekerLoginForm(
  { isDialog, onClickSignUp, onClickResetPassword }:
    {
      isDialog?: boolean,
      onClickSignUp?: () => void,
      onClickResetPassword?: () => void
    }) {
  const t = useTranslations("seeker")
  const formSchema = useLoginSeekerFormSchema()
  const useLoginMutation = useLogin("seekers")
  type formSchemaType = z.infer<typeof formSchema>
  const { toast } = useToast()
  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      contact: "",
      password: ""
    },
  })
  async function onSubmit(values: z.infer<typeof formSchema>) {
    const isEmail = checkIfEmail(values.contact.replaceAll(/\s+/g, ""))
    const data: LoginDto = {
      username: values.contact.trim(),
      password: values.password,
      login_with: isEmail ? "DEFAULT" : "PHONE_NUMBER"
    }
    try {
      const response = await useLoginMutation.mutateAsync(data)
    } catch (e: any) {
      toast({
        title: t("error.failedLogin.title"),
        description: e?.response?.data.message || "",
        variant: "destructive"
      })
    }
  }
  return <div className="grid gap-4">
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4">
        <div className="grid gap-4">
          <DefaultInput
            form={form}
            label={t("form.label.email")}
            name="contact"
            placeholder=""
            type="email"
            variant="float"
            labelClassName="text-xs text-seekers-text-light font-normal"
          />
          <PasswordInput
            form={form}
            label={t("form.label.password")}
            name="password"
            placeholder=""
            variant="float"
            labelClassName="text-xs text-seekers-text-light font-normal"

          />

          <div className="text-xs text-neutral space-x-1 -mt-5">
            <span className="ml-3">{t("form.utility.forgotField", { field: t("form.field.password") })}</span>
            <Button
              variant="link"
              type="button"
              onClick={onClickResetPassword}
              className="p-0 text-seekers-primary font-medium hover:underline text-xs"
            >
              {t("form.utility.resetField", { field: t("form.field.password") })}
            </Button>
          </div>
        </div>

        <Button className="w-full" variant={"default-seekers"} loading={useLoginMutation.isPending}>
          {t("cta.login")}
        </Button>

        <div className="mt-4 text-center">
          <p className="text-sm text-gray-500">
            {t("auth.createAccount")}{" "}
            <Button
              variant={"link"}
              onClick={onClickSignUp}
              className="p-0 h-9 text-seekers-primary hover:underline"
            >
              {t("cta.createAccount")}
            </Button>
          </p>
        </div>

        <div className="relative my-6">
          <Separator />
          <span className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-background px-2 text-sm text-muted-foreground">
            {t('conjuntion.or')} {t("misc.continueWith")}
          </span>
        </div>

        <SeekersSocialAuthentication />
      </form>
    </Form>
  </div>
}