import { getDetailListingService } from "@/core/infrastructures/listing/service";
import { useQuery } from "@tanstack/react-query";

export const LISTING_DETAIL_QUERY_KEY = "detail-listing"
export function useGettListingDetail(id:string){
  const query = useQuery({
    queryKey: [LISTING_DETAIL_QUERY_KEY,id],
    queryFn: async () => {
      const data = await getDetailListingService(id)
      return data
    },
    retry: 0
  })
  return query
}