"use client"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogOverlay,
  DialogPortal,
  DialogTrigger,
} from "@/components/ui/dialog"
import { useMediaQuery } from "@/hooks/use-media-query"
import { cn } from "@/lib/utils"
import { BaseDialog } from "@/types/base"
import React from "react"
import { Drawer, DrawerContent, DrawerTrigger } from "../ui/drawer"


interface DialogWrapperProps extends BaseDialog {
  openTrigger: React.ReactNode
  children: React.ReactNode
}

export default function DialogWrapper({ children, openTrigger, open, setOpen, dialogClassName, drawerClassName, dialogOverlayClassName }: DialogWrapperProps) {
  const isDesktop = useMediaQuery("(min-width:1024px)")
  if (isDesktop) {
    return <Dialog open={open} onOpenChange={setOpen} >
      <DialogTrigger asChild>
        {openTrigger}
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className={dialogOverlayClassName} />
        <DialogContent
          className={cn(
            "max-sm:w-screen md:w-[450px]  md:min-w-[450px] max-sm:h-screen flex flex-col justify-start",
            dialogClassName
          )}
        >
          {children}
        </DialogContent>
      </DialogPortal>
    </Dialog>
  } else {
    return <Drawer
      open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        {openTrigger}
      </DrawerTrigger>
      <DrawerContent>
        <div className={cn("p-4 overflow-auto", drawerClassName)}>
          {children}
        </div>
      </DrawerContent>
    </Drawer>
  }
}
