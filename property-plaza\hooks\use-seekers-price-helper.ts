// Use this to formatting price for seekers based on contract type and the duration

import {
  formatDateSuffix,
  ListingContractType,
} from "@/core/domain/listing/listing";
import { ItemWithSuffix } from "@/core/domain/utils/utils";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

export const useSeekersPriceHelper = (
  price: number,
  contractType: ListingContractType,
  minDuration?: ItemWithSuffix,
  maxDuration?: ItemWithSuffix
) => {
  const t = useTranslations("seeker");
  const [startWord, setStartWord] = useState("");
  const [suffix, setSuffix] = useState("");
  const [formattedPrice, setFormattedPrice] = useState(0);

  useEffect(() => {
    const minSuffix = formatDateSuffix(minDuration?.suffix || "");
    const maxSuffix = formatDateSuffix(maxDuration?.suffix || "");
    const handleSetWording = () => {
      switch (contractType) {
        case "LEASEHOLD":
          const formattedMaxSuffix =
            maxSuffix == "MONTH"
              ? t("misc.month", { count: maxDuration?.value || 1 })
              : maxSuffix == "YEAR"
                ? t("misc.yearWithCount", { count: maxDuration?.value || 1 })
                : maxSuffix;
          setSuffix(
            t("listing.pricing.suffix.leasehold", {
              count: maxDuration?.value || 1,
              durationType: formattedMaxSuffix,
            })
          );
          setFormattedPrice(price);

          return setStartWord("");
        case "FREEHOLD":
          setFormattedPrice(price);
          return setStartWord(t("conjuntion.for"));
        case "RENT":
          setFormattedPrice(price);
          const formattedMinSuffix =
            minSuffix == "MONTH"
              ? t("misc.month", { count: 1 })
              : minSuffix == "YEAR"
                ? t("misc.yearWithCount", { count: 1 })
                : maxSuffix;
          setSuffix(`/ ${formattedMinSuffix}`);
          return setStartWord(t("misc.startFrom"));
        default:
          return "";
      }
    };
    handleSetWording();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    contractType,
    maxDuration?.suffix,
    maxDuration?.value,
    minDuration?.suffix,
    minDuration?.value,
    price,
  ]);

  return { startWord, suffix, formattedPrice };
};
