"use client"
import DialogDescriptionWrapper from "@/components/dialog-wrapper/dialog-description-wrapper";
import DialogHeaderWrapper from "@/components/dialog-wrapper/dialog-header-wrapper";
import DialogTitleWrapper from "@/components/dialog-wrapper/dialog-title-wrapper";
import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper";
import { Button } from "@/components/ui/button";
import { useGetTwoFactorAuthenticationCode } from "@/core/applications/queries/auth/use-get-two-fa-otp";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect, useState } from "react";
import TwoFactorAuthenticationForm from "./form/two-fa.form";
import { useUserStore } from "@/stores/user.store";
import { TwoFactorAuthenticationCodeDto } from "@/core/infrastructures/auth/dto";
import PasswordForm from "./form/password.form";

export default function TwoFADialog({ }: { enableTwoFA: boolean }) {
  const t = useTranslations("seeker")
  const [open, setOpen] = useState(false)
  const [runRequest, setRunRequest] = useState(false)
  const { seekers } = useUserStore()
  const [open2FACode, setOpen2FACode] = useState(false)
  const [requestMode, setRequestMode] = useState("ACTIVE_2FA")
  const [twoFA, setTwoFA] = useState<TwoFactorAuthenticationCodeDto>({
    request_setting: "ACTIVE_2FA",
    password: ""
  })
  const twoFaQuery = useGetTwoFactorAuthenticationCode(twoFA, (open && runRequest))
  useEffect(() => {
    if (seekers.has2FA) {
      setTwoFA(prev => ({ ...prev, request_setting: "INACTIVE_2FA" }))
      setRequestMode("INACTIVE_2FA")

    }
    if (!seekers.has2FA) {
      setTwoFA(prev => ({ ...prev, request_setting: "REQUEST_2FA" }))
      setRequestMode("REQUEST_2FA")

    }
  }, [seekers.has2FA, open])

  useEffect(() => {
    if (twoFaQuery.isFetched && twoFaQuery.isSuccess) {
      setRunRequest(false)
      if (requestMode == "INACTIVE_2FA") return window.location.reload()
      setOpen2FACode(true)
      return
    }
  }, [twoFaQuery.isFetched, twoFaQuery.isSuccess, requestMode])

  const handleSubmit = (val: string) => {
    setTwoFA(prev => ({ ...prev, password: val }))
    setRunRequest(true)
  }

  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    dialogClassName="!max-w-fit !w-fit"
    openTrigger={<Button
      variant={"outline"}
      size={"sm"}
      className={
        seekers.has2FA
          ? "border-red-500 text-red-500 hover:text-red-500 hover:bg-red-50"
          : "border-seekers-primary text-seekers-primary hover:text-seekers-primary hover:bg-seekers-primary/10"
      }
      onClick={() => { }}
    >
      {seekers.has2FA ? t("cta.disable") : t("cta.enable")}
    </Button>}
  >
    <DialogHeaderWrapper className="text-seekers-text text-center flex flex-col items-center">
      <DialogTitleWrapper>{t('setting.profile.security.twoFA.title')}</DialogTitleWrapper>
      <DialogDescriptionWrapper className="text-seekers-text-light">{t('setting.profile.security.twoFA.description')}</DialogDescriptionWrapper>
    </DialogHeaderWrapper>
    {!open2FACode ?
      <div>
        <PasswordForm isLoading={twoFaQuery.isLoading} onSubmit={(val: string) => handleSubmit(val)} />
      </div>
      :
      <div className="flex max-sm:flex-col gap-4">
        <div className="relative aspect-square" style={{ margin: "0 auto", height: 200, width: 200 }}>
          <Image
            src={twoFaQuery.data?.data.qr_image || ""} alt="" fill
            style={{ objectFit: "cover" }}
          />
        </div>
        <div className="md:max-w-xs space-y-4">
          <h2 className="max-sm:text-sm max-sm:text-center text-xl font-bold">{t('setting.profile.security.twoFA.useAuthenticatorApp')}</h2>
          <div className="space-y-2">
            <p><span className="font-bold">{t('misc.step', { count: 1 })} </span> {t('setting.profile.security.twoFA.scanQRCodeWithAuthenticatorApp')}</p>
            <p><span className="font-bold">{t('misc.step', { count: 2 })}</span> {t('setting.profile.security.twoFA.enterAuthenticatorCode')}</p>
          </div>
          <TwoFactorAuthenticationForm setOpenDialog={(val) => setOpen(val)} />
        </div>
      </div>
    }
  </DialogWrapper>
}