"use client"
import { Bad<PERSON> } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { useUpdatePaymentMethod } from "@/core/applications/mutations/transaction/use-update-payment-method";
import { PaymentMethod } from "@/core/domain/transaction/transaction";
import { useToast } from "@/hooks/use-toast";
import { CreditCard } from "lucide-react";
import { useTranslations } from "next-intl";

export default function PaymentItem({ item }: { item: PaymentMethod }) {
  const t = useTranslations("seeker")

  const updatePaymentMutation = useUpdatePaymentMethod()
  const { toast } = useToast()

  const handleRemovePayment = async (id: string) => {
    try {
      await updatePaymentMutation.mutateAsync({ payment_method_id: id, request_for: "REMOVE" })
      toast({
        title: t("success.updatePayment")
      })
      window.location.reload()

    } catch (e) {
      toast({
        title: t("error.failedUpdatePayment")
      })
    }
  }
  const handleSetDefaultPayment = async (id: string) => {
    try {
      await updatePaymentMutation.mutateAsync({ payment_method_id: id, request_for: "SET_DEFAULT" })
      toast({
        title: t("success.updatePayment")
      })
      window.location.reload()
    } catch (e) {
      title: t("error.failedUpdatePayment")
    }
  }
  return <div key={item.id} className="flex border-b border-text-bg-seekers-primary-light justify-between items-center last:border-none py-2">
    <div className="flex items-center space-x-4">
      <CreditCard className="h-6 text-seekers-primary w-6" />
      <div>
        <p className="font-medium"><span className="capitalize">{item.brand}</span> {item.cardNumber}</p>
        <p className="text-muted-foreground text-sm">{t("misc.expires")} {item.expiredMonth}-{item.expiredYear}</p>
      </div>
    </div>
    <div className="flex items-center space-x-4">
      {
        item.isDefault ?
          <Badge
            variant="outline"
            className="bg-[#FAF6F0] border-seekers-primary/20 text-seekers-primary hover:bg-[#FAF6F0]"
          >
            {t("misc.primary")}
          </Badge> :
          <Button onClick={() => handleSetDefaultPayment(item.id)} variant="ghost" size="sm">
            {t("cta.setPrimary")}
          </Button>
      }
      {

        <Button onClick={() => handleRemovePayment(item.id)} variant="ghost" size="sm">
          {t("cta.remove")}
        </Button>
      }
    </div>
  </div>
}