import Sidebar from "./sidebar";
import Detail from "./detail";
import DefaultLayoutContent from "@/components/seekers-content-layout/default-layout-content";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { useTranslations } from "next-intl";
import { Sparkles } from "lucide-react";

export default function SeekerFaQContent() {
  const t = useTranslations("seeker")
  return <div id="faq" className="bg-seekers-foreground/50 text-black w-full py-12 mt-12">
    <MainContentLayout>
      <DefaultLayoutContent title={
        <span className="inline-flex items-center gap-1">
          <Sparkles className="max-sm:hidden" /> {t('faq.title')}
        </span>
      } className="!mt-2">
        <div className="flex md:gap-8">
          <Sidebar />
          <Detail />
        </div>
      </DefaultLayoutContent>
    </MainContentLayout>
  </div>
}