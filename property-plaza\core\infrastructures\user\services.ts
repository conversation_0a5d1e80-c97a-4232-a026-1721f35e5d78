import { BasePaginationRequest } from "@/types/base";
import { allUser, getMe, userDetail } from "./api";
import { transformUser, transformUserDetail } from "./transform";
import { transformMeta } from "../utils/transform";
import { PROPERTY_OWNER, PROPERTY_SEEKERS } from "@/lib/constanta/constant";
import { errorHandling } from "../utils/error-handling";
import { Axios, AxiosRequestConfig } from "axios";

export async function getAllUsersService(searchParam: BasePaginationRequest) {
  try {
    const request = await allUser({ ...searchParam, type: PROPERTY_SEEKERS });
    return {
      data: transformUser(request.data.data.items),
      meta: transformMeta(request.data.data.meta),
    };
  } catch (e) {
    throw new Error(e as any);
  }
}

export async function getAllOwnerService(searchParam: BasePaginationRequest) {
  try {
    const request = await allUser({ ...searchParam, type: PROPERTY_OWNER });
    return {
      data: transformUser(request.data.data.items),
      meta: transformMeta(request.data.data.meta),
    };
  } catch (e) {
    throw new Error(e as any);
  }
}

export async function getUserDetailService(id: string) {
  try {
    const request = await userDetail(id);
    return transformUserDetail(request.data.data);
  } catch (e) {
    throw new Error(e as any);
  }
}

export async function getMeService(options?: AxiosRequestConfig) {
  try {
    const request = await getMe(options);
    return transformUserDetail(request.data.data);
  } catch (e) {
    throw new Error(errorHandling(e) as any);
  }
}
