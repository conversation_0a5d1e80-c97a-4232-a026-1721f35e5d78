"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-intl";
exports.ids = ["vendor-chunks/next-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\n\nexports[\"extends\"] = _extends;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdEO0FBQ0E7QUFDQSxvQkFBb0Isc0JBQXNCO0FBQzFDO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUEsa0JBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9kZXZlbG9wbWVudC9fdmlydHVhbC9fcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmpzP2EwODgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG5mdW5jdGlvbiBfZXh0ZW5kcygpIHtcbiAgcmV0dXJuIF9leHRlbmRzID0gT2JqZWN0LmFzc2lnbiA/IE9iamVjdC5hc3NpZ24uYmluZCgpIDogZnVuY3Rpb24gKG4pIHtcbiAgICBmb3IgKHZhciBlID0gMTsgZSA8IGFyZ3VtZW50cy5sZW5ndGg7IGUrKykge1xuICAgICAgdmFyIHQgPSBhcmd1bWVudHNbZV07XG4gICAgICBmb3IgKHZhciByIGluIHQpICh7fSkuaGFzT3duUHJvcGVydHkuY2FsbCh0LCByKSAmJiAobltyXSA9IHRbcl0pO1xuICAgIH1cbiAgICByZXR1cm4gbjtcbiAgfSwgX2V4dGVuZHMuYXBwbHkobnVsbCwgYXJndW1lbnRzKTtcbn1cblxuZXhwb3J0cy5leHRlbmRzID0gX2V4dGVuZHM7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/index.react-client.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/index.react-client.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar index = __webpack_require__(/*! ./react-client/index.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/index.js\");\nvar useLocale = __webpack_require__(/*! ./react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar NextIntlClientProvider = __webpack_require__(/*! ./shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js\");\nvar useIntl = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/index.js\");\n\n\n\nexports.useFormatter = index.useFormatter;\nexports.useTranslations = index.useTranslations;\nexports.useLocale = useLocale.default;\nexports.NextIntlClientProvider = NextIntlClientProvider.default;\nObject.keys(useIntl).forEach(function (k) {\n\tif (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n\t\tenumerable: true,\n\t\tget: function () { return useIntl[k]; }\n\t});\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvaW5kZXgucmVhY3QtY2xpZW50LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0QsWUFBWSxtQkFBTyxDQUFDLHNHQUF5QjtBQUM3QyxnQkFBZ0IsbUJBQU8sQ0FBQyw4R0FBNkI7QUFDckQsNkJBQTZCLG1CQUFPLENBQUMsNEhBQW9DO0FBQ3pFLGNBQWMsbUJBQU8sQ0FBQyw2REFBVTs7OztBQUloQyxvQkFBb0I7QUFDcEIsdUJBQXVCO0FBQ3ZCLGlCQUFpQjtBQUNqQiw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLEVBQUU7QUFDRixDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvaW5kZXgucmVhY3QtY2xpZW50LmpzPzYxZjYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgaW5kZXggPSByZXF1aXJlKCcuL3JlYWN0LWNsaWVudC9pbmRleC5qcycpO1xudmFyIHVzZUxvY2FsZSA9IHJlcXVpcmUoJy4vcmVhY3QtY2xpZW50L3VzZUxvY2FsZS5qcycpO1xudmFyIE5leHRJbnRsQ2xpZW50UHJvdmlkZXIgPSByZXF1aXJlKCcuL3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzJyk7XG52YXIgdXNlSW50bCA9IHJlcXVpcmUoJ3VzZS1pbnRsJyk7XG5cblxuXG5leHBvcnRzLnVzZUZvcm1hdHRlciA9IGluZGV4LnVzZUZvcm1hdHRlcjtcbmV4cG9ydHMudXNlVHJhbnNsYXRpb25zID0gaW5kZXgudXNlVHJhbnNsYXRpb25zO1xuZXhwb3J0cy51c2VMb2NhbGUgPSB1c2VMb2NhbGUuZGVmYXVsdDtcbmV4cG9ydHMuTmV4dEludGxDbGllbnRQcm92aWRlciA9IE5leHRJbnRsQ2xpZW50UHJvdmlkZXIuZGVmYXVsdDtcbk9iamVjdC5rZXlzKHVzZUludGwpLmZvckVhY2goZnVuY3Rpb24gKGspIHtcblx0aWYgKGsgIT09ICdkZWZhdWx0JyAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIGspKSBPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgaywge1xuXHRcdGVudW1lcmFibGU6IHRydWUsXG5cdFx0Z2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiB1c2VJbnRsW2tdOyB9XG5cdH0pO1xufSk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation.react-client.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation.react-client.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar createSharedPathnamesNavigation = __webpack_require__(/*! ./navigation/react-client/createSharedPathnamesNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js\");\nvar createLocalizedPathnamesNavigation = __webpack_require__(/*! ./navigation/react-client/createLocalizedPathnamesNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js\");\nvar createNavigation = __webpack_require__(/*! ./navigation/react-client/createNavigation.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js\");\n\n\n\nexports.createSharedPathnamesNavigation = createSharedPathnamesNavigation.default;\nexports.createLocalizedPathnamesNavigation = createLocalizedPathnamesNavigation.default;\nexports.createNavigation = createNavigation.default;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi5yZWFjdC1jbGllbnQuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDLEVBQUUsYUFBYSxFQUFDOztBQUU3RCxzQ0FBc0MsbUJBQU8sQ0FBQyxnTEFBOEQ7QUFDNUcseUNBQXlDLG1CQUFPLENBQUMsc0xBQWlFO0FBQ2xILHVCQUF1QixtQkFBTyxDQUFDLGtKQUErQzs7OztBQUk5RSx1Q0FBdUM7QUFDdkMsMENBQTBDO0FBQzFDLHdCQUF3QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2RldmVsb3BtZW50L25hdmlnYXRpb24ucmVhY3QtY2xpZW50LmpzPzNmMzAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgY3JlYXRlU2hhcmVkUGF0aG5hbWVzTmF2aWdhdGlvbiA9IHJlcXVpcmUoJy4vbmF2aWdhdGlvbi9yZWFjdC1jbGllbnQvY3JlYXRlU2hhcmVkUGF0aG5hbWVzTmF2aWdhdGlvbi5qcycpO1xudmFyIGNyZWF0ZUxvY2FsaXplZFBhdGhuYW1lc05hdmlnYXRpb24gPSByZXF1aXJlKCcuL25hdmlnYXRpb24vcmVhY3QtY2xpZW50L2NyZWF0ZUxvY2FsaXplZFBhdGhuYW1lc05hdmlnYXRpb24uanMnKTtcbnZhciBjcmVhdGVOYXZpZ2F0aW9uID0gcmVxdWlyZSgnLi9uYXZpZ2F0aW9uL3JlYWN0LWNsaWVudC9jcmVhdGVOYXZpZ2F0aW9uLmpzJyk7XG5cblxuXG5leHBvcnRzLmNyZWF0ZVNoYXJlZFBhdGhuYW1lc05hdmlnYXRpb24gPSBjcmVhdGVTaGFyZWRQYXRobmFtZXNOYXZpZ2F0aW9uLmRlZmF1bHQ7XG5leHBvcnRzLmNyZWF0ZUxvY2FsaXplZFBhdGhuYW1lc05hdmlnYXRpb24gPSBjcmVhdGVMb2NhbGl6ZWRQYXRobmFtZXNOYXZpZ2F0aW9uLmRlZmF1bHQ7XG5leHBvcnRzLmNyZWF0ZU5hdmlnYXRpb24gPSBjcmVhdGVOYXZpZ2F0aW9uLmRlZmF1bHQ7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar LegacyBaseLink = __webpack_require__(/*! ../shared/LegacyBaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\nfunction ClientLink(_ref, ref) {\n  let {\n    locale,\n    localePrefix,\n    ...rest\n  } = _ref;\n  const defaultLocale = useLocale.default();\n  const finalLocale = locale || defaultLocale;\n  const prefix = utils.getLocalePrefix(finalLocale, localePrefix);\n  return /*#__PURE__*/React__default.default.createElement(LegacyBaseLink.default, _rollupPluginBabelHelpers.extends({\n    ref: ref,\n    locale: finalLocale,\n    localePrefixMode: localePrefix.mode,\n    prefix: prefix\n  }, rest));\n}\n\n/**\n * Wraps `next/link` and prefixes the `href` with the current locale if\n * necessary.\n *\n * @example\n * ```tsx\n * import {Link} from 'next-intl';\n *\n * // When the user is on `/en`, the link will point to `/en/about`\n * <Link href=\"/about\">About</Link>\n *\n * // You can override the `locale` to switch to another language\n * <Link href=\"/\" locale=\"de\">Switch to German</Link>\n * ```\n *\n * Note that when a `locale` prop is passed to switch the locale, the `prefetch`\n * prop is not supported. This is because Next.js would prefetch the page and\n * the `set-cookie` response header would cause the locale cookie on the current\n * page to be overwritten before the user even decides to change the locale.\n */\nconst ClientLinkWithRef = /*#__PURE__*/React.forwardRef(ClientLink);\nClientLinkWithRef.displayName = 'ClientLink';\n\nexports[\"default\"] = ClientLinkWithRef;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\nvar ClientLink = __webpack_require__(/*! ./ClientLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\");\nvar redirects = __webpack_require__(/*! ./redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\nvar useBaseRouter = __webpack_require__(/*! ./useBaseRouter.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\n/**\n * @deprecated Consider switching to `createNavigation` (see https://next-intl-docs.vercel.app/blog/next-intl-3-22#create-navigation)\n **/\nfunction createLocalizedPathnamesNavigation(routing) {\n  const config$1 = config.receiveRoutingConfig(routing);\n  const localeCookie = config.receiveLocaleCookie(routing.localeCookie);\n  function useTypedLocale() {\n    const locale = useLocale.default();\n    const isValid = config$1.locales.includes(locale);\n    if (!isValid) {\n      throw new Error(\"Unknown locale encountered: \\\"\".concat(locale, \"\\\". Make sure to validate the locale in `i18n.ts`.\") );\n    }\n    return locale;\n  }\n  function Link(_ref, ref) {\n    let {\n      href,\n      locale,\n      ...rest\n    } = _ref;\n    const defaultLocale = useTypedLocale();\n    const finalLocale = locale || defaultLocale;\n    return /*#__PURE__*/React__default.default.createElement(ClientLink.default, _rollupPluginBabelHelpers.extends({\n      ref: ref,\n      href: utils.compileLocalizedPathname({\n        locale: finalLocale,\n        // @ts-expect-error -- This is ok\n        pathname: href,\n        // @ts-expect-error -- This is ok\n        params: typeof href === 'object' ? href.params : undefined,\n        pathnames: config$1.pathnames\n      }),\n      locale: locale,\n      localeCookie: localeCookie,\n      localePrefix: config$1.localePrefix\n    }, rest));\n  }\n  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);\n  LinkWithRef.displayName = 'Link';\n  function redirect(href) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n    const locale = useTypedLocale();\n    const resolvedHref = getPathname({\n      href,\n      locale\n    });\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirects.clientRedirect({\n      pathname: resolvedHref,\n      localePrefix: config$1.localePrefix\n    }, ...args);\n  }\n  function permanentRedirect(href) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n    const locale = useTypedLocale();\n    const resolvedHref = getPathname({\n      href,\n      locale\n    });\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    return redirects.clientPermanentRedirect({\n      pathname: resolvedHref,\n      localePrefix: config$1.localePrefix\n    }, ...args);\n  }\n  function useRouter() {\n    const baseRouter = useBaseRouter.default(config$1.localePrefix, localeCookie);\n    const defaultLocale = useTypedLocale();\n    return React.useMemo(() => ({\n      ...baseRouter,\n      push(href) {\n        var _args$;\n        for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n          args[_key3 - 1] = arguments[_key3];\n        }\n        const resolvedHref = getPathname({\n          href,\n          locale: ((_args$ = args[0]) === null || _args$ === void 0 ? void 0 : _args$.locale) || defaultLocale\n        });\n        return baseRouter.push(resolvedHref, ...args);\n      },\n      replace(href) {\n        var _args$2;\n        for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n          args[_key4 - 1] = arguments[_key4];\n        }\n        const resolvedHref = getPathname({\n          href,\n          locale: ((_args$2 = args[0]) === null || _args$2 === void 0 ? void 0 : _args$2.locale) || defaultLocale\n        });\n        return baseRouter.replace(resolvedHref, ...args);\n      },\n      prefetch(href) {\n        var _args$3;\n        for (var _len5 = arguments.length, args = new Array(_len5 > 1 ? _len5 - 1 : 0), _key5 = 1; _key5 < _len5; _key5++) {\n          args[_key5 - 1] = arguments[_key5];\n        }\n        const resolvedHref = getPathname({\n          href,\n          locale: ((_args$3 = args[0]) === null || _args$3 === void 0 ? void 0 : _args$3.locale) || defaultLocale\n        });\n        return baseRouter.prefetch(resolvedHref, ...args);\n      }\n    }), [baseRouter, defaultLocale]);\n  }\n  function usePathname() {\n    const pathname = useBasePathname.default(config$1.localePrefix);\n    const locale = useTypedLocale();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return React.useMemo(() => pathname ? utils.getRoute(locale, pathname, config$1.pathnames) : pathname, [locale, pathname]);\n  }\n  function getPathname(_ref2) {\n    let {\n      href,\n      locale\n    } = _ref2;\n    return utils.compileLocalizedPathname({\n      ...utils.normalizeNameOrNameWithParams(href),\n      locale,\n      pathnames: config$1.pathnames\n    });\n  }\n  return {\n    Link: LinkWithRef,\n    redirect,\n    permanentRedirect,\n    usePathname,\n    useRouter,\n    getPathname\n  };\n}\n\nexports[\"default\"] = createLocalizedPathnamesNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createLocalizedPathnamesNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar createSharedNavigationFns = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\n\nfunction createNavigation(routing) {\n  function useTypedLocale() {\n    return useLocale.default();\n  }\n  const {\n    Link,\n    config,\n    getPathname,\n    ...redirects\n  } = createSharedNavigationFns.default(useTypedLocale, routing);\n\n  /** @see https://next-intl-docs.vercel.app/docs/routing/navigation#usepathname */\n  function usePathname() {\n    const pathname = useBasePathname.default(config.localePrefix);\n    const locale = useTypedLocale();\n\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return React.useMemo(() => pathname &&\n    // @ts-expect-error -- This is fine\n    config.pathnames ? utils.getRoute(locale, pathname,\n    // @ts-expect-error -- This is fine\n    config.pathnames) : pathname, [locale, pathname]);\n  }\n  function useRouter() {\n    const router = navigation.useRouter();\n    const curLocale = useTypedLocale();\n    const nextPathname = navigation.usePathname();\n    return React.useMemo(() => {\n      function createHandler(fn) {\n        return function handler(href, options) {\n          const {\n            locale: nextLocale,\n            ...rest\n          } = options || {};\n\n          // @ts-expect-error -- We're passing a domain here just in case\n          const pathname = getPathname({\n            href,\n            locale: nextLocale || curLocale,\n            domain: window.location.host\n          });\n          const args = [pathname];\n          if (Object.keys(rest).length > 0) {\n            // @ts-expect-error -- This is fine\n            args.push(rest);\n          }\n          fn(...args);\n          syncLocaleCookie.default(config.localeCookie, nextPathname, curLocale, nextLocale);\n        };\n      }\n      return {\n        ...router,\n        /** @see https://next-intl-docs.vercel.app/docs/routing/navigation#userouter */\n        push: createHandler(router.push),\n        /** @see https://next-intl-docs.vercel.app/docs/routing/navigation#userouter */\n        replace: createHandler(router.replace),\n        /** @see https://next-intl-docs.vercel.app/docs/routing/navigation#userouter */\n        prefetch: createHandler(router.prefetch)\n      };\n    }, [curLocale, nextPathname, router]);\n  }\n  return {\n    ...redirects,\n    Link,\n    usePathname,\n    useRouter,\n    getPathname\n  };\n}\n\nexports[\"default\"] = createNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar ClientLink = __webpack_require__(/*! ./ClientLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/ClientLink.js\");\nvar redirects = __webpack_require__(/*! ./redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\");\nvar useBasePathname = __webpack_require__(/*! ./useBasePathname.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\");\nvar useBaseRouter = __webpack_require__(/*! ./useBaseRouter.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\n/**\n * @deprecated Consider switching to `createNavigation` (see https://next-intl-docs.vercel.app/blog/next-intl-3-22#create-navigation)\n **/\nfunction createSharedPathnamesNavigation(routing) {\n  const localePrefix = config.receiveLocalePrefixConfig(routing === null || routing === void 0 ? void 0 : routing.localePrefix);\n  const localeCookie = config.receiveLocaleCookie(routing === null || routing === void 0 ? void 0 : routing.localeCookie);\n  function Link(props, ref) {\n    return /*#__PURE__*/React__default.default.createElement(ClientLink.default, _rollupPluginBabelHelpers.extends({\n      ref: ref,\n      localeCookie: localeCookie,\n      localePrefix: localePrefix\n    }, props));\n  }\n  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);\n  LinkWithRef.displayName = 'Link';\n  function redirect(pathname) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirects.clientRedirect({\n      pathname,\n      localePrefix\n    }, ...args);\n  }\n  function permanentRedirect(pathname) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    return redirects.clientPermanentRedirect({\n      pathname,\n      localePrefix\n    }, ...args);\n  }\n  function usePathname() {\n    const result = useBasePathname.default(localePrefix);\n    // @ts-expect-error -- Mirror the behavior from Next.js, where `null` is returned when `usePathname` is used outside of Next, but the types indicate that a string is always returned.\n    return result;\n  }\n  function useRouter() {\n    return useBaseRouter.default(localePrefix, localeCookie);\n  }\n  return {\n    Link: LinkWithRef,\n    redirect,\n    permanentRedirect,\n    usePathname,\n    useRouter\n  };\n}\n\nexports[\"default\"] = createSharedPathnamesNavigation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/createSharedPathnamesNavigation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/redirects.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar redirects = __webpack_require__(/*! ../shared/redirects.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js\");\n\nfunction createRedirectFn(redirectFn) {\n  return function clientRedirect(params) {\n    let locale;\n    try {\n      // eslint-disable-next-line react-hooks/rules-of-hooks -- Reading from context here is fine, since `redirect` should be called during render\n      locale = useLocale.default();\n    } catch (e) {\n      {\n        throw new Error('`redirect()` and `permanentRedirect()` can only be called during render. To redirect in an event handler or similar, you can use `useRouter()` instead.');\n      }\n    }\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirectFn({\n      ...params,\n      locale\n    }, ...args);\n  };\n}\nconst clientRedirect = createRedirectFn(redirects.baseRedirect);\nconst clientPermanentRedirect = createRedirectFn(redirects.basePermanentRedirect);\n\nexports.clientPermanentRedirect = clientPermanentRedirect;\nexports.clientRedirect = clientRedirect;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/redirects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n\nfunction useBasePathname(localePrefix) {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n\n  // Notes on `useNextPathname`:\n  // - Types aren't entirely correct. Outside of Next.js the\n  //   hook will return `null` (e.g. unit tests)\n  // - A base path is stripped from the result\n  // - Rewrites *are* taken into account (i.e. the pathname\n  //   that the user sees in the browser is returned)\n  const pathname = navigation.usePathname();\n  const locale = useLocale.default();\n  return React.useMemo(() => {\n    if (!pathname) return pathname;\n    const prefix = utils.getLocalePrefix(locale, localePrefix);\n    const isPathnamePrefixed = utils.hasPathnamePrefixed(prefix, pathname);\n    const unlocalizedPathname = isPathnamePrefixed ? utils.unprefixPathname(pathname, prefix) : pathname;\n    return unlocalizedPathname;\n  }, [locale, localePrefix, pathname]);\n}\n\nexports[\"default\"] = useBasePathname;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBasePathname.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils$1 = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ../shared/syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nvar utils = __webpack_require__(/*! ../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n/**\n * Returns a wrapped instance of `useRouter` from `next/navigation` that\n * will automatically localize the `href` parameters it receives.\n *\n * @example\n * ```tsx\n * 'use client';\n *\n * import {useRouter} from 'next-intl/client';\n *\n * const router = useRouter();\n *\n * // When the user is on `/en`, the router will navigate to `/en/about`\n * router.push('/about');\n *\n * // Optionally, you can switch the locale by passing the second argument\n * router.push('/about', {locale: 'de'});\n * ```\n */ function useBaseRouter(localePrefix, localeCookie) {\n    const router = navigation.useRouter();\n    const locale = useLocale.default();\n    const pathname = navigation.usePathname();\n    return React.useMemo(()=>{\n        function localize(href, nextLocale) {\n            let curPathname = window.location.pathname;\n            const basePath = utils.getBasePath(pathname);\n            if (basePath) curPathname = curPathname.replace(basePath, \"\");\n            const targetLocale = nextLocale || locale;\n            // We generate a prefix in any case, but decide\n            // in `localizeHref` if we apply it or not\n            const prefix = utils$1.getLocalePrefix(targetLocale, localePrefix);\n            return utils$1.localizeHref(href, targetLocale, locale, curPathname, prefix);\n        }\n        function createHandler(fn) {\n            return function handler(href, options) {\n                const { locale: nextLocale, ...rest } = options || {};\n                syncLocaleCookie.default(localeCookie, pathname, locale, nextLocale);\n                const args = [\n                    localize(href, nextLocale)\n                ];\n                if (Object.keys(rest).length > 0) {\n                    args.push(rest);\n                }\n                // @ts-expect-error -- This is ok\n                return fn(...args);\n            };\n        }\n        return {\n            ...router,\n            push: createHandler(router.push),\n            replace: createHandler(router.replace),\n            prefetch: createHandler(router.prefetch)\n        };\n    }, [\n        locale,\n        localeCookie,\n        localePrefix,\n        pathname,\n        router\n    ]);\n}\nexports[\"default\"] = useBaseRouter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/react-client/useBaseRouter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar NextLink = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar syncLocaleCookie = __webpack_require__(/*! ./syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar NextLink__default = /*#__PURE__*/ _interopDefault(NextLink);\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction BaseLink(_ref, ref) {\n    let { defaultLocale, href, locale, localeCookie, onClick, prefetch, unprefixed, ...rest } = _ref;\n    const curLocale = useLocale.default();\n    const isChangingLocale = locale !== curLocale;\n    const linkLocale = locale || curLocale;\n    const host = useHost();\n    const finalHref = // Only after hydration (to avoid mismatches)\n    host && // If there is an `unprefixed` prop, the\n    // `defaultLocale` might differ by domain\n    unprefixed && // Unprefix the pathname if a domain matches\n    (unprefixed.domains[host] === linkLocale || // … and handle unknown domains by applying the\n    // global `defaultLocale` (e.g. on localhost)\n    !Object.keys(unprefixed.domains).includes(host) && curLocale === defaultLocale && !locale) ? unprefixed.pathname : href;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = navigation.usePathname();\n    function onLinkClick(event) {\n        syncLocaleCookie.default(localeCookie, pathname, curLocale, locale);\n        if (onClick) onClick(event);\n    }\n    if (isChangingLocale) {\n        if (prefetch && \"development\" !== \"production\") {\n            console.error(\"The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`\");\n        }\n        prefetch = false;\n    }\n    return /*#__PURE__*/ React__default.default.createElement(NextLink__default.default, _rollupPluginBabelHelpers.extends({\n        ref: ref,\n        href: finalHref,\n        hrefLang: isChangingLocale ? locale : undefined,\n        onClick: onLinkClick,\n        prefetch: prefetch\n    }, rest));\n}\nfunction useHost() {\n    const [host, setHost] = React.useState();\n    React.useEffect(()=>{\n        setHost(window.location.host);\n    }, []);\n    return host;\n}\nvar BaseLink$1 = /*#__PURE__*/ React.forwardRef(BaseLink);\nexports[\"default\"] = BaseLink$1;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar useLocale = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar BaseLink = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction LegacyBaseLink(_ref, ref) {\n    let { href, locale, localeCookie, localePrefixMode, prefix, ...rest } = _ref;\n    // The types aren't entirely correct here. Outside of Next.js\n    // `useParams` can be called, but the return type is `null`.\n    const pathname = navigation.usePathname();\n    const curLocale = useLocale.default();\n    const isChangingLocale = locale !== curLocale;\n    const [localizedHref, setLocalizedHref] = React.useState(()=>utils.isLocalizableHref(href) && (localePrefixMode !== \"never\" || isChangingLocale) ? // For the `localePrefix: 'as-needed' strategy, the href shouldn't\n        // be prefixed if the locale is the default locale. To determine this, we\n        // need a) the default locale and b) the information if we use prefixed\n        // routing. The default locale can vary by domain, therefore during the\n        // RSC as well as the SSR render, we can't determine the default locale\n        // statically. Therefore we always prefix the href since this will\n        // always result in a valid URL, even if it might cause a redirect. This\n        // is better than pointing to a non-localized href during the server\n        // render, which would potentially be wrong. The final href is\n        // determined in the effect below.\n        utils.prefixHref(href, prefix) : href);\n    React.useEffect(()=>{\n        if (!pathname) return;\n        setLocalizedHref(utils.localizeHref(href, locale, curLocale, pathname, prefix));\n    }, [\n        curLocale,\n        href,\n        locale,\n        pathname,\n        prefix\n    ]);\n    return /*#__PURE__*/ React__default.default.createElement(BaseLink.default, _rollupPluginBabelHelpers.extends({\n        ref: ref,\n        href: localizedHref,\n        locale: locale,\n        localeCookie: localeCookie\n    }, rest));\n}\nconst LegacyBaseLinkWithRef = /*#__PURE__*/ React.forwardRef(LegacyBaseLink);\nLegacyBaseLinkWithRef.displayName = \"ClientLink\";\nexports[\"default\"] = LegacyBaseLinkWithRef;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/LegacyBaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar config = __webpack_require__(/*! ../../routing/config.js */ \"(ssr)/./node_modules/next-intl/dist/development/routing/config.js\");\nvar utils$1 = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\nvar BaseLink = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/BaseLink.js\");\nvar utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config$1 = config.receiveRoutingConfig(routing || {});\n  {\n    utils.validateReceivedConfig(config$1);\n  }\n  const pathnames = config$1.pathnames;\n\n  // This combination requires that the current host is known in order to\n  // compute a correct pathname. Since that can only be achieved by reading from\n  // headers, this would break static rendering. Therefore, as a workaround we\n  // always add a prefix in this case to be on the safe side. The downside is\n  // that the user might get redirected again if the middleware detects that the\n  // prefix is not needed.\n  const forcePrefixSsr = config$1.localePrefix.mode === 'as-needed' && config$1.domains || undefined;\n  function Link(_ref, ref) {\n    let {\n      href,\n      locale,\n      ...rest\n    } = _ref;\n    let pathname, params;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = utils$1.isLocalizableHref(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = localePromiseOrValue instanceof Promise ? React.use(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname(\n    // @ts-expect-error -- This is ok\n    {\n      locale: locale || curLocale,\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      }\n    }, locale != null || forcePrefixSsr || undefined) : pathname;\n    return /*#__PURE__*/React__default.default.createElement(BaseLink.default, _rollupPluginBabelHelpers.extends({\n      ref: ref\n      // @ts-expect-error -- Available after the validation\n      ,\n      defaultLocale: config$1.defaultLocale\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config$1.localeCookie\n      // Provide the minimal relevant information to the client side in order\n      // to potentially remove the prefix in case of the `forcePrefixSsr` case\n      ,\n      unprefixed: forcePrefixSsr && isLocalizable ? {\n        domains: config$1.domains.reduce((acc, domain) => {\n          // @ts-expect-error -- This is ok\n          acc[domain.domain] = domain.defaultLocale;\n          return acc;\n        }, {}),\n        pathname: getPathname(\n        // @ts-expect-error -- This is ok\n        {\n          locale: curLocale,\n          href: pathnames == null ? pathname : {\n            pathname,\n            params\n          }\n        }, false)\n      } : undefined\n    }, rest));\n  }\n  const LinkWithRef = /*#__PURE__*/React.forwardRef(Link);\n  function getPathname(args, /** @private Removed in types returned below */\n  _forcePrefix) {\n    const {\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += utils.serializeSearchParams(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = utils.compileLocalizedPathname({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...utils.normalizeNameOrNameWithParams(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config$1.pathnames\n      });\n    }\n    return utils.applyPathnamePrefix(pathname, locale, config$1,\n    // @ts-expect-error -- This is ok\n    args.domain, _forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl-docs.vercel.app/docs/routing/navigation#redirect */\n    return function redirectFn(args) {\n      for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        rest[_key - 1] = arguments[_key];\n      }\n      return fn(\n      // @ts-expect-error -- We're forcing the prefix when no domain is provided\n      getPathname(args, args.domain ? undefined : forcePrefixSsr), ...rest);\n    };\n  }\n  const redirect = getRedirectFn(navigation.redirect);\n  const permanentRedirect = getRedirectFn(navigation.permanentRedirect);\n  return {\n    config: config$1,\n    Link: LinkWithRef,\n    redirect,\n    permanentRedirect,\n    // Remove `_forcePrefix` from public API\n    getPathname: getPathname\n  };\n}\n\nexports[\"default\"] = createSharedNavigationFns;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/redirects.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n\nfunction createRedirectFn(redirectFn) {\n  return function baseRedirect(params) {\n    const prefix = utils.getLocalePrefix(params.locale, params.localePrefix);\n\n    // This logic is considered legacy and is replaced by `applyPathnamePrefix`.\n    // We keep it this way for now for backwards compatibility.\n    const localizedPathname = params.localePrefix.mode === 'never' || !utils.isLocalizableHref(params.pathname) ? params.pathname : utils.prefixPathname(prefix, params.pathname);\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return redirectFn(localizedPathname, ...args);\n  };\n}\nconst baseRedirect = createRedirectFn(navigation.redirect);\nconst basePermanentRedirect = createRedirectFn(navigation.permanentRedirect);\n\nexports.basePermanentRedirect = basePermanentRedirect;\nexports.baseRedirect = baseRedirect;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi9zaGFyZWQvcmVkaXJlY3RzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0QsaUJBQWlCLG1CQUFPLENBQUMseUVBQWlCO0FBQzFDLFlBQVksbUJBQU8sQ0FBQyw4RkFBdUI7O0FBRTNDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSwyRkFBMkYsYUFBYTtBQUN4RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSw2QkFBNkI7QUFDN0Isb0JBQW9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvbmF2aWdhdGlvbi9zaGFyZWQvcmVkaXJlY3RzLmpzPzM1NWQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgbmF2aWdhdGlvbiA9IHJlcXVpcmUoJ25leHQvbmF2aWdhdGlvbicpO1xudmFyIHV0aWxzID0gcmVxdWlyZSgnLi4vLi4vc2hhcmVkL3V0aWxzLmpzJyk7XG5cbmZ1bmN0aW9uIGNyZWF0ZVJlZGlyZWN0Rm4ocmVkaXJlY3RGbikge1xuICByZXR1cm4gZnVuY3Rpb24gYmFzZVJlZGlyZWN0KHBhcmFtcykge1xuICAgIGNvbnN0IHByZWZpeCA9IHV0aWxzLmdldExvY2FsZVByZWZpeChwYXJhbXMubG9jYWxlLCBwYXJhbXMubG9jYWxlUHJlZml4KTtcblxuICAgIC8vIFRoaXMgbG9naWMgaXMgY29uc2lkZXJlZCBsZWdhY3kgYW5kIGlzIHJlcGxhY2VkIGJ5IGBhcHBseVBhdGhuYW1lUHJlZml4YC5cbiAgICAvLyBXZSBrZWVwIGl0IHRoaXMgd2F5IGZvciBub3cgZm9yIGJhY2t3YXJkcyBjb21wYXRpYmlsaXR5LlxuICAgIGNvbnN0IGxvY2FsaXplZFBhdGhuYW1lID0gcGFyYW1zLmxvY2FsZVByZWZpeC5tb2RlID09PSAnbmV2ZXInIHx8ICF1dGlscy5pc0xvY2FsaXphYmxlSHJlZihwYXJhbXMucGF0aG5hbWUpID8gcGFyYW1zLnBhdGhuYW1lIDogdXRpbHMucHJlZml4UGF0aG5hbWUocHJlZml4LCBwYXJhbXMucGF0aG5hbWUpO1xuICAgIGZvciAodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCBhcmdzID0gbmV3IEFycmF5KF9sZW4gPiAxID8gX2xlbiAtIDEgOiAwKSwgX2tleSA9IDE7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgIGFyZ3NbX2tleSAtIDFdID0gYXJndW1lbnRzW19rZXldO1xuICAgIH1cbiAgICByZXR1cm4gcmVkaXJlY3RGbihsb2NhbGl6ZWRQYXRobmFtZSwgLi4uYXJncyk7XG4gIH07XG59XG5jb25zdCBiYXNlUmVkaXJlY3QgPSBjcmVhdGVSZWRpcmVjdEZuKG5hdmlnYXRpb24ucmVkaXJlY3QpO1xuY29uc3QgYmFzZVBlcm1hbmVudFJlZGlyZWN0ID0gY3JlYXRlUmVkaXJlY3RGbihuYXZpZ2F0aW9uLnBlcm1hbmVudFJlZGlyZWN0KTtcblxuZXhwb3J0cy5iYXNlUGVybWFuZW50UmVkaXJlY3QgPSBiYXNlUGVybWFuZW50UmVkaXJlY3Q7XG5leHBvcnRzLmJhc2VSZWRpcmVjdCA9IGJhc2VSZWRpcmVjdDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/redirects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\");\n\n/**\n * We have to keep the cookie value in sync as Next.js might\n * skip a request to the server due to its router cache.\n * See https://github.com/amannn/next-intl/issues/786.\n */\nfunction syncLocaleCookie(localeCookie, pathname, locale, nextLocale) {\n  const isSwitchingLocale = nextLocale !== locale && nextLocale != null;\n  if (!localeCookie || !isSwitchingLocale ||\n  // Theoretical case, we always have a pathname in a real app,\n  // only not when running e.g. in a simulated test environment\n  !pathname) {\n    return;\n  }\n  const basePath = utils.getBasePath(pathname);\n  const hasBasePath = basePath !== '';\n  const defaultPath = hasBasePath ? basePath : '/';\n  const {\n    name,\n    ...rest\n  } = localeCookie;\n  if (!rest.path) {\n    rest.path = defaultPath;\n  }\n  let localeCookieString = \"\".concat(name, \"=\").concat(nextLocale, \";\");\n  for (const [key, value] of Object.entries(rest)) {\n    // Map object properties to cookie properties.\n    // Interestingly, `maxAge` corresponds to `max-age`,\n    // while `sameSite` corresponds to `SameSite`.\n    // Also, keys are case-insensitive.\n    const targetKey = key === 'maxAge' ? 'max-age' : key;\n    localeCookieString += \"\".concat(targetKey);\n    if (typeof value !== 'boolean') {\n      localeCookieString += '=' + value;\n    }\n\n    // A trailing \";\" is allowed by browsers\n    localeCookieString += ';';\n  }\n\n  // Note that writing to `document.cookie` doesn't overwrite all\n  // cookies, but only the ones referenced via the name here.\n  document.cookie = localeCookieString;\n}\n\nexports[\"default\"] = syncLocaleCookie;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/syncLocaleCookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/navigation/shared/utils.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar utils = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\");\n\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n\n// For `Link`\n\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\n\nfunction normalizeNameOrNameWithParams(href) {\n  return typeof href === 'string' ? {\n    pathname: href\n  } : href;\n}\nfunction serializeSearchParams(searchParams) {\n  function serializeValue(value) {\n    return String(value);\n  }\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(searchParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(cur => {\n        urlSearchParams.append(key, serializeValue(cur));\n      });\n    } else {\n      urlSearchParams.set(key, serializeValue(value));\n    }\n  }\n  return '?' + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname(_ref) {\n  let {\n    pathname,\n    locale,\n    params,\n    pathnames,\n    query\n  } = _ref;\n  function getNamedPath(value) {\n    let namedPath = pathnames[value];\n    if (!namedPath) {\n      // Unknown pathnames\n      namedPath = value;\n    }\n    return namedPath;\n  }\n  function compilePath(namedPath) {\n    const template = typeof namedPath === 'string' ? namedPath : namedPath[locale];\n    let compiled = template;\n    if (params) {\n      Object.entries(params).forEach(_ref2 => {\n        let [key, value] = _ref2;\n        let regexp, replacer;\n        if (Array.isArray(value)) {\n          regexp = \"(\\\\[)?\\\\[...\".concat(key, \"\\\\](\\\\])?\");\n          replacer = value.map(v => String(v)).join('/');\n        } else {\n          regexp = \"\\\\[\".concat(key, \"\\\\]\");\n          replacer = String(value);\n        }\n        compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);\n      });\n    }\n\n    // Clean up optional catch-all segments that were not replaced\n    compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, '');\n    compiled = utils.normalizeTrailingSlash(compiled);\n    if (compiled.includes('[')) {\n      // Next.js throws anyway, therefore better provide a more helpful error message\n      throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(template, \"\\nParams: \").concat(JSON.stringify(params)));\n    }\n    if (query) {\n      compiled += serializeSearchParams(query);\n    }\n    return compiled;\n  }\n  if (typeof pathname === 'string') {\n    const namedPath = getNamedPath(pathname);\n    const compiled = compilePath(namedPath);\n    return compiled;\n  } else {\n    const {\n      pathname: href,\n      ...rest\n    } = pathname;\n    const namedPath = getNamedPath(href);\n    const compiled = compilePath(namedPath);\n    const result = {\n      ...rest,\n      pathname: compiled\n    };\n    return result;\n  }\n}\nfunction getRoute(locale, pathname, pathnames) {\n  const sortedPathnames = utils.getSortedPathnames(Object.keys(pathnames));\n  const decoded = decodeURI(pathname);\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if (utils.matchesPathname(localizedPathname, decoded)) {\n        return internalPathname;\n      }\n    } else {\n      if (utils.matchesPathname(localizedPathnamesOrPathname[locale], decoded)) {\n        return internalPathname;\n      }\n    }\n  }\n  return pathname;\n}\nfunction getBasePath(pathname) {\n  let windowPathname = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : window.location.pathname;\n  if (pathname === '/') {\n    return windowPathname;\n  } else {\n    return windowPathname.replace(pathname, '');\n  }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, domain, force) {\n  const {\n    mode\n  } = routing.localePrefix;\n  let shouldPrefix;\n  if (force !== undefined) {\n    shouldPrefix = force;\n  } else if (utils.isLocalizableHref(pathname)) {\n    if (mode === 'always') {\n      shouldPrefix = true;\n    } else if (mode === 'as-needed') {\n      let defaultLocale = routing.defaultLocale;\n      if (routing.domains) {\n        const domainConfig = routing.domains.find(cur => cur.domain === domain);\n        if (domainConfig) {\n          defaultLocale = domainConfig.defaultLocale;\n        } else {\n          if (!domain) {\n            console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl-docs.vercel.app/docs/routing#domains-localeprefix-asneeded\");\n          }\n        }\n      }\n      shouldPrefix = defaultLocale !== locale;\n    }\n  }\n  return shouldPrefix ? utils.prefixPathname(utils.getLocalePrefix(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n  var _config$localePrefix;\n  if (((_config$localePrefix = config.localePrefix) === null || _config$localePrefix === void 0 ? void 0 : _config$localePrefix.mode) === 'as-needed' && !('defaultLocale' in config)) {\n    throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n  }\n}\n\nexports.applyPathnamePrefix = applyPathnamePrefix;\nexports.compileLocalizedPathname = compileLocalizedPathname;\nexports.getBasePath = getBasePath;\nexports.getRoute = getRoute;\nexports.normalizeNameOrNameWithParams = normalizeNameOrNameWithParams;\nexports.serializeSearchParams = serializeSearchParams;\nexports.validateReceivedConfig = validateReceivedConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/navigation/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/react-client/index.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/react-client/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar useIntl = __webpack_require__(/*! use-intl */ \"(ssr)/./node_modules/use-intl/dist/index.js\");\n\n/**\n * This is the main entry file when non-'react-server'\n * environments import from 'next-intl'.\n *\n * Maintainer notes:\n * - Make sure this mirrors the API from 'react-server'.\n * - Make sure everything exported from this module is\n *   supported in all Next.js versions that are supported.\n */\n\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction callHook(name, hook) {\n  return function () {\n    try {\n      return hook(...arguments);\n    } catch (_unused) {\n      throw new Error(\"Failed to call `\".concat(name, \"` because the context from `NextIntlClientProvider` was not found.\\n\\nThis can happen because:\\n1) You intended to render this component as a Server Component, the render\\n   failed, and therefore React attempted to render the component on the client\\n   instead. If this is the case, check the console for server errors.\\n2) You intended to render this component on the client side, but no context was found.\\n   Learn more about this error here: https://next-intl-docs.vercel.app/docs/environments/server-client-components#missing-context\") );\n    }\n  };\n}\nconst useTranslations = callHook('useTranslations', useIntl.useTranslations);\nconst useFormatter = callHook('useFormatter', useIntl.useFormatter);\n\nexports.useFormatter = useFormatter;\nexports.useTranslations = useTranslations;\nObject.keys(useIntl).forEach(function (k) {\n  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {\n    enumerable: true,\n    get: function () { return useIntl[k]; }\n  });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/react-client/useLocale.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar _useLocale = __webpack_require__(/*! use-intl/_useLocale */ \"(ssr)/./node_modules/use-intl/dist/_useLocale.js\");\nvar constants = __webpack_require__(/*! ../shared/constants.js */ \"(ssr)/./node_modules/next-intl/dist/development/shared/constants.js\");\n\nfunction useLocale() {\n  // The types aren't entirely correct here. Outside of Next.js\n  // `useParams` can be called, but the return type is `null`.\n  const params = navigation.useParams();\n  let locale;\n  try {\n    // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks, react-compiler/react-compiler -- False positive\n    locale = _useLocale.useLocale();\n  } catch (error) {\n    if (typeof (params === null || params === void 0 ? void 0 : params[constants.LOCALE_SEGMENT_NAME]) === 'string') {\n      locale = params[constants.LOCALE_SEGMENT_NAME];\n    } else {\n      throw error;\n    }\n  }\n  return locale;\n}\n\nexports[\"default\"] = useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcmVhY3QtY2xpZW50L3VzZUxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdELGlCQUFpQixtQkFBTyxDQUFDLHlFQUFpQjtBQUMxQyxpQkFBaUIsbUJBQU8sQ0FBQyw2RUFBcUI7QUFDOUMsZ0JBQWdCLG1CQUFPLENBQUMsbUdBQXdCOztBQUVoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvcmVhY3QtY2xpZW50L3VzZUxvY2FsZS5qcz9lMzQ5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxudmFyIG5hdmlnYXRpb24gPSByZXF1aXJlKCduZXh0L25hdmlnYXRpb24nKTtcbnZhciBfdXNlTG9jYWxlID0gcmVxdWlyZSgndXNlLWludGwvX3VzZUxvY2FsZScpO1xudmFyIGNvbnN0YW50cyA9IHJlcXVpcmUoJy4uL3NoYXJlZC9jb25zdGFudHMuanMnKTtcblxuZnVuY3Rpb24gdXNlTG9jYWxlKCkge1xuICAvLyBUaGUgdHlwZXMgYXJlbid0IGVudGlyZWx5IGNvcnJlY3QgaGVyZS4gT3V0c2lkZSBvZiBOZXh0LmpzXG4gIC8vIGB1c2VQYXJhbXNgIGNhbiBiZSBjYWxsZWQsIGJ1dCB0aGUgcmV0dXJuIHR5cGUgaXMgYG51bGxgLlxuICBjb25zdCBwYXJhbXMgPSBuYXZpZ2F0aW9uLnVzZVBhcmFtcygpO1xuICBsZXQgbG9jYWxlO1xuICB0cnkge1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1jb21waWxlci9yZWFjdC1jb21waWxlclxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1ob29rcy9ydWxlcy1vZi1ob29rcywgcmVhY3QtY29tcGlsZXIvcmVhY3QtY29tcGlsZXIgLS0gRmFsc2UgcG9zaXRpdmVcbiAgICBsb2NhbGUgPSBfdXNlTG9jYWxlLnVzZUxvY2FsZSgpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGlmICh0eXBlb2YgKHBhcmFtcyA9PT0gbnVsbCB8fCBwYXJhbXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHBhcmFtc1tjb25zdGFudHMuTE9DQUxFX1NFR01FTlRfTkFNRV0pID09PSAnc3RyaW5nJykge1xuICAgICAgbG9jYWxlID0gcGFyYW1zW2NvbnN0YW50cy5MT0NBTEVfU0VHTUVOVF9OQU1FXTtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG4gIHJldHVybiBsb2NhbGU7XG59XG5cbmV4cG9ydHMuZGVmYXVsdCA9IHVzZUxvY2FsZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/react-client/useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/routing/config.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/routing/config.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction receiveRoutingConfig(input) {\n  var _input$localeDetectio, _input$alternateLinks;\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: (_input$localeDetectio = input.localeDetection) !== null && _input$localeDetectio !== void 0 ? _input$localeDetectio : true,\n    alternateLinks: (_input$alternateLinks = input.alternateLinks) !== null && _input$alternateLinks !== void 0 ? _input$alternateLinks : true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return (localeCookie !== null && localeCookie !== void 0 ? localeCookie : true) ? {\n    name: 'NEXT_LOCALE',\n    maxAge: ********,\n    // 1 year\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\nexports.receiveLocaleCookie = receiveLocaleCookie;\nexports.receiveLocalePrefixConfig = receiveLocalePrefixConfig;\nexports.receiveRoutingConfig = receiveRoutingConfig;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/routing/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/server.react-client.js":
/*!************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/server.react-client.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar index = __webpack_require__(/*! ./server/react-client/index.js */ \"(ssr)/./node_modules/next-intl/dist/development/server/react-client/index.js\");\n\n\n\nexports.getFormatter = index.getFormatter;\nexports.getLocale = index.getLocale;\nexports.getMessages = index.getMessages;\nexports.getNow = index.getNow;\nexports.getRequestConfig = index.getRequestConfig;\nexports.getTimeZone = index.getTimeZone;\nexports.getTranslations = index.getTranslations;\nexports.setRequestLocale = index.setRequestLocale;\nexports.unstable_setRequestLocale = index.unstable_setRequestLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvc2VydmVyLnJlYWN0LWNsaWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdELFlBQVksbUJBQU8sQ0FBQyxvSEFBZ0M7Ozs7QUFJcEQsb0JBQW9CO0FBQ3BCLGlCQUFpQjtBQUNqQixtQkFBbUI7QUFDbkIsY0FBYztBQUNkLHdCQUF3QjtBQUN4QixtQkFBbUI7QUFDbkIsdUJBQXVCO0FBQ3ZCLHdCQUF3QjtBQUN4QixpQ0FBaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9kZXZlbG9wbWVudC9zZXJ2ZXIucmVhY3QtY2xpZW50LmpzPzIwYWEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuXG52YXIgaW5kZXggPSByZXF1aXJlKCcuL3NlcnZlci9yZWFjdC1jbGllbnQvaW5kZXguanMnKTtcblxuXG5cbmV4cG9ydHMuZ2V0Rm9ybWF0dGVyID0gaW5kZXguZ2V0Rm9ybWF0dGVyO1xuZXhwb3J0cy5nZXRMb2NhbGUgPSBpbmRleC5nZXRMb2NhbGU7XG5leHBvcnRzLmdldE1lc3NhZ2VzID0gaW5kZXguZ2V0TWVzc2FnZXM7XG5leHBvcnRzLmdldE5vdyA9IGluZGV4LmdldE5vdztcbmV4cG9ydHMuZ2V0UmVxdWVzdENvbmZpZyA9IGluZGV4LmdldFJlcXVlc3RDb25maWc7XG5leHBvcnRzLmdldFRpbWVab25lID0gaW5kZXguZ2V0VGltZVpvbmU7XG5leHBvcnRzLmdldFRyYW5zbGF0aW9ucyA9IGluZGV4LmdldFRyYW5zbGF0aW9ucztcbmV4cG9ydHMuc2V0UmVxdWVzdExvY2FsZSA9IGluZGV4LnNldFJlcXVlc3RMb2NhbGU7XG5leHBvcnRzLnVuc3RhYmxlX3NldFJlcXVlc3RMb2NhbGUgPSBpbmRleC51bnN0YWJsZV9zZXRSZXF1ZXN0TG9jYWxlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/server.react-client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/server/react-client/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/server/react-client/index.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\n/**\n * Allows to import `next-intl/server` in non-RSC environments.\n *\n * This is mostly relevant for testing, since e.g. a `generateMetadata`\n * export from a page might use `next-intl/server`, but the test\n * only uses the default export for a page.\n */\n\nfunction notSupported(message) {\n  return () => {\n    throw new Error(\"`\".concat(message, \"` is not supported in Client Components.\"));\n  };\n}\nfunction getRequestConfig() {\n  return notSupported('getRequestConfig');\n}\nconst getFormatter = notSupported('getFormatter');\nconst getNow = notSupported('getNow');\nconst getTimeZone = notSupported('getTimeZone');\nconst getMessages = notSupported('getMessages');\nconst getLocale = notSupported('getLocale');\n\n// The type of `getTranslations` is not assigned here because it\n// causes a type error. The types use the `react-server` entry\n// anyway, therefore this is irrelevant.\nconst getTranslations = notSupported('getTranslations');\nconst unstable_setRequestLocale = notSupported('unstable_setRequestLocale');\nconst setRequestLocale = notSupported('setRequestLocale');\n\nexports.getFormatter = getFormatter;\nexports.getLocale = getLocale;\nexports.getMessages = getMessages;\nexports.getNow = getNow;\nexports.getRequestConfig = getRequestConfig;\nexports.getTimeZone = getTimeZone;\nexports.getTranslations = getTranslations;\nexports.setRequestLocale = setRequestLocale;\nexports.unstable_setRequestLocale = unstable_setRequestLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/server/react-client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar _rollupPluginBabelHelpers = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/development/_virtual/_rollupPluginBabelHelpers.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar _IntlProvider = __webpack_require__(/*! use-intl/_IntlProvider */ \"(ssr)/./node_modules/use-intl/dist/_IntlProvider.js\");\nfunction _interopDefault(e) {\n    return e && e.__esModule ? e : {\n        default: e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefault(React);\nfunction NextIntlClientProvider(_ref) {\n    let { locale, ...rest } = _ref;\n    // TODO: We could call `useParams` here to receive a default value\n    // for `locale`, but this would require dropping Next.js <13.\n    if (!locale) {\n        throw new Error(\"Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl-docs.vercel.app/docs/configuration#locale\");\n    }\n    return /*#__PURE__*/ React__default.default.createElement(_IntlProvider.IntlProvider, _rollupPluginBabelHelpers.extends({\n        locale: locale\n    }, rest));\n}\nexports[\"default\"] = NextIntlClientProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/constants.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/constants.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\n// Should take precedence over the cookie\nconst HEADER_LOCALE_NAME = 'X-NEXT-INTL-LOCALE';\n\n// In a URL like \"/en-US/about\", the locale segment is \"en-US\"\nconst LOCALE_SEGMENT_NAME = 'locale';\n\nexports.HEADER_LOCALE_NAME = HEADER_LOCALE_NAME;\nexports.LOCALE_SEGMENT_NAME = LOCALE_SEGMENT_NAME;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvc2hhcmVkL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7O0FBRTdEO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSwwQkFBMEI7QUFDMUIsMkJBQTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvc2hhcmVkL2NvbnN0YW50cy5qcz85ZThlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxuLy8gU2hvdWxkIHRha2UgcHJlY2VkZW5jZSBvdmVyIHRoZSBjb29raWVcbmNvbnN0IEhFQURFUl9MT0NBTEVfTkFNRSA9ICdYLU5FWFQtSU5UTC1MT0NBTEUnO1xuXG4vLyBJbiBhIFVSTCBsaWtlIFwiL2VuLVVTL2Fib3V0XCIsIHRoZSBsb2NhbGUgc2VnbWVudCBpcyBcImVuLVVTXCJcbmNvbnN0IExPQ0FMRV9TRUdNRU5UX05BTUUgPSAnbG9jYWxlJztcblxuZXhwb3J0cy5IRUFERVJfTE9DQUxFX05BTUUgPSBIRUFERVJfTE9DQUxFX05BTUU7XG5leHBvcnRzLkxPQ0FMRV9TRUdNRU5UX05BTUUgPSBMT0NBTEVfU0VHTUVOVF9OQU1FO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/development/shared/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next-intl/dist/development/shared/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction localizeHref(href, locale) {\n  let curLocale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : locale;\n  let curPathname = arguments.length > 3 ? arguments[3] : undefined;\n  let prefix = arguments.length > 4 ? arguments[4] : undefined;\n  if (!isLocalizableHref(href)) {\n    return href;\n  }\n  const isSwitchingLocale = locale !== curLocale;\n  const isPathnamePrefixed = hasPathnamePrefixed(prefix, curPathname);\n  const shouldPrefix = isSwitchingLocale || isPathnamePrefixed;\n  if (shouldPrefix && prefix != null) {\n    return prefixHref(href, prefix);\n  }\n  return href;\n}\nfunction prefixHref(href, prefix) {\n  let prefixedHref;\n  if (typeof href === 'string') {\n    prefixedHref = prefixPathname(prefix, href);\n  } else {\n    prefixedHref = {\n      ...href\n    };\n    if (href.pathname) {\n      prefixedHref.pathname = prefixPathname(prefix, href.pathname);\n    }\n  }\n  return prefixedHref;\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(\"^\".concat(prefix)), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(\"\".concat(prefix, \"/\"));\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch (_unused) {\n    return false;\n  }\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  if (pathname !== '/') {\n    const pathnameEndsWithSlash = pathname.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      pathname += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      pathname = pathname.slice(0, -1);\n    }\n  }\n  return pathname;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  var _localePrefix$prefixe;\n  return localePrefix.mode !== 'never' && ((_localePrefix$prefixe = localePrefix.prefixes) === null || _localePrefix$prefixe === void 0 ? void 0 : _localePrefix$prefixe[locale]) ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(\"^\".concat(regexPattern, \"$\"));\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\n\nexports.getLocalePrefix = getLocalePrefix;\nexports.getSortedPathnames = getSortedPathnames;\nexports.hasPathnamePrefixed = hasPathnamePrefixed;\nexports.isLocalizableHref = isLocalizableHref;\nexports.localizeHref = localizeHref;\nexports.matchesPathname = matchesPathname;\nexports.normalizeTrailingSlash = normalizeTrailingSlash;\nexports.prefixHref = prefixHref;\nexports.prefixPathname = prefixPathname;\nexports.templateToRegex = templateToRegex;\nexports.unprefixPathname = unprefixPathname;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvc2hhcmVkL3V0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGVBQWU7QUFDakM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsdUJBQXVCO0FBQ3ZCLDBCQUEwQjtBQUMxQiwyQkFBMkI7QUFDM0IseUJBQXlCO0FBQ3pCLG9CQUFvQjtBQUNwQix1QkFBdUI7QUFDdkIsOEJBQThCO0FBQzlCLGtCQUFrQjtBQUNsQixzQkFBc0I7QUFDdEIsdUJBQXVCO0FBQ3ZCLHdCQUF3QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2RldmVsb3BtZW50L3NoYXJlZC91dGlscy5qcz8wYTUyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcblxuZnVuY3Rpb24gaXNSZWxhdGl2ZUhyZWYoaHJlZikge1xuICBjb25zdCBwYXRobmFtZSA9IHR5cGVvZiBocmVmID09PSAnb2JqZWN0JyA/IGhyZWYucGF0aG5hbWUgOiBocmVmO1xuICByZXR1cm4gcGF0aG5hbWUgIT0gbnVsbCAmJiAhcGF0aG5hbWUuc3RhcnRzV2l0aCgnLycpO1xufVxuZnVuY3Rpb24gaXNMb2NhbEhyZWYoaHJlZikge1xuICBpZiAodHlwZW9mIGhyZWYgPT09ICdvYmplY3QnKSB7XG4gICAgcmV0dXJuIGhyZWYuaG9zdCA9PSBudWxsICYmIGhyZWYuaG9zdG5hbWUgPT0gbnVsbDtcbiAgfSBlbHNlIHtcbiAgICBjb25zdCBoYXNQcm90b2NvbCA9IC9eW2Etel0rOi9pLnRlc3QoaHJlZik7XG4gICAgcmV0dXJuICFoYXNQcm90b2NvbDtcbiAgfVxufVxuZnVuY3Rpb24gaXNMb2NhbGl6YWJsZUhyZWYoaHJlZikge1xuICByZXR1cm4gaXNMb2NhbEhyZWYoaHJlZikgJiYgIWlzUmVsYXRpdmVIcmVmKGhyZWYpO1xufVxuZnVuY3Rpb24gbG9jYWxpemVIcmVmKGhyZWYsIGxvY2FsZSkge1xuICBsZXQgY3VyTG9jYWxlID0gYXJndW1lbnRzLmxlbmd0aCA+IDIgJiYgYXJndW1lbnRzWzJdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMl0gOiBsb2NhbGU7XG4gIGxldCBjdXJQYXRobmFtZSA9IGFyZ3VtZW50cy5sZW5ndGggPiAzID8gYXJndW1lbnRzWzNdIDogdW5kZWZpbmVkO1xuICBsZXQgcHJlZml4ID0gYXJndW1lbnRzLmxlbmd0aCA+IDQgPyBhcmd1bWVudHNbNF0gOiB1bmRlZmluZWQ7XG4gIGlmICghaXNMb2NhbGl6YWJsZUhyZWYoaHJlZikpIHtcbiAgICByZXR1cm4gaHJlZjtcbiAgfVxuICBjb25zdCBpc1N3aXRjaGluZ0xvY2FsZSA9IGxvY2FsZSAhPT0gY3VyTG9jYWxlO1xuICBjb25zdCBpc1BhdGhuYW1lUHJlZml4ZWQgPSBoYXNQYXRobmFtZVByZWZpeGVkKHByZWZpeCwgY3VyUGF0aG5hbWUpO1xuICBjb25zdCBzaG91bGRQcmVmaXggPSBpc1N3aXRjaGluZ0xvY2FsZSB8fCBpc1BhdGhuYW1lUHJlZml4ZWQ7XG4gIGlmIChzaG91bGRQcmVmaXggJiYgcHJlZml4ICE9IG51bGwpIHtcbiAgICByZXR1cm4gcHJlZml4SHJlZihocmVmLCBwcmVmaXgpO1xuICB9XG4gIHJldHVybiBocmVmO1xufVxuZnVuY3Rpb24gcHJlZml4SHJlZihocmVmLCBwcmVmaXgpIHtcbiAgbGV0IHByZWZpeGVkSHJlZjtcbiAgaWYgKHR5cGVvZiBocmVmID09PSAnc3RyaW5nJykge1xuICAgIHByZWZpeGVkSHJlZiA9IHByZWZpeFBhdGhuYW1lKHByZWZpeCwgaHJlZik7XG4gIH0gZWxzZSB7XG4gICAgcHJlZml4ZWRIcmVmID0ge1xuICAgICAgLi4uaHJlZlxuICAgIH07XG4gICAgaWYgKGhyZWYucGF0aG5hbWUpIHtcbiAgICAgIHByZWZpeGVkSHJlZi5wYXRobmFtZSA9IHByZWZpeFBhdGhuYW1lKHByZWZpeCwgaHJlZi5wYXRobmFtZSk7XG4gICAgfVxuICB9XG4gIHJldHVybiBwcmVmaXhlZEhyZWY7XG59XG5mdW5jdGlvbiB1bnByZWZpeFBhdGhuYW1lKHBhdGhuYW1lLCBwcmVmaXgpIHtcbiAgcmV0dXJuIHBhdGhuYW1lLnJlcGxhY2UobmV3IFJlZ0V4cChcIl5cIi5jb25jYXQocHJlZml4KSksICcnKSB8fCAnLyc7XG59XG5mdW5jdGlvbiBwcmVmaXhQYXRobmFtZShwcmVmaXgsIHBhdGhuYW1lKSB7XG4gIGxldCBsb2NhbGl6ZWRIcmVmID0gcHJlZml4O1xuXG4gIC8vIEF2b2lkIHRyYWlsaW5nIHNsYXNoZXNcbiAgaWYgKC9eXFwvKFxcPy4qKT8kLy50ZXN0KHBhdGhuYW1lKSkge1xuICAgIHBhdGhuYW1lID0gcGF0aG5hbWUuc2xpY2UoMSk7XG4gIH1cbiAgbG9jYWxpemVkSHJlZiArPSBwYXRobmFtZTtcbiAgcmV0dXJuIGxvY2FsaXplZEhyZWY7XG59XG5mdW5jdGlvbiBoYXNQYXRobmFtZVByZWZpeGVkKHByZWZpeCwgcGF0aG5hbWUpIHtcbiAgcmV0dXJuIHBhdGhuYW1lID09PSBwcmVmaXggfHwgcGF0aG5hbWUuc3RhcnRzV2l0aChcIlwiLmNvbmNhdChwcmVmaXgsIFwiL1wiKSk7XG59XG5mdW5jdGlvbiBoYXNUcmFpbGluZ1NsYXNoKCkge1xuICB0cnkge1xuICAgIC8vIFByb3ZpZGVkIHZpYSBgZW52YCBzZXR0aW5nIGluIGBuZXh0LmNvbmZpZy5qc2AgdmlhIHRoZSBwbHVnaW5cbiAgICByZXR1cm4gcHJvY2Vzcy5lbnYuX25leHRfaW50bF90cmFpbGluZ19zbGFzaCA9PT0gJ3RydWUnO1xuICB9IGNhdGNoIChfdW51c2VkKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG59XG5mdW5jdGlvbiBub3JtYWxpemVUcmFpbGluZ1NsYXNoKHBhdGhuYW1lKSB7XG4gIGNvbnN0IHRyYWlsaW5nU2xhc2ggPSBoYXNUcmFpbGluZ1NsYXNoKCk7XG4gIGlmIChwYXRobmFtZSAhPT0gJy8nKSB7XG4gICAgY29uc3QgcGF0aG5hbWVFbmRzV2l0aFNsYXNoID0gcGF0aG5hbWUuZW5kc1dpdGgoJy8nKTtcbiAgICBpZiAodHJhaWxpbmdTbGFzaCAmJiAhcGF0aG5hbWVFbmRzV2l0aFNsYXNoKSB7XG4gICAgICBwYXRobmFtZSArPSAnLyc7XG4gICAgfSBlbHNlIGlmICghdHJhaWxpbmdTbGFzaCAmJiBwYXRobmFtZUVuZHNXaXRoU2xhc2gpIHtcbiAgICAgIHBhdGhuYW1lID0gcGF0aG5hbWUuc2xpY2UoMCwgLTEpO1xuICAgIH1cbiAgfVxuICByZXR1cm4gcGF0aG5hbWU7XG59XG5mdW5jdGlvbiBtYXRjaGVzUGF0aG5hbWUoLyoqIEUuZy4gYC91c2Vycy9bdXNlcklkXS1bdXNlck5hbWVdYCAqL1xudGVtcGxhdGUsIC8qKiBFLmcuIGAvdXNlcnMvMjMtamFuZWAgKi9cbnBhdGhuYW1lKSB7XG4gIGNvbnN0IG5vcm1hbGl6ZWRUZW1wbGF0ZSA9IG5vcm1hbGl6ZVRyYWlsaW5nU2xhc2godGVtcGxhdGUpO1xuICBjb25zdCBub3JtYWxpemVkUGF0aG5hbWUgPSBub3JtYWxpemVUcmFpbGluZ1NsYXNoKHBhdGhuYW1lKTtcbiAgY29uc3QgcmVnZXggPSB0ZW1wbGF0ZVRvUmVnZXgobm9ybWFsaXplZFRlbXBsYXRlKTtcbiAgcmV0dXJuIHJlZ2V4LnRlc3Qobm9ybWFsaXplZFBhdGhuYW1lKTtcbn1cbmZ1bmN0aW9uIGdldExvY2FsZVByZWZpeChsb2NhbGUsIGxvY2FsZVByZWZpeCkge1xuICB2YXIgX2xvY2FsZVByZWZpeCRwcmVmaXhlO1xuICByZXR1cm4gbG9jYWxlUHJlZml4Lm1vZGUgIT09ICduZXZlcicgJiYgKChfbG9jYWxlUHJlZml4JHByZWZpeGUgPSBsb2NhbGVQcmVmaXgucHJlZml4ZXMpID09PSBudWxsIHx8IF9sb2NhbGVQcmVmaXgkcHJlZml4ZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2xvY2FsZVByZWZpeCRwcmVmaXhlW2xvY2FsZV0pIHx8XG4gIC8vIFdlIHJldHVybiBhIHByZWZpeCBldmVuIGlmIGBtb2RlOiAnbmV2ZXInYC4gSXQncyB1cCB0byB0aGUgY29uc3VtZXJcbiAgLy8gdG8gZGVjaWRlIHRvIHVzZSBpdCBvciBub3QuXG4gICcvJyArIGxvY2FsZTtcbn1cbmZ1bmN0aW9uIHRlbXBsYXRlVG9SZWdleCh0ZW1wbGF0ZSkge1xuICBjb25zdCByZWdleFBhdHRlcm4gPSB0ZW1wbGF0ZVxuICAvLyBSZXBsYWNlIG9wdGlvbmFsIGNhdGNoYWxsICgnW1suLi5zbHVnXV0nKVxuICAucmVwbGFjZSgvXFxbXFxbKFxcLlxcLlxcLlteXFxdXSspXFxdXFxdL2csICc/KC4qKScpXG4gIC8vIFJlcGxhY2UgY2F0Y2hhbGwgKCdbLi4uc2x1Z10nKVxuICAucmVwbGFjZSgvXFxbKFxcLlxcLlxcLlteXFxdXSspXFxdL2csICcoLispJylcbiAgLy8gUmVwbGFjZSByZWd1bGFyIHBhcmFtZXRlciAoJ1tzbHVnXScpXG4gIC5yZXBsYWNlKC9cXFsoW15cXF1dKylcXF0vZywgJyhbXi9dKyknKTtcbiAgcmV0dXJuIG5ldyBSZWdFeHAoXCJeXCIuY29uY2F0KHJlZ2V4UGF0dGVybiwgXCIkXCIpKTtcbn1cbmZ1bmN0aW9uIGlzT3B0aW9uYWxDYXRjaEFsbFNlZ21lbnQocGF0aG5hbWUpIHtcbiAgcmV0dXJuIHBhdGhuYW1lLmluY2x1ZGVzKCdbWy4uLicpO1xufVxuZnVuY3Rpb24gaXNDYXRjaEFsbFNlZ21lbnQocGF0aG5hbWUpIHtcbiAgcmV0dXJuIHBhdGhuYW1lLmluY2x1ZGVzKCdbLi4uJyk7XG59XG5mdW5jdGlvbiBpc0R5bmFtaWNTZWdtZW50KHBhdGhuYW1lKSB7XG4gIHJldHVybiBwYXRobmFtZS5pbmNsdWRlcygnWycpO1xufVxuZnVuY3Rpb24gY29tcGFyZVBhdGhuYW1lUGFpcnMoYSwgYikge1xuICBjb25zdCBwYXRoQSA9IGEuc3BsaXQoJy8nKTtcbiAgY29uc3QgcGF0aEIgPSBiLnNwbGl0KCcvJyk7XG4gIGNvbnN0IG1heExlbmd0aCA9IE1hdGgubWF4KHBhdGhBLmxlbmd0aCwgcGF0aEIubGVuZ3RoKTtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBtYXhMZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IHNlZ21lbnRBID0gcGF0aEFbaV07XG4gICAgY29uc3Qgc2VnbWVudEIgPSBwYXRoQltpXTtcblxuICAgIC8vIElmIG9uZSBvZiB0aGUgcGF0aHMgZW5kcywgcHJpb3JpdGl6ZSB0aGUgc2hvcnRlciBwYXRoXG4gICAgaWYgKCFzZWdtZW50QSAmJiBzZWdtZW50QikgcmV0dXJuIC0xO1xuICAgIGlmIChzZWdtZW50QSAmJiAhc2VnbWVudEIpIHJldHVybiAxO1xuICAgIGlmICghc2VnbWVudEEgJiYgIXNlZ21lbnRCKSBjb250aW51ZTtcblxuICAgIC8vIFByaW9yaXRpemUgc3RhdGljIHNlZ21lbnRzIG92ZXIgZHluYW1pYyBzZWdtZW50c1xuICAgIGlmICghaXNEeW5hbWljU2VnbWVudChzZWdtZW50QSkgJiYgaXNEeW5hbWljU2VnbWVudChzZWdtZW50QikpIHJldHVybiAtMTtcbiAgICBpZiAoaXNEeW5hbWljU2VnbWVudChzZWdtZW50QSkgJiYgIWlzRHluYW1pY1NlZ21lbnQoc2VnbWVudEIpKSByZXR1cm4gMTtcblxuICAgIC8vIFByaW9yaXRpemUgbm9uLWNhdGNoLWFsbCBzZWdtZW50cyBvdmVyIGNhdGNoLWFsbCBzZWdtZW50c1xuICAgIGlmICghaXNDYXRjaEFsbFNlZ21lbnQoc2VnbWVudEEpICYmIGlzQ2F0Y2hBbGxTZWdtZW50KHNlZ21lbnRCKSkgcmV0dXJuIC0xO1xuICAgIGlmIChpc0NhdGNoQWxsU2VnbWVudChzZWdtZW50QSkgJiYgIWlzQ2F0Y2hBbGxTZWdtZW50KHNlZ21lbnRCKSkgcmV0dXJuIDE7XG5cbiAgICAvLyBQcmlvcml0aXplIG5vbi1vcHRpb25hbCBjYXRjaC1hbGwgc2VnbWVudHMgb3ZlciBvcHRpb25hbCBjYXRjaC1hbGwgc2VnbWVudHNcbiAgICBpZiAoIWlzT3B0aW9uYWxDYXRjaEFsbFNlZ21lbnQoc2VnbWVudEEpICYmIGlzT3B0aW9uYWxDYXRjaEFsbFNlZ21lbnQoc2VnbWVudEIpKSB7XG4gICAgICByZXR1cm4gLTE7XG4gICAgfVxuICAgIGlmIChpc09wdGlvbmFsQ2F0Y2hBbGxTZWdtZW50KHNlZ21lbnRBKSAmJiAhaXNPcHRpb25hbENhdGNoQWxsU2VnbWVudChzZWdtZW50QikpIHtcbiAgICAgIHJldHVybiAxO1xuICAgIH1cbiAgICBpZiAoc2VnbWVudEEgPT09IHNlZ21lbnRCKSBjb250aW51ZTtcbiAgfVxuXG4gIC8vIEJvdGggcGF0aG5hbWVzIGFyZSBjb21wbGV0ZWx5IHN0YXRpY1xuICByZXR1cm4gMDtcbn1cbmZ1bmN0aW9uIGdldFNvcnRlZFBhdGhuYW1lcyhwYXRobmFtZXMpIHtcbiAgcmV0dXJuIHBhdGhuYW1lcy5zb3J0KGNvbXBhcmVQYXRobmFtZVBhaXJzKTtcbn1cblxuZXhwb3J0cy5nZXRMb2NhbGVQcmVmaXggPSBnZXRMb2NhbGVQcmVmaXg7XG5leHBvcnRzLmdldFNvcnRlZFBhdGhuYW1lcyA9IGdldFNvcnRlZFBhdGhuYW1lcztcbmV4cG9ydHMuaGFzUGF0aG5hbWVQcmVmaXhlZCA9IGhhc1BhdGhuYW1lUHJlZml4ZWQ7XG5leHBvcnRzLmlzTG9jYWxpemFibGVIcmVmID0gaXNMb2NhbGl6YWJsZUhyZWY7XG5leHBvcnRzLmxvY2FsaXplSHJlZiA9IGxvY2FsaXplSHJlZjtcbmV4cG9ydHMubWF0Y2hlc1BhdGhuYW1lID0gbWF0Y2hlc1BhdGhuYW1lO1xuZXhwb3J0cy5ub3JtYWxpemVUcmFpbGluZ1NsYXNoID0gbm9ybWFsaXplVHJhaWxpbmdTbGFzaDtcbmV4cG9ydHMucHJlZml4SHJlZiA9IHByZWZpeEhyZWY7XG5leHBvcnRzLnByZWZpeFBhdGhuYW1lID0gcHJlZml4UGF0aG5hbWU7XG5leHBvcnRzLnRlbXBsYXRlVG9SZWdleCA9IHRlbXBsYXRlVG9SZWdleDtcbmV4cG9ydHMudW5wcmVmaXhQYXRobmFtZSA9IHVucHJlZml4UGF0aG5hbWU7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/development/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": () => (/* binding */ n)\n/* harmony export */ });\nfunction n(){return n=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)({}).hasOwnProperty.call(t,a)&&(n[a]=t[a])}return n},n.apply(null,arguments)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGFBQWEsd0RBQXdELFlBQVksbUJBQW1CLEtBQUssbUJBQW1CLGtCQUFrQix3Q0FBd0MsU0FBUyx5QkFBOEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcz9iZjk3Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIG4oKXtyZXR1cm4gbj1PYmplY3QuYXNzaWduP09iamVjdC5hc3NpZ24uYmluZCgpOmZ1bmN0aW9uKG4pe2Zvcih2YXIgcj0xO3I8YXJndW1lbnRzLmxlbmd0aDtyKyspe3ZhciB0PWFyZ3VtZW50c1tyXTtmb3IodmFyIGEgaW4gdCkoe30pLmhhc093blByb3BlcnR5LmNhbGwodCxhKSYmKG5bYV09dFthXSl9cmV0dXJuIG59LG4uYXBwbHkobnVsbCxhcmd1bWVudHMpfWV4cG9ydHtuIGFzIGV4dGVuZHN9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js\");\n/* harmony import */ var _syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./syncLocaleCookie.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction s(n, s) {\n    let { defaultLocale: p, href: f, locale: m, localeCookie: u, onClick: h, prefetch: d, unprefixed: k, ...x } = n;\n    const L = (0,_react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(), g = m !== L, j = m || L, v = function() {\n        const [e, o] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n        return (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n            o(window.location.host);\n        }, []), e;\n    }(), w = v && k && (k.domains[v] === j || !Object.keys(k.domains).includes(v) && L === p && !m) ? k.pathname : f, C = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    return g && (d && console.error(\"The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`\"), d = !1), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(next_link__WEBPACK_IMPORTED_MODULE_0__[\"default\"], (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_4__[\"extends\"])({\n        ref: s,\n        href: w,\n        hrefLang: g ? m : void 0,\n        onClick: function(e) {\n            (0,_syncLocaleCookie_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(u, C, L, m), h && h(e);\n        },\n        prefetch: d\n    }, x));\n}\nvar p = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(s);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ p)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../react-client/useLocale.js */ \"(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BaseLink.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction f(l, f) {\n    let { href: p, locale: u, localeCookie: d, localePrefixMode: x, prefix: j, ...k } = l;\n    const h = (0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.usePathname)(), v = (0,_react_client_useLocale_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(), C = u !== v, [L, g] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.isLocalizableHref)(p) && (\"never\" !== x || C) ? (0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.prefixHref)(p, j) : p);\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        h && g((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_3__.localizeHref)(p, u, v, h, j));\n    }, [\n        v,\n        p,\n        u,\n        h,\n        j\n    ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_BaseLink_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_5__[\"extends\"])({\n        ref: f,\n        href: L,\n        locale: u,\n        localeCookie: d\n    }, k));\n}\nconst p = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(f);\np.displayName = \"ClientLink\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/LegacyBaseLink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ o)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\");\nfunction o(o,e,n,a){if(!o||!(a!==n&&null!=a)||!e)return;const c=(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.getBasePath)(e),f=\"\"!==c?c:\"/\",{name:r,...i}=o;i.path||(i.path=f);let l=\"\".concat(r,\"=\").concat(a,\";\");for(const[t,o]of Object.entries(i)){l+=\"\".concat(\"maxAge\"===t?\"max-age\":t),\"boolean\"!=typeof o&&(l+=\"=\"+o),l+=\";\"}document.cookie=l}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vc2hhcmVkL3N5bmNMb2NhbGVDb29raWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUMsb0JBQW9CLG9DQUFvQyxRQUFRLHNEQUFDLG9CQUFvQixZQUFZLEdBQUcsbUJBQW1CLGtDQUFrQyxHQUFHLG9DQUFvQyw0RUFBNEUsRUFBRSxrQkFBdUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vbmF2aWdhdGlvbi9zaGFyZWQvc3luY0xvY2FsZUNvb2tpZS5qcz84ZjNiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtnZXRCYXNlUGF0aCBhcyB0fWZyb21cIi4vdXRpbHMuanNcIjtmdW5jdGlvbiBvKG8sZSxuLGEpe2lmKCFvfHwhKGEhPT1uJiZudWxsIT1hKXx8IWUpcmV0dXJuO2NvbnN0IGM9dChlKSxmPVwiXCIhPT1jP2M6XCIvXCIse25hbWU6ciwuLi5pfT1vO2kucGF0aHx8KGkucGF0aD1mKTtsZXQgbD1cIlwiLmNvbmNhdChyLFwiPVwiKS5jb25jYXQoYSxcIjtcIik7Zm9yKGNvbnN0W3Qsb11vZiBPYmplY3QuZW50cmllcyhpKSl7bCs9XCJcIi5jb25jYXQoXCJtYXhBZ2VcIj09PXQ/XCJtYXgtYWdlXCI6dCksXCJib29sZWFuXCIhPXR5cGVvZiBvJiYobCs9XCI9XCIrbyksbCs9XCI7XCJ9ZG9jdW1lbnQuY29va2llPWx9ZXhwb3J0e28gYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/syncLocaleCookie.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ d),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ s),\n/* harmony export */   getBasePath: () => (/* binding */ l),\n/* harmony export */   getRoute: () => (/* binding */ f),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ i),\n/* harmony export */   serializeSearchParams: () => (/* binding */ c),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ u)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js\");\nfunction i(e){return\"string\"==typeof e?{pathname:e}:e}function c(e){function n(e){return String(e)}const t=new URLSearchParams;for(const[r,o]of Object.entries(e))Array.isArray(o)?o.forEach((e=>{t.append(r,n(e))})):t.set(r,n(o));return\"?\"+t.toString()}function s(e){let{pathname:n,locale:t,params:r,pathnames:o,query:i}=e;function s(e){let n=o[e];return n||(n=e),n}function f(e){const n=\"string\"==typeof e?e:e[t];let o=n;if(r&&Object.entries(r).forEach((e=>{let n,t,[r,a]=e;Array.isArray(a)?(n=\"(\\\\[)?\\\\[...\".concat(r,\"\\\\](\\\\])?\"),t=a.map((e=>String(e))).join(\"/\")):(n=\"\\\\[\".concat(r,\"\\\\]\"),t=String(a)),o=o.replace(new RegExp(n,\"g\"),t)})),o=o.replace(/\\[\\[\\.\\.\\..+\\]\\]/g,\"\"),o=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(o),o.includes(\"[\"))throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(n,\"\\nParams: \").concat(JSON.stringify(r)));return i&&(o+=c(i)),o}if(\"string\"==typeof n){return f(s(n))}{const{pathname:e,...t}=n;return{...t,pathname:f(s(e))}}}function f(t,r,o){const a=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(o)),i=decodeURI(r);for(const e of a){const r=o[e];if(\"string\"==typeof r){if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r,i))return e}else if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r[t],i))return e}return r}function l(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return\"/\"===e?n:n.replace(e,\"\")}function d(e,n,a,i,c){const{mode:s}=a.localePrefix;let f;if(void 0!==c)f=c;else if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(e))if(\"always\"===s)f=!0;else if(\"as-needed\"===s){let e=a.defaultLocale;if(a.domains){const n=a.domains.find((e=>e.domain===i));n?e=n.defaultLocale:i||console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl-docs.vercel.app/docs/routing#domains-localeprefix-asneeded\")}f=e!==n}return f?(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(n,a.localePrefix),e):e}function u(e){var n;if(\"as-needed\"===(null===(n=e.localePrefix)||void 0===n?void 0:n.mode)&&!(\"defaultLocale\"in e))throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\")}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-client/useLocale.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ e)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var use_intl_useLocale__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/_useLocale */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\");\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/constants.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/constants.js\");\nfunction e(){const e=(0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.useParams)();let n;try{n=(0,use_intl_useLocale__WEBPACK_IMPORTED_MODULE_1__.useLocale)()}catch(t){if(\"string\"!=typeof(null==e?void 0:e[_shared_constants_js__WEBPACK_IMPORTED_MODULE_2__.LOCALE_SEGMENT_NAME]))throw t;n=e[_shared_constants_js__WEBPACK_IMPORTED_MODULE_2__.LOCALE_SEGMENT_NAME]}return n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LWNsaWVudC91c2VMb2NhbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF5SixhQUFhLFFBQVEsMERBQUMsR0FBRyxNQUFNLElBQUksRUFBRSw2REFBQyxHQUFHLFNBQVMscUNBQXFDLHFFQUFDLFdBQVcsSUFBSSxxRUFBQyxFQUFFLFNBQThCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LWNsaWVudC91c2VMb2NhbGUuanM/MTU1YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7dXNlUGFyYW1zIGFzIHR9ZnJvbVwibmV4dC9uYXZpZ2F0aW9uXCI7aW1wb3J0e3VzZUxvY2FsZSBhcyBvfWZyb21cInVzZS1pbnRsL191c2VMb2NhbGVcIjtpbXBvcnR7TE9DQUxFX1NFR01FTlRfTkFNRSBhcyByfWZyb21cIi4uL3NoYXJlZC9jb25zdGFudHMuanNcIjtmdW5jdGlvbiBlKCl7Y29uc3QgZT10KCk7bGV0IG47dHJ5e249bygpfWNhdGNoKHQpe2lmKFwic3RyaW5nXCIhPXR5cGVvZihudWxsPT1lP3ZvaWQgMDplW3JdKSl0aHJvdyB0O249ZVtyXX1yZXR1cm4gbn1leHBvcnR7ZSBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/react-client/useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(ssr)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/_IntlProvider */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction r(r) {\n    let { locale: o, ...i } = r;\n    if (!o) throw new Error(\"Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\\n\\nSee https://next-intl-docs.vercel.app/docs/configuration#locale\");\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(use_intl_IntlProvider__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, (0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({\n        locale: o\n    }, i));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9OZXh0SW50bENsaWVudFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OzZEQUNtRTtBQUFxQjtBQUFzRDtBQUFBLFNBQVNLLEVBQUVBLENBQUM7SUFBRSxJQUFHLEVBQUNDLFFBQU9DLENBQUMsRUFBQyxHQUFHQyxHQUFFLEdBQUNIO0lBQUUsSUFBRyxDQUFDRSxHQUFFLE1BQU0sSUFBSUUsTUFBTTtJQUEySyxxQkFBT1AsMERBQWUsQ0FBQ0UsK0RBQUNBLEVBQUNILGdGQUFDQSxDQUFDO1FBQUNLLFFBQU9DO0lBQUMsR0FBRUM7QUFBRztBQUFzQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zaGFyZWQvTmV4dEludGxDbGllbnRQcm92aWRlci5qcz9kY2U0Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0e2V4dGVuZHMgYXMgZX1mcm9tXCIuLi9fdmlydHVhbC9fcm9sbHVwUGx1Z2luQmFiZWxIZWxwZXJzLmpzXCI7aW1wb3J0IGwgZnJvbVwicmVhY3RcIjtpbXBvcnR7SW50bFByb3ZpZGVyIGFzIHR9ZnJvbVwidXNlLWludGwvX0ludGxQcm92aWRlclwiO2Z1bmN0aW9uIHIocil7bGV0e2xvY2FsZTpvLC4uLml9PXI7aWYoIW8pdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIGRldGVybWluZSBsb2NhbGUgaW4gYE5leHRJbnRsQ2xpZW50UHJvdmlkZXJgLCBwbGVhc2UgcHJvdmlkZSB0aGUgYGxvY2FsZWAgcHJvcCBleHBsaWNpdGx5LlxcblxcblNlZSBodHRwczovL25leHQtaW50bC1kb2NzLnZlcmNlbC5hcHAvZG9jcy9jb25maWd1cmF0aW9uI2xvY2FsZVwiKTtyZXR1cm4gbC5jcmVhdGVFbGVtZW50KHQsZSh7bG9jYWxlOm99LGkpKX1leHBvcnR7ciBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6WyJleHRlbmRzIiwiZSIsImwiLCJJbnRsUHJvdmlkZXIiLCJ0IiwiciIsImxvY2FsZSIsIm8iLCJpIiwiRXJyb3IiLCJjcmVhdGVFbGVtZW50IiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ o),\n/* harmony export */   LOCALE_SEGMENT_NAME: () => (/* binding */ L)\n/* harmony export */ });\nconst o=\"X-NEXT-INTL-LOCALE\",L=\"locale\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSx3Q0FBaUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2hhcmVkL2NvbnN0YW50cy5qcz83NzE3Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG89XCJYLU5FWFQtSU5UTC1MT0NBTEVcIixMPVwibG9jYWxlXCI7ZXhwb3J0e28gYXMgSEVBREVSX0xPQ0FMRV9OQU1FLEwgYXMgTE9DQUxFX1NFR01FTlRfTkFNRX07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocalePrefix: () => (/* binding */ f),\n/* harmony export */   getSortedPathnames: () => (/* binding */ g),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ i),\n/* harmony export */   isLocalizableHref: () => (/* binding */ n),\n/* harmony export */   localizeHref: () => (/* binding */ t),\n/* harmony export */   matchesPathname: () => (/* binding */ o),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ c),\n/* harmony export */   prefixHref: () => (/* binding */ e),\n/* harmony export */   prefixPathname: () => (/* binding */ u),\n/* harmony export */   templateToRegex: () => (/* binding */ l),\n/* harmony export */   unprefixPathname: () => (/* binding */ r)\n/* harmony export */ });\nfunction n(n){return function(n){return\"object\"==typeof n?null==n.host&&null==n.hostname:!/^[a-z]+:/i.test(n)}(n)&&!function(n){const t=\"object\"==typeof n?n.pathname:n;return null!=t&&!t.startsWith(\"/\")}(n)}function t(t,r){let u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r,c=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;if(!n(t))return t;const f=r!==u,l=i(o,c);return(f||l)&&null!=o?e(t,o):t}function e(n,t){let e;return\"string\"==typeof n?e=u(t,n):(e={...n},n.pathname&&(e.pathname=u(t,n.pathname))),e}function r(n,t){return n.replace(new RegExp(\"^\".concat(t)),\"\")||\"/\"}function u(n,t){let e=n;return/^\\/(\\?.*)?$/.test(t)&&(t=t.slice(1)),e+=t,e}function i(n,t){return t===n||t.startsWith(\"\".concat(n,\"/\"))}function c(n){const t=function(){try{return\"true\"===process.env._next_intl_trailing_slash}catch(n){return!1}}();if(\"/\"!==n){const e=n.endsWith(\"/\");t&&!e?n+=\"/\":!t&&e&&(n=n.slice(0,-1))}return n}function o(n,t){const e=c(n),r=c(t);return l(e).test(r)}function f(n,t){var e;return\"never\"!==t.mode&&(null===(e=t.prefixes)||void 0===e?void 0:e[n])||\"/\"+n}function l(n){const t=n.replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g,\"?(.*)\").replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g,\"(.+)\").replace(/\\[([^\\]]+)\\]/g,\"([^/]+)\");return new RegExp(\"^\".concat(t,\"$\"))}function s(n){return n.includes(\"[[...\")}function a(n){return n.includes(\"[...\")}function p(n){return n.includes(\"[\")}function h(n,t){const e=n.split(\"/\"),r=t.split(\"/\"),u=Math.max(e.length,r.length);for(let n=0;n<u;n++){const t=e[n],u=r[n];if(!t&&u)return-1;if(t&&!u)return 1;if(t||u){if(!p(t)&&p(u))return-1;if(p(t)&&!p(u))return 1;if(!a(t)&&a(u))return-1;if(a(t)&&!a(u))return 1;if(!s(t)&&s(u))return-1;if(s(t)&&!s(u))return 1}}return 0}function g(n){return n.sort(h)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/shared/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"extends\": () => (/* binding */ n)\n/* harmony export */ });\nfunction n(){return n=Object.assign?Object.assign.bind():function(n){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)({}).hasOwnProperty.call(t,a)&&(n[a]=t[a])}return n},n.apply(null,arguments)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL192aXJ0dWFsL19yb2xsdXBQbHVnaW5CYWJlbEhlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGFBQWEsd0RBQXdELFlBQVksbUJBQW1CLEtBQUssbUJBQW1CLGtCQUFrQix3Q0FBd0MsU0FBUyx5QkFBOEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qcz83ZmJlIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIG4oKXtyZXR1cm4gbj1PYmplY3QuYXNzaWduP09iamVjdC5hc3NpZ24uYmluZCgpOmZ1bmN0aW9uKG4pe2Zvcih2YXIgcj0xO3I8YXJndW1lbnRzLmxlbmd0aDtyKyspe3ZhciB0PWFyZ3VtZW50c1tyXTtmb3IodmFyIGEgaW4gdCkoe30pLmhhc093blByb3BlcnR5LmNhbGwodCxhKSYmKG5bYV09dFthXSl9cmV0dXJuIG59LG4uYXBwbHkobnVsbCxhcmd1bWVudHMpfWV4cG9ydHtuIGFzIGV4dGVuZHN9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var _server_react_server_RequestLocale_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../server/react-server/RequestLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\");\n/* harmony import */ var _shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/createSharedNavigationFns.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js\");\nfunction t(t){const{config:n,...r}=(0,_shared_createSharedNavigationFns_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((function(){return (0,_server_react_server_RequestLocale_js__WEBPACK_IMPORTED_MODULE_1__.getRequestLocale)()}),t);function s(e){return()=>{throw new Error(\"`\".concat(e,\"` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component.\"))}}return{...r,usePathname:s(\"usePathname\"),useRouter:s(\"useRouter\")}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2NyZWF0ZU5hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9JLGNBQWMsTUFBTSxjQUFjLENBQUMsZ0ZBQUMsYUFBYSxPQUFPLHVGQUFDLEdBQUcsS0FBSyxjQUFjLFdBQVcsNkpBQTZKLE9BQU8sNERBQWlGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL25hdmlnYXRpb24vcmVhY3Qtc2VydmVyL2NyZWF0ZU5hdmlnYXRpb24uanM/YmE3ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Z2V0UmVxdWVzdExvY2FsZSBhcyBlfWZyb21cIi4uLy4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZS5qc1wiO2ltcG9ydCBvIGZyb21cIi4uL3NoYXJlZC9jcmVhdGVTaGFyZWROYXZpZ2F0aW9uRm5zLmpzXCI7ZnVuY3Rpb24gdCh0KXtjb25zdHtjb25maWc6biwuLi5yfT1vKChmdW5jdGlvbigpe3JldHVybiBlKCl9KSx0KTtmdW5jdGlvbiBzKGUpe3JldHVybigpPT57dGhyb3cgbmV3IEVycm9yKFwiYFwiLmNvbmNhdChlLFwiYCBpcyBub3Qgc3VwcG9ydGVkIGluIFNlcnZlciBDb21wb25lbnRzLiBZb3UgY2FuIHVzZSB0aGlzIGhvb2sgaWYgeW91IGNvbnZlcnQgdGhlIGNhbGxpbmcgY29tcG9uZW50IHRvIGEgQ2xpZW50IENvbXBvbmVudC5cIikpfX1yZXR1cm57Li4ucix1c2VQYXRobmFtZTpzKFwidXNlUGF0aG5hbWVcIiksdXNlUm91dGVyOnMoXCJ1c2VSb3V0ZXJcIil9fWV4cG9ydHt0IGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/react-server/createNavigation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\_PRIVATE\Property Plaza DEV\property-plaza\node_modules\next-intl\dist\esm\navigation\shared\BaseLink.js#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ d)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../_virtual/_rollupPluginBabelHelpers.js */ \"(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _routing_config_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../routing/config.js */ \"(rsc)/./node_modules/next-intl/dist/esm/routing/config.js\");\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\n/* harmony import */ var _BaseLink_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./BaseLink.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/BaseLink.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\");\nfunction d(d,h){const j=(0,_routing_config_js__WEBPACK_IMPORTED_MODULE_2__.receiveRoutingConfig)(h||{});(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.validateReceivedConfig)(j);const g=j.pathnames,v=\"as-needed\"===j.localePrefix.mode&&j.domains||void 0;function y(o,a){let n,l,{href:c,locale:f,...s}=o;\"object\"==typeof c?(n=c.pathname,l=c.params):n=c;const u=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_4__.isLocalizableHref)(c),p=d(),h=p instanceof Promise?(0,react__WEBPACK_IMPORTED_MODULE_1__.use)(p):p,y=u?L({locale:f||h,href:null==g?n:{pathname:n,params:l}},null!=f||v||void 0):n;return react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_BaseLink_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"],(0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_6__[\"extends\"])({ref:a,defaultLocale:j.defaultLocale,href:\"object\"==typeof c?{...c,pathname:y}:y,locale:f,localeCookie:j.localeCookie,unprefixed:v&&u?{domains:j.domains.reduce(((e,o)=>(e[o.domain]=o.defaultLocale,e)),{}),pathname:L({locale:h,href:null==g?n:{pathname:n,params:l}},!1)}:void 0},s))}const x=(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(y);function L(e,o){const{href:a,locale:t}=e;let n;return null==g?\"object\"==typeof a?(n=a.pathname,a.query&&(n+=(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.serializeSearchParams)(a.query))):n=a:n=(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.compileLocalizedPathname)({locale:t,...(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.normalizeNameOrNameWithParams)(a),pathnames:j.pathnames}),(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.applyPathnamePrefix)(n,t,j,e.domain,o)}function b(e){return function(o){for(var a=arguments.length,t=new Array(a>1?a-1:0),n=1;n<a;n++)t[n-1]=arguments[n];return e(L(o,o.domain?void 0:v),...t)}}const k=b(next_navigation__WEBPACK_IMPORTED_MODULE_0__.redirect),P=b(next_navigation__WEBPACK_IMPORTED_MODULE_0__.permanentRedirect);return{config:j,Link:x,redirect:k,permanentRedirect:P,getPathname:L}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/createSharedNavigationFns.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js":
/*!********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/navigation/shared/utils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPathnamePrefix: () => (/* binding */ d),\n/* harmony export */   compileLocalizedPathname: () => (/* binding */ s),\n/* harmony export */   getBasePath: () => (/* binding */ l),\n/* harmony export */   getRoute: () => (/* binding */ f),\n/* harmony export */   normalizeNameOrNameWithParams: () => (/* binding */ i),\n/* harmony export */   serializeSearchParams: () => (/* binding */ c),\n/* harmony export */   validateReceivedConfig: () => (/* binding */ u)\n/* harmony export */ });\n/* harmony import */ var _shared_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/utils.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\");\nfunction i(e){return\"string\"==typeof e?{pathname:e}:e}function c(e){function n(e){return String(e)}const t=new URLSearchParams;for(const[r,o]of Object.entries(e))Array.isArray(o)?o.forEach((e=>{t.append(r,n(e))})):t.set(r,n(o));return\"?\"+t.toString()}function s(e){let{pathname:n,locale:t,params:r,pathnames:o,query:i}=e;function s(e){let n=o[e];return n||(n=e),n}function f(e){const n=\"string\"==typeof e?e:e[t];let o=n;if(r&&Object.entries(r).forEach((e=>{let n,t,[r,a]=e;Array.isArray(a)?(n=\"(\\\\[)?\\\\[...\".concat(r,\"\\\\](\\\\])?\"),t=a.map((e=>String(e))).join(\"/\")):(n=\"\\\\[\".concat(r,\"\\\\]\"),t=String(a)),o=o.replace(new RegExp(n,\"g\"),t)})),o=o.replace(/\\[\\[\\.\\.\\..+\\]\\]/g,\"\"),o=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.normalizeTrailingSlash)(o),o.includes(\"[\"))throw new Error(\"Insufficient params provided for localized pathname.\\nTemplate: \".concat(n,\"\\nParams: \").concat(JSON.stringify(r)));return i&&(o+=c(i)),o}if(\"string\"==typeof n){return f(s(n))}{const{pathname:e,...t}=n;return{...t,pathname:f(s(e))}}}function f(t,r,o){const a=(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getSortedPathnames)(Object.keys(o)),i=decodeURI(r);for(const e of a){const r=o[e];if(\"string\"==typeof r){if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r,i))return e}else if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.matchesPathname)(r[t],i))return e}return r}function l(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return\"/\"===e?n:n.replace(e,\"\")}function d(e,n,a,i,c){const{mode:s}=a.localePrefix;let f;if(void 0!==c)f=c;else if((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.isLocalizableHref)(e))if(\"always\"===s)f=!0;else if(\"as-needed\"===s){let e=a.defaultLocale;if(a.domains){const n=a.domains.find((e=>e.domain===i));n?e=n.defaultLocale:i||console.error(\"You're using a routing configuration with `localePrefix: 'as-needed'` in combination with `domains`. In order to compute a correct pathname, you need to provide a `domain` parameter.\\n\\nSee: https://next-intl-docs.vercel.app/docs/routing#domains-localeprefix-asneeded\")}f=e!==n}return f?(0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.prefixPathname)((0,_shared_utils_js__WEBPACK_IMPORTED_MODULE_0__.getLocalePrefix)(n,a.localePrefix),e):e}function u(e){var n;if(\"as-needed\"===(null===(n=e.localePrefix)||void 0===n?void 0:n.mode)&&!(\"defaultLocale\"in e))throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\")}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/navigation/shared/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ i)\n/* harmony export */ });\n/* harmony import */ var _virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_virtual/_rollupPluginBabelHelpers.js */ \"(rsc)/./node_modules/next-intl/dist/esm/_virtual/_rollupPluginBabelHelpers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\");\n/* harmony import */ var _server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../server/react-server/getLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js\");\n/* harmony import */ var _server_react_server_getNow_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../server/react-server/getNow.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js\");\n/* harmony import */ var _server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../server/react-server/getTimeZone.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js\");\nasync function i(i){let{locale:n,now:s,timeZone:m,...c}=i;return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_shared_NextIntlClientProvider_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],(0,_virtual_rollupPluginBabelHelpers_js__WEBPACK_IMPORTED_MODULE_2__[\"extends\"])({locale:null!=n?n:await (0,_server_react_server_getLocale_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(),now:null!=s?s:await (0,_server_react_server_getNow_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),timeZone:null!=m?m:await (0,_server_react_server_getTimeZone_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()},c))}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9OZXh0SW50bENsaWVudFByb3ZpZGVyU2VydmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQW1TLG9CQUFvQixJQUFJLCtCQUErQixHQUFHLE9BQU8sMERBQWUsQ0FBQyx5RUFBQyxDQUFDLGdGQUFDLEVBQUUsdUJBQXVCLDZFQUFDLHVCQUF1QiwwRUFBQyw0QkFBNEIsK0VBQUMsR0FBRyxLQUEwQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9yZWFjdC1zZXJ2ZXIvTmV4dEludGxDbGllbnRQcm92aWRlclNlcnZlci5qcz9lZjlkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtleHRlbmRzIGFzIGV9ZnJvbVwiLi4vX3ZpcnR1YWwvX3JvbGx1cFBsdWdpbkJhYmVsSGVscGVycy5qc1wiO2ltcG9ydCByIGZyb21cInJlYWN0XCI7aW1wb3J0IHQgZnJvbVwiLi4vc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIjtpbXBvcnQgbyBmcm9tXCIuLi9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldExvY2FsZS5qc1wiO2ltcG9ydCBsIGZyb21cIi4uL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Tm93LmpzXCI7aW1wb3J0IGEgZnJvbVwiLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRUaW1lWm9uZS5qc1wiO2FzeW5jIGZ1bmN0aW9uIGkoaSl7bGV0e2xvY2FsZTpuLG5vdzpzLHRpbWVab25lOm0sLi4uY309aTtyZXR1cm4gci5jcmVhdGVFbGVtZW50KHQsZSh7bG9jYWxlOm51bGwhPW4/bjphd2FpdCBvKCksbm93Om51bGwhPXM/czphd2FpdCBsKCksdGltZVpvbmU6bnVsbCE9bT9tOmF3YWl0IGEoKX0sYykpfWV4cG9ydHtpIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/getTranslator.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/getTranslator.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\nvar t=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((function(r,t){return (0,use_intl_core__WEBPACK_IMPORTED_MODULE_1__.createTranslator)({...r,namespace:t})}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9nZXRUcmFuc2xhdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0UsTUFBTSw0Q0FBQyxnQkFBZ0IsT0FBTywrREFBQyxFQUFFLGlCQUFpQixFQUFFLEdBQXdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci9nZXRUcmFuc2xhdG9yLmpzPzAzNDEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e2NhY2hlIGFzIHJ9ZnJvbVwicmVhY3RcIjtpbXBvcnR7Y3JlYXRlVHJhbnNsYXRvciBhcyBlfWZyb21cInVzZS1pbnRsL2NvcmVcIjt2YXIgdD1yKChmdW5jdGlvbihyLHQpe3JldHVybiBlKHsuLi5yLG5hbWVzcGFjZTp0fSl9KSk7ZXhwb3J0e3QgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/getTranslator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useConfig.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../server/react-server/getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nfunction r(r){return function(n,r){try{return (0,react__WEBPACK_IMPORTED_MODULE_0__.use)(r)}catch(e){throw e instanceof TypeError&&e.message.includes(\"Cannot read properties of null (reading 'use')\")?new Error(\"`\".concat(n,\"` is not callable within an async component. Please refer to https://next-intl-docs.vercel.app/docs/environments/server-client-components#async-components\"),{cause:e}):e}}(r,(0,_server_react_server_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])())}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUErRSxjQUFjLHFCQUFxQixJQUFJLE9BQU8sMENBQUMsSUFBSSxTQUFTLHlSQUF5UixRQUFRLEtBQUssR0FBRyw2RUFBQyxJQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9yZWFjdC1zZXJ2ZXIvdXNlQ29uZmlnLmpzPzBkNWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0e3VzZSBhcyBlfWZyb21cInJlYWN0XCI7aW1wb3J0IG4gZnJvbVwiLi4vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRDb25maWcuanNcIjtmdW5jdGlvbiByKHIpe3JldHVybiBmdW5jdGlvbihuLHIpe3RyeXtyZXR1cm4gZShyKX1jYXRjaChlKXt0aHJvdyBlIGluc3RhbmNlb2YgVHlwZUVycm9yJiZlLm1lc3NhZ2UuaW5jbHVkZXMoXCJDYW5ub3QgcmVhZCBwcm9wZXJ0aWVzIG9mIG51bGwgKHJlYWRpbmcgJ3VzZScpXCIpP25ldyBFcnJvcihcImBcIi5jb25jYXQobixcImAgaXMgbm90IGNhbGxhYmxlIHdpdGhpbiBhbiBhc3luYyBjb21wb25lbnQuIFBsZWFzZSByZWZlciB0byBodHRwczovL25leHQtaW50bC1kb2NzLnZlcmNlbC5hcHAvZG9jcy9lbnZpcm9ubWVudHMvc2VydmVyLWNsaWVudC1jb21wb25lbnRzI2FzeW5jLWNvbXBvbmVudHNcIikse2NhdXNlOmV9KTplfX0ocixuKCkpfWV4cG9ydHtyIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/react-server/useTranslations.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ o)\n/* harmony export */ });\n/* harmony import */ var _getTranslator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getTranslator.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/getTranslator.js\");\n/* harmony import */ var _useConfig_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js\");\nfunction o(){for(var o=arguments.length,n=new Array(o),e=0;e<o;e++)n[e]=arguments[e];let[s]=n;const a=(0,_useConfig_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"useTranslations\");return (0,_getTranslator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(a,s)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JlYWN0LXNlcnZlci91c2VUcmFuc2xhdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdFLGFBQWEsOENBQThDLElBQUksc0JBQXNCLFNBQVMsUUFBUSx5REFBQyxvQkFBb0IsT0FBTyw2REFBQyxNQUEyQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9yZWFjdC1zZXJ2ZXIvdXNlVHJhbnNsYXRpb25zLmpzP2QwYjYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHIgZnJvbVwiLi9nZXRUcmFuc2xhdG9yLmpzXCI7aW1wb3J0IHQgZnJvbVwiLi91c2VDb25maWcuanNcIjtmdW5jdGlvbiBvKCl7Zm9yKHZhciBvPWFyZ3VtZW50cy5sZW5ndGgsbj1uZXcgQXJyYXkobyksZT0wO2U8bztlKyspbltlXT1hcmd1bWVudHNbZV07bGV0W3NdPW47Y29uc3QgYT10KFwidXNlVHJhbnNsYXRpb25zXCIpO3JldHVybiByKGEscyl9ZXhwb3J0e28gYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/routing/config.js":
/*!***********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/routing/config.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   receiveLocaleCookie: () => (/* binding */ o),\n/* harmony export */   receiveLocalePrefixConfig: () => (/* binding */ l),\n/* harmony export */   receiveRoutingConfig: () => (/* binding */ e)\n/* harmony export */ });\nfunction e(e){var t,n;return{...e,localePrefix:l(e.localePrefix),localeCookie:o(e.localeCookie),localeDetection:null===(t=e.localeDetection)||void 0===t||t,alternateLinks:null===(n=e.alternateLinks)||void 0===n||n}}function o(e){return!(null!=e&&!e)&&{name:\"NEXT_LOCALE\",maxAge:31536e3,sameSite:\"lax\",...\"object\"==typeof e&&e}}function l(e){return\"object\"==typeof e?e:{mode:e||\"always\"}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JvdXRpbmcvY29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLGNBQWMsUUFBUSxPQUFPLDBMQUEwTCxjQUFjLHVCQUF1QiwyRUFBMkUsY0FBYyw0QkFBNEIsa0JBQTRHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3JvdXRpbmcvY29uZmlnLmpzPzBlNDMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZShlKXt2YXIgdCxuO3JldHVybnsuLi5lLGxvY2FsZVByZWZpeDpsKGUubG9jYWxlUHJlZml4KSxsb2NhbGVDb29raWU6byhlLmxvY2FsZUNvb2tpZSksbG9jYWxlRGV0ZWN0aW9uOm51bGw9PT0odD1lLmxvY2FsZURldGVjdGlvbil8fHZvaWQgMD09PXR8fHQsYWx0ZXJuYXRlTGlua3M6bnVsbD09PShuPWUuYWx0ZXJuYXRlTGlua3MpfHx2b2lkIDA9PT1ufHxufX1mdW5jdGlvbiBvKGUpe3JldHVybiEobnVsbCE9ZSYmIWUpJiZ7bmFtZTpcIk5FWFRfTE9DQUxFXCIsbWF4QWdlOjMxNTM2ZTMsc2FtZVNpdGU6XCJsYXhcIiwuLi5cIm9iamVjdFwiPT10eXBlb2YgZSYmZX19ZnVuY3Rpb24gbChlKXtyZXR1cm5cIm9iamVjdFwiPT10eXBlb2YgZT9lOnttb2RlOmV8fFwiYWx3YXlzXCJ9fWV4cG9ydHtvIGFzIHJlY2VpdmVMb2NhbGVDb29raWUsbCBhcyByZWNlaXZlTG9jYWxlUHJlZml4Q29uZmlnLGUgYXMgcmVjZWl2ZVJvdXRpbmdDb25maWd9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/routing/config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\nconst o=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(){const e=(0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)();return e instanceof Promise?await e:e}));const i=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(){let t;try{t=(await o()).get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_2__.HEADER_LOCALE_NAME)||void 0}catch(t){if(t instanceof Error&&\"DYNAMIC_SERVER_USAGE\"===t.digest){const e=new Error(\"Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl-docs.vercel.app/docs/getting-started/app-router/with-i18n-routing#static-rendering\",{cause:t});throw e.digest=t.digest,e}throw t}return t}));async function s(){return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_3__.getCachedRequestLocale)()||await i()}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBcU0sUUFBUSw0Q0FBQyxtQkFBbUIsUUFBUSxxREFBQyxHQUFHLHNDQUFzQyxHQUFHLFFBQVEsNENBQUMsbUJBQW1CLE1BQU0sSUFBSSxrQkFBa0Isb0VBQUMsVUFBVSxTQUFTLDBEQUEwRCwyVkFBMlYsUUFBUSxFQUFFLDBCQUEwQixRQUFRLFNBQVMsR0FBRyxtQkFBbUIsT0FBTyw4RUFBQyxjQUE0QyIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zZXJ2ZXIvcmVhY3Qtc2VydmVyL1JlcXVlc3RMb2NhbGUuanM/OWNmNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7aGVhZGVycyBhcyB0fWZyb21cIm5leHQvaGVhZGVyc1wiO2ltcG9ydHtjYWNoZSBhcyBlfWZyb21cInJlYWN0XCI7aW1wb3J0e0hFQURFUl9MT0NBTEVfTkFNRSBhcyBufWZyb21cIi4uLy4uL3NoYXJlZC9jb25zdGFudHMuanNcIjtpbXBvcnR7Z2V0Q2FjaGVkUmVxdWVzdExvY2FsZSBhcyByfWZyb21cIi4vUmVxdWVzdExvY2FsZUNhY2hlLmpzXCI7Y29uc3Qgbz1lKChhc3luYyBmdW5jdGlvbigpe2NvbnN0IGU9dCgpO3JldHVybiBlIGluc3RhbmNlb2YgUHJvbWlzZT9hd2FpdCBlOmV9KSk7Y29uc3QgaT1lKChhc3luYyBmdW5jdGlvbigpe2xldCB0O3RyeXt0PShhd2FpdCBvKCkpLmdldChuKXx8dm9pZCAwfWNhdGNoKHQpe2lmKHQgaW5zdGFuY2VvZiBFcnJvciYmXCJEWU5BTUlDX1NFUlZFUl9VU0FHRVwiPT09dC5kaWdlc3Qpe2NvbnN0IGU9bmV3IEVycm9yKFwiVXNhZ2Ugb2YgbmV4dC1pbnRsIEFQSXMgaW4gU2VydmVyIENvbXBvbmVudHMgY3VycmVudGx5IG9wdHMgaW50byBkeW5hbWljIHJlbmRlcmluZy4gVGhpcyBsaW1pdGF0aW9uIHdpbGwgZXZlbnR1YWxseSBiZSBsaWZ0ZWQsIGJ1dCBhcyBhIHN0b3BnYXAgc29sdXRpb24sIHlvdSBjYW4gdXNlIHRoZSBgc2V0UmVxdWVzdExvY2FsZWAgQVBJIHRvIGVuYWJsZSBzdGF0aWMgcmVuZGVyaW5nLCBzZWUgaHR0cHM6Ly9uZXh0LWludGwtZG9jcy52ZXJjZWwuYXBwL2RvY3MvZ2V0dGluZy1zdGFydGVkL2FwcC1yb3V0ZXIvd2l0aC1pMThuLXJvdXRpbmcjc3RhdGljLXJlbmRlcmluZ1wiLHtjYXVzZTp0fSk7dGhyb3cgZS5kaWdlc3Q9dC5kaWdlc3QsZX10aHJvdyB0fXJldHVybiB0fSkpO2FzeW5jIGZ1bmN0aW9uIHMoKXtyZXR1cm4gcigpfHxhd2FpdCBpKCl9ZXhwb3J0e3MgYXMgZ2V0UmVxdWVzdExvY2FsZX07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCachedRequestLocale: () => (/* binding */ t),\n/* harmony export */   setCachedRequestLocale: () => (/* binding */ c)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nconst n=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((function(){return{locale:void 0}}));function t(){return n().locale}function c(o){n().locale=o}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBOEIsUUFBUSw0Q0FBQyxhQUFhLE9BQU8sZUFBZSxHQUFHLGFBQWEsa0JBQWtCLGNBQWMsYUFBNkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2VydmVyL3JlYWN0LXNlcnZlci9SZXF1ZXN0TG9jYWxlQ2FjaGUuanM/ZGFiYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgb31mcm9tXCJyZWFjdFwiO2NvbnN0IG49bygoZnVuY3Rpb24oKXtyZXR1cm57bG9jYWxlOnZvaWQgMH19KSk7ZnVuY3Rpb24gdCgpe3JldHVybiBuKCkubG9jYWxlfWZ1bmN0aW9uIGMobyl7bigpLmxvY2FsZT1vfWV4cG9ydHt0IGFzIGdldENhY2hlZFJlcXVlc3RMb2NhbGUsYyBhcyBzZXRDYWNoZWRSZXF1ZXN0TG9jYWxlfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js":
/*!************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRequestLocale: () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _shared_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../shared/constants.js */ \"(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\");\n/* harmony import */ var _RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocaleCache.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\nconst i=(0,react__WEBPACK_IMPORTED_MODULE_2__.cache)((function(){let n;try{n=(0,next_headers__WEBPACK_IMPORTED_MODULE_0__.headers)().get(_shared_constants_js__WEBPACK_IMPORTED_MODULE_3__.HEADER_LOCALE_NAME)}catch(e){throw e instanceof Error&&\"DYNAMIC_SERVER_USAGE\"===e.digest?new Error(\"Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl-docs.vercel.app/docs/getting-started/app-router/with-i18n-routing#static-rendering\",{cause:e}):e}return n||(console.error(\"\\nUnable to find `next-intl` locale because the middleware didn't run on this request. See https://next-intl-docs.vercel.app/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\\n\"),(0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)()),n}));function s(){return (0,_RequestLocaleCache_js__WEBPACK_IMPORTED_MODULE_4__.getCachedRequestLocale)()||i()}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUxlZ2FjeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQWdQLFFBQVEsNENBQUMsYUFBYSxNQUFNLElBQUksRUFBRSxxREFBQyxPQUFPLG9FQUFDLEVBQUUsU0FBUywrWUFBK1ksUUFBUSxJQUFJLGdRQUFnUSx5REFBQyxNQUFNLEdBQUcsYUFBYSxPQUFPLDhFQUFDLFFBQXNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvUmVxdWVzdExvY2FsZUxlZ2FjeS5qcz8wMWI3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtoZWFkZXJzIGFzIGV9ZnJvbVwibmV4dC9oZWFkZXJzXCI7aW1wb3J0e25vdEZvdW5kIGFzIHR9ZnJvbVwibmV4dC9uYXZpZ2F0aW9uXCI7aW1wb3J0e2NhY2hlIGFzIG59ZnJvbVwicmVhY3RcIjtpbXBvcnR7SEVBREVSX0xPQ0FMRV9OQU1FIGFzIG99ZnJvbVwiLi4vLi4vc2hhcmVkL2NvbnN0YW50cy5qc1wiO2ltcG9ydHtnZXRDYWNoZWRSZXF1ZXN0TG9jYWxlIGFzIHJ9ZnJvbVwiLi9SZXF1ZXN0TG9jYWxlQ2FjaGUuanNcIjtjb25zdCBpPW4oKGZ1bmN0aW9uKCl7bGV0IG47dHJ5e249ZSgpLmdldChvKX1jYXRjaChlKXt0aHJvdyBlIGluc3RhbmNlb2YgRXJyb3ImJlwiRFlOQU1JQ19TRVJWRVJfVVNBR0VcIj09PWUuZGlnZXN0P25ldyBFcnJvcihcIlVzYWdlIG9mIG5leHQtaW50bCBBUElzIGluIFNlcnZlciBDb21wb25lbnRzIGN1cnJlbnRseSBvcHRzIGludG8gZHluYW1pYyByZW5kZXJpbmcuIFRoaXMgbGltaXRhdGlvbiB3aWxsIGV2ZW50dWFsbHkgYmUgbGlmdGVkLCBidXQgYXMgYSBzdG9wZ2FwIHNvbHV0aW9uLCB5b3UgY2FuIHVzZSB0aGUgYHNldFJlcXVlc3RMb2NhbGVgIEFQSSB0byBlbmFibGUgc3RhdGljIHJlbmRlcmluZywgc2VlIGh0dHBzOi8vbmV4dC1pbnRsLWRvY3MudmVyY2VsLmFwcC9kb2NzL2dldHRpbmctc3RhcnRlZC9hcHAtcm91dGVyL3dpdGgtaTE4bi1yb3V0aW5nI3N0YXRpYy1yZW5kZXJpbmdcIix7Y2F1c2U6ZX0pOmV9cmV0dXJuIG58fChjb25zb2xlLmVycm9yKFwiXFxuVW5hYmxlIHRvIGZpbmQgYG5leHQtaW50bGAgbG9jYWxlIGJlY2F1c2UgdGhlIG1pZGRsZXdhcmUgZGlkbid0IHJ1biBvbiB0aGlzIHJlcXVlc3QuIFNlZSBodHRwczovL25leHQtaW50bC1kb2NzLnZlcmNlbC5hcHAvZG9jcy9yb3V0aW5nL21pZGRsZXdhcmUjdW5hYmxlLXRvLWZpbmQtbG9jYWxlLiBUaGUgYG5vdEZvdW5kKClgIGZ1bmN0aW9uIHdpbGwgYmUgY2FsbGVkIGFzIGEgcmVzdWx0LlxcblwiKSx0KCkpLG59KSk7ZnVuY3Rpb24gcygpe3JldHVybiByKCl8fGkoKX1leHBvcnR7cyBhcyBnZXRSZXF1ZXN0TG9jYWxlfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getConfig.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ d)\n/* harmony export */ });\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n/* harmony import */ var _RequestLocale_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RequestLocale.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocale.js\");\n/* harmony import */ var _RequestLocaleLegacy_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RequestLocaleLegacy.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleLegacy.js\");\n/* harmony import */ var next_intl_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl/config */ \"(rsc)/./lib/locale/i18n.ts\");\nconst c=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((function(){return new Date}));const l=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}));const u=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(t,n){if(\"function\"!=typeof t)throw new Error(\"Invalid i18n request configuration detected.\\n\\nPlease verify that:\\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\\n2. You have a default export in your i18n request configuration file.\\n\\nSee also: https://next-intl-docs.vercel.app/docs/usage/configuration#i18n-request\\n\");const o={get locale(){return n||(0,_RequestLocaleLegacy_js__WEBPACK_IMPORTED_MODULE_3__.getRequestLocale)()},get requestLocale(){return n?Promise.resolve(n):(0,_RequestLocale_js__WEBPACK_IMPORTED_MODULE_4__.getRequestLocale)()}};let r=t(o);r instanceof Promise&&(r=await r);const s=r.locale||await o.requestLocale;return s||(console.error(\"\\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl-docs.vercel.app/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\\n\"),(0,next_navigation__WEBPACK_IMPORTED_MODULE_0__.notFound)()),{...r,locale:s,now:r.now||c(),timeZone:r.timeZone||l()}})),f=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__._createIntlFormatters),m=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)(use_intl_core__WEBPACK_IMPORTED_MODULE_5__._createCache);const d=(0,react__WEBPACK_IMPORTED_MODULE_1__.cache)((async function(e){const t=await u(next_intl_config__WEBPACK_IMPORTED_MODULE_2__[\"default\"],e);return{...(0,use_intl_core__WEBPACK_IMPORTED_MODULE_5__.initializeConfig)(t),_formatters:f(m())}}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getLocale.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nconst r=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(){const o=await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();return Promise.resolve(o.locale)}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEQsUUFBUSw0Q0FBQyxtQkFBbUIsY0FBYyx5REFBQyxHQUFHLGlDQUFpQyxHQUF3QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldExvY2FsZS5qcz80MWQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjYWNoZSBhcyBvfWZyb21cInJlYWN0XCI7aW1wb3J0IHQgZnJvbVwiLi9nZXRDb25maWcuanNcIjtjb25zdCByPW8oKGFzeW5jIGZ1bmN0aW9uKCl7Y29uc3Qgbz1hd2FpdCB0KCk7cmV0dXJuIFByb21pc2UucmVzb2x2ZShvLmxvY2FsZSl9KSk7ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getLocale.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getMessages.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r),\n/* harmony export */   getMessagesFromConfig: () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nfunction t(e){if(!e.messages)throw new Error(\"No messages found. Have you configured them correctly? See https://next-intl-docs.vercel.app/docs/configuration#messages\");return e.messages}const n=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(e){return t(await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(e))}));async function r(e){return n(null==e?void 0:e.locale)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0TWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEQsY0FBYywySkFBMkosa0JBQWtCLFFBQVEsNENBQUMsb0JBQW9CLGVBQWUseURBQUMsS0FBSyxHQUFHLG9CQUFvQixrQ0FBa0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRNZXNzYWdlcy5qcz80YjA5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjYWNoZSBhcyBlfWZyb21cInJlYWN0XCI7aW1wb3J0IG8gZnJvbVwiLi9nZXRDb25maWcuanNcIjtmdW5jdGlvbiB0KGUpe2lmKCFlLm1lc3NhZ2VzKXRocm93IG5ldyBFcnJvcihcIk5vIG1lc3NhZ2VzIGZvdW5kLiBIYXZlIHlvdSBjb25maWd1cmVkIHRoZW0gY29ycmVjdGx5PyBTZWUgaHR0cHM6Ly9uZXh0LWludGwtZG9jcy52ZXJjZWwuYXBwL2RvY3MvY29uZmlndXJhdGlvbiNtZXNzYWdlc1wiKTtyZXR1cm4gZS5tZXNzYWdlc31jb25zdCBuPWUoKGFzeW5jIGZ1bmN0aW9uKGUpe3JldHVybiB0KGF3YWl0IG8oZSkpfSkpO2FzeW5jIGZ1bmN0aW9uIHIoZSl7cmV0dXJuIG4obnVsbD09ZT92b2lkIDA6ZS5sb2NhbGUpfWV4cG9ydHtyIGFzIGRlZmF1bHQsdCBhcyBnZXRNZXNzYWdlc0Zyb21Db25maWd9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getNow.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nconst t=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(n){return(await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(n)).now}));async function r(n){return t(null==n?void 0:n.locale)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0Tm93LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEQsUUFBUSw0Q0FBQyxvQkFBb0IsYUFBYSx5REFBQyxTQUFTLEdBQUcsb0JBQW9CLGtDQUF1RCIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldE5vdy5qcz9lMmIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjYWNoZSBhcyBufWZyb21cInJlYWN0XCI7aW1wb3J0IG8gZnJvbVwiLi9nZXRDb25maWcuanNcIjtjb25zdCB0PW4oKGFzeW5jIGZ1bmN0aW9uKG4pe3JldHVybihhd2FpdCBvKG4pKS5ub3d9KSk7YXN5bmMgZnVuY3Rpb24gcihuKXtyZXR1cm4gdChudWxsPT1uP3ZvaWQgMDpuLmxvY2FsZSl9ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getNow.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\nfunction t(t){return t}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0UmVxdWVzdENvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsY0FBYyxTQUE4QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtaW50bC9kaXN0L2VzbS9zZXJ2ZXIvcmVhY3Qtc2VydmVyL2dldFJlcXVlc3RDb25maWcuanM/YjNmMiJdLCJzb3VyY2VzQ29udGVudCI6WyJmdW5jdGlvbiB0KHQpe3JldHVybiB0fWV4cG9ydHt0IGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ r)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nconst o=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(t){return(await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(t)).timeZone}));async function r(t){return o(null==t?void 0:t.locale)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0RCxRQUFRLDRDQUFDLG9CQUFvQixhQUFhLHlEQUFDLGNBQWMsR0FBRyxvQkFBb0Isa0NBQXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VGltZVpvbmUuanM/YWI0MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgdH1mcm9tXCJyZWFjdFwiO2ltcG9ydCBuIGZyb21cIi4vZ2V0Q29uZmlnLmpzXCI7Y29uc3Qgbz10KChhc3luYyBmdW5jdGlvbih0KXtyZXR1cm4oYXdhaXQgbih0KSkudGltZVpvbmV9KSk7YXN5bmMgZnVuY3Rpb24gcih0KXtyZXR1cm4gbyhudWxsPT10P3ZvaWQgMDp0LmxvY2FsZSl9ZXhwb3J0e3IgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTimeZone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ s)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_intl_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-intl/core */ \"(rsc)/./node_modules/use-intl/dist/development/core.js\");\n/* harmony import */ var _getConfig_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getConfig.js */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js\");\nvar s=(0,react__WEBPACK_IMPORTED_MODULE_0__.cache)((async function(e){let s,o;\"string\"==typeof e?s=e:e&&(o=e.locale,s=e.namespace);const r=await (0,_getConfig_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(o);return (0,use_intl_core__WEBPACK_IMPORTED_MODULE_2__.createTranslator)({...r,namespace:s,messages:r.messages})}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NlcnZlci9yZWFjdC1zZXJ2ZXIvZ2V0VHJhbnNsYXRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZHLE1BQU0sNENBQUMsb0JBQW9CLFFBQVEscURBQXFELGNBQWMseURBQUMsSUFBSSxPQUFPLCtEQUFDLEVBQUUscUNBQXFDLEVBQUUsR0FBd0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2VydmVyL3JlYWN0LXNlcnZlci9nZXRUcmFuc2xhdGlvbnMuanM/ZTQzZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y2FjaGUgYXMgZX1mcm9tXCJyZWFjdFwiO2ltcG9ydHtjcmVhdGVUcmFuc2xhdG9yIGFzIHR9ZnJvbVwidXNlLWludGwvY29yZVwiO2ltcG9ydCBhIGZyb21cIi4vZ2V0Q29uZmlnLmpzXCI7dmFyIHM9ZSgoYXN5bmMgZnVuY3Rpb24oZSl7bGV0IHMsbztcInN0cmluZ1wiPT10eXBlb2YgZT9zPWU6ZSYmKG89ZS5sb2NhbGUscz1lLm5hbWVzcGFjZSk7Y29uc3Qgcj1hd2FpdCBhKG8pO3JldHVybiB0KHsuLi5yLG5hbWVzcGFjZTpzLG1lc3NhZ2VzOnIubWVzc2FnZXN9KX0pKTtleHBvcnR7cyBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\_PRIVATE\Property Plaza DEV\property-plaza\node_modules\next-intl\dist\esm\shared\NextIntlClientProvider.js#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HEADER_LOCALE_NAME: () => (/* binding */ o),\n/* harmony export */   LOCALE_SEGMENT_NAME: () => (/* binding */ L)\n/* harmony export */ });\nconst o=\"X-NEXT-INTL-LOCALE\",L=\"locale\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1pbnRsL2Rpc3QvZXNtL3NoYXJlZC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSx3Q0FBaUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vc2hhcmVkL2NvbnN0YW50cy5qcz9lOWZlIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG89XCJYLU5FWFQtSU5UTC1MT0NBTEVcIixMPVwibG9jYWxlXCI7ZXhwb3J0e28gYXMgSEVBREVSX0xPQ0FMRV9OQU1FLEwgYXMgTE9DQUxFX1NFR01FTlRfTkFNRX07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/shared/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/shared/utils.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocalePrefix: () => (/* binding */ f),\n/* harmony export */   getSortedPathnames: () => (/* binding */ g),\n/* harmony export */   hasPathnamePrefixed: () => (/* binding */ i),\n/* harmony export */   isLocalizableHref: () => (/* binding */ n),\n/* harmony export */   localizeHref: () => (/* binding */ t),\n/* harmony export */   matchesPathname: () => (/* binding */ o),\n/* harmony export */   normalizeTrailingSlash: () => (/* binding */ c),\n/* harmony export */   prefixHref: () => (/* binding */ e),\n/* harmony export */   prefixPathname: () => (/* binding */ u),\n/* harmony export */   templateToRegex: () => (/* binding */ l),\n/* harmony export */   unprefixPathname: () => (/* binding */ r)\n/* harmony export */ });\nfunction n(n){return function(n){return\"object\"==typeof n?null==n.host&&null==n.hostname:!/^[a-z]+:/i.test(n)}(n)&&!function(n){const t=\"object\"==typeof n?n.pathname:n;return null!=t&&!t.startsWith(\"/\")}(n)}function t(t,r){let u=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r,c=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;if(!n(t))return t;const f=r!==u,l=i(o,c);return(f||l)&&null!=o?e(t,o):t}function e(n,t){let e;return\"string\"==typeof n?e=u(t,n):(e={...n},n.pathname&&(e.pathname=u(t,n.pathname))),e}function r(n,t){return n.replace(new RegExp(\"^\".concat(t)),\"\")||\"/\"}function u(n,t){let e=n;return/^\\/(\\?.*)?$/.test(t)&&(t=t.slice(1)),e+=t,e}function i(n,t){return t===n||t.startsWith(\"\".concat(n,\"/\"))}function c(n){const t=function(){try{return\"true\"===process.env._next_intl_trailing_slash}catch(n){return!1}}();if(\"/\"!==n){const e=n.endsWith(\"/\");t&&!e?n+=\"/\":!t&&e&&(n=n.slice(0,-1))}return n}function o(n,t){const e=c(n),r=c(t);return l(e).test(r)}function f(n,t){var e;return\"never\"!==t.mode&&(null===(e=t.prefixes)||void 0===e?void 0:e[n])||\"/\"+n}function l(n){const t=n.replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g,\"?(.*)\").replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g,\"(.+)\").replace(/\\[([^\\]]+)\\]/g,\"([^/]+)\");return new RegExp(\"^\".concat(t,\"$\"))}function s(n){return n.includes(\"[[...\")}function a(n){return n.includes(\"[...\")}function p(n){return n.includes(\"[\")}function h(n,t){const e=n.split(\"/\"),r=t.split(\"/\"),u=Math.max(e.length,r.length);for(let n=0;n<u;n++){const t=e[n],u=r[n];if(!t&&u)return-1;if(t&&!u)return 1;if(t||u){if(!p(t)&&p(u))return-1;if(p(t)&&!p(u))return 1;if(!a(t)&&a(u))return-1;if(a(t)&&!a(u))return 1;if(!s(t)&&s(u))return-1;if(s(t)&&!s(u))return 1}}return 0}function g(n){return n.sort(h)}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/shared/utils.js\n");

/***/ })

};
;