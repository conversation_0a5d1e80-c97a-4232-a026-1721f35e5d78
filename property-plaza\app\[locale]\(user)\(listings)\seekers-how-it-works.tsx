"use client"
import { useTranslations } from "next-intl"
import { <PERSON>, <PERSON>, HeartHandshake, Truck } from "lucide-react"
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout"
import DefaultLayoutContent from "@/components/seekers-content-layout/default-layout-content"

export default function SeekersHowItWorks() {
  const t = useTranslations("seeker")

  const howItWorks = [
    {
      icon: <Star className="w-6 h-6" />,
      title: t('HowItWorks.optionOne.title'),
      description: t('HowItWorks.optionOne.description')
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: t('HowItWorks.optionTwo.title'),
      description: t('HowItWorks.optionTwo.description')
    },
    {
      icon: <HeartHandshake className="w-6 h-6" />,
      title: t('HowItWorks.optionThree.title'),
      description: t('HowItWorks.optionThree.description')
    },
    {
      icon: <Truck className="w-6 h-6" />,
      title: t('HowItWorks.optionFour.title'),
      description: t('HowItWorks.optionFour.description')
    }
  ]

  return (
    <section className="bg-seekers-foreground/50 py-12">
      <MainContentLayout>
        <DefaultLayoutContent className="!mt-2"
          title={t("listing.homepage.howItWorks")}
          description={t('listing.homepage.howItWorksDescription')}
        >
          <div className="relative max-w-[1200px] mx-auto px-4">
            <div className="absolute hidden xl:block left-1/2 top-24 h-[calc(100%-6rem)] w-0.5 bg-gradient-to-b from-seekers-primary/20 via-seekers-primary/10 to-transparent" />

            <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 relative">
              {howItWorks.map((item, index) => (
                <div
                  key={index}
                  className={`group relative bg-white p-6 rounded-2xl border border-gray-100 
                  hover:border-seekers-primary/20 transition-all duration-300 shadow-sm hover:shadow-md
                  ${index === 0 ? 'xl:translate-x-[-15%]' : ''}
                  ${index === 1 ? 'xl:translate-x-[15%] xl:mt-16' : ''}
                  ${index === 2 ? 'xl:translate-x-[-15%] xl:mt-[-4rem]' : ''}
                  ${index === 3 ? 'xl:translate-x-[15%] xl:mt-10' : ''}
                  h-[180px]`}
                >
                  {/* Verbindingspunt met lijn */}
                  <div className={`absolute hidden xl:block top-1/2 
                  ${index % 2 === 0 ? 'right-0 translate-x-1/2' : 'left-0 -translate-x-1/2'}
                  w-4 h-4 rounded-full bg-seekers-primary/20 group-hover:bg-seekers-primary transition-colors`}>
                    <div className="absolute inset-1 rounded-full bg-seekers-primary/30 group-hover:bg-seekers-primary/60" />
                  </div>

                  <div className="flex gap-6 h-full">
                    {/* Icon container */}
                    <div className="relative shrink-0">
                      <div className="w-12 h-12 bg-seekers-primary/10 rounded-xl flex items-center justify-center 
                      text-seekers-primary group-hover:scale-110 transition-transform duration-300">
                        {item.icon}
                      </div>
                      <div className="absolute inset-0 bg-seekers-primary/5 blur-xl rounded-full 
                      group-hover:blur-2xl transition-all duration-300" />
                    </div>

                    {/* Content */}
                    <div className="flex flex-col justify-center flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-2xl font-bold text-seekers-primary/20 
                        group-hover:text-seekers-primary transition-colors">
                          0{index + 1}
                        </span>
                        <h3 className="text-lg font-semibold text-gray-900 
                        group-hover:text-seekers-primary transition-colors duration-300">
                          {item.title}
                        </h3>
                      </div>
                      <p className="text-gray-600 text-sm leading-relaxed line-clamp-3">
                        {item.description}
                      </p>
                    </div>
                  </div>

                  {/* Hover effect border */}
                  <div className="absolute inset-0 border-2 border-transparent 
                  group-hover:border-seekers-primary/20 rounded-2xl transition-colors duration-300" />
                </div>
              ))}
            </div>
          </div>
        </DefaultLayoutContent>
      </MainContentLayout>
    </section>
  )
}