"use client"
import { useGettListingDetailSeekers } from "@/core/applications/queries/listing/use-get-listing-detail-seekers";
import { ListingDetail } from "@/core/domain/listing/listing";
import { ComponentProps, createContext, forwardRef, useContext, useEffect, useState } from "react";

export interface ListingSeekersDetailContext {
  listing?: ListingDetail,
  isLoading?: boolean,
  isFetched?: boolean,
  isOpenImageDetailCarousel: boolean,
  setOpenImageDetailCarousel: (val: boolean) => void,
  openedIndex: number,
  setOpenedIndex: (index: number) => void
}

export const listingSeekersDetailContext = createContext<ListingSeekersDetailContext | undefined>(undefined)

export const useListingSeekersDetailContext = () => {
  const context = useContext(listingSeekersDetailContext)
  if (!context) throw new Error("useListingSeekersDetailContext must be used within a Listings")
  return context
}
interface ListingDetailSeekersWrapper extends ComponentProps<"div"> { code: string }

export const ListingDetailSeekersWrapper = forwardRef<HTMLDivElement, ListingDetailSeekersWrapper>(({ code, ...rest }, ref) => {
  {

    const listingDetailSeekersQuery = useGettListingDetailSeekers(code)
    const [isOpenImageDetailCarouse, setOpenImageDetailCarousel] = useState(false)
    const [openImageIndex, setOpenImageIndex] = useState(0)
    const [isLoading, setIsLoading] = useState(true)
    useEffect(() => {

      if (listingDetailSeekersQuery.isLoading && !listingDetailSeekersQuery.data) {
        setIsLoading(true)
      } else {
        setIsLoading(false)
      }
    }, [listingDetailSeekersQuery.isLoading, listingDetailSeekersQuery.data])
    return <listingSeekersDetailContext.Provider
      value={{
        listing: listingDetailSeekersQuery.data?.data,
        isLoading: isLoading,
        isFetched: listingDetailSeekersQuery.isFetched,
        isOpenImageDetailCarousel: isOpenImageDetailCarouse,
        setOpenImageDetailCarousel: setOpenImageDetailCarousel,
        openedIndex: openImageIndex,
        setOpenedIndex: setOpenImageIndex
      }}
    >
      <div {...rest} ref={ref}>
        {rest.children}
      </div>
    </listingSeekersDetailContext.Provider>
  }
})

ListingDetailSeekersWrapper.displayName = "ListingDetailSeekersWrapper"