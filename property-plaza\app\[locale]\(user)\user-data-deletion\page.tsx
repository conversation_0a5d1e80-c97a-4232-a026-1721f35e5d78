import { cookies } from "next/headers";
import { getTranslations } from "next-intl/server";
import { getUserDeletionContent } from "@/core/services/sanity/services";
import Content from "./content";
import { Metadata } from "next";
import HeroUserDataDeletionSection from "./hero";
import { userDataDeletionUrl } from "@/lib/constanta/route";

export async function generateMetadata(): Promise<Metadata> {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const t = await getTranslations("seeker")
  return {
    title: t('metadata.userDataDeletion.title'),
    description: t('metadata.userDataDeletion.description'),
    alternates: {
      languages: {
        "id": process.env.USER_DOMAIN + `/id${userDataDeletionUrl}`,
        "en": process.env.USER_DOMAIN + `/en${userDataDeletionUrl}`,
      },

    }
  }
}

export default async function userDataDeletionPage() {
  const cookiesStore = cookies()
  const locale = cookiesStore.get('NEXT_LOCALE')?.value
  const t = await getTranslations("seeker")
  // const termOfUseContent = await getTermsOfUseContent(locale?.toLocaleLowerCase() || "en") // use this when locale for content are implemented
  const userDataDeletionSection = await getUserDeletionContent("en")
  return <>
    <HeroUserDataDeletionSection />
    <Content content={userDataDeletionSection[0]} />
  </>
}