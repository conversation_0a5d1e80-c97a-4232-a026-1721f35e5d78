import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from "@radix-ui/react-icons"
import { Table } from "@tanstack/react-table"

import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { usePaginationRequest } from "@/hooks/use-pagination-request"
import { BaseMeta } from "@/core/domain/utils/utils"
import { useEffect, useState } from "react"
import { useTranslations } from "next-intl"

interface DataTablePaginationProps<TData> {
  table: Table<TData>,
  meta?: BaseMeta,
  isClientPagination?: boolean,
  disableRowPerPage?: boolean
}

const STARTING_PAGE = 1
export function DataTablePagination<TData>({
  table,
  meta, isClientPagination,
  disableRowPerPage
}: DataTablePaginationProps<TData>) {
  const t = useTranslations()
  const { page, perPage, setPageSearch, setPerPageSearch } = usePaginationRequest()
  const [disabledPrev, setDisablePrev] = useState(false)
  const [disabledNext, setDisableNext] = useState(false)
  const [forceDisablePagination, setForceDisablePagination] = useState(false)
  const [forceDisableRowPerPage, setForceDisableRowPerPage] = useState(disableRowPerPage)
  useEffect(() => {
    if (isClientPagination) {
      setDisablePrev(!table.getCanPreviousPage())
      setDisableNext(!table.getCanNextPage())
    } else if (meta) {
      setDisablePrev(meta.page == STARTING_PAGE)
      setDisableNext(meta?.page >= meta?.pageCount)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isClientPagination, meta?.prevPage, meta?.nextPage, table.getCanNextPage(), table.getCanPreviousPage()])
  const setToFirstPage = () => {
    if (isClientPagination) {
      table.setPageIndex(0)
    } else {

      setPageSearch(1)
    }
  }
  const setPerTableSize = (value: string) => {
    if (isClientPagination) {
      table.setPageSize(Number(value))
    } else {
      setPerPageSearch(+value)

    }
  }
  const setPrevPage = () => {
    if (isClientPagination) {
      table.previousPage()
    } else {
      setPageSearch(+page - 1)
    }
  }
  const setNextPage = () => {
    if (isClientPagination) {
      table.nextPage()
    } else {
      setPageSearch(+page + 1)
    }
  }
  const setToLastPage = () => {
    if (isClientPagination) {
      table.setPageIndex(table.getPageCount() - 1)
    } else {
      setPageSearch(meta?.pageCount || 1)
    }
  }
  useEffect(() => {
    const totalPage = +(meta?.pageCount || table.getPageCount() || 1)
    const total = +(meta?.total || 0)
    if (totalPage <= 1) {
      setForceDisablePagination(true)
      return
    }
    if (total < 10) {
      return
    }
    setForceDisablePagination(false)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [meta?.pageCount, table.getPageCount()])
  return (
    <div className="flex max-sm:flex-col max-sm:items-start items-center justify-end px-2 w-full flex-wrap">
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center md:space-x-2">
          {forceDisableRowPerPage ? <></>
            : <>
              <p className="text-sm font-medium max-sm:hidden">{t('component.dataTable.pagination.rowPerPage')} {" "}</p>
              <Select
                value={isClientPagination ? (table.getState().pagination.pageSize.toString()) : perPage.toString()}
                onValueChange={(value) => {
                  setPerTableSize(value)
                }}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue placeholder={table.getState().pagination.pageSize} />
                </SelectTrigger>
                <SelectContent side="top">
                  {[10, 20, 30, 40, 50].map((pageSize) => (
                    <SelectItem key={pageSize} value={`${pageSize}`}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </>
          }
        </div>
        {forceDisablePagination ? <> </> :

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => setToFirstPage()}
              disabled={disabledPrev}
            >
              <span className="sr-only">{t('component.dataTable.pagination.goToFIrstPage')}</span>
              <DoubleArrowLeftIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => setPrevPage()}
              disabled={disabledPrev}
            >
              <span className="sr-only">{t('component.dataTable.pagination.goToPreviousPage')}</span>
              <ChevronLeftIcon className="h-4 w-4" />
            </Button>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              {t('component.dataTable.page')} {meta?.page || table.getState().pagination.pageIndex + 1} {t('conjuntion.of')} {" "}
              {meta?.pageCount || table.getPageCount() || 1}
            </div>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => setNextPage()}
              disabled={disabledNext}
            >
              <span className="sr-only">{t('component.dataTable.pagination.goToNextPage')}</span>
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => setToLastPage()}
              disabled={disabledNext}
            >
              <span className="sr-only">{t('component.dataTable.pagination.goToLastPage')}</span>
              <DoubleArrowRightIcon className="h-4 w-4" />
            </Button>
          </div>
        }

      </div>
    </div>
  )
}
