import { GetChatsDto } from "@/core/infrastructures/messages/dto";
import { getChatListService } from "@/core/infrastructures/messages/services";
import { useQuery } from "@tanstack/react-query";

export const CHAT_LIST = "chat-list"
export default function useGetChatList(data:GetChatsDto){
  const {search,status} = data
  const query = useQuery({
    queryKey: [CHAT_LIST,search,status],
    queryFn: async () => {
      const searchData = {
        search:search || ""
      } 
      return await getChatListService(searchData)
    },
    retry: 0
  })
  return query
}