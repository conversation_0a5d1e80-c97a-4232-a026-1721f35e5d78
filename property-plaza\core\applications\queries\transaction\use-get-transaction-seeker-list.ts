import { GetAllTransactionDto } from "@/core/infrastructures/transaction/dto";
import { getAllSeekerTransactionService } from "@/core/infrastructures/transaction/services";
import { useQuery } from "@tanstack/react-query";

export const GET_TRANSACTION_SEEKER_QUERY_KEY = "transaction-seeker-list";
export function useGetTransactionSeekerQuery(
  data: GetAllTransactionDto,
  enabled?: boolean
) {
  const query = useQuery({
    queryKey: [
      GET_TRANSACTION_SEEKER_QUERY_KEY,
      data?.page,
      data?.per_page,
      data?.search,
      data?.start_date,
      data?.end_date,
      data?.type,
    ],
    queryFn: async () => {
      const searchParam: GetAllTransactionDto = {
        page: data.page || 1,
        per_page: data.per_page || 10,
        search: data.search || "",
        end_date: data.end_date || "",
        start_date: data.start_date || "",
        type: data.type || "",
      };
      const result = await getAllSeekerTransactionService(searchParam);
      return result;
    },
    retry: 0,
    enabled: enabled,
  });
  return query;
}
