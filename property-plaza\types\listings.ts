import { StepperItemId } from "@/lib/constanta/constant"

export type ListingStatus = "active" | "not-active" | "need-payment" | "deactive" | "need-review" | "rejected" | "draft"

interface Coordinate {
  lat: string,
  long: string
}
interface ReviewListing {
  checker: string,
  reviewSummary: string
  reviews :{
    location: string[],
    basicInformation: string[],
    pricing: string[],
    feature: string[],
    images: string[]
  }
}
export interface Listing {
  id: string
  imgUrl: string,
  name: string,
  location: string,
  accountManager: string,
  status: ListingStatus,
  price: string,
  coordinate: Coordinate,
  ownerName:string,
  reviewListing?: ReviewListing
}

export type StepperStatus = "filled" | "partial" | "need-revision" | "valid"
export interface Stepper {
  id: string,
  name: string,
  status: StepperStatus
}

export type StepperListing = keyof typeof StepperItemId
export interface RejectedListingSummaryProps {
  rejectedList?: string[]
}