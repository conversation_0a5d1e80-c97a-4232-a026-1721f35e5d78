import { useTranslations } from "next-intl";
import { z } from "zod";

export function useEmailCaptureFormSchema() {
  const t = useTranslations("seeker");
  const formSchema = z.object({
    name: z.string({
      message: t("form.utility.fieldRequired", { field: t("form.field.name") }),
    }),
    email: z.string().email({
      message: t("form.utility.enterValidField", {
        field: t("form.field.email"),
      }),
    }),
    agreeForNewsletter: z.boolean().default(false),
  });
  return formSchema;
}
