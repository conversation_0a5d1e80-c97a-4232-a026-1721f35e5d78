import { cookies } from "next/headers";
import HeroTermsOfUseSection from "./hero";
import { getTranslations } from "next-intl/server";
import { getTermsOfUseContent } from "@/core/services/sanity/services";
import Content from "./content";
import { Metadata } from "next";
import { termSeekerUrl } from "@/lib/constanta/route";

export async function generateMetadata(): Promise<Metadata> {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const t = await getTranslations("seeker")
  return {
    title: t('metadata.termsOfUse.title'),
    description: t('metadata.termsOfUse.description'),
    alternates: {
      languages: {
        "id": process.env.USER_DOMAIN + `/id/${termSeekerUrl}`,
        "en": process.env.USER_DOMAIN + `/en/${termSeekerUrl}`,
      },

    }
  }
}

export default async function TermsOfUsePage() {
  const cookiesStore = cookies()
  const locale = cookiesStore.get('NEXT_LOCALE')?.value
  const t = await getTranslations("seeker")
  // const termOfUseContent = await getTermsOfUseContent(locale?.toLocaleLowerCase() || "en") // use this when locale for content are implemented
  const termOfUseContent = await getTermsOfUseContent("en")
  return <>
    <HeroTermsOfUseSection />
    <Content content={termOfUseContent[0]} />
  </>
}