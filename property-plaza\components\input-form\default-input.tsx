import { FormField } from '@/components/ui/form'
import { FieldValues } from 'react-hook-form'
import { Input } from '../ui/input'
import { BaseInputForm } from '@/types/base'
import BaseInputLayout from './base-input'
import { ComponentProps } from 'react'
import { cn } from '@/lib/utils'

interface DefaultInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  placeholder: string
  description?: string
  type: string,
  inputProps?: ComponentProps<"input">,
  children?: React.ReactNode,
  labelClassName?: string,
  containerClassName?: string,
  inputContainer?: string,
  variant?: "default" | "float"
}
export default function DefaultInput<T extends FieldValues>({ form, label, name, placeholder, description, type, inputProps, children, labelClassName, containerClassName, inputContainer, variant = "default" }: DefaultInputProps<T>) {
  return <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <BaseInputLayout
        label={label}
        description={description}
        labelClassName={cn(variant == "float" ? "absolute -top-2 left-2 px-1 text-xs bg-background z-10" : "", labelClassName)}
        containerClassName={containerClassName}
        variant={variant}>
        <div className={cn(
          'flex gap-2 w-full overflow-hidden',
          variant == "float" ? "" :
            "border rounded-sm focus-within:border-neutral-light", inputContainer)}>
          <Input
            type={type}
            placeholder={placeholder}
            {...field}
            {...inputProps}
            className={cn('border-none focus:outline-none shadow-none focus-visible:ring-0 w-full', variant == "float" ? "px-0" : "", inputProps?.className)}
          />
          {children}
        </div>
      </BaseInputLayout>
    )}
  />
}