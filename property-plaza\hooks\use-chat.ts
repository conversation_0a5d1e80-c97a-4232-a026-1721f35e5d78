"use client";
import { useMessagingStore } from "@/stores/messaging.store";

import { useEffect } from "react";
import { MessageTextDto } from "@/core/infrastructures/messages/dto";
import { transformMessageText } from "@/core/infrastructures/messages/transform";
import { socket } from "@/core/utils/socket";
import { usePutMessageStatus } from "@/core/applications/mutations/messages/use-put-message-status";
import useGetChatDetail from "@/core/applications/queries/messages/use-get-chat-detail";

export const useChat = () => {
  const {
    roomId,
    participant,
    updateSpecificAllChat,
    setchatDetail,
    setParticipant,
    updatechatDetail,
    setRoomId,
    setlayout,
  } = useMessagingStore((state) => state);
  const updateMessageStatusMutation = usePutMessageStatus();
  const chatDetailQuery = useGetChatDetail(
    roomId || "",
    roomId !== undefined ? true : false
  );
  useEffect(() => {
    if (!socket.connected) {
      socket.connect();
    }

    return () => {
      socket.disconnect();
    };
  }, []);

  useEffect(() => {
    if (!roomId) return;
    // Join the chat room
    if (!chatDetailQuery.data?.data) return;
    const data = chatDetailQuery.data.data;
    setchatDetail(data.allMessages || []);
    setParticipant(data.participant);
  }, [chatDetailQuery?.data?.data, roomId, setParticipant, setchatDetail]);

  const sendMessage = (message: string, receiver: string) => {
    if (!socket.connected) {
      socket.connect();
    }
    socket.emit(
      "sendMessage",
      { code: roomId, requested_by: "CLIENT", message, receiver },
      (response: MessageTextDto) => {
        const message = transformMessageText(response);
        updatechatDetail(message);
        updateSpecificAllChat(message, false, roomId);
      }
    );
  };
  const createChatRoom = (receiverId: string) => {
    if (!socket.connected) {
      socket.connect();
    }
    socket.emit(
      "createRoomChat",
      { requested_by: "CLIENT", receiver: receiverId },
      (response: any) => {}
    );
  };
  const ownerUpdateMessageStatus = async () => {
    if (!roomId) return;
    if (participant?.category !== "SEEKER_OWNER") return;
    if (participant.status !== "WAITING_FOR_RESPONSE") return;
    await updateMessageStatusMutation.mutateAsync(roomId);
  };
  const joinRoom = (id: string) => {
    if (roomId) {
      socket.emit("leaveRoomChat", { code: roomId });
    }
    socket.emit("joinRoomChat", { code: id });
    chatDetailQuery.refetch();
  };
  const leaveRoom = () => {
    // if (!socket.connected) {
    //   return;
    // }
    setRoomId("");
    setchatDetail([]);
    setParticipant();
    setlayout("list");
    socket.emit("leaveRoomChat", { code: roomId }, () => {
      setRoomId("");
    });
  };
  return {
    sendMessage,
    createChatRoom,
    leaveRoom,
    ownerUpdateMessageStatus,
    joinRoom,
  };
};
