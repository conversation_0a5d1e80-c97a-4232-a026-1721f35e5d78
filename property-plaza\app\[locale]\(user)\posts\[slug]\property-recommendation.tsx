"use client"
import useIntersectionObserver from "@/hooks/use-Intersection-observer"
import PropertyRecommendationContent from "./property-recommendation-content"
import { useGetBatchListing } from "@/core/applications/queries/listing/use-get-batch-listing"
import { useEffect } from "react"

export default function PropertyRecommendation({ propertyIds, conversions }: { propertyIds: string[], conversions: { [key: string]: number } }) {
  const { firstTimeVisible, isVisible, sectionRef, setFirstTimeVisible } = useIntersectionObserver()
  const propertiesQuery = useGetBatchListing(propertyIds, (isVisible && firstTimeVisible))
  useEffect(() => {
    if (propertiesQuery.isFetching) setFirstTimeVisible(false)
  }, [propertiesQuery.isFetching, setFirstTimeVisible])
  return <section ref={sectionRef} >

    <PropertyRecommendationContent properties={propertiesQuery.data?.data || []} conversions={conversions} />
  </section>

}
