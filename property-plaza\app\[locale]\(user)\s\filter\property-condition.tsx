import { useTranslations } from "next-intl";
import FilterContentLayout from "./filter-content-layout";
import { BaseSelectInputValue } from "@/types/base";
import CheckboxFilterItem from "./checkbox-filter-item";
import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store";
import SubleasedIcon from "@/components/icons/property-detail/Sublease allowed.svg"
import ConstructionNearbyIcon from "@/components/icons/property-detail/Construction nearby-next to the location.svg"
import MunicipalWaterworkIcon from "@/components/icons/property-detail/Municipal Waterworks.svg"
import PlumbingIcon from "@/components/icons/property-detail/Plumbing.svg"
import RecentlyRenovatedIcon from "@/components/icons/property-detail/Recently renovated.svg"
import Image from "next/image";
import { cn } from "@/lib/utils";
import { SellingPointList } from "@/core/domain/listing/listing-seekers";

export default function PropertyConditionFilter() {
  const t = useTranslations("seeker")
  const { features: propertyCondition, setFeatures: setPropertyCondition } = useSeekerFilterStore(state => state)
  const views: BaseSelectInputValue<string>[] = [
    {
      id: "1",
      content: <div className="flex gap-1 items-center">
        <Image
          src={SubleasedIcon}
          alt=""
          className={cn("w-4 h-4", propertyCondition.includes(SellingPointList.subleaseAllowed) ? "invert" : "invert-0")}
          width={16}
          height={16}
        />
        <span className="">{t('listing.propertyCondition.optionOne.title')}</span>
      </div>,
      value: SellingPointList.subleaseAllowed
    },
    {
      id: "2",
      content: <div className="flex gap-1 items-center">
        <Image
          src={ConstructionNearbyIcon}
          alt=""
          className={cn("w-4 h-4", propertyCondition.includes(SellingPointList.constructionNearby) ? "invert" : "invert-0")}
          width={16}
          height={16}
        />
        <span className="">{t('listing.propertyCondition.optionTwo.title')}</span>
      </div>,
      value: SellingPointList.constructionNearby
    },
    {
      id: "3",
      content: <div className="flex gap-1 items-center">
        <Image
          src={MunicipalWaterworkIcon}
          alt=""
          className={cn("w-4 h-4", propertyCondition.includes("MUNICIPAL_WATERWORK") ? "invert" : "invert-0")}
          width={16}
          height={16}
        />
        <span className="">{t('listing.propertyCondition.optionThree.title')}</span>
      </div>,
      value: "MUNICIPAL_WATERWORK"
    },

    {
      id: "4",
      content: <div className="flex gap-1 items-center">
        <Image
          src={PlumbingIcon}
          alt=""
          className={cn("w-4 h-4", propertyCondition.includes(SellingPointList.plumbing) ? "invert" : "invert-0")}
          width={16}
          height={16}
        />
        <span className="">{t('listing.propertyCondition.optionFour.title')}</span>
      </div>,
      value: SellingPointList.plumbing
    },
    {
      id: "5",
      content: <div className="flex gap-1 items-center">
        <Image
          src={RecentlyRenovatedIcon}
          alt=""
          className={cn("w-4 h-4", propertyCondition.includes(SellingPointList.recentlyRenovated) ? "invert" : "invert-0")}
          width={16}
          height={16}
        />
        <span className="">{t('listing.propertyCondition.optionFive.title')}</span>
      </div>,
      value: SellingPointList.recentlyRenovated
    },
  ]
  return <FilterContentLayout title={t('listing.propertyCondition.title')}>
    <div className="flex flex-wrap gap-2">
      {
        views.map(item =>
          <CheckboxFilterItem
            key={item.id}
            item={item}
            setValue={setPropertyCondition}
            isActive={propertyCondition.includes(item.value)} />
        )}
    </div>
  </FilterContentLayout>
}