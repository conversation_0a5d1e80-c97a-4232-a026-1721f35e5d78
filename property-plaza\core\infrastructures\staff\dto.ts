import { BasePaginationRequest } from "@/types/base"

export interface CreateStaffDto {
  role_id: string,
  username: string,
  email: string,
  password: string,
  fullname: string,
  mobile_phone: string
}

export interface UpdateStaffDto {
  role_id: string,
  username: string,
  email: string,
  password: string,
  fullname: string,
  mobile_phone: string
}

export interface GetStaffs extends BasePaginationRequest {}

export interface GetDetailStaff {
  id:String
}

export interface StaffDto {
  id: string,
  fullname: string,
  email: string,
  phone_code: string,
  phone_number: string,
  created_at: string,
  role: {
    code: string,
    name: string
  }
}