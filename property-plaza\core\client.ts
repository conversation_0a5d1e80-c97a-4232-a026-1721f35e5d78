"use client"
import { ACCESS_TOKEN } from "@/lib/constanta/constant";
import axios from "axios";
import Cookies from "js-cookie";
import https from "https";

const agent = new https.Agent({
  rejectUnauthorized: false,
});
export const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_SERVICE_API,
  headers: {
    Authorization: Cookies.get(ACCESS_TOKEN)
      ? "Bearer" + " " + Cookies.get(ACCESS_TOKEN)
      : "",
  },
  httpsAgent: agent,
});

// localApiClient refered to backend on current project folder
export const localApiClient = axios.create({
  baseURL: "/api/",
  httpsAgent: agent,
})