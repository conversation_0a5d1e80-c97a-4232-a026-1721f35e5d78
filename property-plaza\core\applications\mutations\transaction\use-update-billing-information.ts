import { putBillingInformation } from "@/core/infrastructures/transaction/api";
import { PutCardBillingAddressDto } from "@/core/infrastructures/transaction/dto";
import { useMutation } from "@tanstack/react-query";

export function useUpdateBillingInformation() {
  const mutation = useMutation({
    mutationFn: (data: PutCardBillingAddressDto) => putBillingInformation(data),
  });
  return mutation;
}
