"use client"
import { <PERSON><PERSON> } from "@/components/ui/button";
import { isChatEnded, isSeekerWaitingResponse } from "@/core/domain/messages/messages";
import { useChat } from "@/hooks/use-chat";
import { useToast } from "@/hooks/use-toast";
import { MAX_MESSAGE_COUNT } from "@/lib/constanta/constant";
import { useMessagingStore } from "@/stores/messaging.store";
import { PaperPlaneIcon } from "@radix-ui/react-icons";
import { ArrowLeft } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { ReceiverName } from "./receiver-name";
import { Badge } from "@/components/ui/badge";
import ChatDetailMessages from "./chat-detail-messages";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";




export function ChatDetail() {
  const t = useTranslations("seeker")
  const { currentLayout, roomId, chatDetail, participant, setlayout } = useMessagingStore()
  const headerChatRef = useRef<HTMLDivElement | null>(null)
  const sendChatRef = useRef<HTMLDivElement | null>(null)
  const dummyRef = useRef<HTMLDivElement | null>(null)
  const [message, setMessage] = useState("")
  const { toast } = useToast()
  const chat = useChat()
  useEffect(() => {
    if (!roomId) return
    chat.joinRoom(roomId)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [roomId])
  const sendMessage = (): void => {
    if (!roomId) return
    if (message.trim() == "") return
    if (message.length > MAX_MESSAGE_COUNT) {
      toast({
        title: t('info.messageTooLong.title'),
        description: t('info.messageTooLong.description', { count: MAX_MESSAGE_COUNT })
      })
    }
    chat.sendMessage(message, roomId)
    setMessage("")
  }

  const inputMessage = (message: string) => {
    if (message.length >= MAX_MESSAGE_COUNT) {
      const slicedMessage = message.slice(0, MAX_MESSAGE_COUNT)
      setMessage(slicedMessage)
      return
    }
    setMessage(message)
  }

  useEffect(() => {
    if (dummyRef.current == null) return
    dummyRef.current.scrollIntoView({ behavior: "instant", block: "end" })
  }, [chatDetail])
  return <div
    className={` flex flex-col w-full max-sm:bg-white bg-seekers-primary-light/10 h-full pb-4 px-6 pr-3 md:max-h-full  md:rounded-lg relative 
  ${roomId || currentLayout == "detail-chat" ?
        "max-sm:w-screen max-sm:left-0 max-sm:px-2 max-sm:bottom-0 max-sm:h-screen max-sm:fixed max-sm:inset-0 max-sm:z-20"
        :
        "hidden"}`}>
    {
      roomId ? <>
        {/* Header */}
        <div ref={headerChatRef} className="flex bg-seekers-primary-light/0 w-full absolute gap-2 items-center left-0 md:px-6 pl-1 px-3 py-4 top-0 z-20">
          <Button variant={"ghost"} size={"icon"} className="lg:hidden" onClick={() => chat.leaveRoom()}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex h-10 w-fit cursor-pointer gap-2" onClick={() => setlayout("detail-user")}>
            <Avatar className={cn("w-10 h-10 rounded-full bg-seekers-text-lighter")}>
              <AvatarImage src={participant?.property?.image || participant?.image || ""} className="border" />
              <AvatarFallback className="bg-transparent text-white">
                <span>{participant?.fullName[0][0]}{participant?.fullName[participant?.fullName.length / 2]?.[0] || ""}</span>
              </AvatarFallback>
            </Avatar>
            <div >
              <h2>
                <ReceiverName category={''} name={participant?.property?.title || participant?.fullName || ""} />
              </h2>
              <div className="flex flex-wrap gap-2 items-center">
                {participant?.property?.title && <p className="text-xs">{participant?.fullName}</p>}
                {
                  isSeekerWaitingResponse(participant?.status || "") ?
                    <Badge variant={"outline"} className="border-seekers-text-lighter text-seekers-text-light mt-1">
                      {t('message.waitingResponse')}
                    </Badge>
                    : isChatEnded(participant?.status || "") ? <Badge variant={"outline"} className="border-seekers-text-lighter text-seekers-text-light mt-1">
                      {t('message.chatEnded')}
                    </Badge> : <></>
                }
              </div>
            </div>

          </div>
          <div className="flex-grow">
          </div>
        </div>

        <div className="flex-1 mt-[90px] overflow-hidden">
          <ScrollArea className="h-full max-sm:px-3 overflow-y-auto pr-3" >
            <ChatDetailMessages messages={chatDetail} />
            <div ref={dummyRef} className="h-10"></div>
          </ScrollArea>
        </div>

        {/* sendChatRef */}
        <div ref={sendChatRef} className="bg-seekers-primary-light/0 w-full absolute bottom-0 left-0 max-sm:px-3 pt-2 px-6 py-4">

          <div className="flex bg-background border rounded-sm focus-within:border-neutral-light items-end overflow-hidden">
            <Input
              value={message}
              maxLength={MAX_MESSAGE_COUNT}
              onChange={e => inputMessage(e.target.value)}
              onKeyDown={e => e.key === "Enter" ? sendMessage() : () => { }}
              className={cn('border-none focus:outline-none shadow-none focus-visible:ring-0')}
              disabled={isSeekerWaitingResponse(participant?.status || "")}
              placeholder={t("form.placeholder.basePlaceholder", { field: t("form.field.message").toLowerCase() })}
            />

            <p className="text-[10px] text-seekers-text-light px-2">{message.length}/{MAX_MESSAGE_COUNT}</p>
            <Button
              type="submit"
              variant={"default-seekers"}
              className="rounded-none text-white w-12"
              size={"icon"}
              onClick={sendMessage}
              disabled={isSeekerWaitingResponse(participant?.status || "")}
            >
              <PaperPlaneIcon />
            </Button>
          </div>
        </div>
      </>
        : <></>

    }
  </div >
}