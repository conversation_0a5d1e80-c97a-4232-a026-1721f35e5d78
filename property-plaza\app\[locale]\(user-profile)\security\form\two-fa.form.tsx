import { z } from "zod";
import useTwoFactorAuthenticationFormSchema from "./two-fa-form.schema";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormField } from "@/components/ui/form";
import BaseInputLayout from "@/components/input-form/base-input";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";
import { useVerifyTotp } from "@/core/applications/mutations/auth/use-verify-totp";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import { useUserStore } from "@/stores/user.store";
import { useEffect } from "react";
import { useActivateTotp } from "@/core/applications/mutations/auth/use-activate-totp";

const MAX_OTP = 6
export default function TwoFactorAuthenticationForm({ setOpenDialog }: { setOpenDialog: (val: boolean) => void }) {
  const t = useTranslations("seeker")
  const formSchema = useTwoFactorAuthenticationFormSchema()
  const { seekers } = useUserStore()
  const otpFormMutation = useVerifyTotp()
  const otpFormActivation = useActivateTotp()
  type typeFormSchema = z.infer<typeof formSchema>
  const form = useForm<typeFormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      otp: ""
    }
  })
  const { toast } = useToast()

  const formWatcher = form.watch("otp")

  const submitHandler = async (value: z.infer<typeof formSchema>) => {
    try {
      if (seekers.has2FA) {

        await otpFormMutation.mutateAsync({ otp: value.otp, requested_by: seekers.code })
      } else {
        await otpFormActivation.mutateAsync({ otp: +(value.otp), request_setting: "ACTIVE_2FA" })
      }
      toast({
        title: t("success.activateTotp")
      })
      setOpenDialog?.(false)
      window.location.reload()
    } catch (e) {
      const data: any = (e as any).response.data
      toast({
        variant: "destructive",
        title: t('error.failedEnablingTwoFA'),
        description: data.message
      })
    }
  }
  useEffect(() => {
    if (formWatcher.length == 6) {
      document.getElementById("submit-form")?.dispatchEvent(new Event("submit", { cancelable: true, bubbles: true }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formWatcher, form])

  return <Form {...form}>
    <form id="submit-form" onSubmit={form.handleSubmit(submitHandler)}>
      <FormField
        control={form.control}
        name={"otp"}
        render={({ field }) => (
          <div className="flex justify-start">
            <BaseInputLayout label="">
              <InputOTP maxLength={MAX_OTP} {...field}
                pattern={REGEXP_ONLY_DIGITS} required containerClassName="flex justify-start" >
                <InputOTPGroup>
                  {Array.from({ length: MAX_OTP }, (_, idx) =>
                    <InputOTPSlot key={idx} index={idx} className="w-10 h-10 text-xl" />)}
                </InputOTPGroup>
              </InputOTP>
            </BaseInputLayout>
          </div>
        )}
      />
    </form>
  </Form>
}