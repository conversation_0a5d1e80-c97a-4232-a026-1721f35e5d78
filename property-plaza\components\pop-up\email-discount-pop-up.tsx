"use client"

import { useEffect, useState } from "react"
import Dialog<PERSON>rapper from "../dialog-wrapper/dialog-wrapper"
import DialogHeaderWrapper from "../dialog-wrapper/dialog-header-wrapper"
import EmailInputDiscountForm from "./email-discount-pop-up/email-input-discount.form"
import { useMotionValueEvent, useScroll } from "framer-motion"
import { useUserStore } from "@/stores/user.store"
import { useLocale, useTranslations } from "next-intl"
import { Button } from "../ui/button"
import Link from "next/link"
import { noLoginPlanUrl } from "@/lib/constanta/route"
import { useToast } from "@/hooks/use-toast"
import PopUpBackground from "@/public/pop-up-background.jpeg"
import Image from "next/image"
import { Clock, Star, Users, X, Gift, Sparkles } from "lucide-react"

const DISCOUNT_CODE = "WELCOME25"
const DISCOUNT_PERCENTAGE = 25
const POPUP_DELAY = 8000 // 8 seconds
const SCROLL_THRESHOLD = 0.25 // 25% scroll

export default function EmailDiscountPopup() {
  const t = useTranslations("seeker")
  const locale = useLocale()
  const [open, setOpen] = useState(false)
  const [isValidScroll, setIsValidScroll] = useState(false)
  const [timeSpent, setTimeSpent] = useState(0)
  const [showUrgency, setShowUrgency] = useState(false)
  const { scrollYProgress } = useScroll()
  const { seekers, hydrated } = useUserStore()
  const { toast } = useToast()
  const [isSubmittedEmail, setSubmittedEmail] = useState(false)

  // Enhanced popup triggering logic
  useMotionValueEvent(scrollYProgress, "change", (latest) => {
    if (latest > SCROLL_THRESHOLD) {
      setIsValidScroll(true)
    }
  })

  // Time-based and exit-intent triggers
  useEffect(() => {
    if (!hydrated) return
    if (seekers.email) return

    // Time-based trigger
    const timeTimer = setTimeout(() => {
      setTimeSpent(prev => prev + 1000)
      if (timeSpent >= POPUP_DELAY && !open) {
        setOpen(true)
      }
    }, 1000)

    // Scroll-based trigger with delay
    if (isValidScroll && timeSpent >= 3000) {
      const scrollTimer = setTimeout(() => {
        if (!open) setOpen(true)
      }, 1500)
      return () => clearTimeout(scrollTimer)
    }

    // Exit-intent simulation (mouse leave detection)
    const handleMouseLeave = (e: MouseEvent) => {
      if (e.clientY <= 0 && !open && timeSpent >= 5000) {
        setOpen(true)
      }
    }

    document.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      clearTimeout(timeTimer)
      document.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [hydrated, seekers.email, isValidScroll, timeSpent, open])

  // Urgency timer
  useEffect(() => {
    if (open) {
      const urgencyTimer = setTimeout(() => {
        setShowUrgency(true)
      }, 10000) // Show urgency after 10 seconds
      return () => clearTimeout(urgencyTimer)
    }
  }, [open])
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={<></>}
    dialogClassName="overflow-hidden max-w-md"
    drawerClassName="overflow-hidden"
  >
    {/* Enhanced Header with Visual Appeal */}
    <DialogHeaderWrapper className="relative h-56 bg-gradient-to-br from-teal-600 via-teal-500 to-emerald-500">
      {/* Close button */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-4 right-4 z-20 text-white hover:bg-white/20 rounded-full"
        onClick={() => setOpen(false)}
      >
        <X className="h-4 w-4" />
      </Button>

      {/* Floating elements for visual appeal */}
      <div className="absolute inset-0 overflow-hidden">
        <Sparkles className="absolute top-6 left-6 h-6 w-6 text-yellow-300 animate-pulse" />
        <Gift className="absolute top-8 right-16 h-5 w-5 text-yellow-200 animate-bounce" />
        <Star className="absolute bottom-8 left-8 h-4 w-4 text-yellow-300 animate-pulse" />
      </div>

      {/* Background pattern */}
      <div className="absolute inset-0 bg-black/10 backdrop-blur-sm"></div>

      {/* Main header content */}
      <div className="relative z-10 h-full flex flex-col justify-center items-center text-center px-6">
        <div className="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-sm font-bold mb-3 animate-pulse">
          🎉 LIMITED TIME OFFER
        </div>
        <h3 className="text-xl font-bold text-white mb-2 leading-tight">
          Don't Get Scammed in Bali!
        </h3>
        <p className="text-white/90 text-sm">
          Get {DISCOUNT_PERCENTAGE}% OFF + Exclusive Scam Protection Guide
        </p>
      </div>
    </DialogHeaderWrapper>

    {/* Enhanced Content */}
    <div className="space-y-6 p-6">
      {isSubmittedEmail ? (
        /* Success State */
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Gift className="h-8 w-8 text-green-600" />
          </div>

          <h4 className="text-lg font-bold text-seekers-text">
            🎉 Your Discount Code is Ready!
          </h4>

          <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-4">
            <p className="text-sm text-gray-600 mb-2">Your exclusive discount code:</p>
            <div className="bg-gradient-to-r from-teal-500 to-emerald-500 text-white px-4 py-2 rounded-lg font-mono text-lg font-bold tracking-wider">
              {DISCOUNT_CODE}
            </div>
          </div>

          <p className="text-sm text-seekers-text-light">
            {t.rich('promotion.popUp.couponCodeDescription', {
              code: () => <span className="font-bold text-teal-600">{DISCOUNT_CODE}</span>
            })}
          </p>

          <Button
            asChild
            className="w-full bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white font-semibold py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
            onClick={() => {
              navigator.clipboard.writeText(DISCOUNT_CODE)
              toast({
                title: t("misc.copy.successCopyContent", { content: t('misc.promoCode') })
              })
            }}
          >
            <Link href={noLoginPlanUrl} hrefLang={locale}>
              🚀 Claim Your Discount Now
            </Link>
          </Button>
        </div>
      ) : (
        /* Email Capture State */
        <div className="space-y-5">
          {/* Value Proposition */}
          <div className="text-center space-y-3">
            <h4 className="text-lg font-bold text-seekers-text">
              Join 2,500+ Smart Expats Who Avoided Bali Scams
            </h4>
            <p className="text-seekers-text-light text-sm leading-relaxed">
              Get instant access to verified properties + our exclusive "Bali Scam Protection Guide"
              that has saved expats over $50,000 in fraudulent deals.
            </p>
          </div>

          {/* Social Proof & Benefits */}
          <div className="grid grid-cols-3 gap-3 text-center">
            <div className="space-y-1">
              <div className="flex justify-center">
                <Users className="h-5 w-5 text-teal-600" />
              </div>
              <p className="text-xs font-semibold text-seekers-text">2,500+</p>
              <p className="text-xs text-seekers-text-light">Protected Expats</p>
            </div>
            <div className="space-y-1">
              <div className="flex justify-center">
                <Star className="h-5 w-5 text-yellow-500" />
              </div>
              <p className="text-xs font-semibold text-seekers-text">4.9/5</p>
              <p className="text-xs text-seekers-text-light">Trust Rating</p>
            </div>
            <div className="space-y-1">
              <div className="flex justify-center">
                <Clock className="h-5 w-5 text-orange-500" />
              </div>
              <p className="text-xs font-semibold text-seekers-text">48hrs</p>
              <p className="text-xs text-seekers-text-light">Valid Only</p>
            </div>
          </div>

          {/* Urgency Element */}
          {showUrgency && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-center animate-pulse">
              <p className="text-red-700 text-sm font-semibold">
                ⚡ Only 47 discount codes left today!
              </p>
            </div>
          )}

          {/* Email Form */}
          <EmailInputDiscountForm setIsSubmitted={setSubmittedEmail} />

          {/* Alternative Actions */}
          <div className="text-center space-y-2">
            <Button
              variant="link"
              className="text-sm text-gray-500 hover:text-gray-700"
              onClick={() => setOpen(false)}
            >
              Maybe later (you'll miss this deal)
            </Button>

            <p className="text-xs text-center text-seekers-text-light leading-relaxed">
              {t.rich('promotion.popUp.termsAndCondition', {
                term: (chunk) => <span className="font-bold text-seekers-text underline cursor-pointer">{chunk}</span>,
                privacy: (chunk) => <span className="font-bold text-seekers-text underline cursor-pointer">{chunk}</span>
              })}
            </p>
          </div>
        </div>
      )}
    </div>
  </DialogWrapper>
}