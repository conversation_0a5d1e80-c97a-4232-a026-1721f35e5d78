"use client"

import { useEffect, useState } from "react"
import DialogWrapper from "../dialog-wrapper/dialog-wrapper"
import DialogHeaderWrapper from "../dialog-wrapper/dialog-header-wrapper"
import EmailInputDiscountForm from "./email-discount-pop-up/email-input-discount.form"
import { useMotionValueEvent, useScroll } from "framer-motion"
import { useUserStore } from "@/stores/user.store"
import { useLocale, useTranslations } from "next-intl"
import { Button } from "../ui/button"
import Link from "next/link"
import { noLoginPlanUrl } from "@/lib/constanta/route"
import { useToast } from "@/hooks/use-toast"
import PopUpBackground from "@/public/pop-up-background.jpeg"
import Image from "next/image";

const DISCOUNT_CODE = "WELCOME25"
const DISCOUNT_PERCENTAGE = 25
export default function EmailDiscountPopup() {
  const t = useTranslations("seeker")
  const locale = useLocale()
  const [open, setOpen] = useState(false)
  const [isValidScroll, setIsValidScroll] = useState(false)
  const { scrollYProgress } = useScroll()
  const { seekers, hydrated } = useUserStore()
  const { toast } = useToast()
  useMotionValueEvent(scrollYProgress, "change", (latest) => {
    if (latest > 0.3) {
      setIsValidScroll(true)
    }
  })
  useEffect(() => {
    if (!hydrated) return
    if (seekers.email) return
    if (isValidScroll) {
      const timer = setTimeout(() =>
        setOpen(true), 500)
      return () => clearTimeout(timer)
    }
  }, [hydrated, seekers.email, isValidScroll])
  const [isSubmittedEmail, setSubmittedEmail] = useState(false)
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={<></>}
    dialogClassName="overflow-hidden"
    drawerClassName="overflow-hidden"
  >
    <DialogHeaderWrapper className="relative h-48">

      <h3 className="text-base font-bold text-seekers-text hidden">
        {t('promotion.popUp.title')}
      </h3>
    </DialogHeaderWrapper>
    <div className="absolute top-0 left-0 w-full h-52 overflow-hidden rounded-t-lg -z-10">
      <Image src={PopUpBackground} alt="pop-up-background" className="object-cover" fill />
    </div>
    <div className="space-y-4">

      <h3 className="text-base font-bold text-seekers-text">
        {t('promotion.popUp.title')}
      </h3>
      {
        isSubmittedEmail ?
          <>
            <p className="text-seekers-text-light">
              {t.rich('promotion.popUp.couponCodeDescription', {
                code: () => <b>{DISCOUNT_CODE}</b>
              })}
            </p>
            <Button asChild onClick={() => {
              navigator.clipboard.writeText(DISCOUNT_CODE)
              toast(
                {
                  title: t("misc.copy.successCopyContent", { content: t('misc.promoCode') })
                }
              )
            }}>
              <Link className="w-full" href={noLoginPlanUrl} hrefLang={locale}>
                {t('cta.useDiscountCode')}
              </Link>
            </Button>
          </> :
          <>
            <p className="text-seekers-text-light">{t('promotion.popUp.description', { count: DISCOUNT_PERCENTAGE })}</p>
            <EmailInputDiscountForm setIsSubmitted={setSubmittedEmail} />
            <Button variant={"link"} className="w-full" onClick={() => setOpen(false)}>{t('misc.maybeLater')}</Button>
            <p className="text-xs text-center text-seekers-text-light">{t.rich('promotion.popUp.termsAndCondition', {
              term: (chunk) => <span className="font-bold text-seekers-text">{chunk}</span>,
              privacy: (chunk) => <span className="font-bold text-seekers-text">{chunk}</span>
            })}</p>
          </>
      }
    </div>
  </DialogWrapper >
}