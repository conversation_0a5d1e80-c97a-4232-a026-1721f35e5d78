"use client"

import { useEffect, useState } from "react"
import DialogWrapper from "../dialog-wrapper/dialog-wrapper"
import DialogHeaderWrapper from "../dialog-wrapper/dialog-header-wrapper"
import EmailInputDiscountForm from "./email-discount-pop-up/email-input-discount.form"
import { useMotionValueEvent, useScroll } from "framer-motion"
import { useUserStore } from "@/stores/user.store"
import { useLocale, useTranslations } from "next-intl"
import { Button } from "../ui/button"
import Link from "next/link"
import { noLoginPlanUrl } from "@/lib/constanta/route"
import { useToast } from "@/hooks/use-toast"
import PopUpBackground from "@/public/pop-up-background.jpeg"
import Image from "next/image"
import { X, Gift } from "lucide-react"

const DISCOUNT_CODE = "WELCOME25"
const DISCOUNT_PERCENTAGE = 25
const SCROLL_THRESHOLD = 0.3 // 30% scroll

export default function EmailDiscountPopup() {
  const t = useTranslations("seeker")
  const locale = useLocale()
  const [open, setOpen] = useState(false)
  const [isValidScroll, setIsValidScroll] = useState(false)
  const { scrollYProgress } = useScroll()
  const { seekers, hydrated } = useUserStore()
  const { toast } = useToast()
  const [isSubmittedEmail, setSubmittedEmail] = useState(false)

  // Popup triggering logic
  useMotionValueEvent(scrollYProgress, "change", (latest) => {
    if (latest > SCROLL_THRESHOLD) {
      setIsValidScroll(true)
    }
  })

  useEffect(() => {
    if (!hydrated) return
    if (seekers.email) return
    if (isValidScroll) {
      const timer = setTimeout(() =>
        setOpen(true), 500)
      return () => clearTimeout(timer)
    }
  }, [hydrated, seekers.email, isValidScroll])
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={<></>}
    dialogClassName="overflow-hidden max-w-md"
    drawerClassName="overflow-hidden"
  >
    {/* Header with Background Image */}
    <DialogHeaderWrapper className="relative h-48">
      {/* Close button */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-4 right-4 z-20 text-white hover:bg-white/20 rounded-full"
        onClick={() => setOpen(false)}
      >
        <X className="h-4 w-4" />
      </Button>

      {/* Background Image */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden rounded-t-lg -z-10">
        <Image src={PopUpBackground} alt="pop-up-background" className="object-cover" fill />
        <div className="absolute inset-0 bg-black/40"></div>
      </div>

      {/* Header content */}
      <div className="relative z-10 h-full flex flex-col justify-center items-center text-center px-6">
        <h3 className="text-xl font-bold text-white mb-2 leading-tight">
          {t('promotion.popUp.title')}
        </h3>
        <p className="text-white/90 text-sm">
          {t('promotion.popUp.description', { count: DISCOUNT_PERCENTAGE })}
        </p>
      </div>
    </DialogHeaderWrapper>

    {/* Enhanced Content */}
    <div className="space-y-6 p-6">
      {isSubmittedEmail ? (
        /* Success State */
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Gift className="h-8 w-8 text-green-600" />
          </div>

          <h4 className="text-lg font-bold text-seekers-text">
            Your Discount Code is Ready!
          </h4>

          <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-4">
            <p className="text-sm text-gray-600 mb-2">Your exclusive discount code:</p>
            <div className="bg-gradient-to-r from-teal-500 to-emerald-500 text-white px-4 py-2 rounded-lg font-mono text-lg font-bold tracking-wider">
              {DISCOUNT_CODE}
            </div>
          </div>

          <p className="text-sm text-seekers-text-light">
            {t.rich('promotion.popUp.couponCodeDescription', {
              code: () => <span className="font-bold text-teal-600">{DISCOUNT_CODE}</span>
            })}
          </p>

          <Button
            asChild
            className="w-full bg-gradient-to-r from-teal-600 to-emerald-600 hover:from-teal-700 hover:to-emerald-700 text-white font-semibold py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
            onClick={() => {
              navigator.clipboard.writeText(DISCOUNT_CODE)
              toast({
                title: t("misc.copy.successCopyContent", { content: t('misc.promoCode') })
              })
            }}
          >
            <Link href={noLoginPlanUrl} hrefLang={locale}>
              {t('cta.useDiscountCode')}
            </Link>
          </Button>
        </div>
      ) : (
        /* Email Capture State */
        <div className="space-y-4">
          {/* Email Form */}
          <EmailInputDiscountForm setIsSubmitted={setSubmittedEmail} />

          {/* Alternative Actions */}
          <div className="text-center space-y-2">
            <Button
              variant="link"
              className="text-sm text-gray-500 hover:text-gray-700"
              onClick={() => setOpen(false)}
            >
              {t('misc.maybeLater')}
            </Button>

            <p className="text-xs text-center text-seekers-text-light leading-relaxed">
              {t.rich('promotion.popUp.termsAndCondition', {
                term: (chunk) => <span className="font-bold text-seekers-text underline cursor-pointer">{chunk}</span>,
                privacy: (chunk) => <span className="font-bold text-seekers-text underline cursor-pointer">{chunk}</span>
              })}
            </p>
          </div>
        </div>
      )}
    </div>
  </DialogWrapper>
}