"use client"

import { useEffect, useState } from "react"
import DialogWrapper from "../dialog-wrapper/dialog-wrapper"
import DialogHeaderWrapper from "../dialog-wrapper/dialog-header-wrapper"
import EmailInputDiscountForm from "./email-discount-pop-up/email-input-discount.form"
import { useMotionValueEvent, useScroll } from "framer-motion"
import { useUserStore } from "@/stores/user.store"
import { useLocale, useTranslations } from "next-intl"
import { Button } from "../ui/button"
import Link from "next/link"
import { noLoginPlanUrl } from "@/lib/constanta/route"
import { useToast } from "@/hooks/use-toast"
import PopUpBackground from "@/public/pop-up-background.jpeg"
import Image from "next/image"
import { X, Gift } from "lucide-react"

const DISCOUNT_CODE = "WELCOME25"
const DISCOUNT_PERCENTAGE = 25
const SCROLL_THRESHOLD = 0.3 // 30% scroll

export default function EmailDiscountPopup() {
  const t = useTranslations("seeker")
  const locale = useLocale()
  const [open, setOpen] = useState(false)
  const [isValidScroll, setIsValidScroll] = useState(false)
  const { scrollYProgress } = useScroll()
  const { seekers, hydrated } = useUserStore()
  const { toast } = useToast()
  const [isSubmittedEmail, setSubmittedEmail] = useState(false)

  // Popup triggering logic
  useMotionValueEvent(scrollYProgress, "change", (latest) => {
    if (latest > SCROLL_THRESHOLD) {
      setIsValidScroll(true)
    }
  })

  useEffect(() => {
    if (!hydrated) return
    if (seekers.email) return
    if (isValidScroll) {
      const timer = setTimeout(() =>
        setOpen(true), 500)
      return () => clearTimeout(timer)
    }
  }, [hydrated, seekers.email, isValidScroll])
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={<></>}
    dialogClassName="overflow-hidden max-w-[1400px] w-[95vw] h-[700px] mx-2 p-0"
    drawerClassName="overflow-hidden"
  >
    {/* Close button */}
    <Button
      variant="ghost"
      size="icon"
      className="absolute top-6 right-6 z-30 text-gray-600 hover:bg-gray-100 rounded-full w-12 h-12"
      onClick={() => setOpen(false)}
    >
      <X className="h-6 w-6" />
    </Button>

    {/* Responsive layout - stacked on mobile, side-by-side on desktop */}
    <div className="flex flex-row h-[700px] w-full">
      {/* Image section */}
      <div className="w-full lg:w-3/5 h-[400px] lg:h-full relative">
        <Image
          src={PopUpBackground}
          alt="Bali Property"
          className="object-cover w-full h-full"
          fill
        />
      </div>

      {/* Content section */}
      <div className="w-full lg:w-2/5 p-8 md:p-12 lg:p-16 xl:p-20 bg-white flex flex-col justify-center min-h-[500px]">
        {isSubmittedEmail ? (
          /* Success State */
          <div className="space-y-8">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
              <Gift className="h-8 w-8 text-green-600" />
            </div>

            <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Your Discount Code is Ready!
            </h3>

            <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <p className="text-base md:text-lg text-gray-600 mb-3">Your exclusive discount code:</p>
              <div className="bg-seekers-primary text-white px-6 py-3 rounded-lg font-mono text-xl md:text-2xl font-bold tracking-wider">
                {DISCOUNT_CODE}
              </div>
            </div>

            <p className="text-base md:text-lg text-gray-600">
              {t.rich('promotion.popUp.couponCodeDescription', {
                code: () => <span className="font-bold text-seekers-primary">{DISCOUNT_CODE}</span>
              })}
            </p>

            <Button
              asChild
              className="w-full bg-gray-900 hover:bg-gray-800 text-white"
              onClick={() => {
                navigator.clipboard.writeText(DISCOUNT_CODE)
                toast({
                  title: t("misc.copy.successCopyContent", { content: t('misc.promoCode') })
                })
              }}
            >
              <Link href={noLoginPlanUrl} hrefLang={locale}>
                {t('cta.useDiscountCode')}
              </Link>
            </Button>
          </div>
        ) : (
          /* Email Capture State */
          <div className="space-y-6">
            <div>
              <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                {t('promotion.popUp.title')}
              </h3>
              <p className="text-base md:text-lg lg:text-xl text-gray-600 leading-relaxed">
                {t('promotion.popUp.description', { count: DISCOUNT_PERCENTAGE })}
              </p>
            </div>

            <EmailInputDiscountForm setIsSubmitted={setSubmittedEmail} />

            <div className="text-center">
              <Button
                variant="link"
                className="text-base text-gray-500 hover:text-gray-700"
                onClick={() => setOpen(false)}
              >
                {t('misc.maybeLater')}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  </DialogWrapper>
}