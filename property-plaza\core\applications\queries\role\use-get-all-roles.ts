import { getAllRolesService } from "@/core/infrastructures/role/service";
import { BasePaginationRequest } from "@/types/base";
import { useQuery } from "@tanstack/react-query";

export const ALL_ROLES_QUERY_KEY = "all-roles"
export function useGetAllRoles(data:BasePaginationRequest){
  const {
    page,
    per_page,
    search
  } = data
  const query = useQuery({
    queryKey: [ALL_ROLES_QUERY_KEY,page,per_page,search],
    queryFn: async () => {
      const data:BasePaginationRequest = {
        page,per_page,search
      } 
      const response = await getAllRolesService(data)
      return response
    },
    retry: 0
  },
)
  return query
}