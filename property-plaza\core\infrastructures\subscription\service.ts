import { errorHandling } from "../utils/error-handling";
import { ssrGetAllSubscriptionPackages } from "./api";
import { SubscriptionPackagesDto } from "./dto";
import { transfromSubscriptionPackages } from "./transform";

export async function getAllSubscriptionPackagesService() {
  try {
    const result =
      await ssrGetAllSubscriptionPackages<SubscriptionPackagesDto[]>();
    const data = result.data;
    const subscriptionPackages = transfromSubscriptionPackages(data || []);

    return {
      data: subscriptionPackages,
    };
  } catch (e: any) {
    const error = errorHandling(e);
    return { error };
  }
}
