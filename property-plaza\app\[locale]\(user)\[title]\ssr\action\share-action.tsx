import { Button } from "@/components/ui/button";
import ShareDialog from "../../../share-dialog";
import { ShareIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { headers } from 'next/headers';


export default function ShareAction() {
  const t = useTranslations("seeker")
  return <ShareDialog
    trigger={
      <>
        <Button
          variant={"ghost"}
          className="shadow-none max-md:!h-6 max-md:!w-6 rounded-full 
          text-seekers-text border-seekers-text-lighter md:px-3 md:py-2 md:w-fit md:h-fit md:border md:border-seekers-text-lighter"
          size={"sm"}>
          <ShareIcon className="max-md:!w-4 max-md:!h-4" />
          <span className="max-md:hidden">
            {t('cta.share')}
          </span>
        </Button>
      </>
    }
  />
}