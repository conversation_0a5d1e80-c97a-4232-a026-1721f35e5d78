import { create } from "zustand"
import { persist } from "zustand/middleware"

import Cookies from 'js-cookie';
export interface SeekersSettings {
  isLoading?: boolean,
  setIsLoading: (val: boolean) => void
  currency: string,
  setCurrency: (val: string) => void
}

export const useSeekersSettingsStore = create<SeekersSettings>()(
  persist(
    set => ({
      currency: "IDR",
      setCurrency: (val: string) => set(({ currency: val })),
      isLoading: true,
      setIsLoading: (val: boolean) => set(({ isLoading: val }))

    }),
    {
      name: "seekers-settings",
      storage: {
        // Custom storage using js-cookie
        getItem: (name) => {
          const cookieValue = Cookies.get(name);
          return cookieValue ? JSON.parse(cookieValue) : undefined;
        },
        setItem: (name, value) => {
          Cookies.set(name, JSON.stringify(value), { expires: 7, path: '/' }); // <PERSON><PERSON> expires in 7 days
        },
        removeItem: (name) => {
          Cookies.remove(name);
        },
      },
      onRehydrateStorage: () => state => {
        if (state) {
          state.setIsLoading(false)
        }
      }
    }
  )
)