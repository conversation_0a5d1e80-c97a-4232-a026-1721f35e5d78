"use client"

import { formatCurrency } from "@/lib/utils"
import { useSeekersSettingsStore } from "@/stores/seekers-settings.store"
import { useLocale } from "next-intl"
import { useEffect, useState } from "react"

export default function FormatPrice({ price, currency_ = "EUR", locale_ = "EN", conversions }: { price: number, currency_: string, locale_: string, conversions: { [key: string]: number } }) {
  const { currency: currencyStored, isLoading } = useSeekersSettingsStore()
  const [currency, setCurrency] = useState(currency_)
  const locale = useLocale()
  useEffect(() => {
    if (isLoading) return
    setCurrency(currencyStored)
  }, [currencyStored, isLoading])
  return <p className="font-bold max-md:text-base text-2xl 2xl:text-3xl text-end">{formatCurrency((price * (conversions[currency.toUpperCase()] || 1)) || 0, currency, locale)}</p>

}