import {
  MessageText,
  Participant,
  MessageItem,
} from "@/core/domain/messages/messages";
import { MessagingLayout } from "@/types/base";
import moment from "moment";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

interface MessagingState {
  currentLayout: MessagingLayout;
  setlayout: (layout: MessagingLayout) => void;
  roomId?: string;
  setRoomId: (roomId?: string) => void;
  chatDetail: MessageText[];
  setchatDetail: (data: MessageText[]) => void;
  updatechatDetail: (message: MessageText) => void;
  participant?: Participant;
  setParticipant: (participant?: Participant) => void;
  allChat: MessageItem[];
  setAllChat: (data: MessageItem[]) => void;
  updateSpecificAllChat: (
    data: MessageItem | MessageText,
    isNewChat?: boolean,
    roomId?: string
  ) => void;
  updateParticipantStatus: (status: string) => void;
}

export const useMessagingStore = create<MessagingState>()(
  persist(
    (set) => ({
      currentLayout: "list",
      setlayout: (layout) => set({ currentLayout: layout }),
      roomId: undefined,
      setRoomId: (roomId) => set({ roomId }),
      chatDetail: [],
      setchatDetail: (data) => set({ chatDetail: data }),
      updatechatDetail: (message) =>
        set((state) => {
          const lenChat = state.chatDetail.length;
          if (state.chatDetail[lenChat - 1].id == message.id) return {};
          const data = [...state.chatDetail, message];
          return { chatDetail: data };
        }),
      participant: undefined,
      setParticipant: (participant) => set({ participant }),
      allChat: [],
      setAllChat: (allChat) => set({ allChat }),
      updateSpecificAllChat: (chat, isNewChat, code) =>
        set(({ allChat }) => {
          const isMessageItem = (chat: any): chat is MessageItem =>
            "roomId" in chat;
          const isMessageText = (chat: any): chat is MessageText =>
            "id" in chat;
          const sortingMessages = (messages: MessageItem[]) =>
            messages.sort(
              (a, b) =>
                moment(b.lastMessages.createdAt).unix() -
                moment(a.lastMessages.createdAt).unix()
            );
          // Adding a new chat if it's a new chat
          if (isNewChat) {
            const newAllChat = [...allChat, chat as MessageItem];
            return { allChat: sortingMessages(newAllChat) };
          }

          if (code) {
            const specificIndex = allChat.findIndex(
              (item) => item.code === code
            );
            if (isMessageItem(chat)) {
              if (specificIndex < 0) {
                // Add new chat if not found
                const newAllChat = [...allChat, chat];
                return { allChat: sortingMessages(newAllChat) };
              } else {
                // Update existing chat
                const newAllChat = [...allChat];
                newAllChat[specificIndex] = chat;
                return { allChat: sortingMessages(newAllChat) };
              }
            }
            if (isMessageText(chat)) {
              if (specificIndex >= 0) {
                // Update the lastMessages field for the existing MessageItem
                const newAllChat = allChat.map((item, index) =>
                  index === specificIndex
                    ? { ...item, lastMessages: chat }
                    : item
                );
                return { allChat: sortingMessages(newAllChat) };
              } else {
                return { allChat };
              }
            }
          }
          // Case 2: Handling MessageItem type
          if (isMessageItem(chat)) {
            const specificIndex = allChat.findIndex(
              (item) => item.code === chat.code
            );

            if (specificIndex < 0) {
              // Add new chat if not found
              const newAllChat = [...allChat, chat].sort(
                (a, b) =>
                  moment(b.lastMessages.createdAt).unix() -
                  moment(a.lastMessages.createdAt).unix()
              );
              return { allChat: sortingMessages(newAllChat) };
            } else {
              // Update existing chat
              const newAllChat = [...allChat];
              newAllChat[specificIndex] = chat;
              return { allChat: sortingMessages(newAllChat) };
            }
          }

          // Case 3: Handling MessageText type
          if (isMessageText(chat)) {
            const specificIndex = allChat.findIndex((item) => {
              return item.code === chat.code;
            });

            if (specificIndex >= 0) {
              // Update the lastMessages field for the existing MessageItem
              const newAllChat = allChat.map((item, index) =>
                index === specificIndex ? { ...item, lastMessages: chat } : item
              );
              return { allChat: sortingMessages(newAllChat) };
            }
          }

          return { allChat };
        }),
      updateParticipantStatus: (val) =>
        set((state) => {
          if (!state.participant) return {};
          return {
            participant: {
              ...state.participant,
              status: val,
            },
          };
        }),
    }),
    {
      name: "messagingLayout",
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);
