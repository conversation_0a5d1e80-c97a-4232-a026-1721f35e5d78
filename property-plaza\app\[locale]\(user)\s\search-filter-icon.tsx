import { cn } from "@/lib/utils";
import React, { ComponentProps } from "react";

interface SearchFilterIconProps extends ComponentProps<"div"> {
  icon: React.ReactNode
  isActive?: boolean,
  title: string
}
export default function SearchFilterIcon({ icon, title, isActive, ...rest }: SearchFilterIconProps) {
  return <div className={cn("p-2 rounded-md cursor-pointer hover:bg-accent flex flex-col items-center justify-center gap-2", isActive ? "border-b-2 border-primary text-primary rounded-b-none" : "text-neutral-500", rest.className)} {...rest}>
    {icon}
    <p className={cn("text-xs", isActive ? "font-semibold" : "")}>
      {title}
    </p>
  </div>
}