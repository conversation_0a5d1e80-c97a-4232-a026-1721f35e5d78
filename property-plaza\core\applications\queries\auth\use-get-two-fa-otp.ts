import { TwoFactorAuthenticationCodeDto } from "@/core/infrastructures/auth/dto";
import { getTwoFactorAuthenticationCodeService } from "@/core/infrastructures/auth/service";
import { useQuery } from "@tanstack/react-query";

export const TWO_FA_OTP_QUERY_KEY = "two-fa-otp";
export function useGetTwoFactorAuthenticationCode(
  data: TwoFactorAuthenticationCodeDto,
  isEnable: boolean = false
) {
  const query = useQuery({
    queryKey: [TWO_FA_OTP_QUERY_KEY, data],
    queryFn: async () => await getTwoFactorAuthenticationCodeService(data),
    retry: 0,
    enabled: isEnable,
  });
  return query;
}
