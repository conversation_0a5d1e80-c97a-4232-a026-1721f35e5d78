"use client"

import { AUTH_BUTTON_ID, SUBSCRIPTION_BUTTON_ID } from "../utils/use-image-gallery";
import dynamic from "next/dynamic";

const SubscribeDialog = dynamic(() => import("@/components/subscribe/subscribe-dialog"), { ssr: false })
const SeekerAuthDialog = dynamic(() => import("../../../(auth)/seekers-auth-dialog"), { ssr: false })
export default function PopUpContent() {
  return <>
    <SeekerAuthDialog customTrigger={<button type="button" id={AUTH_BUTTON_ID} className="hidden" />} />
    <SubscribeDialog trigger={<button type="button" id={SUBSCRIPTION_BUTTON_ID} className="hidden" />} />
  </>
}