import { getFilterParameterSeekersService } from "@/core/infrastructures/listing/service"
import { useQuery } from "@tanstack/react-query"

export const FILTER_PARAMETER_LISTING_QUERY_KEY = "filter-parameter-listing"

export function useGetFilterParameter(){
  const query = useQuery({
    queryKey: [FILTER_PARAMETER_LISTING_QUERY_KEY],
    queryFn: async () => await getFilterParameterSeekersService()
  })
  return query
}