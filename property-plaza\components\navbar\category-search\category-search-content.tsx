import { DropdownMenuCheckboxItem } from "@/components/ui/dropdown-menu"
import useSeekersSearch from "@/hooks/use-seekers-search"
import { useTranslations } from "next-intl"
import ListingCategoryIcon from "@/app/[locale]/(user)/s/listing-category-icon"

export default function TypeSearchContent() {
  const t = useTranslations()
  const { handleSetType, seekersSearch, propertyType } = useSeekersSearch()
  return <>
    {propertyType.map(item =>
      <DropdownMenuCheckboxItem
        checkboxPosition="start"
        className="
          hover:bg-seekers-background 
          gap-2 text-xs 
          text-seekers-text-light 
          data-[state='checked']:font-medium 
          data-[state='checked']:text-seekers-text
          flex flex-col
          md:w-28 p-4
          relative
          border
          "
        key={item.id}
        checked={seekersSearch.propertyType.includes(item.value)}
        onClick={(e) => {
          e.preventDefault()
          e.stopPropagation()
          handleSetType(item.value)
        }}
        data-inside-dropdown

      >
        <ListingCategoryIcon category={item.value} className="!w-6 !h-6" />
        <span className="text-center">
          {item.content}
        </span>
      </DropdownMenuCheckboxItem>)}
  </>
}