import { SeekerListingFilterType } from "@/core/domain/listing/listing-seekers";
import { ReactNode } from "react";
import { FieldValues, Path, UseFormReturn } from "react-hook-form";

export type User = "user";
export type AccountManager = "account-manager";
export type Owner = "OWNER";
export type Seeker = "SEEKER";
export type QualityControl = "quality-control";
export type SuperAdmin = "super-admin";
export type Role = Owner | Seeker;

export type MessagingLayout = "list" | "detail-user" | "detail-chat";

export interface BaseResponse<T> {
  data: T;
  message: string;
  status_code: number;
}
export interface BaseLayout {
  children: React.ReactNode;
}

export interface BaseInputForm<T extends FieldValues> {
  form: UseFormReturn<T>;
  name: Path<T>;
}
export type BaseCurrency = "IDR" | "USD";
export interface BaseSelectInputValue<T> {
  id: string;
  value: T;
  content: ReactNode;
}

export interface BaseCheckboxFormInput<T extends FieldValues> {
  id: string;
  label: string;
  name: Path<T>;
  description?: string;
}

export interface BaseItemList {
  id: string;
  key: string;
  value: string;
}

export interface BasePaginationRequest {
  page: number;
  per_page: number;
  search: string;
}

export interface BaseDialog {
  open: boolean;
  setOpen: (open: boolean) => void;
  dialogClassName?: string;
  dialogOverlayClassName?: string;
  drawerClassName?: string;
}

export interface BaseProps {
  params: { query: string };
  searchParams: { [key: string]: string | string[] | undefined };
}

export interface BaseMetadataProps<T> {
  params: T;
  searchParams: { [key: string]: string | string[] | undefined };
}
