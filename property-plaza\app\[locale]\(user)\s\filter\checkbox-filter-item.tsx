import { cn } from "@/lib/utils";
import { BaseSelectInputValue } from "@/types/base";
import { ComponentProps } from "react";

interface CheckboxFilterItemProps extends ComponentProps<"div"> {
  item: BaseSelectInputValue<any>,
  setValue: (val: string) => void,
  isActive?: boolean
}
export default function CheckboxFilterItem({ item, setValue, isActive, ...rest }: CheckboxFilterItemProps) {
  return <div
    {...rest}
    className={
      cn("px-4 h-10 w-fit hover:bg-accent flex items-center cursor-pointer justify-start rounded-full ",
        (isActive ? "bg-seekers-primary text-white hover:bg-seekers-primary-light hover:border-seekers-primary-light border border-seekers-primary" : "text-seekers-text-light border border-seekers-text-lighter")
        , rest.className
      )
    }
    onClick={() => setValue(item.value)}
  >

    {item.content}
  </div>
}