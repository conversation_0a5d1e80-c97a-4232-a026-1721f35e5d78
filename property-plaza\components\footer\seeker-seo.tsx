import { getHomepageSeoContent } from "@/core/services/sanity/services";
import SeekersSeoContent from "./seekers-seo-content";
import { cookies } from "next/headers";

export default async function SeekerSeo() {
  const cookiesStore = cookies()
  const locale = cookiesStore.get('NEXT_LOCALE')?.value
  const seoContent = await getHomepageSeoContent(locale?.toLowerCase() || "en")

  return <SeekersSeoContent content={seoContent[0]} />
} 