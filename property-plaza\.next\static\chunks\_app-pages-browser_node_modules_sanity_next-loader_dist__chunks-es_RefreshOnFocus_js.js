"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_sanity_next-loader_dist__chunks-es_RefreshOnFocus_js"],{

/***/ "(app-pages-browser)/./node_modules/@sanity/next-loader/dist/_chunks-es/RefreshOnFocus.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@sanity/next-loader/dist/_chunks-es/RefreshOnFocus.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RefreshOnFocus; }\n/* harmony export */ });\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation.js */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\nconst focusThrottleInterval = 5e3;\nfunction RefreshOnFocus() {\n  const router = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    const controller = new AbortController();\n    let nextFocusRevalidatedAt = 0;\n    const callback = () => {\n      const now = Date.now();\n      now > nextFocusRevalidatedAt && document.visibilityState !== \"hidden\" && (router.refresh(), nextFocusRevalidatedAt = now + focusThrottleInterval);\n    }, { signal } = controller;\n    return document.addEventListener(\"visibilitychange\", callback, { passive: !0, signal }), window.addEventListener(\"focus\", callback, { passive: !0, signal }), () => controller.abort();\n  }, [router]), null;\n}\nRefreshOnFocus.displayName = \"RefreshOnFocus\";\n\n//# sourceMappingURL=RefreshOnFocus.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FuaXR5L25leHQtbG9hZGVyL2Rpc3QvX2NodW5rcy1lcy9SZWZyZXNoT25Gb2N1cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0M7QUFDYjtBQUNsQztBQUNBO0FBQ0EsaUJBQWlCLDZEQUFTO0FBQzFCLFNBQVMsZ0RBQVM7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUssSUFBSSxTQUFTO0FBQ2xCLHFFQUFxRSxxQkFBcUIsZ0RBQWdELHFCQUFxQjtBQUMvSixHQUFHO0FBQ0g7QUFDQTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL0BzYW5pdHkvbmV4dC1sb2FkZXIvZGlzdC9fY2h1bmtzLWVzL1JlZnJlc2hPbkZvY3VzLmpzPzA4ZWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvbi5qc1wiO1xuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG5jb25zdCBmb2N1c1Rocm90dGxlSW50ZXJ2YWwgPSA1ZTM7XG5mdW5jdGlvbiBSZWZyZXNoT25Gb2N1cygpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIHJldHVybiB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgbGV0IG5leHRGb2N1c1JldmFsaWRhdGVkQXQgPSAwO1xuICAgIGNvbnN0IGNhbGxiYWNrID0gKCkgPT4ge1xuICAgICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcbiAgICAgIG5vdyA+IG5leHRGb2N1c1JldmFsaWRhdGVkQXQgJiYgZG9jdW1lbnQudmlzaWJpbGl0eVN0YXRlICE9PSBcImhpZGRlblwiICYmIChyb3V0ZXIucmVmcmVzaCgpLCBuZXh0Rm9jdXNSZXZhbGlkYXRlZEF0ID0gbm93ICsgZm9jdXNUaHJvdHRsZUludGVydmFsKTtcbiAgICB9LCB7IHNpZ25hbCB9ID0gY29udHJvbGxlcjtcbiAgICByZXR1cm4gZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihcInZpc2liaWxpdHljaGFuZ2VcIiwgY2FsbGJhY2ssIHsgcGFzc2l2ZTogITAsIHNpZ25hbCB9KSwgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJmb2N1c1wiLCBjYWxsYmFjaywgeyBwYXNzaXZlOiAhMCwgc2lnbmFsIH0pLCAoKSA9PiBjb250cm9sbGVyLmFib3J0KCk7XG4gIH0sIFtyb3V0ZXJdKSwgbnVsbDtcbn1cblJlZnJlc2hPbkZvY3VzLmRpc3BsYXlOYW1lID0gXCJSZWZyZXNoT25Gb2N1c1wiO1xuZXhwb3J0IHtcbiAgUmVmcmVzaE9uRm9jdXMgYXMgZGVmYXVsdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVJlZnJlc2hPbkZvY3VzLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@sanity/next-loader/dist/_chunks-es/RefreshOnFocus.js\n"));

/***/ })

}]);