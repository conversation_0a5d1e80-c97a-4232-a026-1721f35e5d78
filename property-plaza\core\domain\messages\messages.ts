import _ from "lodash";

export interface MessageItem {
  code: string;
  category: string;
  roomId: string;
  updatedAt: string;
  lastMessages: MessageText;
  allMessages?: MessageText[];
  participant?: Participant;
}

export interface Participant {
  email: string;
  fullName: string;
  image?: string;
  phoneNumber: string;
  id: string;
  category: string;
  status: string;
  property?: {
    title?: string;
    image?: string;
    id?: string;
  };
  middleman?: {
    id?: string;
    name?: string;
  };
  moreProperty?: {
    title?: string;
    image?: string;
    id?: string;
    address?: string;
    price?: string;
  }[];
}

export interface MessageText {
  isRead: boolean;
  isSent: boolean;
  displayName: string;
  displayAs: string;
  createdAt: string;
  text: string;
  id: string;
  code: string;
  status?: string;
}

export const messageCategory = {
  accountManager: "LISTING",
  customerSupport: "CUSTOMER_SUPPORT",
  seekers: "SEEKER_OWNER",
};

export const messageSeekerCategory = {
  customerSupport: "CUSTOMER_SUPPORT",
  owner: "SEEKER_OWNER",
};

export const seekersMessageCategory = {
  customerSupport: "CUSTOMER_SUPPORT",
  owner: "OWNER",
};

export const messagingStatus = {
  waitingResponse: "WAITING_FOR_RESPONSE",
  endedResponse: "CONVERSATION_ENDED",
};

export const isCustomerSupportChat = (category: string): boolean =>
  category === messageCategory.customerSupport;

export const isAccountManagerChat = (category: string): boolean =>
  category === messageCategory.accountManager;

export const isSeekerWaitingResponse = (status: string) =>
  status == messagingStatus.waitingResponse ||
  status == messagingStatus.endedResponse;

export const isChatEnded = (status: string) =>
  status == messagingStatus.endedResponse;
