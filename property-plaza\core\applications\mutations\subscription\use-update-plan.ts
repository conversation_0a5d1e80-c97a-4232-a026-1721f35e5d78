import { postUpdateSubscriptionPackage } from "@/core/infrastructures/subscription/api";
import {
  PostSubscriptionDto,
  PostSubscriptionResponse,
} from "@/core/infrastructures/subscription/dto";
import { useMutation } from "@tanstack/react-query";

export function useUpdateSubscribePlan() {
  const mutation = useMutation({
    mutationFn: async (data: PostSubscriptionDto) =>
      await postUpdateSubscriptionPackage(data),
  });
  return mutation;
}
