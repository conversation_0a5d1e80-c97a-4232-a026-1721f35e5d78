/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/progress-stream";
exports.ids = ["vendor-chunks/progress-stream"];
exports.modules = {

/***/ "(ssr)/./node_modules/progress-stream/index.js":
/*!***********************************************!*\
  !*** ./node_modules/progress-stream/index.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var through = __webpack_require__(/*! through2 */ \"(ssr)/./node_modules/through2/through2.js\");\nvar speedometer = __webpack_require__(/*! speedometer */ \"(ssr)/./node_modules/speedometer/index.js\");\n\nmodule.exports = function(options, onprogress) {\n\tif (typeof options === 'function') return module.exports(null, options);\n\toptions = options || {};\n\n\tvar length = options.length || 0;\n\tvar time = options.time || 0;\n\tvar drain = options.drain || false;\n\tvar transferred = options.transferred || 0;\n\tvar nextUpdate = Date.now()+time;\n\tvar delta = 0;\n\tvar speed = speedometer(options.speed || 5000);\n\tvar startTime = Date.now();\n\n\tvar update = {\n\t\tpercentage: 0,\n\t\ttransferred: transferred,\n\t\tlength: length,\n\t\tremaining: length,\n\t\teta: 0,\n\t\truntime: 0\n\t};\n\n\tvar emit = function(ended) {\n\t\tupdate.delta = delta;\n\t\tupdate.percentage = ended ? 100 : (length ? transferred/length*100 : 0);\n\t\tupdate.speed = speed(delta);\n\t\tupdate.eta = Math.round(update.remaining / update.speed);\n\t\tupdate.runtime = parseInt((Date.now() - startTime)/1000);\n\t\tnextUpdate = Date.now()+time;\n\n\t\tdelta = 0;\n\n\t\ttr.emit('progress', update);\n\t};\n\tvar write = function(chunk, enc, callback) {\n\t\tvar len = options.objectMode ? 1 : chunk.length;\n\t\ttransferred += len;\n\t\tdelta += len;\n\t\tupdate.transferred = transferred;\n\t\tupdate.remaining = length >= transferred ? length - transferred : 0;\n\n\t\tif (Date.now() >= nextUpdate) emit(false);\n\t\tcallback(null, chunk);\n\t};\n\tvar end = function(callback) {\n\t\temit(true);\n\t\tcallback();\n\t};\n\n\tvar tr = through(options.objectMode ? {objectMode:true, highWaterMark:16} : {}, write, end);\n\tvar onlength = function(newLength) {\n\t\tlength = newLength;\n\t\tupdate.length = length;\n\t\tupdate.remaining = length - update.transferred;\n\t\ttr.emit('length', length);\n\t};\n\t\n\t// Expose `onlength()` handler as `setLength()` to support custom use cases where length\n\t// is not known until after a few chunks have already been pumped, or is\n\t// calculated on the fly.\n\ttr.setLength = onlength;\n\t\n\ttr.on('pipe', function(stream) {\n\t\tif (typeof length === 'number') return;\n\t\t// Support http module\n\t\tif (stream.readable && !stream.writable && stream.headers) {\n\t\t\treturn onlength(parseInt(stream.headers['content-length'] || 0));\n\t\t}\n\n\t\t// Support streams with a length property\n\t\tif (typeof stream.length === 'number') {\n\t\t\treturn onlength(stream.length);\n\t\t}\n\n\t\t// Support request module\n\t\tstream.on('response', function(res) {\n\t\t\tif (!res || !res.headers) return;\n\t\t\tif (res.headers['content-encoding'] === 'gzip') return;\n\t\t\tif (res.headers['content-length']) {\n\t\t\t\treturn onlength(parseInt(res.headers['content-length']));\n\t\t\t}\n\t\t});\n\t});\n\n\tif (drain) tr.resume();\n\tif (onprogress) tr.on('progress', onprogress);\n\n\ttr.progress = function() {\n\t\tupdate.speed = speed(0);\n\t\tupdate.eta = Math.round(update.remaining / update.speed);\n\n\t\treturn update;\n\t};\n\treturn tr;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/progress-stream/index.js\n");

/***/ })

};
;