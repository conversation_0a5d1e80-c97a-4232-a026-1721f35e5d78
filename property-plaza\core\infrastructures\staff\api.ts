import { apiClient } from "@/core/client";
import { CreateStaffDto, UpdateStaffDto } from "./dto";
import { BasePaginationRequest } from "@/types/base";

export const postStaff = (data:CreateStaffDto) => apiClient.post("portal/staff",data)

export const getStaffs = (searchParam:BasePaginationRequest) => 
  apiClient.get(`portal/staff?page=${searchParam.page}&per_page=${searchParam.per_page}&search=${searchParam.search}`)

export const updateStaff = (id:string,data:UpdateStaffDto) => apiClient.put(`portal/staff/${id}`,data)

export const getDetailStaff = (id:string) => apiClient.get(`portal/staff/${id}`)