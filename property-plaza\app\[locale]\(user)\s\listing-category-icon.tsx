import { listingCategory, ListingCategory } from "@/core/domain/listing/listing-seekers";
import { cn } from "@/lib/utils";
import { Coffee, DollarSign, Home, Hotel, LandPlot, Palmtree, Presentation, School, ShoppingCart, Star } from "lucide-react";

export default function ListingCategoryIcon({ category, className }: { category: ListingCategory, className?: string }) {
  switch (category) {
    case listingCategory.villa:
    case listingCategory.villas:
      return <Palmtree className={cn("!w-6 !h-6", className)} />
    case listingCategory.apartment:
      return <Hotel className={cn("!w-6 !h-6", className)} />
    case listingCategory.homestay:
    case listingCategory.guestHouse:
    case listingCategory.rooms:
      return <Home className={cn("!w-6 !h-6", className)} />
    case listingCategory.ruko:
    case listingCategory.commercialSpace:
      return <DollarSign className={cn("!w-6 !h-6", className)} />
    case listingCategory.cafeOrRestaurants:
      return <Coffee className={cn("!w-6 !h-6", className)} />
    case listingCategory.offices:
      return <Presentation className={cn("!w-6 !h-6", className)} />
    case listingCategory.shops:
      return <ShoppingCart className={cn("!w-6 !h-6", className)} />
    case listingCategory.shellAndCore:
      return <School className={cn("!w-6 !h-6", className)} />
    case listingCategory.lands:
      return <LandPlot className={cn("!w-6 !h-6", className)} />
    default:
      return <Star className={cn("!w-6 !h-6", className)} />
  }
}