import { Search } from "lucide-react";
import { CardDescription, CardTitle } from "../ui/card";
import { SearchCardContentWrapper, SearchCardHeaderWrapper, SearchCardWrapper } from "./search-card-wrapper";
import { Input } from "../ui/input";
import { useTranslations } from "next-intl";
import useSeekersSearch from "@/hooks/use-seekers-search";
import { Checkbox } from "../ui/checkbox";
import { useEffect, useState } from "react";

export default function SeekerPropertyTypeSearch({ isActive, setActive, id }: { isActive?: boolean, setActive: (val: string) => void, id: string }) {
  const t = useTranslations("seeker")
  const { propertyType, handleSetType, seekersSearch } = useSeekersSearch()
  const [type, setType] = useState<string[]>(seekersSearch.propertyType)
  useEffect(() => {
    setType(seekersSearch.propertyType)
  }, [seekersSearch.propertyType])
  return <SearchCardWrapper setActive={setActive} id={id}>
    <SearchCardHeaderWrapper>
      <CardTitle>{t('navbar.search.propertyType')}</CardTitle>
      <CardDescription className="!mt-0">{t('navbar.search.flexibleLocation')}</CardDescription>
    </SearchCardHeaderWrapper>
    <SearchCardContentWrapper isActive={isActive}>
      <div className="pb-4">
        <div className="space-y-1">
          {propertyType.map(item => <div key={item.id} className="flex gap-2">
            <Checkbox
              checked={type.includes(item.value)}
              onCheckedChange={() => {
                handleSetType(item.value)
              }}
              className="data-[state=checked]:bg-seekers-primary data-[state=checked]:text-white border-seekers-primary"
            />
            <p className="text-xs">
              {item.content}
            </p>
          </div>)}
        </div>
      </div>
    </SearchCardContentWrapper>
  </SearchCardWrapper>
}