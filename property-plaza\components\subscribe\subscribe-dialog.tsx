"use client"
import DialogWrapper from "../dialog-wrapper/dialog-wrapper";
import { DialogFooter } from "../ui/dialog";
import DialogTitleWrapper from "../dialog-wrapper/dialog-title-wrapper";
import { Button } from "../ui/button";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import Link from "next/link";
import { plansUrl, noLoginPlanUrl } from "@/lib/constanta/route";
import DialogDescriptionWrapper from "../dialog-wrapper/dialog-description-wrapper";
import DialogHeaderWrapper from "../dialog-wrapper/dialog-header-wrapper";
import { useUserStore } from "@/stores/user.store";

export default function SubscribeDialog({ trigger }: { trigger: React.ReactNode }) {
  const [open, setOpen] = useState(false)
  const { email } = useUserStore(state => state.seekers)
  const t = useTranslations("seeker")
  const locale = useLocale()
  return <DialogWrapper openTrigger={trigger} open={open} setOpen={setOpen} dialogClassName="max-w-md">
    <DialogHeaderWrapper>
      <DialogTitleWrapper>{t('subscription.upgradeSubscription.title')}</DialogTitleWrapper>
      <DialogDescriptionWrapper className="text-seekers-text-light">{t('subscription.upgradeSubscription.description')}</DialogDescriptionWrapper>
    </DialogHeaderWrapper>
    <DialogFooter>
      <Button variant={"ghost"} onClick={() => setOpen(false)} >{t('cta.close')}</Button>
      <Button variant={"default-seekers"} asChild>
        <Link href={email ? plansUrl : noLoginPlanUrl} hrefLang={locale} >
          {t('cta.subscribe')}
        </Link>
      </Button>
    </DialogFooter>
  </DialogWrapper>
}