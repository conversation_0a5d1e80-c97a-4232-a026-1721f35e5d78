import { groq } from "next-sanity";
const postData = `{
_id,
  title,
  metadata,
  slug,
  tags,
  author->{
    _id,
    name,
    slug,
    image{asset -> {url}},
    bio
  },
  coverImage{
  asset->{url}},
  mainImage{
  asset->{url}},
  publishedAt,
  category -> {
  _id,
  title
  },
  body
}`;

export const postQuery = groq`*[_type == "post" && author != "hidden"] ${postData}`;
export const postQueryHomepage = groq`*[_type == "post" && author != "hidden"][0...2] ${postData}`; // use this on homepage

export const postQueryBySlug = groq`*[_type == "post" && slug.current == $slug][0]  ${postData}
  

`;

export const postQueryByTag = groq`*[_type == "post" && $slug in tags[]->slug.current] ${postData}`;

export const postQueryByAuthor = groq`*[_type == "post" && author->slug.current == $slug] ${postData}`;

export const postQueryByCategory = groq`*[_type == "post" && category->slug.current == $slug] ${postData}`;

// search by category and exclude id
export const morePostsBasedOnCategoryQuery = groq`
  *[_type == "post" && _id != $id] | order(date asc, _updatedAt asc) [0...4] 
    ${postData}
  `;

export const homepageSeoContent = groq`*[_type == "seoContent" && language == $language]{title,body}`;

export const TermsOfUseContent = groq`*[_type == "termsOfUse" && language == $language]{title,body}`;

export const privacyPolicyContent = groq`*[_type == "privacyPolicy" && language == $language]{title,body}`;

export const userDataDeletionContent = groq`*[_type == "userDataDeletion" && language == $language]{title,body}`;
