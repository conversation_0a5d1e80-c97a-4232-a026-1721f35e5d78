import { useTranslations } from "next-intl"
import { useState } from "react"
import { Button } from "../ui/button"
import { SeoContent } from "@/core/services/sanity/types"
import { PortableText } from "next-sanity"


export default function SeekerSeoArticleContent({ content }: { content: SeoContent }) {
  const t = useTranslations("seeker")
  const [isExpanded, setIsExpanded] = useState(false)

  return <div className="mt-8 border-t border-gray-200 pt-6">
    {isExpanded && (
      <article className="prose prose-big max-w-3xl text-seekers-text mb-4">
        <h1 className="text-3xl font-bold text-seekers-text">{content.title}</h1>
        <PortableText value={content.body}
          components={{
            block: {
              h1: ({ children }) => <h2 className="text-2xl font-semibold text-seekers-text mt-4">{children}</h2>,
              h2: ({ children }) => <h3 className="text-xl font-semibold mt-4">{children}</h3>,
              h3: ({ children }) => <h3 className="text-lg font-semibold mt-4">{children}</h3>,
              h4: ({ children }) => <h3 className="">{children}</h3>,
              normal: ({ children }) => <p className=" leading-relaxed mt-2">{children}</p>
            },
            list: {
              number: ({ children }) => <ol className="list-decimal list-inside mt-2">{children}</ol>,
              bullet: ({ children }) => <ul className="list-disc pl-4 mt-2">{children}</ul>
            }
          }} />
      </article>
    )}

    <Button
      variant="link"
      onClick={() => setIsExpanded(!isExpanded)}
      className="text-seekers-primary hover:text-seekers-primary/80"
    >
      {isExpanded ? t('misc.readLess') : t('misc.readMore')}
    </Button>
  </div>
}