import { useMutation } from "@tanstack/react-query";
import { ACCESS_TOKEN } from "@/lib/constanta/constant";
import { registerUser } from "@/core/infrastructures/user/api";
import { RegisterUserDto } from "@/core/infrastructures/user/dto";
import Cookies from "js-cookie";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import { accountUrl } from "@/lib/constanta/route";
import { useReCaptcha } from "next-recaptcha-v3";

export function useRegister(
  onSuccessRegister?: () => void,
  typeUser: "seekers" | "owner" = "owner"
) {
  const { toast } = useToast();
  const t = useTranslations("universal");
  const { executeRecaptcha } = useReCaptcha();

  const mutation = useMutation({
    mutationFn: async (data: RegisterUserDto) => {
      try {
        const token = await executeRecaptcha("form_submit");
        return registerUser(data, token);
      } catch (e: unknown) {
        console.log(e);
        return {
          data: null,
        };
      }
    },
    onSuccess: (response) => {
      const data = response.data;
      Cookies.set(ACCESS_TOKEN, data.data.access_token, { expires: 7 });
      onSuccessRegister?.();
      if (typeUser == "owner") {
        window.location.assign(accountUrl);
      } else {
        window.location.reload();
      }
    },
    onError: (error) => {
      const data: any = (error as any).response.data;
      toast({
        title: t("misc.foundError"),
        description: data.message,
        variant: "destructive",
      });
    },
  });
  return mutation;
}
