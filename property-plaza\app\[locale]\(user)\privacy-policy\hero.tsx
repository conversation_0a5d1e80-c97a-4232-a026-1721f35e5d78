
import { useTranslations } from "next-intl";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import DefaultLayoutContent from "@/components/seekers-content-layout/default-layout-content";

export default function HeroTermsOfUseSection() {
  const t = useTranslations("seeker")
  return (
    <MainContentLayout id="privacy-policy" className="mt-12">
      <DefaultLayoutContent
        title={t('privacyPolicy.title')}
      >

      </DefaultLayoutContent>
    </MainContentLayout>
  )
}