import { useEffect, useState } from "react";
import useSearchParamWrapper from "./use-search-param-wrapper";
import { useRouter } from "nextjs-toploader/app";

export const usePaginationRequest = (page_:number=1,perPage_:number=10) => {
  const {createMultipleQueryString,searchParams,generateQueryString,pathname, createQueryString} = useSearchParamWrapper();
  const page = searchParams.get("page") || "1"
  const perPage = searchParams.get("per_page") || "10"


  useEffect(() => {
    const page = searchParams.get("page") || page_
    const perPage = searchParams.get("per_page") || perPage_
    createMultipleQueryString([{name: "page", value: page.toString()}, {name: "per_page", value:perPage.toString()}])
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[])

  const setPageSearch = (value:number) => {
    createQueryString("page",value.toString())
  }

  const setPerPageSearch = (value:number) => {
    createQueryString("per_page",value.toString())

  }

  return {page,perPage, setPageSearch,setPerPageSearch}
}