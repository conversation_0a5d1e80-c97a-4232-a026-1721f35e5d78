"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useRequestResetPassword } from "@/core/applications/mutations/auth/use-request-reset-password"
import { RequestForgetPasswordDto } from "@/core/infrastructures/auth/dto"
import { useToast } from "@/hooks/use-toast"
import { useUserStore } from "@/stores/user.store"
import { Shield } from "lucide-react"
import { useTranslations } from "next-intl"

export default function ChangePassword() {
  const t = useTranslations("seeker")
  const requestResetPasswordMutation = useRequestResetPassword()
  const userEmail = useUserStore(state => state.seekers.email)
  const { toast } = useToast()

  const onSubmit = async () => {
    try {
      const data: RequestForgetPasswordDto = {
        email: userEmail
      }
      const result = await requestResetPasswordMutation.mutateAsync(data)
      toast({
        title: t('success.requestForgetPassword.title'),
        description: t('success.requestForgetPassword.description', { email: userEmail })
      })
    } catch (e) {
      toast({
        title: t('error.requestForgetPassword.title'),
      })
    }
  }

  return <Card>
    < CardHeader className="flex max-sm:flex-col max-sm:items-start max-sm:gap-4 max-sm:pb-4 flex-row  items-center justify-between space-y-0  pb-2" >
      < div className="space-y-1" >
        <CardTitle className="text-seekers-primary flex items-center">
          <Shield className="mr-2 h-4 w-4" />
          {t("settings.profile.security.password.title")}
        </CardTitle>
        <CardDescription>{t("settings.profile.security.password.description")}</CardDescription>
      </div >
      <Button
        size={"sm"}
        variant="outline"
        className="border-seekers-primary text-seekers-primary hover:text-seekers-primary hover:bg-seekers-primary/10"
        onClick={() => onSubmit()}
      >
        {t("cta.changePassword")}
      </Button>
    </CardHeader >
    <CardContent>
      <div className="text-sm text-muted-foreground">
        {t("setting.profile.security.password.lastChanged")} January 15, 2025
      </div>
    </CardContent>
  </Card >
}