import type { Marker } from '@googlemaps/markerclusterer';
import React, { useCallback } from 'react';
import { AdvancedMarker } from '@vis.gl/react-google-maps';
import { ListingListSeekers } from '@/core/domain/listing/listing-seekers';
import { cn } from '@/lib/utils';
import { useSeekersSearchMapUtil } from '@/stores/seekers-search-map-utils';
import ListingCategoryIcon from '../listing-category-icon';

export type TreeMarkerProps = {
  data: ListingListSeekers;
  onClick: (data: ListingListSeekers) => void;
  setMarkerRef: (marker: Marker | null, key: string) => void;
};

/**
 * Wrapper Component for an AdvancedMarker for a single data.
 */
export const ClusteredPinMap = (props: TreeMarkerProps) => {
  const { data, onClick, setMarkerRef } = props;
  const { focusedListing, setFocusedListing } = useSeekersSearchMapUtil()

  const handleClick = useCallback(() => {
    onClick(data)
    setFocusedListing(data.code)
  }, [onClick, data, setFocusedListing]);
  const ref = useCallback(
    (marker: google.maps.marker.AdvancedMarkerElement) =>
      setMarkerRef(marker, data.code),
    [setMarkerRef, data.code]
  );

  return (
    <AdvancedMarker position={{ lat: data.geolocation[0], lng: data.geolocation[1] }} ref={ref} onClick={handleClick}>
      <div className={cn(
        focusedListing == data.code ? "w-16 h-16 bg-seekers-text text-white" : "hover:w-10 hover:h-10 w-8 h-8 bg-white",
        "flex items-center justify-center py-3 rounded-full text-sm font-medium shadow-md border")}>
        <ListingCategoryIcon category={data.category || ""} className={focusedListing == data.code ? "" : "!w-4 !h-4 text-seekers-primary"} />
      </div>
    </AdvancedMarker>
  );
};