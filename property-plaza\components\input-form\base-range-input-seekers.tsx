import { cn } from '@/lib/utils'
import ReactSlider from 'react-slider'
interface BaseRangeInputProps {
  value: number[],
  onValueChange: (val: number[]) => void,
  min: number,
  max: number,
  className?: string,
  thumbClassName?: string,
  trackClassName?: string
}
export default function BaseRangeInputSeekers({ max, min, onValueChange, value, className, thumbClassName, trackClassName }: BaseRangeInputProps) {
  return <>
    <div className='w-full'>
      <ReactSlider
        className={cn("w-full  h-2 flex items-center rounded-full", className)}
        thumbClassName={cn("w-4 h-4 rounded-full shadow-md bg-white border", thumbClassName)}
        trackClassName={cn("track", trackClassName)}
        max={max}
        min={min}
        value={value}
        onChange={(val) => onValueChange(val)}
        pearling
        renderThumb={(props, state) => <div {...props}>{ }</div>}
        withTracks
        renderTrack={(props) =>
          <div {...props}
            className={
              cn(
                props.className, "h-2 rounded-full",
                props.className?.includes("track-1") && "bg-seekers-primary",
                props.className?.includes("track-0") && "bg-seekers-text-lighter/30",
                props.className?.includes("track-2") && "bg-seekers-text-lighter/30",

              )}
          ></div>}
      />
    </div>
  </>
}