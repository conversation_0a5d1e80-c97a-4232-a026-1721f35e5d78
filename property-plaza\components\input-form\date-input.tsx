import { FormControl, FormField } from '@/components/ui/form'
import { FieldValues } from 'react-hook-form'
import { Button } from '../ui/button'
import { useState } from 'react'
import { CalendarIcon } from 'lucide-react'
import { format } from "date-fns"
import { BaseInputForm } from '@/types/base'
import BaseInputLayout from './base-input'
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover'
import { cn } from '@/lib/utils'
import { Calendar } from '../ui/calendar'
interface DateInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  placeholder: string
  description?: string,
  disabled?: boolean
}
export default function DateInput<T extends FieldValues>({ form, label, name, placeholder, description, disabled }: DateInputProps<T>) {
  return <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <BaseInputLayout label={label} description={description}>
        <Popover>
          <PopoverTrigger asChild>
            <FormControl>
              <Button
                variant={"outline"}
                className={cn(
                  "w-full pl-3 text-left font-normal",
                  !field.value && "text-muted-foreground"
                )}
                disabled={field.disabled || disabled}
              >
                {field.value ? (
                  format(field.value, "PPP")
                ) : (
                  <span>{placeholder}</span>
                )}
                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </FormControl>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={field.value}
              onSelect={field.onChange}
              disabled={(date) =>
                date < new Date()
              }
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </BaseInputLayout>
    )}
  />
}