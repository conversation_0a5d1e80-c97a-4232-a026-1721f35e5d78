"use client"

import { useLocale } from "next-intl"
import { Switch } from "../ui/switch"
import { startTransition, useState } from "react"
import { useRouter, usePathname } from '@/lib/locale/navigations';
import useSearchParamWrapper from "@/hooks/use-search-param-wrapper";

export default function LocaleSwitcher() {
  const locale = useLocale()
  const router = useRouter();
  const pathname = usePathname();
  const [switchStatus, setSwitchStatus] = useState(locale.toLowerCase() == "id")

  const { generateQueryString } = useSearchParamWrapper()

  const changeLanguage = () => {
    const query = generateQueryString("", "")
    if (switchStatus) {
      startTransition(() => {
        router.replace(
          // @ts-expect-error -- TypeScript will validate that only known `params`
          // are used in combination with a given `pathname`. Since the two will
          // always match for the current route, we can skip runtime checks.
          pathname + "?" + query,
          { locale: "en" }
        );
      });
      setSwitchStatus(false)
    } else {
      startTransition(() => {
        router.replace(
          // @ts-expect-error -- TypeScript will validate that only known `params`
          // are used in combination with a given `pathname`. Since the two will
          // always match for the current route, we can skip runtime checks.
          pathname + "?" + query,
          { locale: "id" }
        );
      });
      setSwitchStatus(true)
    }
  }
  return <div className="flex gap-2 h-full items-center text-xs">
    <p>EN</p>
    <Switch
      checked={switchStatus}
      onCheckedChange={() => changeLanguage()}
    />
    <p>ID</p>
  </div>
}