import { Check, Pencil, X } from "lucide-react"
import { But<PERSON> } from "../ui/button"

interface ActionInsideFormProps {
  isFormDisabled?: boolean
  setDisabledStatus: (status?: boolean, isEdited?: boolean) => void

}
export default function ActionInsideForm({ setDisabledStatus, isFormDisabled }: ActionInsideFormProps) {
  return <>
    {
      isFormDisabled ?
        <Button variant={"ghost"} className=' rounded-none bg-neutral-lightest' onClick={() => setDisabledStatus(false, false)}>
          <Pencil className='w-4 h-4' />
        </Button>
        :
        <>
          <Button variant={"ghost"} className='rounded-none bg-neutral-lightest' onClick={() => {
            setDisabledStatus(true, false)
          }} >
            <X className='w-4 h-4' />
          </Button>
          <Button variant={"ghost"} className='rounded-none bg-neutral-lightest' onClick={() => setDisabledStatus(true, true)}>
            <Check className='w-4 h-4' />
          </Button>

        </>
    }
  </>
}