"use client"
import { use<PERSON>ocale, useTranslations } from "next-intl"
import { usePathname } from "next/navigation"
import { Sidebar, SidebarContent, SidebarGroup, SidebarGroupContent, SidebarGroupLabel } from "../ui/sidebar"
import { Heart, MessageSquare, Receipt, User } from "lucide-react"
import { SidebarLink } from "./sidebar-link"
import { billingUrl, favoriteUrl, notificationSettingUrl, plansUrl, profileUrl, securitySettingUrl, seekersMessageUrl } from "@/lib/constanta/route"

export default function SeekersSidebar() {
  const pathname = usePathname()
  const locale = useLocale()
  const t = useTranslations("seeker")

  return (
    <Sidebar collapsible="icon" className="sticky bottom-0 h-full z-0 overflow-hidden">
      <SidebarContent className="text-seekers-text mt-10">
        <SidebarGroup>
          <SidebarGroupLabel className="text-seekers-text">
            <User className="mr-2 h-4 w-4" />
            {t("setting.profile.title")}
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarLink href={profileUrl} active={pathname.includes(profileUrl)}>
              {t("setting.profile.personalInfo.title")}
            </SidebarLink>
            <SidebarLink
              href={notificationSettingUrl}
              active={pathname.includes(notificationSettingUrl)}
            >
              {t("setting.profile.notifications.title")}
            </SidebarLink>
            <SidebarLink href={securitySettingUrl} active={pathname.includes(securitySettingUrl)}>
              {t("setting.profile.security.title")}
            </SidebarLink>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel className="text-seekers-text">
            <Receipt className="mr-2 h-4 w-4" />
            {t("setting.subscriptionStatus.title")}
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarLink
              href={plansUrl}
              active={pathname.includes(plansUrl)}
            >
              {t("setting.subscriptionStatus.subscription.title")}
            </SidebarLink>
            <SidebarLink href={billingUrl} active={pathname.includes(billingUrl)}>
              {t("setting.subscriptionStatus.billing.title")}
            </SidebarLink>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel className="text-seekers-text">
            <Heart className="mr-2 h-4 w-4" />
            {t("setting.favorites.title")}
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarLink href={favoriteUrl} active={pathname.includes(favoriteUrl)}>
              {t("setting.favorites.savedItems.title")}
            </SidebarLink>
            {/* <SidebarLink href={`/${locale}/settings/sellers`} active={pathname === `/${locale}/settings/sellers`}>
              {t("setting.favorites.favoriteSellers.title")}
            </SidebarLink> */}
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>
            <MessageSquare className="mr-2 h-4 w-4" />
            {t("setting.messages.title")}
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarLink href={seekersMessageUrl} active={pathname.includes(seekersMessageUrl)}>
              {t("setting.messages.messages.title")}
            </SidebarLink>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}