import { postSubscriptionSignUp } from "@/core/infrastructures/subscription/api";
import { PostSubscriptionSignUpDto } from "@/core/infrastructures/subscription/dto";
import { useMutation } from "@tanstack/react-query";

export function useSubscriptionSignUp() {
  const mutation = useMutation({
    mutationFn: async (data: PostSubscriptionSignUpDto) =>
      await postSubscriptionSignUp(data),
  });
  return mutation;
}
