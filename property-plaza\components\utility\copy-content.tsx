"use client"
import { useToast } from "@/hooks/use-toast"
import { cn } from "@/lib/utils"
import { Copy } from "lucide-react"
import { useTranslations } from "next-intl"

export default function CopyContent({ content,
  iconClassName,
  containerClassName,
  toastContent }: {
    content: string,
    iconClassName?: string,
    containerClassName?: string,
    toastContent: string
  }) {
  const t = useTranslations()
  const { toast } = useToast()

  return <button className={cn(containerClassName)} onClick={e => {
    e.stopPropagation()
    navigator.clipboard.writeText(content)
    toast({
      title: t('misc.copy.successCopyContent', { content: toastContent }),

    })
  }}>
    <Copy className={cn("w-4 h-4", iconClassName)} />
  </button>
}