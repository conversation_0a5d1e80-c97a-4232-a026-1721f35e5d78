import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from "next/server";
import { MiddlewareFactory } from "./types";
import { matchesBaseRoute, removeLocaleFromUrl } from "@/lib/utils";
import {
  BASE_ADMIN_ROUTE,
  BASE_MIDDLEMAN_ROUTE,
  joinOwnerUrl,
} from "@/lib/constanta/route";

export const withDomain: MiddlewareFactory =
  (next: NextMiddleware) =>
  async (request: NextRequest, _next: NextFetchEvent) => {
    let nextResult = await next(request, _next);
    let response = NextResponse.next();
    const rawPathName = request.nextUrl.pathname;
    const pathName = removeLocaleFromUrl(rawPathName);
    if (
      pathName.startsWith("/api") ||
      pathName.startsWith("/icon.ico") ||
      pathName.startsWith("/sounds") ||
      pathName.startsWith("/sitemap") ||
      pathName.startsWith("/robots")
    ) {
      return nextResult;
    }

    const hasAdminRoute = matchesBaseRoute(
      [...BASE_ADMIN_ROUTE, ...BASE_MIDDLEMAN_ROUTE],
      pathName
    );

    if (hasAdminRoute) {
      return NextResponse.redirect(new URL(process.env.USER_DOMAIN!));
    }

    if (nextResult) {
      return nextResult;
    }
    return response;
  };
