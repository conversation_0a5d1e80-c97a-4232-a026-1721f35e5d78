import { Clock, MapPin, Search } from "lucide-react";
import { CardDescription, CardTitle } from "../ui/card";
import { SearchCardContentWrapper, SearchCardHeaderWrapper, SearchCardWrapper } from "./search-card-wrapper";
import { Input } from "../ui/input";
import useSeekersSearch from "@/hooks/use-seekers-search";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "../ui/button";
import { useDebounce } from "@/hooks/use-debounce";
import { useGetLocationSuggestion } from "@/core/applications/queries/listing/use-get-location-suggestion";
import { useSeekerSearchStore } from "@/stores/seeker-search.store";

export default function SeekerLocationSearch({ isActive, setActive, id }: { isActive?: boolean, setActive: (val: string) => void, id: string }) {
  const t = useTranslations("seeker")
  const { query } = useSeekerSearchStore(state => state)

  const { handleSetQuery, handleSearch, seekersSearch, } = useSeekersSearch()
  const [focusedInput, setFocusedInput] = useState(false)
  const debounceValue = useDebounce(query, 500)
  const locationSuggestion = useGetLocationSuggestion({ search: debounceValue })
  return <SearchCardWrapper setActive={setActive} id={id}>
    <SearchCardHeaderWrapper>
      <CardTitle className={isActive ? "" : "text-seekers-text font-normal"}>{t('navbar.search.locationTitle')}</CardTitle>
      <CardDescription className={`!mt-0 line-clamp-1 text-xs ${isActive ? "opacity-0" : "font-semibold text-black"}`}>{seekersSearch.query || t('navbar.search.flexibleLocation')}</CardDescription>
    </SearchCardHeaderWrapper>
    <SearchCardContentWrapper isActive={isActive}>
      <div className="pb-4 space-y-2">
        <div
          className="border rounded-lg flex items-center h-9 max-sm:h-8 
                      has-[:focus-visible]:outline-none has-[:focus-visible]:ring-1 has-[:focus-visible]:ring-inset has-[:focus-visible]:ring-ring ">
          <div className="w-9 h-full flex items-center justify-center">
            <Search className="w-4 h-4 text-seekers-text" />
          </div>
          <Input
            className="border-0 px-2.5 h-full focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-inset text-xs placeholder:text-seekers-text-lighter"
            value={query}
            onChange={e => {
              handleSetQuery(e.target.value)
            }}
            onFocus={() => setFocusedInput(true)}
            onBlur={e => {
              if (e.target.value == "") {
                setFocusedInput(true)
              } else {
                setFocusedInput(false)
              }
            }}
            placeholder={t('form.placeholder.seekersFindPropertyLocation')}
          />
        </div>
        {query.length >= 3 ?
          <div className="space-y-1">
            {
              locationSuggestion.data?.data ? <>
                <p className="text-seekers-text-light text-xs !mt-2">{t('misc.suggestion')}</p>
                {locationSuggestion.data?.data?.map((item, idx) => <div
                  key={idx}
                  onClick={() => handleSetQuery(item)}
                  className="flex gap-2 items-center active:bg-seekers-background hover:bg-seekers-background rounded-lg pr-4 cursor-pointer">
                  <Button size={"icon"} variant={"secondary"} className="bg-seekers-background hover:bg-seekers-background" >
                    <MapPin />
                  </Button>
                  <div className="text-xs">
                    <p className=" font-medium">{item}</p>
                  </div>
                </div>)
                }
              </>
                : <></>
            }
          </div>
          :
          <div className="space-y-1">
            {
              (seekersSearch.searchHistory.length >= 1) &&
              <>
                <p className="text-seekers-text-light text-xs !mt-2">{t('misc.recentSearch')}</p>
                {
                  seekersSearch.searchHistory.map((item, idx) => item.query && <div
                    key={idx}
                    onClick={() => handleSearch(item.query, item.propertyType)}
                    className="flex gap-2 items-center active:bg-foreground rounded-lg">
                    <Button size={"icon"} variant={"secondary"} className="bg-foreground  hover:bg-foreground">
                      <MapPin />
                    </Button>
                    <div>
                      <p className="text-xs text-neutral-500">{item.query}</p>
                    </div>
                  </div>
                  )
                }
              </>
            }
          </div>
        }
      </div>
    </SearchCardContentWrapper>
  </SearchCardWrapper>
}