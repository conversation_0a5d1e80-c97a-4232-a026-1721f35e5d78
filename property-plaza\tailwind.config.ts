import type { Config } from "tailwindcss"

const config = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
	],
  prefix: "",
  theme: {
  	container: {
  		center: true,
  		padding: '2rem',
  		screens: {
  			'2xl': '1400px'
  		}
  	},
  	extend: {
  		colors: {
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			background: 'hsl(var(--background))',
  			'seekers-background': 'hsl(var(--seekers-background))',
  			foreground: 'hsl(var(--foreground))',
  			'seekers-foreground': 'hsl(var(--seekers-foreground))',
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))',
  				light: 'hsl(var(--primary-light))',
  				lighter: 'hsl(var(--primary-lighter))',
  				dark: 'hsl(var(--primary-dark))',
  				darker: 'hsl(var(--primary-darker))'
  			},
  			'seekers-primary': {
  				DEFAULT: 'hsl(var(--seekers-primary))',
  				foreground: 'hsl(var(--seekers-primary-foreground))',
  				light: 'hsl(var(--seekers-primary-light))',
  				lighter: 'hsl(var(--seekers-primary-lighter))',
  				dark: 'hsl(var(--seekers-primary-dark))',
  				darker: 'hsl(var(--seekers-primary-darker))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))',
  				light: 'hsl(var(--secondary-light))',
  				lighter: 'hsl(var(--secondary-lighter))',
  				dark: 'hsl(var(--secondary-dark))',
  				darker: 'hsl(var(--secondary-darker))'
  			},
  			white: 'hsl(var(--white))',
  			black: 'hsl(var(--black))',
  			neutral: {
  				DEFAULT: 'hsl(var(--neutral))',
  				light: 'hsl(var(--neutral-light))',
  				lighter: 'hsl(var(--neutral-lighter))',
  				lightest: 'hsl(var(--neutral-lightest))',
  				dark: 'hsl(var(--neutral-dark))',
  				darker: 'hsl(var(--neutral-darker))',
  				darkest: 'hsl(var(--neutral-darkest))'
  			},
  			'seekers-text': {
  				DEFAULT: '#303030',
  				light: '#707070',
  				lighter: '#C0C0C0'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			'color-1': 'hsl(var(--color-1))',
  			'color-2': 'hsl(var(--color-2))',
  			'color-3': 'hsl(var(--color-3))',
  			'color-4': 'hsl(var(--color-4))',
  			'color-5': 'hsl(var(--color-5))',
  			sidebar: {
  				DEFAULT: 'hsl(var(--sidebar-background))',
  				foreground: 'hsl(var(--sidebar-foreground))',
  				primary: 'hsl(var(--sidebar-primary))',
  				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
  				accent: 'hsl(var(--sidebar-accent))',
  				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
  				border: 'hsl(var(--sidebar-border))',
  				ring: 'hsl(var(--sidebar-ring))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			},
  			'caret-blink': {
  				'0%,70%,100%': {
  					opacity: '1'
  				},
  				'20%,50%': {
  					opacity: '0'
  				}
  			},
  			marquee: {
  				from: {
  					transform: 'translateX(0)'
  				},
  				to: {
  					transform: 'translateX(calc(-100% - var(--gap)))'
  				}
  			},
  			'marquee-vertical': {
  				from: {
  					transform: 'translateY(0)'
  				},
  				to: {
  					transform: 'translateY(calc(-100% - var(--gap)))'
  				}
  			},
  			rainbow: {
  				'0%': {
  					'background-position': '0%'
  				},
  				'100%': {
  					'background-position': '200%'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out',
  			'caret-blink': 'caret-blink 1.25s ease-out infinite',
  			marquee: 'marquee var(--duration) infinite linear',
  			'marquee-vertical': 'marquee-vertical var(--duration) linear infinite',
  			rainbow: 'rainbow var(--speed, 2s) infinite linear'
  		},
  		minHeight: {
  			screen: '100dvh'
  		},
  		height: {
  			screen: '100dvh'
  		},
  		maxHeight: {
  			screen: '100dvh'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config

export default config