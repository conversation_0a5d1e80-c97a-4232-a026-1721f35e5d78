import { useTranslations } from "next-intl";
import { useEditBillingSchema } from "./use-edit-billing-form.schema";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { BillingInformation } from "@/core/domain/transaction/transaction";
import { PutCardBillingAddressDto } from "@/core/infrastructures/transaction/dto";
import { Form } from "@/components/ui/form";
import DefaultInput from "@/components/input-form/default-input";
import { useUpdateBillingInformation } from "@/core/applications/mutations/transaction/use-update-billing-information";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import en from 'react-phone-number-input/locale/en'
import CountryFlag from "@/components/utility/country-flag";
import { getCountries, Country } from "react-phone-number-input";
import BaseInputLayout from "@/components/input-form/base-input";
import { cn } from "@/lib/utils";


export default function EditBillingForm({ billingInfo, onSuccess }: { onSuccess?: () => void, billingInfo?: BillingInformation }) {
  const t = useTranslations("seeker")

  const formSchema = useEditBillingSchema()
  const { toast } = useToast()
  type formSchemaType = z.infer<typeof formSchema>

  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      addressOne: billingInfo?.addressOne,
      addressTwo: billingInfo?.addressTwo,
      city: billingInfo?.city,
      country: billingInfo?.country,
      name: billingInfo?.name,
      state: billingInfo?.state,
      postalCode: billingInfo?.postalCode
    }
  })
  const watcher = form.watch()
  const updateBillingInformationMutation = useUpdateBillingInformation()

  async function onSubmit(values: formSchemaType) {
    const data: PutCardBillingAddressDto = {
      city: values.city,
      country: values.country,
      line1: values.addressOne,
      line2: values.addressTwo != "" ? values.addressTwo : undefined,
      name: values.name,
      postal_code: values.postalCode,
      state: values.state
    }
    try {
      await updateBillingInformationMutation.mutateAsync(data)
      toast({
        title: t('success.updateBillingInformation.title')
      })
      onSuccess?.()
      // window.location.reload()
    } catch (e: any) {
      toast({
        title: t("error.failedUpdateBilling.title"),
        description: e?.response?.data.message || "",
        variant: "destructive"
      })
    }
  }
  return <Form {...form}>
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <DefaultInput
        type="text"
        form={form}
        name="name"
        variant="float"
        label={t("form.label.name")}
        labelClassName="text-xs text-seekers-text-light font-normal"
        placeholder={""}
      />
      <DefaultInput
        type="text"
        form={form}
        name="addressOne"
        variant="float"
        label={t("form.label.addressLineOne")}
        labelClassName="text-xs text-seekers-text-light font-normal"
        placeholder={""}
      />
      <DefaultInput
        type="text"
        form={form}
        name="addressTwo"
        variant="float"
        label={t("form.label.addressLineTwo")}
        labelClassName="text-xs text-seekers-text-light font-normal"
        placeholder={""}
      />
      <DefaultInput
        type="text"
        form={form}
        name="city"
        variant="float"
        label={t("form.label.city")}
        labelClassName="text-xs text-seekers-text-light font-normal"
        placeholder={""}
      />
      <DefaultInput
        type="text"
        form={form}
        name="state"
        variant="float"
        label={t("form.label.state")}
        labelClassName="text-xs text-seekers-text-light font-normal"
        placeholder={""}
      />
      <div className="grid grid-cols-2 gap-4">
        <BaseInputLayout
          label={t("form.label.country")}
          labelClassName={"absolute -top-2 left-2 px-1 text-xs bg-background z-10 text-seekers-text-light font-normal"}
          variant={"float"}>
          <div className={cn(
            'flex gap-2 w-full overflow-hidden')}>
            <Select value={watcher.country} defaultValue={en["ID"]} onValueChange={(val) => form.setValue("country", val)}>
              <SelectTrigger className='border-none focus:ring-0 shadow-none px-0'>
                <SelectValue asChild>
                  <div className="flex gap-2">
                    <CountryFlag code={watcher.country} className='rounded-full w-fit h-5 border' />
                    <p>{en[watcher.country as Country]}</p>
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={en["ZZ"]}>{en["ZZ"]}</SelectItem>
                {getCountries().map((item) => <SelectItem key={item} value={item} className='flex gap-2'>
                  <div className='flex gap-2'>
                    <CountryFlag code={item} className='rounded-full w-fit h-5' />
                    <p>{en[item]}</p>
                  </div>
                </SelectItem>)}
              </SelectContent>
            </Select>
          </div>
        </BaseInputLayout>

        <DefaultInput
          type="text"
          form={form}
          name="postalCode"
          variant="float"
          label={t("form.label.postalCode")}
          labelClassName="text-xs text-seekers-text-light font-normal"
          placeholder={""}
        />
      </div>
      <div className="flex justify-end">
        <Button type="submit" loading={updateBillingInformationMutation.isPending}>Update</Button>
      </div>
    </form>
  </Form>
}