import { GetListingParam } from "@/core/infrastructures/listing/dto";
import { getAllListingsService } from "@/core/infrastructures/listing/service";
import { BasePaginationRequest } from "@/core/utils/types";
import { useToast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";

export const ALL_QUALITY_CONTROL_LISTING_QUERY_KEY = "listing-quality-control"
export function useGetAllOwnerListing(data:BasePaginationRequest){
  const {page,per_page,search} = data
  const {toast} = useToast()
    const query = useQuery({
    queryKey: [ALL_QUALITY_CONTROL_LISTING_QUERY_KEY,page,per_page,search],
    queryFn: async () => {
      const data:GetListingParam = {
        page,per_page,search,type:"active"
      } 
        const response = await getAllListingsService(data)
        if(response.error){
          toast({ 
          title:response.error
        })
        }
        return response
      
    },
    retry: 0
  })
  return query
}