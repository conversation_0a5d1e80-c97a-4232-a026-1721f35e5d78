import { MetadataRoute } from "next";
import { getSSRSitemapPropertiesService } from "@/core/infrastructures/listing/service";
import { getPosts } from "@/core/services/sanity/services";

export const dynamic = "force-dynamic";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = "https://property-plaza.com";
  // Current date for lastModified
  const seekerListing = await getSSRSitemapPropertiesService();
  const now = new Date().toISOString();
  const property: MetadataRoute.Sitemap = [];
  if (seekerListing.data) {
    seekerListing.data?.map((item, idx) => {
      if (idx == 0) {
        property.push({
          url: `${baseUrl}/en/${item.title}?code=${item.id}`,
          priority: 0.9,
          changeFrequency: "monthly",
          lastModified: now,
        });
      } else {
        property.push({
          url: `${baseUrl}/en/${item.title}?code=${item.id}`,
          priority: 0.9,
          changeFrequency: "monthly",
          lastModified: item.updateAt
            ? new Date(item.updateAt).toISOString()
            : now,
        });
      }
    });
  }
  const blogPosts = await getPosts();
  const blogs: MetadataRoute.Sitemap = [];
  if (blogPosts) {
    blogPosts.map((item, idx) => {
      blogs.push({
        url: `${baseUrl}/en/posts/${item.slug.current}`,
        priority: 0.7,
        changeFrequency: "monthly",
        lastModified: now,
        // lastModified: item.publishedAt
        //   ? new Date(item.publishedAt).toISOString()
        //   : now,
      });
    });
  }
  return [
    {
      url: `${baseUrl}/en`,
      changeFrequency: "yearly",
      priority: 1,
      lastModified: now,
    },
    {
      url: `${baseUrl}/en/plan`,
      changeFrequency: "yearly",
      priority: 0.8,
      lastModified: now,
    },
    {
      url: `${baseUrl}/en/about-us`,
      changeFrequency: "yearly",
      priority: 0.8,
      lastModified: now,
    },
    {
      url: `${baseUrl}/en/contact-us`,
      changeFrequency: "yearly",
      priority: 0.5,
      lastModified: now,
    },
    {
      url: `${baseUrl}/en/privacy-policy`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
    },
    {
      url: `${baseUrl}/en/user-data-deletion`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
    },
    {
      url: `${baseUrl}/en/terms-of-use`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
    },
    {
      url: `${baseUrl}/en/s/all`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
    },
    {
      url: `${baseUrl}/en/terms-of-use`,
      changeFrequency: "yearly",
      priority: 0.3,
      lastModified: now,
    },
    ...property,
    ...blogs,
  ];
}
