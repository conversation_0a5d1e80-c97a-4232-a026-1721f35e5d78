import { TopUpBalance, TopUpDetail } from "@/core/domain/transaction/balance";
import {
  CardBillingAddressDto,
  PaymentMethodDto,
  TopUpDetailDto,
  TopUpDto,
  TransactionDetailDto,
  TransactionSeekerDto,
} from "./dto";
import {
  BillingInformation,
  PaymentMethod,
  Transaction,
  TransactionDetail,
} from "@/core/domain/transaction/transaction";
import { getCountryData, TCountryCode } from "countries-list";

export function transformTopUp(dto: TopUpDto[]): TopUpBalance[] {
  const data: TopUpBalance[] = dto.map((item) => ({
    credit: item.point,
    name: item.name,
    price: item.price,
    id: item.id,
  }));
  return data.sort((a, b) => a.price - b.price);
}

export function transformTopUpDetail(dto: TopUpDetailDto): TopUpDetail {
  const data: TopUpDetail = {
    description: dto.description,
    id: dto.id,
    name: dto.name,
    point: dto.point,
    price: dto.price,
  };
  return data;
}

export function transformTransactionSeeker(dto: TransactionSeekerDto[]) {
  const data: (Transaction | null)[] = dto.map((item) => {
    if (item.status == "EXPIRED") return null;
    return {
      isActive: item?.metadata?.subscription_status == "active" ? true : false,
      nextBilling: item?.metadata?.period_end_date_text || "",
      code: item.code,
      credit: 0,
      grandTotal: item.grand_total / 100,
      PayedAt: item?.metadata?.period_start_date_text || "",
      productName: item.items[0].name,
      requestAt: item.created_at,
      url: item?.url,
      status: item.status,
      type: item.type,
    } as Transaction;
  });

  const firstRow = dto[0];
  const metadata: BillingInformation = {
    state: firstRow.metadata.customer_billing_state,
    addressOne: firstRow.metadata.customer_billing_line1,
    addressTwo: firstRow.metadata.customer_billing_line2,
    city: firstRow.metadata.customer_billing_city,
    country: getCountryData(
      firstRow.metadata.customer_billing_country as TCountryCode
    ).name,
    name: firstRow.metadata.customer_name,
    postalCode: firstRow.metadata.customer_billing_postal_code,
  };
  return { data: data.filter((item) => item !== null), metadata: metadata };
}

export function transformTransactionDetail(
  dto: TransactionDetailDto
): TransactionDetail {
  const data: TransactionDetail = {
    code: dto.code,
    PayedAt: dto.updated_at,
    grandTotal: dto.grand_total,
    price: dto.items[0].total.toString(),
    productName: dto.items[0].name,
    requestAt: dto.created_at,
    status: dto.status,
    type: dto.type,
    url: dto.url,
    credit: dto.items[0].quantity,
  };
  return data;
}

export function transformCardBillingInfo(
  dto: PaymentMethodDto
): BillingInformation {
  return {
    addressOne: dto.card_billing.address.line1,
    addressTwo: dto.card_billing.address.line2,
    city: dto.card_billing.address.city,
    country: dto.card_billing.address.country,
    name: dto.card_billing.name,
    postalCode: dto.card_billing.address.postal_code.toString(),
    state: dto.card_billing.address.state,
  };
}

export function transfromPaymentMethod(
  dto: PaymentMethodDto[]
): PaymentMethod[] {
  return dto.map((item) => ({
    brand: item.display_brand.replaceAll("_", " "),
    cardNumber: item.card_number
      .replaceAll("*", "")
      .replaceAll(" ", "")
      .replaceAll("-", " "),
    expiredMonth: item.card_exp_month.toString().padStart(2, "0"),
    expiredYear: item.card_exp_year,
    id: item.id,
    isDefault: item.is_default,
  }));
}
