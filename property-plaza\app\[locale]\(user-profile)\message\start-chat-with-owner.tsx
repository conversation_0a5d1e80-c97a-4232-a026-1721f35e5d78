"use client"
import DialogWrapper from "@/components/dialog-wrapper/dialog-wrapper";
import { DialogTitle } from "@/components/ui/dialog";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import DialogHeaderWrapper from "@/components/dialog-wrapper/dialog-header-wrapper";
import ChatWithOwnerForm from "./form/chat-with-owner.form";

export default function StartChatWithOwner({ customTrigger, ownerId, propertyId }: { customTrigger: React.ReactNode, ownerId: string, propertyId: string }) {
  const t = useTranslations("seeker")
  const [propertyCode, setPropertyCode] = useState(propertyId)
  const [open, setOpen] = useState(false)
  useEffect(() => {
    if (!propertyId) return
    setPropertyCode(propertyId)
  }, [propertyId])
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={customTrigger}
    dialogClassName="sm:!min-w-[400px]"
  >
    <DialogHeaderWrapper
      className="text-start px-0"

    >
      <DialogTitle className="font-semibold">{t('message.chatOwner.title')}</DialogTitle>
    </DialogHeaderWrapper>
    <div className="space-y-2">
      <p>{t('message.chatOwner.description')}</p>
      <ChatWithOwnerForm
        submitHandler={() => setOpen(false)}
        ownerId={ownerId}
        propertyId={propertyCode} />
    </div>
  </DialogWrapper>
} 