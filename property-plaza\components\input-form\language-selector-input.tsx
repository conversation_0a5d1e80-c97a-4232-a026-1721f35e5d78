"use client"
import { FieldValues } from "react-hook-form"
import { FormControl, FormField, } from "../ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"
import { localLeanguageSelector } from "@/lib/locale/language-selector"
import { BaseInputForm } from "@/types/base"
import CountryFlag from "../utility/country-flag"
import BaseInputLayout from "./base-input"
import { useTranslations } from "next-intl"

interface LanguageInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  placeholder: string,
  description?: string,
  children?: React.ReactNode,
  isEditable?: boolean,
  showTitle?: boolean
}

export default function LanguageSelectorInput<T extends FieldValues>({ form, label, name, description, showTitle = true }: LanguageInputProps<T>) {
  const t = useTranslations("universal")
  return <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <BaseInputLayout label={label} description={description} containerClassName="flex justify-between items-center items-center">
        <Select onValueChange={field.onChange} defaultValue={field.value} >
          <FormControl>
            <SelectTrigger className={`w-fit ${showTitle ? "min-w[200px]" : "w-fit"}`}>
              <SelectValue placeholder={t('form.placeholder.pickLanguage')} />
            </SelectTrigger>
          </FormControl>
          <SelectContent className={`z-10`}>
            {localLeanguageSelector.map(item => <SelectItem className="w-full" key={item.id} value={item.value}>
              <div className="flex gap-2">
                <CountryFlag code={item.code.toUpperCase()} />
                {
                  showTitle &&
                  item.name
                }
              </div>
            </SelectItem>)}
          </SelectContent>
        </Select>
      </BaseInputLayout>
    )}
  />
}