"use client"
import { <PERSON><PERSON>ield } from '@/components/ui/form'
import { FieldValues } from 'react-hook-form'
import { Input } from '../ui/input'
import { BaseInputForm } from '@/types/base'
import BaseInputLayout from './base-input'
import { ComponentProps, useState } from 'react'
import { Button } from '../ui/button'
import { Eye, EyeOff } from 'lucide-react'
import { cn } from '@/lib/utils'

interface PasswordInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  placeholder: string
  description?: string
  inputProps?: ComponentProps<"input">,
  labelClassName?: string,
  inputContainer?: string,
  containerClassName?: string,
  variant?: "default" | "float"
}
export default function PasswordInput<T extends FieldValues>({ form, label, name, placeholder, description, inputProps, labelClassName, containerClassName, inputContainer, variant = "default" }: PasswordInputProps<T>) {
  const [isShowPassword, setShowPassword] = useState(false)
  return <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <BaseInputLayout
        label={label}
        description={description}
        labelClassName={cn(variant == "float" ? "absolute -top-2 left-2 px-1 text-xs bg-background z-10" : "", labelClassName)}
        containerClassName={containerClassName}
        variant={variant}

      >
        <div className={cn(
          'flex gap-2 w-full overflow-hidden',
          variant == "float" ? "" :
            "border rounded-sm focus-within:border-neutral-light", inputContainer)}>
          <Input
            type={isShowPassword ? "text" : "password"}
            placeholder={placeholder}
            {...field}
            {...inputProps}
            className={cn('border-none focus:outline-none shadow-none focus-visible:ring-0 w-full', variant == "float" ? "px-0" : "", inputProps?.className)}
          />
          <Button
            size={"icon"}
            variant={"ghost"}
            className='bg-transparent hover:bg-transparent text-seekers-text-light'
            type='button'
            onClick={e => {
              e.preventDefault()
              e.stopPropagation()
              setShowPassword(prev => !prev)
            }}>
            {
              isShowPassword ?
                <EyeOff className='w-4 h-4' />
                :
                <Eye className='w-4 h-4' />
            }
          </Button>
        </div>
      </ BaseInputLayout>
    )}
  />
}