import { apiClient } from "@/core/client";
import { RegisterUserDto, UpdateUserDto } from "./dto";
import { BasePaginationRequest } from "@/types/base";
import { AxiosRequestConfig } from "axios";

export const registerUser = async (
  data: RegisterUserDto,
  captchaToken?: string
) =>
  apiClient.post("auth/register", data, {
    headers: {
      "g-token": captchaToken || "",
    },
  });

export const allUser = async (
  searchParam: BasePaginationRequest & { type: string }
) =>
  apiClient.get(
    `portal/users?page=${searchParam.page}&per_page=${searchParam.per_page}&search=${searchParam.search}&type=${searchParam.type}`
  );

export const userDetail = async (id: string) =>
  apiClient.get(`portal/users/${id}`);

export const updateUserDetail = async (data: UpdateUserDto) =>
  apiClient.put("users/update", data);

export const getMe = async (option?: AxiosRequestConfig) =>
  apiClient.get("auth/me", option);
