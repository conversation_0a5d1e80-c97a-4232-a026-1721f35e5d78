
"use client"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useTranslations } from "next-intl";
import useSearchParamWrapper from "@/hooks/use-search-param-wrapper";
import { useEffect, useState } from "react";
import { BaseSelectInputValue } from "@/types/base";
import dynamic from "next/dynamic";

const FilterDialog = dynamic(() => import("./filter/filter-dialog"), { ssr: false })
export default function FilterHeader({ conversions, showFilter = true }: { showFilter?: boolean, conversions: { [key: string]: number } }) {
  const t = useTranslations("seeker")
  const { searchParams, createMultipleQueryString } = useSearchParamWrapper()
  const [filter, setFilter] = useState<string>("most-view")

  const filterContent: BaseSelectInputValue<string>[] = [
    {
      id: '1',
      content: t('listing.filter.sortBy.higherPrice'),
      value: 'PRICE_HIGHEST'
    },
    {
      id: '2',
      content: t('listing.filter.sortBy.lowerPrice'),
      value: 'PRICE_LOWEST'
    }, {
      id: '3',
      content: t('listing.filter.sortBy.newestFirst'),
      value: 'DATE_NEWEST'
    },
    {
      id: '4',
      content: t('listing.filter.sortBy.oldest'),
      value: 'DATE_OLDEST'
    },
    {
      id: '5',
      content: t('listing.filter.sortBy.smallest'),
      value: 'LAND_SMALLEST'
    },
    {
      id: '6',
      content: t('listing.filter.sortBy.largest'),
      value: 'LAND_LARGEST'
    },
    {
      id: '7',
      content: t('listing.filter.sortBy.mostViewed'),
      value: 'POPULARITY'
    },
    {
      id: '8',
      content: t('listing.filter.sortBy.mostFavorited'),
      value: 'FAVORITE'
    },
    {
      id: '9',
      content: t('listing.filter.sortBy.natureView'),
      value: 'VIEW_SCRENERY'
    },

  ]

  useEffect(() => {
    const sortBy = searchParams.get("sort")
    const sortBySelected = filterContent.find(item => item.value === sortBy)
    setFilter(sortBySelected?.value || "")

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const handleUpdateFilter = (value: string) => {
    setFilter(value)
    createMultipleQueryString([{ name: "page", value: "1" }, { name: "sortBy", value: value }])
  }
  return <div className="flex gap-2">
    <Select defaultValue="DATE_NEWEST" onValueChange={val => handleUpdateFilter(val)}>
      <SelectTrigger className="min-w-[164px] bg-seekers-primary text-white w-fit text-xs font-medium">
        <SelectValue placeholder="Filter" />
      </SelectTrigger>
      <SelectContent>
        {
          filterContent.map(item =>
            <SelectItem key={item.id} value={item.value} className="font-medium text-[#AFB1B6] text-xs">{item.content}</SelectItem>

          )
        }
      </SelectContent>
    </Select>
    {showFilter &&
      <FilterDialog conversions={conversions} />
    }
  </div>
}