"use client"
import dynamic from "next/dynamic";
const SeekersFooter = dynamic(() => import("@/components/footer/seeker-footer"), { ssr: false })
const SetupSeekersStore = dynamic(() => import("./setup-seekers"), { ssr: false })
const PopUp = dynamic(() => import("./pop-up"), { ssr: false })
export default function ClientUserLayout() {
  return <>
    <SetupSeekersStore />
    <SeekersFooter />
    <PopUp />

  </>
}