import { useLocale } from "next-intl";
import { useRouter, usePathname } from "@/lib/locale/navigations";
import { startTransition, useState } from "react";
import useSearchParamWrapper from "./use-search-param-wrapper";

export default function useLocaleSwitcher() {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const { generateQueryString } = useSearchParamWrapper();

  const changeLanguage = (language: string) => {
    const query = generateQueryString("", "");
    startTransition(() => {
      router.replace(
        // @ts-expect-error -- TypeScript will validate that only known `params`
        // are used in combination with a given `pathname`. Since the two will
        // always match for the current route, we can skip runtime checks.
        pathname + "?" + query,
        { locale: language }
      );
      router.refresh();
    });
  };
  return { changeLanguage, locale };
}
