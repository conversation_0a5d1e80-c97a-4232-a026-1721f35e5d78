import { updateUserDetail } from "@/core/infrastructures/user/api"
import { UpdateUserDto } from "@/core/infrastructures/user/dto"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { MY_DETAIL_QUERY_KEY } from "../../queries/users/use-get-me"
import { useToast } from "@/hooks/use-toast"
import { useTranslations } from "next-intl"

export function useUpadeUserDetail(){
  const queryClient = useQueryClient()
  const {toast} = useToast()
  const t = useTranslations("universal")
  const mutation = useMutation({
    mutationFn: (data:UpdateUserDto) => updateUserDetail(data),
    onSuccess: () => {
      queryClient.invalidateQueries({queryKey: [MY_DETAIL_QUERY_KEY]})
      toast({
        title: t('success.updateUser')
      })
    },
    onError: (error) => {
      const data: any = (error as any).response.data
      toast({
        title: t('error.foundError'),
        description: data.message,
        variant: "destructive"
      })
    },
  })
  return mutation
}