import { useTranslations } from "next-intl";
import FilterContentLayout from "./filter-content-layout";
import { BaseSelectInputValue } from "@/types/base";
import { Mountain, Sprout, Star, Trees } from "lucide-react";
import CheckboxFilterItem from "./checkbox-filter-item";
import { useSeekerFilterStore } from "@/stores/seekers-filter-2.store";
import { FaWater } from "react-icons/fa";
import { listingViewType } from "@/core/domain/listing/listing-seekers";

export default function ViewFilter() {
  const t = useTranslations("seeker")
  const { view, setView, setViewToAnything } = useSeekerFilterStore(state => state)
  const views: BaseSelectInputValue<string>[] = [
    {
      id: "1",
      content: <div className="flex gap-1 items-center">
        <Star className="w-4 h-4" strokeWidth={1.5} />
        <span className="">{t('listing.filter.view.optionOne.title')}</span>
      </div>,
      value: listingViewType.all
    },
    {
      id: "2",
      content: <div className="flex gap-1 items-center">
        <Mountain className="w-4 h-4" strokeWidth={1.5} />
        <span className="">{t('listing.filter.view.optionTwo.title')}</span>
      </div>,
      value: listingViewType.mountain
    },
    {
      id: "3",
      content: <div className="flex gap-1 items-center">
        <FaWater className="w-4 h-4" strokeWidth={1} />
        <span className="">{t('listing.filter.view.optionThree.title')}</span>
      </div>,
      value: listingViewType.ocean
    },
    {
      id: "4",
      content: <div className="flex gap-1 items-center">
        <Sprout className="w-4 h-4" strokeWidth={1} />
        <span className="">{t('listing.filter.view.optionFour.title')}</span>
      </div>,
      value: listingViewType.ricefield
    },
    {
      id: "5",
      content: <div className="flex gap-1 items-center">
        <Trees className="w-4 h-4" strokeWidth={1} />
        <span className="">{t('listing.filter.view.optionFive.title')}</span>
      </div>,
      value: listingViewType.jungle
    },
  ]

  const handleSetView = (val: string) => {
    if (val == listingViewType.all) {
      setViewToAnything()
    } else {
      setView(val)
    }
  }
  return <FilterContentLayout title={t('listing.filter.typeView.title')}>
    <div className="flex gap-2 max-sm:grid max-sm:grid-cols-2">
      {
        views.map(item =>
          <CheckboxFilterItem
            key={item.id}
            item={item}
            setValue={handleSetView}
            isActive={view.includes(item.value) || (view.length == 0 && item.value == "ANY")}
            className="p-6 h-16 rounded-xl w-full text-center justify-center" />
        )}
    </div>
  </FilterContentLayout>
}