"use client"

import { useMediaQuery } from "@/hooks/use-media-query"
import { DrawerDescription } from "../ui/drawer"
import { DialogDescription } from "../ui/dialog"

export default function DialogDescriptionWrapper({ children, className }: { children: React.ReactNode, className?: string }) {
  const isDesktop = useMediaQuery("(min-width:768px)")
  if (isDesktop) {
    return <DialogDescription className={className}>
      {children}
    </DialogDescription>
  } else {
    return <DrawerDescription className={className}>
      {children}
    </DrawerDescription>
  }
}