/** @type {import('next').NextConfig} */
import createNextIntlPlugin from "next-intl/plugin";
import bundleAnalyzer from "@next/bundle-analyzer";
const withNextIntl = createNextIntlPlugin("./lib/locale/i18n.ts");
const nextConfig = {
  // compiler: {
  //   removeConsole: true,
  // },
  reactStrictMode: true,
  transpilePackages: ["lucide-react"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "filestorage.property-plaza.id",
        port: "",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "placehold.co",
        port: "",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "images.pexels.com",
        port: "",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "www.facebook.com",
        port: "",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "maps.googleapis.com",
        port: "",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "cdn.sanity.io",
        port: "",
        pathname: "**",
      },
    ],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    minimumCacheTTL: 60,
  },
  productionBrowserSourceMaps: false, // Disable source maps in development
  optimizeFonts: false, // Disable font optimization
  env: {
    PAYMENT_URL: process.env.PAYMENT_URL,
    API_KEY: process.env.API_KEY,
    SUCCESS_RETURN_URL: process.env.SUCCESS_RETURN_URL,
    CANCEL_RETURN_URL: process.env.CANCEL_RETURN_URL,
  },
  // experimental: {
  //   webVitalsAttribution: ["CLS", "LCP"],
  // },
  // webpack(config, { dev, isServer }) {
  //   // Code splitting
  //   // Ensure splitChunks is an object before modifying
  //   if (
  //     !config.optimization.splitChunks ||
  //     config.optimization.splitChunks === false
  //   ) {
  //     config.optimization.splitChunks = {};
  //   }

  //   // Code splitting settings
  //   config.optimization.splitChunks = {
  //     ...config.optimization.splitChunks,
  //     chunks: "async",
  //     minSize: 20000,
  //     maxAsyncRequests: 5,
  //     maxInitialRequests: 3,
  //     cacheGroups: {
  //       defaultVendors: {
  //         test: /[\\/]node_modules[\\/]/,
  //         priority: -10,
  //         reuseExistingChunk: true,
  //       },
  //       default: {
  //         minChunks: 2,
  //         priority: -20,
  //         reuseExistingChunk: true,
  //       },
  //     },
  //   };

  //   //Only minimize the bundle in production
  //   if (!dev && !isServer) {
  //     config.optimization.minimize = true;
  //     config.optimization.concatenateModules = true;
  //     config.optimization.usedExports = true;
  //   }
  //   return config;
  // },
};
const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
});

export default withNextIntl(withBundleAnalyzer(nextConfig));
